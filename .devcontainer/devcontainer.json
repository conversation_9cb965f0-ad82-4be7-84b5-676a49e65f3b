// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/javascript-node
{
  "name": "WebPortal",
  // Or use a Dockerfile or Docker Compose file. More info: https://containers.dev/guide/dockerfile
  "image": "mcr.microsoft.com/devcontainers/javascript-node:1-18-bookworm",
  "features": {
    "ghcr.io/devcontainers/features/github-cli:1": {}
  },

  // Use 'forwardPorts' to make a list of ports inside the container available locally.
  "forwardPorts": [4000],

  // Use 'postCreateCommand' to run commands after the container is created.
  "postCreateCommand": "bash $(pwd)/.devcontainer/post-install.sh",

  "runArgs": ["--network=localstack_default"],

  // Configure tool-specific properties.
  "customizations": {
    // Configure properties specific to VS Code.
    "vscode": {
      "settings": {
        "workbench.colorTheme": "Default Dark+",
        "prettier.singleQuote": true
      },
      // Add the IDs of extensions you want installed when the container is created.
      "extensions": [
        "dbaeumer.vscode-eslint",
        "eamodio.gitlens",
        "esbenp.prettier-vscode",
        "sonarsource.sonarlint-vscode",
        "GitLab.gitlab-workflow"
      ]
    }
  }
  // Uncomment if want to mount your ssh keys to the container
  // "mounts": [
  //   "source=${localEnv:HOME}/.ssh,target=/home/<USER>/.ssh,type=bind,consistency=cached"
  // ]
  // Uncomment to connect as root instead. More info: https://aka.ms/dev-containers-non-root.
  // "remoteUser": "root"
}
