# Version control
.git
.gitignore
.github
.gitlab-ci.yml

# Build outputs
dist
build
node_modules
coverage
*.log
logs
*.tsbuildinfo

# Development files
.vscode
.idea
.devcontainer
.husky
.eslintcache
.env.local
.env.*.local
*.local
.DS_Store
Thumbs.db

# Docker files
Dockerfile*
docker-compose*
.docker
.dockerignore

# Documentation
README.md
CHANGELOG.md
docs
*.md

# Configuration files not needed in production
.prettierrc
.eslintrc
.editorconfig
.nvmrc
.tool-versions
