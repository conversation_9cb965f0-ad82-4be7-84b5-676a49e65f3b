ARG NODE_VERSION=18.20.4
ARG ARTIFACTORY_URL
FROM $ARTIFACTORY_URL/hmd-docker-virtual/node:$NODE_VERSION-alpine AS base

# Stage 1: Dependencies/Node Modules
FROM base AS deps
ARG JFROG_USERNAME
ARG JFROG_PASSWORD

RUN cp /dev/null /etc/apk/repositories
RUN echo "https://$JFROG_USERNAME:$<EMAIL>/artifactory/hmd-alpinelinux/v3.19/main" >>/etc/apk/repositories
RUN echo "https://$JFROG_USERNAME:$<EMAIL>/artifactory/hmd-alpinelinux/v3.19:/community" >>/etc/apk/repositories

# Install build dependencies
RUN apk --no-cache add \
  bash \
  g++ \
  make \
  python3 \
  zlib-dev \
  libc-dev \
  bsd-compat-headers \
  py-setuptools \
  openssl-dev \
  lz4-dev \
  musl-dev \
  cyrus-sasl-dev \
  ca-certificates

WORKDIR /home/<USER>/app
COPY package*.json ./
RUN npm install \
  --dist-url=https://$JFROG_USERNAME:$JFROG_PASSWORD@$ARTIFACTORY_URL/artifactory/hmd-npm-virtual \
  --verbose

WORKDIR /home/<USER>/app/server
COPY server/package*.json ./
RUN npm install \
  --dist-url=https://$JFROG_USERNAME:$JFROG_PASSWORD@$ARTIFACTORY_URL/artifactory/hmd-npm-virtual \
  --verbose

# Stage 2: Build frontend static files
FROM base AS builder

WORKDIR /home/<USER>/app
COPY --from=deps /home/<USER>/app/node_modules ./node_modules
COPY . .

# Uncomment this if local testing/development
# RUN cp .env.example .env
RUN npm run build

# Stage 3: Runtime
FROM base as runner

RUN cp /dev/null /etc/apk/repositories
RUN echo "https://$JFROG_USERNAME:$<EMAIL>/artifactory/hmd-alpinelinux/v3.19/main" >>/etc/apk/repositories
RUN echo "https://$JFROG_USERNAME:$<EMAIL>/artifactory/hmd-alpinelinux/v3.19:/community" >>/etc/apk/repositories

# Install runtime dependencies
RUN apk --no-cache add \
  lz4-dev \
  cyrus-sasl \
  ca-certificates

WORKDIR /home/<USER>/app
COPY --from=builder /home/<USER>/app/dist ./dist
COPY --from=deps /home/<USER>/app/server/node_modules ./server/node_modules
COPY server/server.js /home/<USER>/app/server/server.js

USER node
EXPOSE 3000

# Set the command to serve the built static files.
ENTRYPOINT ["node", "server/server.js"]
