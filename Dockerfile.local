ARG NODE_VERSION=18.20.4
FROM node:$NODE_VERSION-alpine AS base

# Stage 1: Dependencies/Node Modules
FROM base AS deps

RUN apk --no-cache add \
  bash \
  g++ \
  make \
  python3 \
  zlib-dev \
  libc-dev \
  bsd-compat-headers \
  py-setuptools \
  openssl-dev \
  lz4-dev \
  musl-dev \
  cyrus-sasl-dev \
  ca-certificates

WORKDIR /home/<USER>/app
COPY package*.json ./
RUN npm ci

WORKDIR /home/<USER>/app/server
COPY server/package*.json ./
RUN npm ci

# Stage 2: Build
FROM base AS builder

WORKDIR /home/<USER>/app
COPY --from=deps /home/<USER>/app/node_modules ./node_modules
COPY . .

RUN cp .env.example .env
RUN npm run build

# Stage 3: Runtime
FROM base AS runner

# Install runtime dependencies
RUN apk --no-cache add \
  lz4-dev \
  cyrus-sasl \
  ca-certificates

WORKDIR /home/<USER>/app
COPY --from=builder /home/<USER>/app/dist ./dist
COPY --from=deps /home/<USER>/app/server/node_modules ./server/node_modules
COPY server/server.js /home/<USER>/app/server/server.js
COPY .env.example server/.env

USER node
EXPOSE 3000

# Set the command to serve the built static files.
ENTRYPOINT ["node", "server/server.js"]
