version: 0.2

artifacts:
  files:
    - build/**/*
    - scripts/fortify.sh
    - src/**/*
    - .eslintrc.js
    - .flowconfig
    - .gitignore 
    - .nvmrc
    - .prettierrc
    - package-lock.json
    - package.json
    - sonar-project-globe.properties

phases:
  install:
    runtime-versions:
      nodejs: 10
  pre_build:
    commands:
      - hwclock -s || date
      - bash scripts/fortify.sh
      - npm i npm@latest -g
  # Uncomment when CodeBuild is integrated and Parameter Store is available
  build:
    commands:
      - echo "Perform build..."
      # - aws s3 sync . s3://pgwdev-redirect
      # - echo "$APP_DEV_ENV" > .env
      # - npm install && npm run build
  post_build:
    commands:
      - mv sonar-project-globe.properties sonar-project.properties
      - sonar-scanner
