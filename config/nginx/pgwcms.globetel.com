log_format main 'server="$server_name" host="$host" dest_port="$server_port"'
                'src="$remote_addr" ip="$realip_remote_addr" user="$remote_user" '
                'time_local="$time_local" http_status="$status" '
                'http_referer="$http_referer" http_user_agent="$http_user_agent" '
                'http_x_forwarded_for="$http_x_forwarded_for" '
                'http_x_header="$http_x_header" uri_query="$query_string" uri_path="$uri" '
                'request=$request http_method="$request_method"';

upstream web-portal-api {
  ip_hash;
  server 127.0.0.1:3000;
}

server {
  listen 80;
  listen [::]:80;
  server_name pgwcms.globetel.com;
  return 301 https://$server_name$request_uri;
}

server {
  ssl on;
  listen 443;
  ssl_certificate /etc/ssl/certs/pgwcmsprod_02212020.crt;
  ssl_certificate_key /etc/ssl/private/VMPGWAPD01pgwcms.key;
  ssl_dhparam /etc/ssl/certs/dhparam.pem;

  ssl_protocols TLSv1.2;
  ssl_prefer_server_ciphers on;
  ssl_ciphers ALL:!EXP:!NULL:!ADH:!LOW:!SSLv2:!SSLv3:!MD5:!RC4;
  # ssl_ciphers "EECDH+AESGCM:EDH+AESGCM:AES256+EECDH:AES256+EDH";
  ssl_ecdh_curve secp384r1;
  ssl_session_cache shared:SSL:10m;
  ssl_session_timeout 10m;
  ssl_session_tickets off;
  ssl_stapling on;
  ssl_stapling_verify on;
  resolver ******* ******* valid=300s;
  resolver_timeout 5s;
  ssl_trusted_certificate /usr/local/share/ca-certificates/pgwcms.cer;

  # Disable preloading HSTS for now.  You can use the commented out header line that includes
  # the "preload" directive if you understand the implications.
  # add_header Strict-Transport-Security "max-age=63072000; includeSubdomains; preload";
  add_header Strict-Transport-Security "max-age=63072000; includeSubdomains";
  add_header X-Frame-Options DENY;
  add_header X-Content-Type-Options nosniff;
  add_header X-Xss-Protection "1; mode=block";
  
  server_name pgwcms.globetel.com;

  keepalive_timeout 60;
  send_timeout 60;
  server_tokens off;
  client_body_timeout 10;
  client_header_timeout 10;
  client_max_body_size 100K;
  large_client_header_buffers 2 1k;
  proxy_hide_header X-Powered-By;
  # need to install Nginx module - headers-more-nginx-module 
  more_set_headers 'Server: ';

  location /graphql {
      proxy_pass http://web-portal-api;
      proxy_http_version 1.1;
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection 'upgrade';
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_cache_bypass $http_upgrade;
  }

  location / {
      root /var/www/pgwcms.globetel.com/;
      index index.html;
      proxy_http_version 1.1;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection 'upgrade';
      proxy_set_header Host $host;
      proxy_cache_bypass $http_upgrade;

      if ($request_method = POST) {
              rewrite ^/(.*)$ /post_redirect/$1 last;
      }
      try_files $uri $uri/ /index.html;
      access_log /logs/portal/web-portal.access.log;
      error_log  /logs/portal/web-portal.error.log;
  }
}

