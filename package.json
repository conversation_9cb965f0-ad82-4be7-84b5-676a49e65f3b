{"name": "gpayo-cms-frontend-service", "version": "0.1.0", "private": true, "engines": {"node": ">=18.x"}, "author": "Stratpoint Technologies Inc.", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "Jzauwne Abalus <<EMAIL>>", "<PERSON> <z<PERSON><PERSON><PERSON>@stratpoint.com>"], "dependencies": {"@adyen/adyen-web": "^5.57.0", "@apollo/client": "^3.9.5", "@fortawesome/fontawesome-svg-core": "^1.2.25", "@fortawesome/free-brands-svg-icons": "^5.11.2", "@fortawesome/free-solid-svg-icons": "^5.11.2", "@fortawesome/react-fontawesome": "^0.1.19", "@okta/okta-auth-js": "^7.12.1", "@okta/okta-react": "^6.10.0", "@react-oauth/google": "^0.12.1", "@stratpoint-fe/react-onclickout": "^1.0.0", "apollo-link-error": "^1.1.13", "apollo-upload-client": "^18.0.1", "date-fns": "^1.30.1", "dotenv": "^10.0.0", "eslint": "^8.57.0", "graphql": "^16.8.1", "json-2-csv": "5.0.1", "normalize.css": "^8.0.1", "react": "^18.2.0", "react-apollo-network-status": "^5.2.1", "react-datepicker": "^6.1.0", "react-dom": "^18.2.0", "react-loading-skeleton": "^3.4.0", "react-router-dom": "^5.3.4", "react-spring": "^8.0.27", "recharts": "^2.11.0", "styled-components": "^6.1.8", "yup": "1.3.3"}, "devDependencies": {"@testing-library/jest-dom": "^6.4.6", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "@vitejs/plugin-react": "^4.2.1", "@vitest/coverage-v8": "^1.6.0", "@vitest/ui": "^1.6.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.1.3", "eslint-plugin-react": "7.33.2", "eslint-plugin-react-hooks": "4.6.0", "eslint-plugin-vitest": "^0.3.22", "eslint-plugin-vitest-globals": "^1.4.0", "husky": "^9.0.11", "jsdom": "^24.1.0", "lint-staged": "^15.2.7", "prettier": "3.2.4", "prop-types": "^15.7.2", "vite": "^5.2.8", "vite-plugin-svgr": "^4.2.0", "vitest": "^1.6.0"}, "scripts": {"start": "vite", "build": "vite build", "build:dev": "vite build --mode development", "test": "vitest", "test:ui": "vitest --ui", "coverage": "vitest run --coverage", "eslint": "eslint . --ignore-path .gitignore", "prettier": "prettier \"**/*.+(js|jsx|json|yaml)\"", "check-format": "npm run prettier -- --list-different", "format": "npm run prettier -- --write", "dev": "vite --host", "start:server": "node server/server.js"}, "lint-staged": {"src/**/*.{js,jsx}": ["eslint --fix", "git add "]}, "browserslist": [">0.2%", "not dead", "not ie <= 11", "not op_mini all"]}