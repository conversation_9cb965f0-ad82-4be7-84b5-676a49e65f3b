require('dotenv').config();

const fastify = require('fastify')();
const path = require('path');
const PORT = process.env.PORT || 3000;
const HOST = process.env.HOST || '0.0.0.0';
const fs = require('fs');

const getRuntimeConfig = () => {
  // Add any environment variables you want to expose to the frontend
  return {
    VITE_REACT_APP_API_URL: process.env.VITE_REACT_APP_API_URL,
    VITE_REACT_APP_PS_API_URL: process.env.VITE_REACT_APP_PS_API_URL,
    VITE_REACT_APP_OKTA_ISSUER: process.env.VITE_REACT_APP_OKTA_ISSUER,
    VITE_REACT_APP_OKTA_CLIENT_ID: process.env.VITE_REACT_APP_OKTA_CLIENT_ID,
    VITE_ENABLE_OPERATION_LOGGING: process.env.VITE_ENABLE_OPERATION_LOGGING,
    VITE_NODE_ENV: process.env.VITE_NODE_ENV,
  };
};

const extractBaseDomain = url => {
  const match = url.match(/^(https?:\/\/[^\/]+)/) || null;
  return match ? match[1] : url;
};

// Serve runtime config as JavaScript
fastify.get('/config.js', (_, reply) => {
  const config = getRuntimeConfig();
  const configScript = `window.__RUNTIME_CONFIG__ = ${JSON.stringify(config, null, 2)};`;
  reply
    .type('application/javascript')
    .header('cache-control', 'no-store')
    .send(configScript);
});

// Configure security with more restrictive settings
fastify.register(require('@fastify/helmet'), {
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'"],
      connectSrc: [
        "'self'",
        process.env.VITE_REACT_APP_API_URL || 'http://localhost:3000',
        extractBaseDomain(process.env.VITE_REACT_APP_OKTA_ISSUER) ||
          'https://globemfa.okta.com',
      ],
      frameSrc: [
        "'self'",
        extractBaseDomain(process.env.VITE_REACT_APP_OKTA_ISSUER) ||
          'https://globemfa.okta.com',
      ],
      imgSrc: ["'self'", 'data:'],
      styleSrc: ["'self'", "'unsafe-inline'"],
    },
  },
  crossOriginOpenerPolicy: { policy: 'same-origin-allow-popups' }, // For auth popups
  crossOriginResourcePolicy: { policy: 'same-site' },
});

fastify.register(require('@fastify/compress'), {
  global: true,
  threshold: 1024, // Only compress responses larger than 1KB
  encodings: ['gzip', 'deflate'],
});

// Add CORS support - restricted to only the GraphQL API
fastify.register(require('@fastify/cors'), {
  origin: [process.env.VITE_REACT_APP_API_URL || 'http://localhost:3000'], // Only allow this origin
  methods: ['GET', 'POST', 'OPTIONS'], // GraphQL typically only needs these methods
  credentials: true,
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'pswebtoken',
    'psaccesstoken',
  ],
});

// Health check endpoint
fastify.get('/healthz', (_, reply) => {
  reply.code(200).send('OK');
});

// Static file serving
fastify.register(require('@fastify/static'), {
  root: path.join(__dirname, '../dist'),
  maxAge: '1y',
});

// Catch-all route for client-side routing
fastify.setNotFoundHandler((request, reply) => {
  const indexPath = path.join(__dirname, '../dist/index.html');
  const stream = fs.createReadStream(indexPath);
  reply.type('text/html').send(stream);
});

fastify
  .listen({ port: PORT, host: HOST })
  .then(address => {
    console.log(`[gpayo-cms-frontend] Serving on ${address}`);
  })
  .catch(err => {
    console.error(err);
    process.exit(1);
  });

// Graceful shutdown
const shutdown = async signal => {
  console.log(
    `[gpayo-cms-frontend] Received ${signal}, shutting down gracefully`
  );

  try {
    await fastify.close();
    console.log('[gpayo-cms-frontend] Server closed successfully');
    process.exit(0);
  } catch (err) {
    console.error('[gpayo-cms-frontend] Error during shutdown:', err);
    process.exit(1);
  }
};

// Handle shutdown signals
['SIGINT', 'SIGTERM'].forEach(signal => {
  process.on(signal, () => shutdown(signal));
});
