# Must be unique in a given SonarQube instance
sonar.projectKey=ups-web-portal

# This is the name and version displayed in the SonarQube UI. Was mandatory prior to SonarQube 6.1.
sonar.projectName=UPS Web Portal
sonar.projectVersion=1.0

# Path is relative to the sonar-project.properties file. Replace "\" by "/" on Windows.
# This property is optional if sonar.modules is set. 
sonar.sources=/globe-ups/web-portal

# Login credentials
sonar.host.url=https://sonarqube.console.stratpoint.io
sonar.login=****************************************
