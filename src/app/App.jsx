import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import React, { useContext } from 'react';
import styled from 'styled-components';
import AuthContext from './context/AuthContext/AuthContext';
import { ConfigProvider } from './context/ConfigContext/ConfigContext';
import { NavProvider } from './context/NavContext';
import { ResponsiveProvider } from './context/ResponsiveContext';
import Router from './router';
import NotificationAlert from './components/NotificationAlert/index';
import { NotificationProvider } from './context/NotificationContext';

const PageContainer = styled.div`
  display: flex;
  height: 100vh;
  width: 100vw;
  justify-content: center;
  align-items: center;
  flex-direction: column;
`;

const LoadingLabel = styled.h2`
  color: #009bdd;
`;

const App = () => {
  const { isAuthenticating } = useContext(AuthContext);

  if (isAuthenticating) {
    return (
      <PageContainer>
        <FontAwesomeIcon color="#009BDD" icon="spinner" spin size="6x" />
        <LoadingLabel>Loading...</LoadingLabel>
      </PageContainer>
    );
  }

  return (
    <ResponsiveProvider>
      <ConfigProvider>
        <NavProvider>
          <NotificationProvider>
            <>
              <Router />
              <NotificationAlert />
            </>
          </NotificationProvider>
        </NavProvider>
      </ConfigProvider>
    </ResponsiveProvider>
  );
};

export default App;
