import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import PropTypes from 'prop-types';
import React from 'react';
import styled from 'styled-components';
import Button from '../Button/Button';

export const ActionButtonsContainer = styled.div`
  display: flex;
  flex-direction: row;
  max-width: 100px;
`;

export const ActionButton = styled(Button)`
  font-size: 16px;
  flex: 1;
  color: #474747;
`;

export const Check = styled(Button)`
  font-size: 16px;
  flex: 1;
  color: #00ff00;

  &:disabled {
    filter: opacity(0);
  }
`;

export const Cross = styled(Button)`
  font-size: 16px;
  flex: 1;
  color: #ff0000;

  &:disabled {
    filter: opacity(0);
  }
`;

const ActionButtons = ({
  handleView,
  handleEdit,
  handleDelete,
  checkIcon,
  disabled = {
    view: false,
    edit: false,
    delete: false,
    checkIcon: false,
  },
}) => {
  return (
    <ActionButtonsContainer>
      {checkIcon && !disabled.checkIcon ? (
        <Check onClick={checkIcon} disabled={disabled.checkIcon}>
          <FontAwesomeIcon icon="check-circle" />
        </Check>
      ) : (
        checkIcon &&
        disabled.checkIcon && (
          <Cross>
            <FontAwesomeIcon icon="times-circle" />
          </Cross>
        )
      )}
      {handleView && (
        <ActionButton onClick={handleView} disabled={disabled.view}>
          <FontAwesomeIcon icon="eye" />
        </ActionButton>
      )}
      {handleEdit && (
        <ActionButton onClick={handleEdit} disabled={disabled.edit}>
          <FontAwesomeIcon icon="pen" />
        </ActionButton>
      )}
      {handleDelete && (
        <ActionButton onClick={handleDelete} disabled={disabled.delete}>
          <FontAwesomeIcon icon="trash-alt" />
        </ActionButton>
      )}
    </ActionButtonsContainer>
  );
};

ActionButtons.propTypes = {
  handleView: PropTypes.oneOfType([PropTypes.func, PropTypes.bool]),
  handleEdit: PropTypes.oneOfType([PropTypes.func, PropTypes.bool]),
  handleDelete: PropTypes.oneOfType([PropTypes.func, PropTypes.bool]),
  checkIcon: PropTypes.oneOfType([PropTypes.func, PropTypes.bool]),
  disabled: PropTypes.object,
};

export default ActionButtons;
