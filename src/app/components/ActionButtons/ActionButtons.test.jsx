import React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { render } from '../../utils/test-utils';
import { library } from '@fortawesome/fontawesome-svg-core';
import {
  faCheckCircle,
  faTimesCircle,
  faEye,
  faPen,
  faTrashAlt,
} from '@fortawesome/free-solid-svg-icons';
import ActionButtons from './ActionButtons';
import '@testing-library/jest-dom';

library.add(faCheckCircle, faTimesCircle, faEye, faPen, faTrashAlt);

describe('ActionButtons', () => {
  it('renders ActionButtons with all buttons', () => {
    const handleView = vi.fn();
    const handleEdit = vi.fn();
    const handleDelete = vi.fn();
    const checkIcon = vi.fn();

    const { container } = render(
      <ActionButtons
        handleView={handleView}
        handleEdit={handleEdit}
        handleDelete={handleDelete}
        checkIcon={checkIcon}
        disabled={{ view: false, edit: false, delete: false, checkIcon: false }}
      />
    );

    expect(container.querySelector('[data-icon="eye"]')).toBeInTheDocument();
    expect(container.querySelector('[data-icon="pen"]')).toBeInTheDocument();
    expect(
      container.querySelector('[data-icon="trash-alt"]')
    ).toBeInTheDocument();
    expect(
      container.querySelector('[data-icon="check-circle"]')
    ).toBeInTheDocument();
  });
});
