import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import PropTypes from 'prop-types';
import React from 'react';
import { Link } from 'react-router-dom';
import format from 'date-fns/format';
import styled from 'styled-components';

const BreadcrumbContainer = styled.div`
  flex: 1;
  padding: 5px 17px;
  border-radius: 2px;
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
  font-size: ${props => props.theme.fontSize.m};
`;

const BreadcrumbTrail = styled.div`
  white-space: pre;
`;

const BreadcrumbCurrent = styled.div`
  font-weight: bold;
`;

const BreadcrumbLink = styled(Link)`
  text-decoration: none;
  color: black;

  &:hover {
    text-decoration: underline;
  }
`;

const BreadcrumbTime = styled.div`
  font-size: 14px;
`;

const Breadcrumb = ({ path, withHome, refreshTime }) => {
  return (
    <BreadcrumbContainer>
      {withHome && (
        <BreadcrumbTrail>
          <BreadcrumbLink to="/">
            <FontAwesomeIcon icon="home" />
          </BreadcrumbLink>{' '}
          \{' '}
        </BreadcrumbTrail>
      )}
      {path.map((breadcrumb, index) => {
        if (index !== path.length - 1) {
          if (breadcrumb.to && typeof breadcrumb !== 'string') {
            return (
              <BreadcrumbTrail key={index}>
                <BreadcrumbLink to={breadcrumb.to}>
                  {breadcrumb.label}
                </BreadcrumbLink>{' '}
                \{' '}
              </BreadcrumbTrail>
            );
          } else {
            return (
              <BreadcrumbTrail key={index}>
                {typeof breadcrumb === 'string' ? breadcrumb : breadcrumb.label}{' '}
                \{' '}
              </BreadcrumbTrail>
            );
          }
        } else {
          return (
            <BreadcrumbCurrent key={index}>
              {typeof breadcrumb === 'string' ? breadcrumb : breadcrumb.label}
            </BreadcrumbCurrent>
          );
        }
      })}
      {refreshTime && (
        <BreadcrumbTrail style={{ margin: 'auto', marginRight: '0px' }}>
          <BreadcrumbTime>
            Updated as of{' '}
            <strong>{format(refreshTime, 'MM/DD/YYYY | hh:mm A')}</strong>
          </BreadcrumbTime>
        </BreadcrumbTrail>
      )}
    </BreadcrumbContainer>
  );
};

Breadcrumb.propTypes = {
  /**
   * Example: ['User Management', 'User Accounts', { to: '/user/123', label: 'User 123'}]
   * If element is string, will just render normally, if object, `to` and `label` are expected
   * and will render a <Link> from react-router
   */
  path: PropTypes.arrayOf(
    PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.shape({
        to: PropTypes.string.isRequired,
        label: PropTypes.string.isRequired,
      }),
    ])
  ).isRequired,
  withHome: PropTypes.bool,
  refreshTime: PropTypes.any,
};

export default Breadcrumb;
