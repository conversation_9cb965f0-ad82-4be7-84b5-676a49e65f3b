import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import PropTypes from 'prop-types';
import React from 'react';
import styled from 'styled-components';

const OpacityContainer = styled.div`
  filter: ${props => (props.loading ? 'opacity(0.25)' : 'opacity(1)')};
`;

const Loader = styled.div`
  position: absolute;
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  top: 0;
  left: 0;
`;

const ButtonStyles = styled.button`
  border: 0;
  outline: none;
  background-color: transparent;
  font-size: ${props => props.theme.fontSize.s};
  position: relative;
  transition: all 0.15s ease;
  cursor: pointer;

  &:disabled {
    cursor: default;
  }

  &:focus {
    outline: 1px solid #009cde;
    outline-offset: 2px;
  }
`;

const Button = ({
  children,
  onClick,
  style,
  disabled = false,
  type,
  loading = false,
  icon = null,
  iconPosition = 'right',
  className,
  label,
}) => {
  const opacityContainerProps = loading ? { loading } : {};

  return (
    <ButtonStyles
      className={className}
      style={style}
      onClick={onClick}
      disabled={disabled || loading}
      type={type}
      aria-label={label}
    >
      <OpacityContainer {...opacityContainerProps}>
        {icon && iconPosition === 'left' && (
          <FontAwesomeIcon
            icon={icon}
            style={{ marginRight: children ? 10 : 0 }}
          />
        )}

        {children}

        {icon && iconPosition === 'right' && (
          <FontAwesomeIcon
            icon={icon}
            style={{ marginLeft: children ? 10 : 0 }}
          />
        )}
      </OpacityContainer>

      {loading && (
        <Loader>
          <FontAwesomeIcon icon="spinner" spin />
        </Loader>
      )}
    </ButtonStyles>
  );
};

Button.propTypes = {
  children: PropTypes.node,
  className: PropTypes.string,
  disabled: PropTypes.bool,
  style: PropTypes.object,
  onClick: PropTypes.func,
  loading: PropTypes.bool,
  type: PropTypes.string,
  icon: PropTypes.string,
  iconPosition: PropTypes.oneOf(['left', 'right']),
  label: PropTypes.string,
};

export default Button;
