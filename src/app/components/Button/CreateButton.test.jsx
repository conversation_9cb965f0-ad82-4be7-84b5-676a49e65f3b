import React from 'react';
import { describe, expect, it } from 'vitest';
import { render, screen } from '../../utils/test-utils';
import { library } from '@fortawesome/fontawesome-svg-core';
import {
  faCheckCircle,
  faTimesCircle,
  faEye,
  faPen,
  faTrashAlt,
  faCoffee,
  faSpinner,
} from '@fortawesome/free-solid-svg-icons';
import CreateButton from './CreateButton';
import '@testing-library/jest-dom';

library.add(
  faCheckCircle,
  faTimesCircle,
  faEye,
  faPen,
  faTrashAlt,
  faCoffee,
  faSpinner
);

describe('CreateButton', () => {
  it('renders children correctly', () => {
    render(<CreateButton>Test Button</CreateButton>);
    expect(
      screen.getByRole('button', { name: 'Test Button' })
    ).toBeInTheDocument();
  });

  it('displays an icon when provided', () => {
    const { container } = render(
      <CreateButton icon={faCoffee}>With Icon</CreateButton>
    );
    expect(container.querySelector('[data-icon="coffee"]')).toBeInTheDocument();
  });

  it('shows loading indicator and disables button when loading', () => {
    const { container } = render(
      <CreateButton loading>Test Loading</CreateButton>
    );
    const div = screen.getByText('Test Loading');
    expect(div.closest('button')).toBeDisabled();
    expect(
      container.querySelector('[data-icon="spinner"]')
    ).toBeInTheDocument();
  });
});
