import React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { render, screen } from '../../utils/test-utils';
import { library } from '@fortawesome/fontawesome-svg-core';
import {
  faCheckCircle,
  faTimesCircle,
  faEye,
  faPen,
  faTrashAlt,
  faFileImport,
  faFileExport,
  faFileDownload,
} from '@fortawesome/free-solid-svg-icons';
import FileButton from './FileButton';
import '@testing-library/jest-dom';
import { ExportButton } from './ExportButton';

library.add(
  faCheckCircle,
  faTimesCircle,
  faEye,
  faPen,
  faTrashAlt,
  faFileImport,
  faFileExport,
  faFileDownload
);

describe('FileButton', () => {
  it('renders FileButton', () => {
    const onImport = vi.fn();
    const onExport = vi.fn();
    const onTemplate = vi.fn();
    const loading = false;

    render(
      <FileButton
        onImport={onImport}
        onExport={onExport}
        onTemplate={onTemplate}
        loading={loading}
      >
        <ExportButton label="exportbutton">Export</ExportButton>
      </FileButton>
    );

    expect(screen.getByText(/export/i)).toBeInTheDocument();
  });
});
