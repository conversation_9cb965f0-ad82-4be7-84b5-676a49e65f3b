import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import PropTypes from 'prop-types';
import React, { useRef, useState } from 'react';
import ClickOutHandler from '@stratpoint-fe/react-onclickout';
import styled from 'styled-components';
import { ExportButton } from './ExportButton';

const FileButtonMenu = styled.div`
  z-index: 10;
  position: absolute;
  top: 35px;
  right: -30px;
  display: flex;
  flex-direction: column;
  background-color: white;
  font-size: 12px;
  border-radius: 2px;
  border: 1px solid #cfcfcf;
  box-shadow:
    inset 0 1px 3px 0 rgba(0, 0, 0, 0.5),
    1px 3px 2px 0 #dadada;
`;

export const FileButtonMenuItem = styled.button`
  background-color: white;
  color: #787878;
  font-size: 12px;
  padding: 10px 10px;
  display: flex;
  border: 0;
  cursor: pointer;
  align-items: center;
  font-size: 13px;

  &:focus:not(:disabled) {
    background-color: #ddeef7;
    outline: none;
  }

  &:hover:not(:disabled) {
    background-color: #ddeef7;
  }

  &:disabled {
    cursor: initial;
    background-color: #d8d8d8;
    color: #5f7186;
  }
`;

export const FileButtonMenuItemIcon = styled(FontAwesomeIcon)`
  margin-right: 10px;
`;

export const FileUploadInput = styled.input`
  display: none;
`;

function FileButton({
  onImport,
  onExport,
  onTemplate,
  onImportUpdate,
  onTemplateUpdate,
  ...props
}) {
  const [isOpen, setIsOpen] = useState(false);
  const fileInputRef = useRef(null);
  const fileInputRefUpdate = useRef(null);

  return (
    <div style={{ position: 'relative' }}>
      {onImport && (
        <FileUploadInput
          data-testid="fileuploadinput"
          type="file"
          accept=".csv"
          ref={fileInputRef}
          onChange={event => {
            const [file] = event.target.files;
            if (file && onImport) {
              onImport(file);
            }

            fileInputRef.current.value = '';
          }}
        />
      )}

      {onImportUpdate && (
        <FileUploadInput
          type="file"
          accept=".csv"
          ref={fileInputRefUpdate}
          onChange={event => {
            const [file] = event.target.files;
            if (onImportUpdate && file) {
              onImportUpdate(file);
            }

            fileInputRefUpdate.current.value = '';
          }}
        />
      )}
      <ExportButton
        {...props}
        onClick={() => {
          setIsOpen(true);
        }}
      />
      {isOpen && (
        <ClickOutHandler onClickOut={() => setIsOpen(false)}>
          <FileButtonMenu>
            {onImport && (
              <FileButtonMenuItem
                onClick={() => {
                  setIsOpen(false);
                  fileInputRef.current.click();
                }}
              >
                <FileButtonMenuItemIcon icon="file-import" />
                Add Import
              </FileButtonMenuItem>
            )}

            {onTemplate && (
              <FileButtonMenuItem
                onClick={() => {
                  onTemplate();
                  setIsOpen(false);
                }}
              >
                <FileButtonMenuItemIcon icon="file-download" />
                Add Template
              </FileButtonMenuItem>
            )}
            {onImportUpdate && (
              <FileButtonMenuItem
                onClick={() => {
                  setIsOpen(false);
                  fileInputRefUpdate.current.click();
                }}
              >
                <FileButtonMenuItemIcon icon="file-import" />
                Update Import
              </FileButtonMenuItem>
            )}
            {onTemplateUpdate && (
              <FileButtonMenuItem
                onClick={() => {
                  onTemplateUpdate();
                  setIsOpen(false);
                }}
              >
                <FileButtonMenuItemIcon icon="file-download" />
                Update Template
              </FileButtonMenuItem>
            )}
            {onExport && (
              <FileButtonMenuItem
                onClick={() => {
                  if (onExport) {
                    onExport();
                  }
                  setIsOpen(false);
                }}
              >
                <FileButtonMenuItemIcon icon="file-export" />
                Export All Users
              </FileButtonMenuItem>
            )}
          </FileButtonMenu>
        </ClickOutHandler>
      )}
    </div>
  );
}

FileButton.propTypes = {
  onImport: PropTypes.func,
  onExport: PropTypes.func,
  onTemplate: PropTypes.func,
  onImportUpdate: PropTypes.func,
  onTemplateUpdate: PropTypes.func,
};

export default FileButton;
