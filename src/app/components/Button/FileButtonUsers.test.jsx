import React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { render, screen } from '../../utils/test-utils';
import { library } from '@fortawesome/fontawesome-svg-core';
import {
  faCheckCircle,
  faTimesCircle,
  faEye,
  faPen,
  faTrashAlt,
  faFileImport,
  faFileExport,
  faFileDownload,
} from '@fortawesome/free-solid-svg-icons';
import FileButtonUsers from './FileButtonUsers';
import '@testing-library/jest-dom';

library.add(
  faCheckCircle,
  faTimesCircle,
  faEye,
  faPen,
  faTrashAlt,
  faFileImport,
  faFileExport,
  faFileDownload
);

describe('FileButton', () => {
  it('renders FileButton', () => {
    const onImport = vi.fn();
    const onExport = vi.fn();
    const onTemplate = vi.fn();
    const onImportUpdate = vi.fn();
    const onTemplateUpdate = vi.fn();

    render(
      <FileButtonUsers
        onImport={onImport}
        onExport={onExport}
        onTemplate={onTemplate}
        onImportUpdate={onImportUpdate}
        onTemplateUpdate={onTemplateUpdate}
      ></FileButtonUsers>
    );

    expect(screen.getByTestId('fileuploadinput')).toBeInTheDocument();
  });
});
