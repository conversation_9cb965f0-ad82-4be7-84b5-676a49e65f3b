import styled from 'styled-components';
import Button from './Button';

const PrimaryButton = styled(Button)`
  border: 1px solid transparent;
  color: white;
  background-color: #009cde;
  border-radius: 5px;
  padding: 10px 20px;

  &:active:not(:disabled),
  &:hover:not(:disabled) {
    background-color: #0086b7;
  }
  &:disabled {
    background-color: #d8d8d8;
    color: #5f7186;
  }
`;

export default PrimaryButton;
