import styled from 'styled-components';
import Button from './Button';

const SecondaryButton = styled(Button)`
  background-color: transparent;
  border-radius: 5px;
  color: #009cde;
  padding: 10px 20px;

  &:active:not(:disabled),
  &:hover:not(:disabled) {
    background-color: #e5e5e5;
  }

  &:focus:not(:disabled) {
    outline: 1px solid #009cde;
    outline-offset: 2px;
  }
`;

export default SecondaryButton;
