import styled from 'styled-components';

const DataContainer = styled.div`
  border-radius: 2px;
  padding: ${props => (props.loading ? 0 : 20)}px;
  background-color: white;
  flex: 1;
  display: ${props => (props.loading ? 'flex' : 'block')};

  justify-content: center;
  align-items: stretch;
  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    padding: ${props => (props.loading ? 0 : 10)}px;
  }
`;

export default DataContainer;
