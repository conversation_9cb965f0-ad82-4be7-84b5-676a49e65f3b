import styled from 'styled-components';

const DataHeader = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30px;
  display: flex;
  flex-wrap: wrap;
  width: 100%;

  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    & > * {
      min-width: 100%;
      margin-bottom: 10px;
    }
  }
`;

const DataHeaderTitle = styled.h2`
  margin: 0;
  font-weight: bold;
  font-size: 20px;
  color: #333333;

  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    min-width: 100%;
  }
`;

DataHeader.Title = DataHeaderTitle;

export default DataHeader;
