import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import PropTypes from 'prop-types';
import React from 'react';
import styled from 'styled-components';
import Button from '../../Button/Button';

const StyledSortButtons = styled(Button)`
  display: flex;
  flex-direction: column;
`;

const SortButtonIcon = styled(FontAwesomeIcon)`
  color: #1472cd;
  position: relative;
`;

const SortButtons = ({ sortBy, onClick }) => {
  return (
    <StyledSortButtons onClick={onClick}>
      <SortButtonIcon
        icon={
          sortBy === null ? 'sort' : sortBy === 'ASC' ? 'sort-up' : 'sort-down'
        }
      />
    </StyledSortButtons>
  );
};

SortButtons.propTypes = {
  sortBy: PropTypes.oneOf([null, 'ASC', 'DESC']),
  onClick: PropTypes.func,
};

export default SortButtons;
