import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import isAfter from 'date-fns/is_after';
import isBefore from 'date-fns/is_before';
import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import DatePicker from 'react-datepicker';
import styled from 'styled-components';

const DateRangeContainer = styled.div`
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin: 0 8px;

  .react-datepicker-wrapper {
    flex: 1;

    .react-datepicker__input-container {
      width: 100%;
    }
  }
`;

export const StyledDatePicker = styled(DatePicker)`
  flex: 1;
  border: 0;
  border-bottom: 1px solid #6a6a6a;
  padding: 5px 12px;

  background-color: transparent;

  &:focus {
    border-color: #0090e1;
  }
`;

const CalendarIcon = styled(FontAwesomeIcon)`
  position: absolute;
  right: 3px;
  color: #aaaaaa;
`;

const DateRange = ({
  className,
  name,
  handleChange,
  hasDelimiter,
  initialValue,
  value,
  minDate,
  maxDate,
  ...datePickerProps
}) => {
  const [dateRangeValue, setDateRangeValue] = useState(
    initialValue ||
      value || {
        start: null,
        end: null,
      }
  );

  // if parent's value prop changes, update internal state
  useEffect(() => {
    if (
      typeof value !== 'undefined' &&
      JSON.stringify(value) !== JSON.stringify(dateRangeValue)
    ) {
      setDateRangeValue(value);
    }
  }, [value]);

  // call parent's handleChange function everytime internal state changes
  useEffect(() => {
    handleChange(dateRangeValue);
  }, [JSON.stringify(dateRangeValue)]);

  return (
    <>
      <DateRangeContainer>
        <StyledDatePicker
          className={className}
          name={`${name}.start`}
          selectsStart
          selected={dateRangeValue?.start}
          startDate={dateRangeValue?.start}
          endDate={dateRangeValue?.end}
          onChange={start => {
            if (dateRangeValue.end && isAfter(start, dateRangeValue.end)) {
              setDateRangeValue({
                start: dateRangeValue.end,
                end: start,
              });
            } else {
              setDateRangeValue({ ...dateRangeValue, start });
            }
          }}
          placeholderText="From"
          minDate={minDate}
          maxDate={maxDate}
          {...datePickerProps}
        />
        {!dateRangeValue?.start ? (
          <CalendarIcon icon="calendar-alt" />
        ) : (
          <CalendarIcon
            icon="times-circle"
            onClick={() => {
              setDateRangeValue({
                ...dateRangeValue,
                start: maxDate && minDate ? minDate : null,
              });
            }}
          />
        )}
      </DateRangeContainer>
      <div>{!!hasDelimiter && '—'}</div>
      <DateRangeContainer>
        <StyledDatePicker
          className={className}
          name={`${name}.end`}
          selectsStart
          selected={dateRangeValue?.end}
          startDate={dateRangeValue?.start}
          endDate={dateRangeValue?.end}
          onChange={end => {
            if (dateRangeValue.start && isBefore(end, dateRangeValue.start)) {
              setDateRangeValue({
                end: dateRangeValue.start,
                start: end,
              });
            } else {
              setDateRangeValue({ ...dateRangeValue, end });
            }
          }}
          placeholderText="To"
          minDate={minDate}
          maxDate={maxDate}
          {...datePickerProps}
        />
        {!dateRangeValue?.end ? (
          <CalendarIcon icon="calendar-alt" />
        ) : (
          <CalendarIcon
            icon="times-circle"
            onClick={() => {
              setDateRangeValue({
                ...dateRangeValue,
                end: maxDate && minDate ? maxDate : null,
              });
            }}
          />
        )}
      </DateRangeContainer>
    </>
  );
};

DateRange.propTypes = {
  className: PropTypes.string,
  name: PropTypes.string,
  handleChange: PropTypes.func.isRequired,
  hasDelimiter: PropTypes.bool,
  initialValue: PropTypes.shape({
    start: PropTypes.instanceOf(Date),
    end: PropTypes.instanceOf(Date),
  }),
  value: PropTypes.shape({
    start: PropTypes.instanceOf(Date),
    end: PropTypes.instanceOf(Date),
  }),
  minDate: PropTypes.instanceOf(Date),
  maxDate: PropTypes.instanceOf(Date),
};

export default DateRange;
