import React from 'react';
import styled from 'styled-components';
import Row from '../Row';

const StyledFilterNotification = styled.div`
  background-color: #f5f5f5;
  border-radius: 2px;
  padding: 20px 40px;
  color: #4a4a4a;
  text-align: center;

  h1 {
    font-size: 16px;
    margin: 0;
    margin-bottom: 20px;
  }

  p {
    margin: 0;
    font-size: 16px;
  }
`;

const CenterRow = styled(Row)`
  justify-content: center;
`;

const FilterNotification = () => {
  return (
    <CenterRow>
      <StyledFilterNotification>
        <h1>NOTIFICATION</h1>
        <p>This page cannot display ALL the data.</p>
        <p>to see records, use Search Bar to filter the data.</p>
      </StyledFilterNotification>
    </CenterRow>
  );
};

export default FilterNotification;
