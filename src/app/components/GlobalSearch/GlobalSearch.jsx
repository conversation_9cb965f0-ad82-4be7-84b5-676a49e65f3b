import PropTypes from 'prop-types';
import React, { useContext, useMemo, useRef, useState } from 'react';
import <PERSON><PERSON>OutHandler from '@stratpoint-fe/react-onclickout';
import ResponsiveContext from '../../context/ResponsiveContext';
import useForm from '../../hooks/useForm';
import DateRange from '../DateRange/DateRange';
import MonthRange from '../MonthRange/MonthRange';
import { FIELD_TYPES } from '../Form/constants';
import {
  FilterIcon,
  GlobalSearchFilterDropdown,
  GlobalSearchFilterInput,
  GlobalSearchFilterLabel,
  GlobalSearchFilterRow,
  GlobalSearchFiltersContainer,
  GlobalSearchForm,
  GlobalSearchStretchedRow,
  SearchBar,
  SearchButton,
  SearchIcon,
  SearchInput,
} from './styled';
import { getFilterSummary, sanitizeFilters, MONTHS } from './utils';
import YearRange from '../YearRange/YearRange';
import YTD from '../YTD/YTD';

const GlobalSearch = ({ fields, placeholder, onSearch }) => {
  const { isMobile } = useContext(ResponsiveContext);

  const [isFiltersOpen, setIsFiltersOpen] = useState(false);
  const [conditions, setConditions] = useState(
    fields
      ? fields.reduce((conditions, field) => {
          if (field.type === FIELD_TYPES.NUMBER) {
            conditions[field.name] = '=';
          }
          return conditions;
        }, {})
      : []
  );
  const [input, setInput] = useState('');

  function handleOnSearch(values) {
    const filters = fields ? sanitizeFilters(fields, values) : input;
    setIsFiltersOpen(false);
    if (onSearch) {
      onSearch(filters);
    }
  }

  const { values, onChange, onSubmit, reset } = useForm(
    fields
      ? fields.reduce(
          (values, field) => ({
            ...values,
            [field.name]: {
              initialValue:
                field.type === FIELD_TYPES.DATE_RANGE
                  ? { start: null, end: null }
                  : field.type === FIELD_TYPES.MONTH_RANGE
                    ? { start: 'January', end: 'December' }
                    : field.type === FIELD_TYPES.YEAR_RANGE
                      ? {
                          start: new Date().getFullYear(),
                          end: new Date().getFullYear(),
                        }
                      : field.type === FIELD_TYPES.YTD
                        ? {
                            month:
                              new Date().getMonth() !== 0
                                ? MONTHS[new Date().getMonth() - 1].value
                                : '',
                            year: new Date().getFullYear(),
                          }
                        : field.type === FIELD_TYPES.MULTISELECT
                          ? []
                          : (field.type === FIELD_TYPES.TEXT ||
                                field.type === FIELD_TYPES.SELECT) &&
                              field.initialValue
                            ? field.initialValue
                            : undefined,
            },
          }),
          {}
        )
      : {},
    handleOnSearch
  );

  const searchBar = useRef(null);

  const filterSummary = useMemo(
    () => getFilterSummary(fields, values),
    [fields, values]
  );

  const searchList = fields
    ? fields
        // eslint-disable-next-line array-callback-return
        .map(field => {
          if (field.isKey) return field.label;
        })
        .filter(field => {
          return field !== undefined;
        })
    : [];

  return (
    <GlobalSearchForm onSubmit={onSubmit}>
      <SearchBar
        noFields={!fields}
        ref={searchBar}
        onClick={() => {
          if (fields) {
            setIsFiltersOpen(true);
          }
        }}
      >
        <SearchIcon icon="search" />
        <SearchInput
          placeholder={placeholder || 'Search'}
          value={fields ? filterSummary : input}
          disabled={!!fields}
          onChange={e => {
            setInput(e.target.value);
          }}
        />
        {(!!fields || !!input) && (
          <FilterIcon
            icon={filterSummary || !!input ? 'times' : 'filter'}
            fontSize={filterSummary || !!input ? 32 : 26}
            onClick={event => {
              event.stopPropagation();
              reset();
              setConditions({});
              setInput('');
              if (filterSummary || !!input) {
                onSearch(fields ? {} : '');
              }
            }}
          />
        )}
      </SearchBar>
      {!isMobile && <SearchButton type="submit">Search</SearchButton>}
      {isFiltersOpen && (
        <ClickOutHandler
          onClickOut={() => {
            setIsFiltersOpen(false);
          }}
        >
          <GlobalSearchFiltersContainer
            width={searchBar ? searchBar.current.clientWidth : 0}
            top={searchBar ? searchBar.current.clientHeight : 0}
          >
            {fields.map((field, index) => (
              <GlobalSearchFilterRow key={index}>
                <GlobalSearchFilterLabel>
                  {field.label}
                  {field.disabled !== undefined && '*'}
                </GlobalSearchFilterLabel>
                {FIELD_TYPES.NUMBER === field.type && !field.noOperator && (
                  <GlobalSearchFilterDropdown
                    placeholder="Condition"
                    style={{ maxWidth: 120 }}
                    value={conditions[field.name]}
                    name={`${field.name}-conditin`}
                    options={[
                      { label: 'equal to', value: '=' },
                      {
                        label: 'greater than',
                        value: '>',
                      },
                      { label: 'less than', value: '<' },
                    ]}
                    onChange={value => {
                      setConditions({ ...conditions, [field.name]: value });
                    }}
                    disabled={
                      field.disabled !== undefined && field.disabled(values)
                    }
                  />
                )}
                {[FIELD_TYPES.TEXT, FIELD_TYPES.NUMBER].includes(
                  field.type
                ) && (
                  <GlobalSearchFilterInput
                    type={field.type}
                    name={field.name}
                    value={values[field.name] ? values[field.name] : ''}
                    onChange={onChange[field.name]}
                    disabled={
                      field.disabled !== undefined && field.disabled(values)
                    }
                  />
                )}
                {(FIELD_TYPES.MULTISELECT === field.type ||
                  FIELD_TYPES.SELECT === field.type) && (
                  <GlobalSearchFilterDropdown
                    multi={FIELD_TYPES.MULTISELECT === field.type}
                    placeholder=" "
                    value={values[field.name]}
                    name={field.name}
                    options={field.options}
                    onChange={onChange[field.name]}
                    disabled={
                      field.disabled !== undefined && field.disabled(values)
                    }
                    showMulti
                  />
                )}
                {FIELD_TYPES.DATE_RANGE === field.type && (
                  <GlobalSearchStretchedRow>
                    <DateRange
                      maxDate={new Date()}
                      value={values[field.name]}
                      name={field.name}
                      handleChange={onChange[field.name]}
                      disabled={
                        field.disabled !== undefined && field.disabled(values)
                      }
                    />
                  </GlobalSearchStretchedRow>
                )}
                {FIELD_TYPES.DATE_RANGE_NO_MAX === field.type && (
                  <GlobalSearchStretchedRow>
                    <DateRange
                      maxDate={null}
                      value={values[field.name]}
                      name={field.name}
                      handleChange={onChange[field.name]}
                      disabled={
                        field.disabled !== undefined && field.disabled(values)
                      }
                    />
                  </GlobalSearchStretchedRow>
                )}
                {FIELD_TYPES.MONTH_RANGE === field.type && (
                  <GlobalSearchStretchedRow>
                    <MonthRange
                      value={values[field.name]}
                      name={field.name}
                      handleChange={onChange[field.name]}
                      disabled={
                        field.disabled !== undefined && field.disabled(values)
                      }
                    />
                  </GlobalSearchStretchedRow>
                )}
                {FIELD_TYPES.YEAR_RANGE === field.type && (
                  <GlobalSearchStretchedRow>
                    <YearRange
                      value={values[field.name]}
                      name={field.name}
                      handleChange={onChange[field.name]}
                      disabled={
                        field.disabled !== undefined && field.disabled(values)
                      }
                    />
                  </GlobalSearchStretchedRow>
                )}
                {FIELD_TYPES.YTD === field.type && (
                  <GlobalSearchStretchedRow>
                    <YTD
                      value={values[field.name]}
                      name={field.name}
                      handleChange={onChange[field.name]}
                      disabled={
                        field.disabled !== undefined && field.disabled(values)
                      }
                    />
                  </GlobalSearchStretchedRow>
                )}
              </GlobalSearchFilterRow>
            ))}
            {fields.some(field => field.disabled !== undefined) && (
              <div style={{ marginTop: 10, fontSize: 11, color: '#4a4a4a' }}>
                Search Inputs* below are valid for one [1] entry only. Other
                fields will be disabled once Input has changed.
              </div>
            )}
            {searchList.length !== 0 && (
              <div style={{ marginTop: 10, fontSize: 11, color: '#4a4a4a' }}>
                To allow a faster search, please provide any of the following
                information:&nbsp;
                {searchList.join(', ')}
              </div>
            )}
            {isMobile && (
              <SearchButton type="submit" isMobile>
                Search
              </SearchButton>
            )}
          </GlobalSearchFiltersContainer>
        </ClickOutHandler>
      )}
    </GlobalSearchForm>
  );
};

GlobalSearch.propTypes = {
  fields: PropTypes.array,
  placeholder: PropTypes.string,
  onSearch: PropTypes.func,
};

export default GlobalSearch;
