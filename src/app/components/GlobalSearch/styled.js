import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import styled from 'styled-components';
import PrimaryButton from '../Button/PrimaryButton';
import Dropdown from '../Dropdown';
import { DropdownButton } from '../Dropdown/Dropdown';
import Row from '../Row';

export const GlobalSearchStretchedRow = styled(Row)`
  flex: 1;
`;

export const GlobalSearchForm = styled.form`
  position: relative;
  display: flex;
  flex-direction: row;
  flex: 1;
  max-width: 60%;
  height: 36px;

  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    max-width: 100%;
  }
`;

export const SearchInput = styled.input`
  text-align: left;
  border: none;
  flex: 1;
  margin: 0 10px;
  background-color: white;
  cursor: pointer;
`;

export const SearchBar = styled.div`
  flex: 1;
  display: flex;
  flex-direction: row;
  border: 1px solid #ced4da;
  background-color: #ffffff;
  border-radius: 2px;
  padding: 5px 10px;
  font-size: 14px;
  font-weight: 300;
  align-items: center;
  cursor: ${props => (props.noFields ? 'initial' : 'pointer')};
  color: initial;
  border-radius: 5px;

  ${SearchInput} {
    background-color: white;
    cursor: pointer;
  }
`;

export const SearchIcon = styled(FontAwesomeIcon)`
  font-size: 18px;
`;

export const FilterIcon = styled(FontAwesomeIcon)`
  padding: 5px;
  height: 32px;
  font-size: ${props => props.fontSize}px;
`;

export const SearchButton = styled(PrimaryButton)`
  margin-left: ${props => (props.isMobile ? 0 : 10)}px;
  margin-right: ${props => (props.isMobile ? 0 : 10)}px;
  margin-top: ${props => (props.isMobile ? 10 : 0)}px;
  width: ${props => (props.isMobile ? '100%' : 'auto')};
  border-radius: 5px;
`;

export const GlobalSearchFiltersContainer = styled.div`
  width: ${props => props.width}px;
  top: ${props => props.top}px;
  z-index: 2000;
  position: absolute;
  padding: 20px;
  border-top: 1px solid #ced4da;
  background-color: #ffffff;
  border-bottom-left-radius: 2px;
  border-bottom-right-radius: 2px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
`;

export const GlobalSearchFilterRow = styled.div`
  display: flex;
  flex-direction: row;
  min-height: 30px;
  font-size: ${props => props.theme.fontSize.xs};

  .react-datepicker__close-icon {
    top: calc(50% - 8px);
    right: 14px;

    &::after {
      background-color: #0090e1;
    }
  }

  .react-datepicker-wrapper {
    flex: 1;
    display: flex;
    align-items: center;
    height: 100%;
  }

  .react-datepicker__input-container {
    height: 100%;
    width: 100%;
    display: flex;

    .filter-input {
      width: 100%;
    }
  }
`;

export const GlobalSearchFilterLabel = styled.div`
  display: flex;
  align-items: center;
  width: 20%;
  color: #4a4a4a;

  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    width: 30%;
  }
`;

export const GlobalSearchFilterInput = styled.input`
  flex: 1;
  border: 0;
  border-bottom: 1px solid #6a6a6a;
  padding: 0 12px;
  margin: 0 8px;

  &:focus {
    border-color: #0090e1;
  }
`;

export const GlobalSearchFilterDropdown = styled(Dropdown)`
  flex: 1;
  border: 0;
  border-bottom: 1px solid #6a6a6a;
  padding: 0;
  margin: 0 8px;
  display: flex;

  &:focus {
    border-color: #0090e1;
  }

  ${DropdownButton} {
    border: 0;
    flex: 1;
  }
`;
