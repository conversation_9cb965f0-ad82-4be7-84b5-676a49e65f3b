import addDays from 'date-fns/add_days';
import format from 'date-fns/format';
import sanitize from '../../utils/sanitize';
import { FIELD_TYPES } from '../Form/constants';
import { json2csv } from 'json-2-csv';

export const MONTHS = [
  { label: 'January', value: 'January' },
  { label: 'February', value: 'February' },
  { label: 'March', value: 'March' },
  { label: 'April', value: 'April' },
  { label: 'May', value: 'May' },
  { label: 'June', value: 'June' },
  { label: 'July', value: 'July' },
  { label: 'August', value: 'August' },
  { label: 'September', value: 'September' },
  { label: 'October', value: 'October' },
  { label: 'November', value: 'November' },
  { label: 'December', value: 'December' },
];

export function getMonthNumber(monthName) {
  return MONTHS.findIndex(month => month.value === monthName);
}

export function getFilterSummary(fields, values) {
  if (!fields) return '';
  return fields
    .filter(field => {
      const value = values[field.name];

      if (field.disabled && field.disabled(values)) {
        return false;
      }

      return (
        value !== undefined &&
        ((field.type === FIELD_TYPES.TEXT && value !== '') ||
          (field.type === FIELD_TYPES.MULTISELECT && value.length !== 0) ||
          ((field.type === FIELD_TYPES.DATE_RANGE ||
            field.type === FIELD_TYPES.DATE_RANGE_NO_MAX) &&
            (value.start !== null || value.end !== null)) ||
          (field.type === FIELD_TYPES.MONTH_RANGE &&
            (value.start !== null || value.end != null)) ||
          (field.type === FIELD_TYPES.YEAR_RANGE &&
            (value.start !== null || value.end != null)) ||
          (field.type === FIELD_TYPES.YTD &&
            (value.month !== null || value.year != null)) ||
          (field.type === FIELD_TYPES.SELECT && value !== null) ||
          (field.type === FIELD_TYPES.NUMBER && value !== null))
      );
    })
    .map(field => {
      const name = field.name;

      if (
        field.type === FIELD_TYPES.DATE_RANGE ||
        field.type === FIELD_TYPES.DATE_RANGE_NO_MAX
      ) {
        let fieldString = [];
        if (values[field.name].start)
          fieldString.push(
            `${name}Start:${format(values[field.name].start, 'MM/DD/YYYY')}`
          );
        if (values[field.name].end)
          fieldString.push(
            `${name}End:${format(values[field.name].end, 'MM/DD/YYYY')}`
          );
        return fieldString.join(' ');
      } else if (
        [FIELD_TYPES.MONTH_RANGE, FIELD_TYPES.YEAR_RANGE].includes(field.type)
      ) {
        let fieldString = [];
        if (values[field.name].start)
          fieldString.push(`${name}Start:${values[field.name].start}`);
        if (values[field.name].end)
          fieldString.push(`${name}End:${values[field.name].end}`);
        return fieldString.join(' ');
      } else if (field.type === FIELD_TYPES.YTD) {
        let fieldString = [];
        if (values[field.name].month)
          fieldString.push(`${name}Month:${values[field.name].month}`);
        if (values[field.name].year)
          fieldString.push(`${name}Year:${values[field.name].year}`);
        return fieldString.join(' ');
      } else if (field.type === FIELD_TYPES.SELECT) {
        return `${name}:${field.options.find(option => option.value === values[field.name]).label}`;
      } else if (field.type === FIELD_TYPES.MULTISELECT) {
        return `${name}:${values[field.name]
          .map(value => {
            const option = field.options.find(option => option.value === value);
            return option ? option.label : '';
          })
          .join(',')}`;
      } else {
        return `${name}:${values[field.name]}`;
      }
    })
    .join(' ');
}

export function sanitizeFilters(fields, values) {
  const filters = { ...sanitize(values) };
  for (const field of fields) {
    if (field.disabled && field.disabled(values)) {
      delete filters[field.name];
      continue;
    }
    if (
      field.type === FIELD_TYPES.NUMBER &&
      Object.prototype.hasOwnProperty.call(filters, field.name) &&
      !field.noOperator
    ) {
      filters[field.name] = {
        operator: this.state.conditions[field.name],
        number: filters[field.name],
      };
    }

    if (
      (field.type === FIELD_TYPES.DATE_RANGE ||
        field.type === FIELD_TYPES.DATE_RANGE_NO_MAX) &&
      Object.prototype.hasOwnProperty.call(filters, field.name)
    ) {
      filters[field.name] = { ...filters[field.name] };
      if (
        filters[field.name].start === null &&
        filters[field.name].end === null
      ) {
        delete filters[field.name];
      } else {
        if (filters[field.name].start === undefined) {
          filters[field.name].start = null;
        }

        if (filters[field.name].end === undefined) {
          filters[field.name].end = null;
        } else {
          filters[field.name].end = addDays(filters[field.name].end, 1);
        }
      }
    }

    if (
      field.type === FIELD_TYPES.MONTH_RANGE &&
      Object.prototype.hasOwnProperty.call(filters, field.name)
    ) {
      filters[field.name] = { ...filters[field.name] };
      if (
        filters[field.name].start === null &&
        filters[field.name].end === null
      ) {
        delete filters[field.name];
      } else {
        if (filters[field.name].start === undefined) {
          filters[field.name].start = null;
        }

        if (filters[field.name].end === undefined) {
          filters[field.name].end = null;
        }
      }
    }
  }
  return filters;
}

export function numberWithCommas(number, fractionDigits = 0) {
  if (typeof number === 'string') return number;

  return parseFloat(number).toLocaleString(navigator.language, {
    minimumFractionDigits: fractionDigits,
  });
}

export function createYearOptions(yearsBefore = 0, yearsAfter = 0) {
  let years = [];
  let before = 1;
  let after = yearsAfter;
  const currentYear = new Date().getFullYear();

  while (after > 0) {
    let value = currentYear + after;
    years.push({ value: value, label: value });
    after--;
  }

  years.push({ value: currentYear, label: currentYear });

  while (before <= yearsBefore) {
    let value = currentYear - before;
    years.push({ value: value, label: value });
    before++;
  }

  return years;
}

export function checkNullValue(value) {
  if (value === undefined || value === 'undefined, undefined') {
    return '';
  }
  return value;
}

export async function json2CSVYTD(data, tableConfig) {
  return await json2csv(
    data.map(datum => {
      if (datum) {
        const obj = {};
        for (const key in tableConfig) {
          const config = tableConfig[key];
          if (config) {
            obj[config.headerLabel] = config.renderAs
              ? checkNullValue(config.renderAs(datum))
              : checkNullValue(datum[key]);
          }
        }
        return obj;
      }
    })
  );
}
