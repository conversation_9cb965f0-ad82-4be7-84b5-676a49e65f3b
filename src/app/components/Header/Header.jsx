import PropTypes from 'prop-types';
import React, { useContext } from 'react';
import styled from 'styled-components';
import ResponsiveContext from '../../context/ResponsiveContext';
import Breadcrumb from '../Breadcrumb';
import Row from '../Row';
import Timestamp from '../Timestamp';

const HeaderTitle = styled.div`
  flex: 1;
  font-size: 18px;
  background-color: rgba(167, 167, 167, 0.21);
  padding: 5px 17px;
  border-radius: 2px;
  min-height: 50px;
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
  color: #333333;
  font-weight: bold;

  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    justify-content: center;
  }
`;

const HeaderRow = styled(Row)`
  margin-bottom: 10px;
  min-height: 50px;
`;

const Header = ({ withHome, title, path, refreshTime }) => {
  const { isMobile } = useContext(ResponsiveContext);
  return (
    <>
      {!isMobile ? (
        <>
          <HeaderRow>
            <HeaderTitle>{title}</HeaderTitle>
            <Timestamp />
          </HeaderRow>
          <HeaderRow>
            <Breadcrumb
              withHome={withHome}
              path={path}
              refreshTime={refreshTime}
            />
          </HeaderRow>
        </>
      ) : (
        <>
          <HeaderRow>
            <HeaderTitle>{title}</HeaderTitle>
          </HeaderRow>
          <HeaderRow>
            <Timestamp />
          </HeaderRow>
          <HeaderRow>
            <Breadcrumb
              withHome={withHome}
              path={path}
              refreshTime={refreshTime}
            />
          </HeaderRow>
        </>
      )}
    </>
  );
};

Header.propTypes = {
  /** See Breadcrumb */
  path: PropTypes.arrayOf(
    PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.shape({
        to: PropTypes.string.isRequired,
        label: PropTypes.string.isRequired,
      }),
    ])
  ).isRequired,
  /** From withRouter */
  history: PropTypes.object,
  withHome: PropTypes.bool,
  title: PropTypes.string,
  refreshTime: PropTypes.any,
};

export default Header;
