import styled from 'styled-components';
import PrimaryButton from '../Button/PrimaryButton';
import SecondaryButton from '../Button/SecondaryButton';
import Row from '../Row';

const ButtonsContainer = styled(Row)`
  justify-content: space-between;
  margin-top: 20px;
  width: 100%;

  ${SecondaryButton} {
    @media (max-width: ${props => props.theme.breakpoint.phone}) {
      width: 100%;
      margin-bottom: 10px;
    }
  }

  ${PrimaryButton} {
    margin-left: 10px;
    @media (max-width: ${props => props.theme.breakpoint.phone}) {
      margin-left: 0px;
      width: 100%;
      margin-bottom: 10px;
    }
  }

  ${Row} {
    @media (max-width: ${props => props.theme.breakpoint.phone}) {
      width: 100%;
    }
  }
`;

export default ButtonsContainer;
