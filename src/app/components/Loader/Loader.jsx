import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import React from 'react';
import styled from 'styled-components';

const LoaderContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  padding: 20px;
  height: ${props => (props.fullPage ? '100%' : 'auto')};
  width: ${props => (props.fullPage ? '100%' : 'auto')};
`;

const Loader = () => {
  return (
    <LoaderContainer>
      <FontAwesomeIcon color="#009BDD" icon="spinner" spin size="6x" />
    </LoaderContainer>
  );
};

export default Loader;
