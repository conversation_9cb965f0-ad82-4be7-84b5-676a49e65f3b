import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import PropTypes from 'prop-types';
import React, { useContext, useEffect } from 'react';
import styled from 'styled-components';
import * as Yup from 'yup';
import ResponsiveContext from '../../context/ResponsiveContext';
import useForm from '../../hooks/useForm';
import PrimaryButton from '../Button/PrimaryButton';
import SecondaryButton from '../Button/SecondaryButton';
import { DropdownButton, DropdownMenu } from '../Dropdown/Dropdown';
import { FIELD_TYPES } from '../Form/constants';
import FormField from '../Form/FormField';
import Modal, { ModalContent } from './Modal';

const StyledAlertModal = styled(Modal)`
  ${ModalContent} {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;

    color: #333333;
  }
`;

const AlertModalIcon = styled(FontAwesomeIcon)`
  margin-bottom: 20px;
  font-size: 4em;
  color: ${props =>
    props.variant === 'warn'
      ? '#FF9933'
      : props.variant === 'success'
        ? '#57A74F'
        : '#FB1733'};
`;

const AlertModalHeader = styled.div`
  font-size: 18px;
  font-weight: bold;
`;

const AlertModalSubHeader = styled.div`
  font-size: 18px;
  font-weight: normal;
  margin: 10px;
`;

const AlertModalSelectContainer = styled.div`
  margin-top: 10px;
  margin-bottom: 20px;
  text-align: left;
  width: 80%;
  font-size: 12px;

  ${DropdownButton} {
    border: 0;
    border-bottom: 1px solid #979797;
  }

  ${DropdownMenu} {
    border: 1px solid #979797;
  }
`;

const AlertModalDescriptions = styled.div`
  display: flex;
  flex-direction: column;
  font-size: ${props => props.theme.fontSize.xs};
  margin-bottom: 20px;
  font-weight: lighter;
`;

const AlertModalButtonsContainer = styled.div`
  width: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;

  align-items: center;
  justify-content: ${props => (props.center ? 'center' : 'space-between')};
`;

const AlertModal = ({
  header,
  subHeader,
  description,
  icon,
  variant,
  handleConfirm,
  confirmText,
  handleCancel,
  cancelText,
  confirmLoading,
  selectLabel,
  options,
  content,
  isOpen,
  ...modalProps
}) => {
  const { isMobile } = useContext(ResponsiveContext);

  const { onSubmit, values, onChange, onBlur, errors, isFormValid, reset } =
    useForm(
      {
        reason: {
          initialValue: null,
          onChange: reason => {
            if (reason === 'Others') {
              onChange.others('');
            }
          },
          validation: Yup.string().nullable().required('Please enter a value'),
        },
        others: {
          initialValue: '',
          validation: values =>
            values.reason === 'Others'
              ? Yup.string()
                  .required('Please enter a value')
                  .matches(/[^\s]/, 'Must not be a whitespace')
              : Yup.string(),
        },
      },
      () => {
        handleConfirm(
          values.reason === 'Others'
            ? values.reason + ' - ' + values.others
            : values.reason
        );
      }
    );

  useEffect(() => {
    if (!isOpen) {
      reset();
    }
  }, [isOpen]);

  const confirmButton = !!handleConfirm && !!confirmText && (
    <PrimaryButton
      onClick={() => {
        if (selectLabel && options) {
          onSubmit();
        } else {
          handleConfirm();
        }
      }}
      loading={confirmLoading}
      disabled={selectLabel && options && !isFormValid}
    >
      {confirmText}
    </PrimaryButton>
  );

  const cancelButton = !!handleCancel && !!cancelText && (
    <SecondaryButton onClick={handleCancel}>{cancelText}</SecondaryButton>
  );

  return (
    <StyledAlertModal width="360px" {...modalProps} isOpen={isOpen}>
      <AlertModalIcon icon={icon} variant={variant} />
      <AlertModalHeader>{header}</AlertModalHeader>
      <AlertModalSubHeader>{subHeader}</AlertModalSubHeader>
      {selectLabel && options && (
        <AlertModalSelectContainer>
          <FormField
            label={selectLabel}
            type={FIELD_TYPES.SELECT}
            options={options}
            placeholder="-Select-"
            onChange={onChange.reason}
            onBlur={onBlur.reason}
            value={values.reason}
            error={errors.reason}
            required
            horizontalGap={0}
            verticalGap={5}
          />
          {values.reason === 'Others' && (
            <FormField
              label="Others"
              type={FIELD_TYPES.TEXT}
              required
              onChange={onChange.others}
              onBlur={onBlur.others}
              value={values.others}
              error={errors.others}
              horizontalGap={0}
              verticalGap={5}
            />
          )}
        </AlertModalSelectContainer>
      )}
      {content}
      <AlertModalDescriptions>
        {typeof description === 'string'
          ? description
          : typeof description === 'function'
            ? description()
            : description.map((item, index) => <div key={index}>{item}</div>)}
      </AlertModalDescriptions>
      <AlertModalButtonsContainer
        center={!handleCancel || !cancelText || !handleConfirm || !confirmText}
      >
        {isMobile ? (
          <>
            {confirmButton}
            {cancelButton}
          </>
        ) : (
          <>
            {cancelButton}
            {confirmButton}
          </>
        )}
      </AlertModalButtonsContainer>
    </StyledAlertModal>
  );
};

AlertModal.propTypes = {
  /** Modal props */
  isOpen: PropTypes.bool.isRequired,
  title: PropTypes.string.isRequired,
  handleClose: PropTypes.func.isRequired,

  header: PropTypes.string.isRequired,
  subHeader: PropTypes.oneOfType([PropTypes.string, PropTypes.element])
    .isRequired,
  description: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.arrayOf(PropTypes.string),
    PropTypes.func,
  ]).isRequired,
  icon: PropTypes.string.isRequired,
  variant: PropTypes.oneOf(['info', 'warn', 'error', 'success']),

  handleConfirm: PropTypes.func,
  handleCancel: PropTypes.func,

  confirmText: PropTypes.string,
  cancelText: PropTypes.string,

  confirmLoading: PropTypes.bool,

  options: PropTypes.array,
  selectLabel: PropTypes.string,

  content: PropTypes.any,
};

export default AlertModal;
