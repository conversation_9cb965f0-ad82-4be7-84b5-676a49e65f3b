import PropTypes from 'prop-types';
import React from 'react';
import styled from 'styled-components';
import useForm from '../../hooks/useForm';
import PrimaryButton from '../Button/PrimaryButton';
import FormField from '../Form/FormField';
import Row from '../Row';
import Modal from './Modal';

const Instructions = styled.div`
  margin-bottom: 20px;
  font-size: ${props => props.theme.fontSize.s};
`;

const ButtonContainer = styled(Row)`
  justify-content: center;
  margin: 20px 0;
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  max-height: -webkit-fill-available;
`;

const FormRow = styled(Row)`
  flex: 1;
  overflow: auto;
`;

const FormModal = ({
  instructions,
  fields,
  submitText,
  changeListener,
  handleSubmit,
  ...modalProps
}) => {
  const { values, setValues, onChange, onBlur, errors, onSubmit, isFormValid } =
    useForm(fields, handleSubmit);

  return (
    <Modal {...modalProps}>
      <Form onSubmit={onSubmit}>
        {instructions && <Instructions>{instructions}</Instructions>}
        <FormRow>
          {Object.keys(fields).map(name => (
            <FormField
              key={name}
              name={name}
              type={
                fields[name].type ||
                (fields[name].typeWhen ? fields[name].typeWhen(values) : false)
              }
              label={
                fields[name].label ||
                (fields[name].labelWhen
                  ? fields[name].labelWhen(values)
                  : false)
              }
              placeholder={fields[name].placeholder}
              value={values[name]}
              onChange={
                changeListener
                  ? data => {
                      onChange[name](data);
                      changeListener(name, data, values, setValues, fields);
                    }
                  : onChange[name]
              }
              onBlur={onBlur[name]}
              error={errors[name]}
              required={
                fields[name].required ||
                (fields[name].requiredWhen
                  ? fields[name].requiredWhen(values)
                  : false)
              }
              options={
                fields[name].dynamicOptions
                  ? fields[name].dynamicOptions(values)
                  : fields[name].options
              }
              isPercent={fields[name].isPercent}
              horizontalGap={
                typeof fields[name].horizontalGap === 'undefined'
                  ? 20
                  : fields[name].horizontalGap
              }
              perRow={fields[name].perRow || 2}
              defaultValue={
                (fields[name].defaultValue !== undefined
                  ? fields[name].defaultValue
                  : '') ||
                (fields[name].initialValueWhen &&
                fields[name].initialValueWhen !== undefined
                  ? fields[name].initialValueWhen(values)
                  : '')
              }
              readOnly={
                fields[name].readOnly ||
                (fields[name].disableWhen
                  ? fields[name].disableWhen(values)
                  : false)
              }
              row={fields[name].row}
              verticalGap={fields[name].verticalGap}
            />
          ))}
        </FormRow>
        <ButtonContainer>
          <PrimaryButton disabled={!isFormValid} type="submit">
            {submitText}
          </PrimaryButton>
        </ButtonContainer>
      </Form>
    </Modal>
  );
};

FormModal.propTypes = {
  instructions: PropTypes.any,
  fields: PropTypes.object,
  changeListener: PropTypes.func,
  handleSubmit: PropTypes.func,
  submitText: PropTypes.string,
};

export default FormModal;
