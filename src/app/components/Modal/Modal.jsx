import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import PropTypes from 'prop-types';
import React, { useLayoutEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import styled from 'styled-components';
import Button from '../Button/Button';

export const ModalOverlay = styled.div`
  position: fixed;
  background-color: rgba(0, 0, 0, 0.5);
  height: 100vh;
  width: 100vw;
  top: 0;
  left: 0;
  z-index: 9999;
  overflow-y: auto;

  display: flex;
  justify-content: center;
  align-items: center;

  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    align-items: flex-start;
  }
`;

export const ModalBody = styled.div`
  border-radius: 2px;
  background-color: white;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.5);
  max-height: 90%;
  width: ${props => props.width};
  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    margin: 20px;
    margin-bottom: 200px;
  }
`;

export const ModalTitleContainer = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 10px 10px 10px 20px;
  background-color: ${props => (props.hasTitle ? '#009cde' : 'transparent')};
  color: white;
  font-size: 18px;
`;

export const ModalCloseButton = styled(Button)`
  background-color: transparent;
  padding: 0;
  margin: 0;
  cursor: pointer;
  margin-left: 10px;
  width: 20px;
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: ${props => props.theme.fontSize.m};

  outline-color: ${props => (props.hasTitle ? 'white' : 'inherit')};
`;

export const ModalContent = styled.div`
  padding: 20px;
`;

const Modal = ({
  isOpen,
  children,
  title,
  handleClose,
  width = 'auto',
  className,
}) => {
  const root = useRef(document.getElementById('root')).current;

  useLayoutEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'initial';
    }
    return () => (document.body.style.overflow = 'initial');
  }, [isOpen]);

  if (!root) return null;

  if (!isOpen) return null;

  return createPortal(
    <>
      <ModalOverlay className={className}>
        <ModalBody width={width}>
          <ModalTitleContainer hasTitle={!!title}>
            <span>{title}</span>
            <ModalCloseButton onClick={handleClose} hasTitle={!!title}>
              <FontAwesomeIcon
                icon="times"
                color={title ? 'white' : '#363636'}
              />
            </ModalCloseButton>
          </ModalTitleContainer>
          <ModalContent>{children}</ModalContent>
        </ModalBody>
      </ModalOverlay>
    </>,
    root
  );
};

Modal.propTypes = {
  children: PropTypes.node.isRequired,
  isOpen: PropTypes.bool.isRequired,
  title: PropTypes.string,
  handleClose: PropTypes.func.isRequired,
  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),

  contentClassName: PropTypes.string,
  bodyClassName: PropTypes.string,
  className: PropTypes.string,
};

export default Modal;
