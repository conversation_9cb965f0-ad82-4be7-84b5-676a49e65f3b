import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { GlobalSearchFilterDropdown } from '../GlobalSearch/styled';

const MonthRangeContainer = styled.div`
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin: 0px;
`;

const MonthRange = ({
  name,
  handleChange,
  hasDelimiter,
  initialValue,
  value,
}) => {
  const [endMonth, setEndMonth] = useState([]);
  const [startMonth, setStartMonth] = useState([]);
  const [monthRangeValue, setMonthRangeValue] = useState(
    initialValue ||
      value || {
        start: null,
        end: null,
      }
  );

  // if parent's value prop changes, update internal state
  useEffect(() => {
    if (JSON.stringify(value) !== JSON.stringify(monthRangeValue)) {
      setMonthRangeValue(value);
    }
  }, [value]);

  // call parent's handleChange function everytime internal state changes
  useEffect(() => {
    handleChange(monthRangeValue);
  }, [JSON.stringify(monthRangeValue)]);

  useEffect(() => {
    let startIndex = 0;
    if (monthRangeValue.start) {
      startIndex = months.findIndex(x => x.value === monthRangeValue.start);
    }
    setEndMonth(months.filter((month, index) => index >= startIndex));
  }, [monthRangeValue]);

  useEffect(() => {
    let endIndex = 0;
    if (monthRangeValue.end) {
      endIndex = months.findIndex(x => x.value === monthRangeValue.end);
    }
    setStartMonth(months.filter((month, index) => index <= endIndex));
  }, [monthRangeValue]);

  const months = [
    { label: 'January', value: 'January' },
    { label: 'February', value: 'February' },
    { label: 'March', value: 'March' },
    { label: 'April', value: 'April' },
    { label: 'May', value: 'May' },
    { label: 'June', value: 'June' },
    { label: 'July', value: 'July' },
    { label: 'August', value: 'August' },
    { label: 'September', value: 'September' },
    { label: 'October', value: 'October' },
    { label: 'November', value: 'November' },
    { label: 'December', value: 'December' },
  ];

  return (
    <>
      <MonthRangeContainer>
        <GlobalSearchFilterDropdown
          placeholder="From"
          name={`${name}.start`}
          value={
            monthRangeValue && monthRangeValue.start === null
              ? 'January'
              : monthRangeValue.start
          }
          options={startMonth}
          onChange={start => {
            setMonthRangeValue({ ...monthRangeValue, start });
          }}
        />
      </MonthRangeContainer>
      <div>{!!hasDelimiter && '—'}</div>
      <MonthRangeContainer>
        <GlobalSearchFilterDropdown
          placeholder="To"
          name={`${name}.end`}
          value={
            monthRangeValue && monthRangeValue.end === null
              ? 'December'
              : monthRangeValue.end
          }
          options={endMonth}
          onChange={end => {
            setMonthRangeValue({ ...monthRangeValue, end });
          }}
        />
      </MonthRangeContainer>
    </>
  );
};

MonthRange.propTypes = {
  name: PropTypes.string,
  handleChange: PropTypes.func.isRequired,
  hasDelimiter: PropTypes.bool,
  initialValue: PropTypes.shape({
    start: PropTypes.string,
    end: PropTypes.string,
  }),
  value: PropTypes.shape({
    start: PropTypes.string,
    end: PropTypes.string,
  }),
};

export default MonthRange;
