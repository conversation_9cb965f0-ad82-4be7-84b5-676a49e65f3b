import PropTypes from 'prop-types';
import React from 'react';
import { withRouter } from 'react-router-dom';
import styled from 'styled-components';
import PrimaryButton from '../Button/PrimaryButton';

const StyledNotFound = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  h1 {
    font-size: 150px;
    color: #4a4a4a;
    margin: 0;

    @media (max-width: ${props => props.theme.breakpoint.phone}) {
      font-size: 80px;
    }
  }

  h3 {
    color: #333333;
    font-size: 25px;
    font-weight: normal;
    margin: 0;
    margin-bottom: 40px;
    @media (max-width: ${props => props.theme.breakpoint.phone}) {
      font-size: 18px;
      margin-bottom: 20px;
    }
  }

  h2 {
    color: #333333;
    font-size: 30px;
    font-weight: normal;
    margin: 0;
    margin-bottom: 20px;
    text-align: center;

    @media (max-width: ${props => props.theme.breakpoint.phone}) {
      font-size: 20px;
    }
  }

  p {
    margin: 0;
    color: #333333;
    font-size: 14px;
    margin-bottom: 20px;
    width: 35%;
    text-align: center;
    @media (max-width: ${props => props.theme.breakpoint.phone}) {
      width: 75%;
    }
  }
`;

const NotFound = ({ history }) => {
  return (
    <StyledNotFound>
      <h1>404</h1>
      <h3>PAGE NOT FOUND</h3>
      <h2>Oops! Looks like you got lost!</h2>
      <p>
        {
          "It looks like you're trying to access a page that either has been deleted or never even existed."
        }
      </p>
      <PrimaryButton
        onClick={() => {
          history.goBack();
        }}
      >
        Go to Previous Page
      </PrimaryButton>
    </StyledNotFound>
  );
};

NotFound.propTypes = {
  history: PropTypes.object,
};

export default withRouter(NotFound);
