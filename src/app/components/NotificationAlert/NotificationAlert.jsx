import React, { useContext } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import styled from 'styled-components';
import PropTypes from 'prop-types';
import ResponsiveContext from '../../context/ResponsiveContext';
import NotificationContext from '../../context/NotificationContext';
import theme from '../../theme';
import useQueryReportDownloader from '../../hooks/useQueryReportDownloader';
import { json2csv } from 'json-2-csv';
import { animated, useTransition } from 'react-spring';
import useQueryRecordDownloader from '../../hooks/useQueryRecordDownloader';

const POPUP_TYPES = ['info', 'warning', 'error', 'success'];

const getPopupStyle = popupType => {
  switch (popupType) {
    case POPUP_TYPES[0]:
      return {
        bg: 'rgb(240, 240, 255)',
        border: 'rgb(0, 0, 255)',
        icon: 'info-circle',
      };
    case POPUP_TYPES[1]:
      return {
        bg: 'rgb(255, 244, 240)',
        border: 'rgb(255, 165, 0)',
        icon: 'exclamation-circle',
      };
    case POPUP_TYPES[2]:
      return {
        bg: 'rgb(255, 240, 240)',
        border: 'rgb(255, 0, 0)',
        icon: 'times-circle',
      };
    case POPUP_TYPES[3]:
      return {
        bg: 'rgb(240, 255, 240)',
        border: 'rgb(0, 255, 0)',
        icon: 'check-circle',
      };
    default:
      return {
        bg: 'rgb(240, 240, 255)',
        border: 'rgb(0, 0, 255)',
        icon: 'info-circle',
      };
  }
};

const getProgressBG = percent => {
  return percent <= 10
    ? '#f63a0f'
    : percent <= 30
      ? '#f27011'
      : percent <= 60
        ? '#f2b01e'
        : percent <= 90
          ? '#f2d31b'
          : /* <= 100 ? */ '#86e01e';
};

const POPUP_DIM = {
  width: '350px',
  height: '150px',
};

const NotificationPopupStyles = styled.div`
  position: relative;
  overflow: hidden;
  width: ${POPUP_DIM.width};
  height: ${POPUP_DIM.height};
  background-color: ${props => props.popupStyle.bg};
  border-top: 5px solid ${props => props.popupStyle.border};
  opacity: ${props => (props.isMobile ? 1 : 0.5)};
  margin: 10px;
  box-shadow: 5px 5px 5px grey;
  pointer-events: auto;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  transition: 0.3s all linear;

  &:hover {
    opacity: 1;
    transform: scale(1.1, 1.1);
  }
`;

const NotificationIcon = styled(FontAwesomeIcon)`
  font-size: 40px;
  margin: 20px;
`;

const NotificationContentStyle = styled.div`
  display: flex;
  flex: 1;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  margin: 10px 0px 10px 0px;
`;

const NotificationTitleStyle = styled.b`
  font-size: ${theme.fontSize.m};
`;

const NotificationMsgStyle = styled.p`
  font-size: ${theme.fontSize.s};
  overflow: auto;
`;

const NotificationClose = styled(FontAwesomeIcon)`
  font-size: 40px;
  margin: 10px;
  color: red;
`;

const NotificationAlertsStyle = styled.div`
  width: ${props => (props.isMobile ? '100%' : '500px')};
  position: fixed;
  z-index: 1000;
  right: 0px;
  top: 0px;
  display: flex;
  flex-direction: column-reverse;
  align-items: center;
  justify-content: flex-end;
  pointer-events: none;
`;

const NotificationReportProgressStyle = styled.div`
  width: 210px;
  height: 15px;
  display: flex;
  flex-direction: row;
`;

const NotificationProgressBar = styled.div`
  flex: 1;
  border-radius: 15px;
  background-color: grey;
`;

const NotificationCurrentProgress = styled.div`
  width: ${props => props.percent}%;
  height: 15px;
  border-radius: 15px;
  transition: 0.4s linear;
  background-color: ${props => props.bg};
`;

const NotificationReportAction = styled(FontAwesomeIcon)`
  margin: 0px 0px 0px 10px;
`;

async function handleClickReportAction(
  percent,
  isFetching,
  reportData,
  reportParams,
  actions
) {
  if (percent === 100) {
    reportParams.onDownload();
    console.log(reportData);
    let csv = await json2csv(
      reportData.map(datum => {
        const obj = {};
        for (const key in datum) {
          const config = reportParams.tableConfig[key];
          if (config) {
            obj[config.headerLabel] = config.renderAs
              ? config.renderAs(datum)
              : datum[key];
          }
        }
        return obj;
      })
    );
    if (reportParams.tableSummary) {
      const csv2 = await json2csv(
        reportParams.tableSummary.data.map(datum => {
          const obj = {};
          for (const key in datum) {
            const config = reportParams.tableSummary.config[key];
            if (config) {
              obj[config.headerLabel] = config.renderAs
                ? config.renderAs(datum)
                : datum[key];
            }
          }
          return obj;
        })
      );
      csv = csv.concat('\n\n' + csv2);
    }
    const fileData = {
      mime: 'text/csv',
      filename: reportParams.fileName,
      contents: csv,
    };
    const blob = new Blob([fileData.contents], {
      type: fileData.mime,
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    document.body.appendChild(a);
    a.download = fileData.filename;
    a.href = url;
    a.click();
    document.body.removeChild(a);
    actions.closeNotif();
  } else {
    if (isFetching) {
      actions.stop();
    } else {
      actions.start();
    }
  }
}

const NotificationPopup = props => {
  const popupStyle = getPopupStyle(props.notif.type);

  return (
    <NotificationPopupStyles popupStyle={popupStyle} isMobile={props.isMobile}>
      <NotificationIcon icon={popupStyle.icon} color={popupStyle.border} />
      <NotificationContent
        content={props.notif}
        color={popupStyle.border}
        closeNotif={() => props.closeNotif()}
      />
      <NotificationClose
        icon={'times'}
        onClick={() => {
          props.closeNotif();
        }}
      />
    </NotificationPopupStyles>
  );
};

const NotificationContent = props => {
  return (
    <NotificationContentStyle>
      <NotificationTitleStyle color={props.color}>
        {props.content.title}
      </NotificationTitleStyle>
      <NotificationMsgStyle>{props.content.message}</NotificationMsgStyle>
      {props.content.isProgress &&
        (props.content.reportDLParams ? (
          <NotificationReportProgress
            notifId={props.content.id}
            reportDLParams={props.content.reportDLParams}
            closeNotif={() => props.closeNotif()}
          />
        ) : props.content.recordDLParams ? (
          <NotificationRecordProgress
            notifId={props.content.id}
            recordDLParams={props.content.recordDLParams}
            closeNotif={() => props.closeNotif()}
          />
        ) : null)}
    </NotificationContentStyle>
  );
};

const NotificationRecordProgress = props => {
  const { progress, consolidatedData, isFetching, setIsFetching } =
    useQueryRecordDownloader(props.recordDLParams, props.notifId);
  const percent = progress * 100;
  const bg = getProgressBG(percent);

  return (
    <NotificationReportProgressStyle>
      <NotificationProgressBar>
        <NotificationCurrentProgress percent={percent} bg={bg} />
      </NotificationProgressBar>

      <NotificationReportAction
        icon={
          percent === 100
            ? 'arrow-circle-down'
            : isFetching
              ? 'pause-circle'
              : 'play-circle'
        }
        color={
          percent === 100 || !isFetching ? 'rgb(0, 255, 0)' : 'rgb(255, 0, 0)'
        }
        onClick={() =>
          handleClickReportAction(
            percent,
            isFetching,
            consolidatedData,
            props.recordDLParams,
            {
              closeNotif: () => props.closeNotif(),
              start: () => setIsFetching(true),
              stop: () => setIsFetching(false),
            }
          )
        }
      />
    </NotificationReportProgressStyle>
  );
};

const NotificationReportProgress = props => {
  const { progress, consolidatedData, isFetching, setIsFetching } =
    useQueryReportDownloader(props.reportDLParams, props.notifId);
  const percent = progress * 100;
  const bg = getProgressBG(percent);

  return (
    <NotificationReportProgressStyle>
      <NotificationProgressBar>
        <NotificationCurrentProgress percent={percent} bg={bg} />
      </NotificationProgressBar>

      <NotificationReportAction
        icon={
          percent === 100
            ? 'arrow-circle-down'
            : isFetching
              ? 'pause-circle'
              : 'play-circle'
        }
        color={
          percent === 100 || !isFetching ? 'rgb(0, 255, 0)' : 'rgb(255, 0, 0)'
        }
        onClick={() =>
          handleClickReportAction(
            percent,
            isFetching,
            consolidatedData,
            props.reportDLParams,
            {
              closeNotif: () => props.closeNotif(),
              start: () => setIsFetching(true),
              stop: () => setIsFetching(false),
            }
          )
        }
      />
    </NotificationReportProgressStyle>
  );
};

const NotificationAlerts = () => {
  const { isMobile } = useContext(ResponsiveContext);
  const { notifications, closeNotif } = useContext(NotificationContext);
  const transitions = useTransition(
    notifications,
    notification => notification.id,
    {
      from: { height: 0, transform: 'scale(0,0)', marginTop: 10 },
      enter: { height: 150, transform: 'scale(1,1)', marginTop: 10 },
      leave: { height: 0, transform: 'scale(0,0)', marginTop: 0 },
    }
  );

  return (
    <NotificationAlertsStyle isMobile={isMobile}>
      {transitions.map(({ item: notif, key, props }) => {
        return (
          <animated.div key={key} style={props}>
            <NotificationPopup
              isMobile={isMobile}
              notif={notif}
              closeNotif={() => closeNotif(notif.id)}
            />
          </animated.div>
        );
      })}
    </NotificationAlertsStyle>
  );
};

NotificationPopup.propTypes = {
  notif: PropTypes.object,
  isMobile: PropTypes.bool,
  closeNotif: PropTypes.func.isRequired,
};

NotificationRecordProgress.propTypes = {
  recordDLParams: PropTypes.object,
  notifId: PropTypes.string,
  closeNotif: PropTypes.func,
};

NotificationContent.propTypes = {
  content: PropTypes.object,
  color: PropTypes.string,
  closeNotif: PropTypes.func,
};

NotificationReportProgress.propTypes = {
  reportDLParams: PropTypes.object,
  notifId: PropTypes.string,
  closeNotif: PropTypes.func,
};

export default NotificationAlerts;
