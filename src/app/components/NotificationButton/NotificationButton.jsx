import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import PropTypes from 'prop-types';
import React from 'react';
import { useQuery } from '@apollo/client';
import { withRouter } from 'react-router-dom';
import styled from 'styled-components';
import Button from '../Button/Button';
import { GET_UNREAD_NOTIFS } from './query';

const StyledNotificationsButton = styled(Button)`
  margin-right: 5px;
`;

const NotificationsButtonCount = styled.div`
  position: absolute;
  top: -8px;
  right: -12px;
  font-weight: bold;
  font-size: 12px;
  border-radius: 50%;
  background-color: #f10036;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 16px;
  width: 16px;
`;

const NotificationsButtonIcon = styled(FontAwesomeIcon)`
  font-size: 20px;
`;

const NotificationsButton = ({ history, className }) => {
  const { data } = useQuery(GET_UNREAD_NOTIFS, {
    fetchPolicy: 'network-only',
    pollInterval: 1000 * 60 * 10,
    variables: { isPolling: true },
  });

  return (
    <StyledNotificationsButton
      className={className}
      onClick={() => {
        history.push('/notifications');
      }}
    >
      <NotificationsButtonIcon icon="bell" />
      {data &&
        data.notifIsNotViewCount &&
        !!+data.notifIsNotViewCount.isNotViewed && (
          <NotificationsButtonCount>
            {+data.notifIsNotViewCount.isNotViewed}
          </NotificationsButtonCount>
        )}
    </StyledNotificationsButton>
  );
};

NotificationsButton.propTypes = {
  history: PropTypes.object,
  className: PropTypes.string,
};

export default withRouter(NotificationsButton);
