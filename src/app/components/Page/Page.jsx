import PropTypes from 'prop-types';
import React, { useContext } from 'react';
import styled from 'styled-components';
import ResponsiveContext from '../../context/ResponsiveContext';
import Sidebar from './Sidebar';
import Globe<PERSON>ogo from './Sidebar/GlobeLogo';
import Sidebar<PERSON>immer from './Sidebar/SidebarDimmer';
import UserDetails from './Sidebar/UserDetails';

const PageContainer = styled.div`
  display: flex;
  flex-direction: column;
`;

const PageContent = styled.div`
  padding: 1em;
  margin-left: 300px;
  width: calc(100vw - 300px);
  height: calc(100vh - 40px);
  overflow-y: auto;
  display: flex;
  flex-direction: column;

  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    width: 100vw;
    height: auto;
    min-height: calc(100vh - 160px);
    margin: 0;
    overflow-y: auto;
  }
`;

const PageFooter = styled.div`
  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    position: initial;
  }

  background-color: #002244;
  height: 40px;
  position: fixed;
  width: 100vw;
  bottom: 0;
`;

const PageFooterSidebarSection = styled.div`
  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    margin-left: 0;
    justify-content: center;
    text-align: center;
    padding: 0 15%;
  }
  color: white;
  height: 100%;
  display: flex;
  align-items: center;
  font-size: ${props => props.theme.fontSize.xs};
  margin-left: 55px; /* 1/4 of sidebar width */
`;

const Page = ({ children }) => {
  const { isMobile } = useContext(ResponsiveContext);

  return (
    <PageContainer>
      <Sidebar />
      {isMobile && (
        <>
          <SidebarDimmer />
          <GlobeLogo />
          <UserDetails withMenu />
        </>
      )}
      <PageContent>{children}</PageContent>
      <PageFooter>
        <PageFooterSidebarSection>
          © GLOBE TELECOM - Payment Service 2019. All rights reserved.
        </PageFooterSidebarSection>
      </PageFooter>
    </PageContainer>
  );
};

Page.propTypes = {
  children: PropTypes.any,
};

export default Page;
