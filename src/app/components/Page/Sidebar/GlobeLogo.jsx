import React from 'react';
import styled from 'styled-components';
import globeLogo from '../../../assets/globe-logo.png';

const GlobeLogoContainer = styled.div`
  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    display: flex;
    flex-direction: row;
    align-items: center;
    background-color: white;
    padding: 10px;
  }
`;

const LogoContainer = styled.div`
  margin: 14px 0;
  text-align: center;
  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    margin: 0;
    margin-right: 10px;
  }
`;

const GlobelogoImage = styled.img`
  width: 112px;

  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    width: 100px;
  }
`;

const GlobeLogoTitle = styled.div`
  font-size: ${props => props.theme.fontSize.m};
  color: #244958;
  margin-left: 10px;
  font-weight: 300;
  margin-bottom: 14px;
  text-align: center;

  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    margin-bottom: 0;
    text-align: left;
  }
`;

const GlobeLogo = () => {
  return (
    <GlobeLogoContainer>
      <LogoContainer>
        <GlobelogoImage src={globeLogo} alt="Globe" />
      </LogoContainer>
      <GlobeLogoTitle>PAYMENT SERVICE</GlobeLogoTitle>
    </GlobeLogoContainer>
  );
};

export default GlobeLogo;
