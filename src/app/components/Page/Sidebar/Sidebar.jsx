import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import PropTypes from 'prop-types';
import React, { useContext, useEffect } from 'react';
import { Link, NavLink, withRouter } from 'react-router-dom';
import styled from 'styled-components';
import AuthContext from '../../../context/AuthContext/AuthContext';
import NavContext from '../../../context/NavContext';
import SecondaryButton from '../../Button/SecondaryButton';
import GlobeLogo from './GlobeLogo';
import links from './links';
import UserDetails from './UserDetails';
import { useOktaAuth } from '@okta/okta-react';

const SidebarContainer = styled.aside`
  z-index: 1000;
  width: 300px;
  background-color: white;

  position: fixed;
  overflow-y: auto;
  height: calc(100vh - 40px);

  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    top: 0;
    left: ${props => (props.open ? 0 : -300)}px;
    width: 300px;
    position: fixed;
    height: 100vh;

    transition: left 0.3s ease;
  }
`;

const NavLinkIcon = styled(FontAwesomeIcon)`
  margin-right: 10px;
`;

const NavChevronIcon = styled(FontAwesomeIcon)`
  font-size: 12px;
  margin: auto;
  margin-right: 0px;
`;

const MenuButton = styled(Link)`
  display: flex;
  flex-direction: row;
  align-items: center;
  cursor: pointer;

  width: 100%;
  min-height: 45px;
  padding: 0 20px;
  background-color: ${props => (props.isActive() ? '#c5edfd' : 'white')};
  border: 0;
  text-align: left;

  color: #333333;

  transition: background-color 0.1s ease;

  &:hover {
    background-color: #cccccc;
  }

  &.active,
  &:focus {
    color: #0072ce;
    background-color: #c5edfd;
    outline: none;

    &:hover {
      background-color: #afd2e0;
    }
  }
`;

const SubMenuButton = styled(NavLink)`
  color: #333333;
  display: flex;
  align-items: center;
  text-decoration: none;
  font-size: 14px;
  min-height: 45px;
  margin-left: 39px;
  padding-left: 10px;
  border-bottom: 2px solid
    ${props => (props.selected ? '#26aeef' : 'transparent')};
  transition: all 0.15s ease;
  background-color: white;
  text-align: left;

  &:hover {
    background-color: #cccccc;
  }

  &:focus {
    border-bottom: 2px solid #26aeef;

    &:hover {
      background-color: #cccccc;
    }
  }
`;

const HelpSupportButton = styled(SecondaryButton)`
  color: #333333;
  padding: 5px 20px;
  width: 100%;
  min-height: 45px;
  font-size: 16px;
  text-align: left;
`;

const LogoutButton = styled(SecondaryButton)`
  color: #333333;
  padding: 5px 20px;
  width: 100%;
  min-height: 45px;
  font-size: 16px;
  text-align: left;
  margin-bottom: 10px;
`;

const Sidebar = ({ location, history }) => {
  const { permissions, reportPermissions, logout } = useContext(AuthContext);
  const { selectedSidebar, handleSelectSidebar, handleToggleNav, isNavOpen } =
    useContext(NavContext);

  const { oktaAuth } = useOktaAuth();

  function handleClickMenu(link) {
    handleSelectSidebar(
      selectedSidebar === link.label && !link.to ? null : link.label
    );
    if (link.to) {
      history.push(link.to);
      handleToggleNav();
    }
  }

  useEffect(() => {
    if (selectedSidebar) {
      let found = false;
      const path = location.pathname.split('/')[1];
      for (const link of links) {
        if (link.menu) {
          for (const submenu of link.menu) {
            if (submenu.to.split('/')[1] === path) {
              found = true;
              break;
            }
          }
        } else if (link.to) {
          if (link.to.split('/')[1] === path) {
            found = true;
            break;
          }
        }
      }
      if (!found) {
        handleSelectSidebar(null);
      }
    }
  }, [location.pathname]);

  if (location.pathname === '/checkout') return null;

  return (
    <SidebarContainer open={isNavOpen}>
      <GlobeLogo />
      <UserDetails />
      <div>
        {links
          .filter(
            link =>
              !link.visible ||
              { ...permissions, ...reportPermissions }[link.visible].view
          )
          .filter(
            link =>
              !link.menu ||
              link.menu.find(menuItem => {
                return { ...permissions, ...reportPermissions }[
                  menuItem.visible
                ].view;
              })
          )
          .map((link, index) => (
            <React.Fragment key={index}>
              <MenuButton
                as="button"
                activeClassname="active"
                isActive={() => link.label === selectedSidebar}
                onClick={() => handleClickMenu(link)}
              >
                {link.icon && <NavLinkIcon icon={link.icon} />}
                {link.label}
                {link.menu ? (
                  <NavChevronIcon
                    icon={
                      link.label === selectedSidebar
                        ? 'chevron-down'
                        : 'chevron-right'
                    }
                  />
                ) : null}
              </MenuButton>

              {link.label === selectedSidebar &&
                link.menu &&
                link.menu
                  .filter(menuItem => {
                    return { ...permissions, ...reportPermissions }[
                      menuItem.visible
                    ].view;
                  })
                  .map((menuItem, index) => (
                    <SubMenuButton
                      onClick={() => {
                        handleToggleNav();
                      }}
                      key={index}
                      to={menuItem.to}
                      selected={location.pathname === menuItem.to}
                    >
                      {menuItem.label}
                    </SubMenuButton>
                  ))}
            </React.Fragment>
          ))}
      </div>
      <hr style={{ border: '0.5px solid #e1e1e1' }} />
      <HelpSupportButton
        iconPosition="left"
        icon="life-ring"
        onClick={() => {
          history.push('/help');
        }}
      >
        Help & Support
      </HelpSupportButton>

      <LogoutButton
        iconPosition="left"
        icon="sign-out-alt"
        onClick={async () => {
          await logout();
          await oktaAuth.signOut();
          handleSelectSidebar(null);
          history.push('/login');
        }}
      >
        Logout
      </LogoutButton>
    </SidebarContainer>
  );
};

Sidebar.propTypes = {
  /** from withRouter, see react-router-dom */
  history: PropTypes.object,
  location: PropTypes.object,
};

export default withRouter(Sidebar);
