import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import React, { useContext } from 'react';
import styled from 'styled-components';
import NavContext from '../../../context/NavContext';
import Button from '../../Button/Button';

const StyledSidebarDimmer = styled.div`
  position: fixed;
  height: 100vh;
  width: 100vw;
  background-color: ${props =>
    props.active ? 'rgba(0, 0, 0, 0.5)' : 'transparent'};
  z-index: ${props => (props.active ? 100 : -100)};
`;

const CloseButton = styled(Button)`
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 20px;
  color: white;
`;

const SidebarDimmer = () => {
  const { isNavOpen, handleToggleNav } = useContext(NavContext);

  return (
    <StyledSidebarDimmer
      active={isNavOpen}
      onClick={() => {
        handleToggleNav();
      }}
    >
      {isNavOpen && (
        <CloseButton>
          <FontAwesomeIcon icon="times" />
        </CloseButton>
      )}
    </StyledSidebarDimmer>
  );
};

export default SidebarDimmer;
