import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import PropTypes from 'prop-types';
import React, { useContext } from 'react';
import styled from 'styled-components';
import AuthContext from '../../../context/AuthContext/AuthContext';
import NavContext from '../../../context/NavContext';
import Button from '../../Button/Button';
import NotificationButton from '../../NotificationButton';

const UserDetailsContainer = styled.div`
  background-color: #009cde;
  min-height: 60px;

  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 10px 14px;

  margin-bottom: ${props => (props.withMenu ? '0px' : '40px')};
`;

const UserDetailsCircle = styled(FontAwesomeIcon)`
  color: #72cbf3;
  background-color: white;
  border-radius: 50%;
  font-size: 38px;
`;

const UserDetailsPanel = styled.div`
  margin: 0 12px;
  flex: 1;
`;

const UserDetailsName = styled.div`
  font-weight: bold;
  color: #f8f8f8;
  font-size: ${props => props.theme.fontSize.m};
`;

const UserDetailsRole = styled.div`
  color: #cbf0ff;
  font-weight: 300;
  font-size: ${props => props.theme.fontSize.xs};
`;

const UserDetailsMenuIcon = styled(FontAwesomeIcon)`
  color: white;
  font-size: 20px;
`;

const UserDetailsNotificationsButton = styled(NotificationButton)`
  color: white;
`;

const UserDetails = ({ withMenu = false }) => {
  const { authUser } = useContext(AuthContext);
  const { handleToggleNav } = useContext(NavContext);

  if (!authUser) return null;
  return (
    <UserDetailsContainer withMenu={withMenu}>
      <UserDetailsCircle icon="user-circle" />
      <UserDetailsPanel>
        <UserDetailsName>Hi! {authUser.name.split(/\s+/)[0]}</UserDetailsName>
        <UserDetailsRole>{authUser.role.name}</UserDetailsRole>
      </UserDetailsPanel>
      <UserDetailsNotificationsButton />
      {withMenu && (
        <Button
          onClick={() => {
            handleToggleNav();
          }}
        >
          <UserDetailsMenuIcon icon="bars" />
        </Button>
      )}
    </UserDetailsContainer>
  );
};

UserDetails.propTypes = {
  withMenu: PropTypes.bool,
};

export default UserDetails;
