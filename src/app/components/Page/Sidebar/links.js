export default [
  {
    label: 'Dashboard',
    icon: 'desktop',
    to: '/',
  },
  {
    label: 'User Management',
    icon: 'users-cog',
    menu: [
      {
        to: '/user-accounts',
        label: 'User Accounts',
        visible: 'User',
      },
      {
        to: '/user-roles',
        label: 'User Roles',
        visible: 'Role',
      },
    ],
  },
  {
    to: '/channels-management',
    label: 'Channel Management',
    icon: 'credit-card',
    visible: 'Channel',
  },
  {
    label: 'MID Management',
    icon: 'credit-card',
    visible: 'Mid',
    to: '/mid',
  },
  {
    label: 'Pay by Link',
    icon: 'credit-card',
    visible: 'PayByLinkModule',
    to: '/pay-by-link',
  },
  {
    label: 'Payment Mode Management',
    icon: 'credit-card',
    visible: 'PostPaymentConfig',
    to: '/payment-mode-management',
  },
  {
    label: 'GCash Refund',
    icon: 'credit-card',
    menu: [
      {
        to: '/gcash-refund-request',
        label: 'GCash Refund Request',
        visible: 'GcashRefundRequest',
      },
      {
        to: '/gcash-refund-approval',
        label: 'GCash Refund Approval',
        visible: 'GcashRefundApproval',
      },
    ],
  },
  {
    label: 'Adyen Refund',
    icon: 'credit-card',
    menu: [
      {
        to: '/adyen-refund-request',
        label: 'Adyen Refund Request',
        visible: 'CardRefundRequest',
      },
      {
        to: '/adyen-refund-approval',
        label: 'Adyen Refund Approval',
        visible: 'CardRefundApproval',
      },
    ],
  },
  {
    label: 'Xendit Refund',
    icon: 'credit-card',
    menu: [
      {
        to: '/xendit-refund-request',
        label: 'Xendit Refund Request',
        visible: 'XenditRefundRequest',
      },
      {
        to: '/xendit-refund-approval',
        label: 'Xendit Refund Approval',
        visible: 'XenditRefundApproval',
      },
    ],
  },
  {
    label: 'Convenience Fee',
    icon: 'coins',
    visible: 'ConvenienceFee',
    to: '/convenience-fee',
  },
  {
    label: 'Convenience Fee Brands',
    icon: 'coins',
    visible: 'ConvenienceFeeBrand',
    to: '/convenience-fee-brands',
  },
  {
    label: 'Installment',
    icon: 'calendar-alt',
    visible: 'InstallmentMid',
    to: '/installment-mid',
  },
  {
    label: 'Provider Management',
    icon: 'clipboard-list',
    visible: 'Provider',
    to: '/provider-management',
  },
  {
    label: 'BankCode Management',
    icon: 'university',
    visible: 'Bank',
    to: '/bankcode-management',
  },
  {
    to: '/gcash-enrolled-payment-methods',
    icon: 'credit-card',
    label: 'GCash Enrolled Payment Methods',
    visible: 'GCashBindingReport',
  },

  {
    label: 'Reports',
    icon: 'chart-bar',
    menu: [
      {
        to: '/transaction-logs',
        label: 'Transaction Logs',
        visible: 'Transaction',
      },
      {
        to: '/endgame-transaction-logs',
        label: 'EndGame Transaction Logs',
        visible: 'EndGameReport',
      },
      {
        to: '/channel-transactions',
        label: 'Channel Transactions',
        visible: 'ChannelReport',
      },
      {
        to: '/or-report',
        label: 'OR Report',
        visible: 'LoadORReport',
      },
      {
        to: '/gots-report',
        label: 'GOTS Reports',
        visible: 'GotsReport',
      },
      {
        to: '/installment-report',
        label: 'Installment Report',
        visible: 'InstallmentReport',
      },
      {
        to: '/gcash-ecpay-wallet',
        label: 'ECPay Wallet',
        visible: 'ECPay',
      },
      {
        to: '/paybylink',
        label: 'GlobeBBand Shop',
        visible: 'PayByLink',
      },
      {
        to: '/globe-one-logs',
        label: 'GlobeOne Logs',
        visible: 'GlobeOne',
      },
      {
        to: '/content-gcash-reports',
        label: 'Content-GCash Reports',
        visible: 'ContentGcashReport',
      },
      {
        to: '/content-fraud-reports',
        label: 'Swipe Transaction Logs',
        visible: 'ContentFraudReport',
      },
      {
        to: '/gcash-refund-report-detailed',
        label: 'GCash Refund Detailed Report',
        visible: 'GcashRefundDetailedReport',
      },
      {
        to: '/gcash-refund-report-summarized',
        label: 'GCash Refund Summarized Report',
        visible: 'GcashRefundSummaryReport',
      },
      {
        to: '/adyen-refund-detailed-report',
        label: 'Adyen Refund Detailed Report',
        visible: 'CardRefundDetailedReport',
      },
      {
        to: '/adyen-refund-summary-report',
        label: 'Adyen Refund Summarized Report',
        visible: 'CardRefundSummaryReport',
      },
      {
        to: '/xendit-refund-report-detailed',
        label: 'Xendit Refund Detailed Report',
        visible: 'XenditRefundDetailedReport',
      },
      {
        to: '/xendit-refund-report-summarized',
        label: 'Xendit Refund Summarized Report',
        visible: 'XenditRefundSummaryReport',
      },
      {
        to: '/ada-declined-rate-detailed-report',
        label: 'ADA Declined Rate Detailed Report',
        visible: 'ADADeclinedReport',
      },
      {
        to: '/ada-declined-rate-summary-report',
        label: 'ADA Declined Rate Summarized Report',
        visible: 'ADASummaryReport',
      },
      {
        to: '/failed-postings',
        label: 'Failed Postings',
        visible: 'Failed',
      },
      {
        to: '/billing',
        label: 'Billing Reports',
        visible: 'Billing',
      },
      {
        to: '/gateway-credit-card',
        label: 'Gateway for Online Payments',
        visible: 'Gateway',
      },
      {
        to: '/gateway-collection',
        label: 'Credit Card Collection Summary',
        visible: 'Collection',
      },
      {
        to: '/revenue-accounting-report',
        label: 'Revenue Accounting Report',
        visible: 'Wireline',
      },
      {
        to: '/treasury-bill',
        label: 'Treasury Bills',
        visible: 'Treasury',
      },
      {
        to: '/monthly-generated',
        label: 'Monthly Generated',
        visible: 'MonthlyGenerated',
      },
      {
        to: '/batchfiles',
        label: 'Batch Files',
        visible: 'LukeBatchFile',
      },
      {
        to: '/pay-by-link-report',
        label: 'Pay by Link Report',
        visible: 'PayByLinkReport',
      },
    ],
  },
  {
    label: 'System',
    icon: 'cogs',
    menu: [
      {
        to: '/audit-trail',
        label: 'Audit Trail',
        visible: 'Audit',
      },
      {
        to: '/archive',
        label: 'Archive',
        visible: 'Audit',
      },
      {
        to: '/config',
        label: 'Configuration',
        visible: 'Config',
      },
      {
        to: '/bill-liner-config',
        label: 'Bill Liner Config',
        visible: 'BillLinerConfig',
      },
    ],
  },
  {
    to: '/checkout',
    label: 'Checkout',
  },
  {
    to: '/ADAsimulation',
    label: 'ADA Simulation',
  },
  {
    to: '/DropinSimulation',
    label: 'DropIn Simulator',
    visible: 'DropinSimulator',
  },
];
