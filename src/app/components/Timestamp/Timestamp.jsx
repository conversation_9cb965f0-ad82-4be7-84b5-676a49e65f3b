import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import format from 'date-fns/format';
import React, { useEffect, useState } from 'react';
import styled from 'styled-components';

const TimestampContainer = styled.div`
  background-color: #ddeef7;
  margin-left: 10px;
  padding: 0 17px;
  border-radius: 2px;
  border-color: #ddeef7;
  font-weight: 300;
  font-size: 14px;
  display: flex;
  flex-direction: row;
  align-items: center;
  min-height: 50px;

  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    margin-left: 0;
    flex: 1;
    justify-content: center;
  }
`;

const TimestampIcon = styled(FontAwesomeIcon)`
  margin-right: 10px;
`;

const Timestamp = () => {
  const [date, setDate] = useState(new Date());
  useEffect(() => {
    const intervalId = setInterval(() => {
      setDate(new Date());
    }, 1000);
    return () => clearInterval(intervalId);
  }, []);

  return (
    <TimestampContainer>
      <TimestampIcon icon="calendar-day" />
      {format(date, 'MM/DD/YYYY - hh:mm:ss A')}
    </TimestampContainer>
  );
};

export default Timestamp;
