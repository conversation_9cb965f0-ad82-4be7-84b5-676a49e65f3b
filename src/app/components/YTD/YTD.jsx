import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { GlobalSearchFilterDropdown } from '../GlobalSearch/styled';
import {
  createYearOptions,
  MONTHS,
  getMonthNumber,
} from '../GlobalSearch/utils';

const YTDContainer = styled.div`
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin: 0px;
`;

let YEARS = createYearOptions(9, 0);

const YTD = ({ name, handleChange, hasDelimiter, initialValue, value }) => {
  const [months, setMonths] = useState([]);
  const [ytd, setYTD] = useState(
    initialValue ||
      value || {
        month: null,
        year: null,
      }
  );

  // if parent's value prop changes, update internal state
  useEffect(() => {
    if (JSON.stringify(value) !== JSON.stringify(ytd)) {
      setYTD(value);
    }
  }, [value]);

  // call parent's handleChange function everytime internal state changes
  useEffect(() => {
    handleChange(ytd);
  }, [JSON.stringify(ytd)]);

  useEffect(() => {
    let monthIndex = 0;
    if (ytd.year) {
      monthIndex =
        ytd.year === new Date().getFullYear() && new Date().getMonth() !== 11
          ? new Date().getMonth()
          : 12;
    }

    setMonths(MONTHS.filter((month, index) => index < monthIndex));
  }, [ytd.year]);

  useEffect(() => {
    if (ytd.year === new Date().getFullYear()) {
      if (getMonthNumber(ytd.month) >= new Date().getMonth()) {
        setYTD({ ...ytd, month: '' });
      }
    }
  }, [JSON.stringify(months)]);

  return (
    <>
      <YTDContainer>
        <GlobalSearchFilterDropdown
          placeholder="Month"
          name={`${name}.month`}
          value={
            ytd && ytd.month === null
              ? new Date().getMonth() > 0
                ? MONTHS[new Date().getMonth() - 1].value
                : ''
              : ytd.month
          }
          options={months}
          onChange={month => {
            setYTD({ ...ytd, month });
          }}
        />
      </YTDContainer>
      <div>{!!hasDelimiter && '—'}</div>
      <YTDContainer>
        <GlobalSearchFilterDropdown
          placeholder="Year"
          name={`${name}.year`}
          value={ytd && ytd.year === null ? new Date().getFullYear() : ytd.year}
          options={YEARS}
          onChange={year => {
            setYTD({ ...ytd, year });
          }}
        />
      </YTDContainer>
    </>
  );
};

YTD.propTypes = {
  name: PropTypes.string,
  handleChange: PropTypes.func.isRequired,
  hasDelimiter: PropTypes.bool,
  initialValue: PropTypes.shape({
    month: PropTypes.string,
    year: PropTypes.string,
  }),
  value: PropTypes.shape({
    month: PropTypes.string,
    year: PropTypes.string,
  }),
};

export default YTD;
