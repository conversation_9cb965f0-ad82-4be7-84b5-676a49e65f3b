import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { GlobalSearchFilterDropdown } from '../GlobalSearch/styled';
import { createYearOptions } from '../GlobalSearch/utils';

const YearRangeContainer = styled.div`
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin: 0px;
`;

let YEARS = createYearOptions(9, 0);

const YearRange = ({
  name,
  handleChange,
  hasDelimiter,
  initialValue,
  value,
}) => {
  const [startYears, setStartYears] = useState([]);
  const [endYears, setEndYears] = useState([]);
  const [yearRangeValue, setYearRangeValue] = useState(
    initialValue ||
      value || {
        start: null,
        end: null,
      }
  );

  // if parent's value prop changes, update internal state
  useEffect(() => {
    if (JSON.stringify(value) !== JSON.stringify(yearRangeValue)) {
      setYearRangeValue(value);
    }
  }, [value]);

  // call parent's handleChange function everytime internal state changes
  useEffect(() => {
    handleChange(yearRangeValue);
  }, [JSON.stringify(yearRangeValue)]);

  useEffect(() => {
    let endIndex = 0;
    if (yearRangeValue.end) {
      endIndex = YEARS.findIndex(x => x.value === yearRangeValue.end);
    }

    setStartYears(YEARS.filter((year, index) => index >= endIndex));
  }, [yearRangeValue]);

  useEffect(() => {
    let startIndex = 0;
    if (yearRangeValue.start) {
      startIndex = YEARS.findIndex(x => x.value === yearRangeValue.start);
    }
    setEndYears(YEARS.filter((year, index) => index <= startIndex));
  }, [yearRangeValue]);

  return (
    <>
      <YearRangeContainer>
        <GlobalSearchFilterDropdown
          placeholder="From"
          name={`${name}.start`}
          value={
            yearRangeValue && yearRangeValue.start === null
              ? new Date().getFullYear()
              : yearRangeValue.start
          }
          options={startYears}
          onChange={start => {
            setYearRangeValue({ ...yearRangeValue, start });
          }}
        />
      </YearRangeContainer>
      <div>{!!hasDelimiter && '—'}</div>
      <YearRangeContainer>
        <GlobalSearchFilterDropdown
          placeholder="To"
          name={`${name}.end`}
          value={
            yearRangeValue && yearRangeValue.end === null
              ? new Date().getFullYear()
              : yearRangeValue.end
          }
          options={endYears}
          onChange={end => {
            setYearRangeValue({ ...yearRangeValue, end });
          }}
        />
      </YearRangeContainer>
    </>
  );
};

YearRange.propTypes = {
  name: PropTypes.string,
  handleChange: PropTypes.func.isRequired,
  hasDelimiter: PropTypes.bool,
  initialValue: PropTypes.shape({
    start: PropTypes.string,
    end: PropTypes.string,
  }),
  value: PropTypes.shape({
    start: PropTypes.string,
    end: PropTypes.string,
  }),
};

export default YearRange;
