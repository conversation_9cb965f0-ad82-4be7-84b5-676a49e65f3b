import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import { useApolloClient } from '@apollo/client';
import { createNetworkStatusNotifier } from 'react-apollo-network-status';
import AlertModal from '../../components/Modal/AlertModal';
import { useMutation } from '@apollo/client';
import usePrevious from '../../hooks/usePrevious';
import { LOGIN, LOGOUT, VALIDATE_TOKEN } from './mutation';

const AuthContext = React.createContext({});

const { useApolloNetworkStatus } = createNetworkStatusNotifier();

const entities = [
  'User',
  'Role',
  'Channel',
  'Mid',
  'Provider',
  'Bank',
  'Audit',
  'Config',
  'BillLinerConfig',
  'Archive',
  'GcashRefundRequest',
  'GcashRefundApproval',
  'XenditRefundRequest',
  'XenditRefundApproval',
  'CardRefundRequest',
  'CardRefundApproval',
  'InstallmentMid',
  'PayByLinkModule',
  'PostPaymentConfig',
  'GCashBindingReport',
  'ConvenienceFee',
  'ConvenienceFeeBrand',
];

const reports = [
  'Transaction',
  'EndGameReport',
  'ChannelReport',
  'GotsReport',
  'PayByLink',
  'ECPay',
  'GlobeOne',
  'Failed',
  'Billing',
  'Gateway',
  'Collection',
  'Wireline',
  'MonthlyGenerated',
  'Treasury',
  'LukeBatchFile',
  'LoadORReport',
  'ContentGcashReport',
  'ContentFraudReport',
  'GcashRefundDetailedReport',
  'GcashRefundSummaryReport',
  'CardRefundDetailedReport',
  'CardRefundSummaryReport',
  'ADADeclinedReport',
  'ADASummaryReport',
  'InstallmentReport',
  'DropinSimulator',
  'XenditRefundDetailedReport',
  'XenditRefundSummaryReport',
  'PayByLinkReport',
];

const dashboard = [
  'transactions',
  'onlineCCGCash',
  'revenuePerChannel',
  'notifications',
  'transactionsPerChannel',
  'transactionsPercentage',
  'gatewayStatus',
  'performance',
  'adyen',
  'userMgmt',
  'channelTransaction',
];

export const AuthProvider = ({ children }) => {
  const client = useApolloClient();

  const [authUser, setAuthUser] = useState(null);
  const [isAuthenticating, setIsAuthenticating] = useState(true);

  const [dashboardPermissions, setDashboardPermissions] = useState(
    dashboard.reduce((previous, current) => {
      return {
        ...previous,
        [current]: true,
      };
    }, {})
  );

  const [permissions, setPermissions] = useState(
    entities.reduce((acc, curr) => {
      acc[curr] = {};
      return acc;
    }, {})
  );

  const [reportPermissions, setReportPermissions] = useState(
    reports.reduce((acc, curr) => {
      acc[curr] = {};
      return acc;
    }, {})
  );

  const [login] = useMutation(LOGIN);
  const [validateToken] = useMutation(VALIDATE_TOKEN);
  const [logout] = useMutation(LOGOUT);

  useEffect(() => {
    handleValidateToken();
  }, []);

  function handleSetPermissions(data) {
    if (data) {
      let newPermissions = entities.reduce((newPerm, entity) => {
        const arrPermissions = data[entity];
        let listOfPermissions = {};

        if (arrPermissions) {
          listOfPermissions = arrPermissions.reduce((arrPerm, permission) => {
            return {
              ...arrPerm,
              [permission]: true,
            };
          }, {});
        }

        return {
          ...newPerm,
          [entity]: listOfPermissions,
        };
      }, {});

      setPermissions(newPermissions);
    }
  }

  function handleSetReportPermissions(data) {
    if (data) {
      let reportView = false;
      let newReportPermissions = reports.reduce((repPerm, report) => {
        const arrPermissions = data[report];

        let listOfPermissions = {
          view: false,
          export: false,
        };

        if (arrPermissions) {
          reportView = true;
          listOfPermissions = arrPermissions.reduce(
            (arrPerm, permission) => {
              return {
                ...arrPerm,
                [permission]: true,
              };
            },
            {
              view: false,
              export: false,
            }
          );
        }

        return {
          ...repPerm,
          [report]: listOfPermissions,
        };
      }, {});

      newReportPermissions['Report'] = { view: reportView };

      setReportPermissions(newReportPermissions);
    }
  }

  function handleSetDashboardPermissions(data) {
    if (data) {
      let newDashboardPermissions = data.Dashboard.reduce(
        (finalValue, current) => {
          return {
            ...finalValue,
            [current]: true,
          };
        },
        {}
      );

      setDashboardPermissions(newDashboardPermissions);
    }
  }

  async function handleValidateToken() {
    const pswebtoken = window.localStorage.getItem('pswebtoken');
    if (pswebtoken) {
      try {
        const response = await validateToken({
          variables: { data: { token: pswebtoken } },
        });

        if (
          response.data &&
          response.data.validateToken &&
          response.data.validateToken.role
        ) {
          handleSetPermissions(response.data.validateToken.role.permissions);
          handleSetReportPermissions(
            response.data.validateToken.role.permissions
          );
          handleSetDashboardPermissions(
            response.data.validateToken.role.permissions
          );
        }

        if (
          response.data &&
          response.data.validateToken &&
          response.data.validateToken.id
        ) {
          setAuthUser(response.data.validateToken);
        }
      } catch (err) {
        //
      } finally {
        setIsAuthenticating(false);
      }
    } else {
      setIsAuthenticating(false);
    }
  }

  async function handleLogin({ accessToken, idToken }) {
    try {
      const payload = {
        variables: {
          data: {
            idToken: accessToken.accessToken,
          },
        },
      }
      const response = await login(payload);
      window.localStorage.setItem('pswebtoken', response.data.login.token);
      if (
        response.data &&
        response.data.login.user &&
        response.data.login.user.role
      ) {
        handleSetPermissions(response.data.login.user.role.permissions);
        handleSetReportPermissions(response.data.login.user.role.permissions);
        handleSetDashboardPermissions(
          response.data.login.user.role.permissions
        );
      }

      setAuthUser(response.data.login.user);
    } catch (err) {
      console.error(err);
      throw err;
    }
  }

  async function handleLogout() {
    try {
      // const {accessToken} = JSON.parse(window.localStorage.getItem('okta-token-storage'));
      // await logout({
      //   variables: {
      //     data: {
      //       accessToken: accessToken.accessToken,
      //     },
      //   },
      // });
      client.cache.reset();
      window.localStorage.removeItem('pswebtoken');
      window.localStorage.removeItem('okta-token-storage');
      setAuthUser(null);
      setPermissions(
        entities.reduce((acc, curr) => {
          acc[curr] = {};
          return acc;
        }, {})
      );
      setReportPermissions(
        reports.reduce((acc, curr) => {
          acc[curr] = {};
          return acc;
        }, {})
      );
      setDashboardPermissions(
        dashboard.reduce((previous, current) => {
          return {
            ...previous,
            [current]: false,
          };
        }, {})
      );
    } catch (err) {
      console.error(err);
      throw err;
    }
  }

  async function handleForceLogout() {
    try {
      client.cache.reset();
      window.localStorage.removeItem('pswebtoken');
      window.localStorage.removeItem('okta-token-storage');
      setAuthUser(null);
      setPermissions(
        entities.reduce((acc, curr) => {
          acc[curr] = {};
          return acc;
        }, {})
      );
      setReportPermissions(
        reports.reduce((acc, curr) => {
          acc[curr] = {};
          return acc;
        }, {})
      );
      setDashboardPermissions(
        dashboard.reduce((previous, current) => {
          return {
            ...previous,
            [current]: false,
          };
        }, {})
      );
    } catch (err) {
      console.error(err);
      throw err;
    }
  }

  const networkStatus = useApolloNetworkStatus();

  const [authorizationModal, setAuthorizationModal] = useState(false);
  const prevQueryError = usePrevious(JSON.stringify(networkStatus.queryError));
  const prevMutationError = usePrevious(
    JSON.stringify(networkStatus.mutationError)
  );

  useEffect(() => {
    let error = null;
    if (
      networkStatus.queryError &&
      JSON.stringify(networkStatus.queryError) !== prevQueryError
    ) {
      error = networkStatus.queryError;
    } else if (
      networkStatus.mutationError &&
      JSON.stringify(networkStatus.mutationError) !== prevMutationError
    ) {
      error = networkStatus.mutationError;
    }

    if (
      error &&
      error.networkError &&
      error.networkError.result &&
      (error.networkError.result.message === 'Unauthorized Access!' ||
        error.networkError.result.message === 'Authorization Error!')
    ) {
      handleForceLogout();
      setAuthorizationModal(true);
    }
  }, [networkStatus]);

  return (
    <AuthContext.Provider
      value={{
        authUser,
        dashboardPermissions,
        permissions,
        reportPermissions,
        isAuthenticating,
        login: handleLogin,
        logout: handleLogout,
      }}
    >
      {children}
      <AlertModal
        isOpen={authorizationModal}
        title="Authorization Alert"
        icon="exclamation-circle"
        variant="warn"
        header="OOPS! DON'T PANIC."
        subHeader="Due to authorization issues, you need to login again."
        description="Apologies for the inconvenience."
        handleClose={() => setAuthorizationModal(false)}
        confirmText="Login Again"
        handleConfirm={() => {
          setAuthorizationModal(false);
        }}
      />
    </AuthContext.Provider>
  );
};

AuthProvider.propTypes = {
  children: PropTypes.element,
};

export default AuthContext;
