import { gql } from '@apollo/client';

export const LOGIN = gql`
  mutation login($data: LoginInput!) {
    login(data: $data) {
      token
      accessToken
      user {
        id
        name
        email
        role {
          name
          permissions {
            Dashboard
            User
            Role
            Channel
            Mid
            Provider
            Bank
            Transaction
            ChannelReport
            GotsReport
            Failed
            Billing
            Gateway
            Collection
            Wireline
            MonthlyGenerated
            Treasury
            LukeBatchFile
            Audit
            Archive
            Config
            ECPay
            GlobeOne
            PayByLink
            LoadORReport
            ContentGcashReport
            ContentFraudReport
            GcashRefundRequest
            GcashRefundApproval
            CardRefundRequest
            CardRefundApproval
            XenditRefundRequest
            XenditRefundApproval
            GcashRefundDetailedReport
            GcashRefundSummaryReport
            CardRefundDetailedReport
            CardRefundSummaryReport
            XenditRefundDetailedReport
            XenditRefundSummaryReport
            InstallmentReport
            InstallmentMid
            ADADeclinedReport
            ADASummaryReport
            EndGameReport
            DropinSimulator
            BillLinerConfig
            PayByLinkModule
            PayByLinkReport
            PostPaymentConfig
            GCashBindingReport
            ConvenienceFee
            ConvenienceFeeBrand
          }
        }
      }
    }
  }
`;

export const VALIDATE_TOKEN = gql`
  mutation validateToken($data: ValidateToken!) {
    validateToken(data: $data) {
      id
      name
      email
      role {
        name
        permissions {
          Dashboard
          User
          Role
          Channel
          Mid
          Provider
          Bank
          Transaction
          ChannelReport
          GotsReport
          Failed
          Billing
          Gateway
          Collection
          Wireline
          MonthlyGenerated
          Treasury
          LukeBatchFile
          Audit
          Archive
          Config
          ECPay
          GlobeOne
          PayByLink
          LoadORReport
          ContentGcashReport
          ContentFraudReport
          GcashRefundRequest
          GcashRefundApproval
          CardRefundRequest
          CardRefundApproval
          XenditRefundRequest
          XenditRefundApproval
          GcashRefundDetailedReport
          GcashRefundSummaryReport
          CardRefundDetailedReport
          CardRefundSummaryReport
          XenditRefundDetailedReport
          XenditRefundSummaryReport
          InstallmentReport
          InstallmentMid
          ADADeclinedReport
          ADASummaryReport
          EndGameReport
          DropinSimulator
          BillLinerConfig
          PayByLinkModule
          PayByLinkReport
          PostPaymentConfig
          GCashBindingReport
          ConvenienceFee
          ConvenienceFeeBrand
        }
      }
    }
  }
`;

export const LOGOUT = gql`
  mutation logout($data: LogoutInput!) {
    logout(data: $data) {
      status
    }
  }
`;
