import PropTypes from 'prop-types';
import React from 'react';
import { useQuery } from '@apollo/client';
import { GET_CONFIGS } from './query';

const ConfigContext = React.createContext({});

export const ConfigProvider = ({ children }) => {
  const { data, loading, refetch } = useQuery(GET_CONFIGS);

  return (
    <ConfigContext.Provider
      value={{
        config: data?.configs || {},
        isFetchingConfig: loading,
        refetch,
      }}
    >
      {!loading && children}
    </ConfigContext.Provider>
  );
};

ConfigProvider.propTypes = {
  children: PropTypes.element,
};

export default ConfigContext;
