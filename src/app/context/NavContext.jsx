import PropTypes from 'prop-types';
import React, { useState } from 'react';

const NavContext = React.createContext({});

export const NavProvider = ({ children }) => {
  const [isNavOpen, setIsNavOpen] = useState(false);
  const [selectedSidebar, setSelectedBar] = useState(false);
  const [isUserMenuCollapsed, setIsUserMenuCollapsed] = useState(false);

  const handleToggleNav = () => {
    setTimeout(() => {
      setIsNavOpen(!isNavOpen);
    }, 0);
  };

  const handleSelectSidebar = value => setSelectedBar(value);

  const handleCollapseUserMenu = value => setIsUserMenuCollapsed(value);

  return (
    <NavContext.Provider
      value={{
        isNavOpen,
        selectedSidebar,
        isUserMenuCollapsed,
        handleCollapseUserMenu,
        handleSelectSidebar,
        handleToggleNav,
      }}
    >
      {children}
    </NavContext.Provider>
  );
};

NavProvider.propTypes = {
  children: PropTypes.element,
};

export default NavContext;
