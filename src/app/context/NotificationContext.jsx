import React, { useState } from 'react';
import PropTypes from 'prop-types';

const VISIBILITY_PERIOD = 3 * 1000;

/* NOTIF TEMPLATE
  {
    id: '001',
    type: 'info',
    title: 'Downloading Report File',
    message: 'Downloading Transaction Logs Report',
    isProgress: true,
    progressData: {
      progress: 0,
      isProgressive: false,
    },
    isLocal: true,
    data: {},
    reportDLParams: {
      query: GET_TRANSACTION_LOGS_INFO, 
      path: 'reports', 
      queryFilter: filter, 
      reportPagination: {
        startKeys: '',
        limit: 100,
      }
    },

  }

*/

const NotificationContext = React.createContext({});

export const NotificationProvider = ({ children }) => {
  const [notifications, setNotifications] = useState([]);

  const newNotifications = newNotif => {
    setNotifications(currNotifs => [
      ...currNotifs,
      {
        ...newNotif,
        expiryTime: newNotif.notifTime + VISIBILITY_PERIOD,
      },
    ]);
  };

  const closeNotification = id => {
    setNotifications(notifications.filter(notif => notif.id !== id));
  };

  const updateNotifProgress = (id, newProgressData) => {
    setNotifications(
      notifications.map(notif => {
        if (notif.id === id) {
          return {
            ...notif,
            progressData: newProgressData,
          };
        }
        return notif;
      })
    );
  };

  const changeNotifType = (id, type) => {
    setNotifications(
      notifications.map(notif => {
        if (notif.id === id) {
          return {
            ...notif,
            type,
          };
        }
        return notif;
      })
    );
  };

  const getProgressDataById = id => {
    const notif = notifications.find(notif => notif.id === id);
    return notif ? notif.progressData : 0;
  };

  const getNotificationById = id =>
    notifications.find(notif => notif.id === id);

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        addNotif: newNotifications,
        closeNotif: closeNotification,
        updateNotifProgress,
        getProgressDataById,
        changeNotifType,
        getNotificationById,
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
};

NotificationProvider.propTypes = {
  children: PropTypes.element,
};

export default NotificationContext;
