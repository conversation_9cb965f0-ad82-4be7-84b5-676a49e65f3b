import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import theme from '../theme';

const ResponsiveContext = React.createContext({});

const PHONE_BREAKPOINT = +theme.breakpoint.phone.slice(0, -2);
export const ResponsiveProvider = ({ children }) => {
  let [isMobile, setIsMobile] = useState(
    window.screen.width <= PHONE_BREAKPOINT
  );

  function handleResize() {
    // if (window.screen.width <= PHONE_BREAKPOINT && !isMobile) {
    //   setIsMobile(true);
    // }

    // if (window.screen.width > PHONE_BREAKPOINT && isMobile) {
    //   setIsMobile(false);
    // }

    setIsMobile(window.screen.width <= PHONE_BREAKPOINT);
  }

  useEffect(() => {
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return (
    <ResponsiveContext.Provider value={{ isMobile }}>
      {children}
    </ResponsiveContext.Provider>
  );
};

ResponsiveProvider.propTypes = {
  children: PropTypes.element,
};

export default ResponsiveContext;
