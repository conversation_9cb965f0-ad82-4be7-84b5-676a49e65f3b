import { library } from '@fortawesome/fontawesome-svg-core';
import { faGooglePlusG } from '@fortawesome/free-brands-svg-icons';
import {
  faAngleDown,
  faAngleRight,
  faAngleUp,
  faArrowCircleDown,
  faBars,
  faBell,
  faCalendarAlt,
  faCalendarDay,
  faCaretDown,
  faCaretUp,
  faChartBar,
  faCheckCircle,
  faChevronDown,
  faChevronRight,
  faClipboardList,
  faCogs,
  faCreditCard,
  faDatabase,
  faDesktop,
  faEnvelope,
  faExclamationCircle,
  faEye,
  faEyeSlash,
  faFileAlt,
  faFileCsv,
  faFileDownload,
  faFileExcel,
  faFileExport,
  faFileImport,
  faFilter,
  faHome,
  faInfoCircle,
  faLifeRing,
  faPlusCircle,
  faMinusCircle,
  faPaperPlane,
  faPauseCircle,
  faPen,
  faPlayCircle,
  faPlus,
  faQuestionCircle,
  faSave,
  faSearch,
  faSignOutAlt,
  faSlidersH,
  faSort,
  faSortDown,
  faSortUp,
  faSpinner,
  faStore,
  faSyncAlt,
  faTasks,
  faTimes,
  faTimesCircle,
  faToggleOff,
  faToggleOn,
  faTrashAlt,
  faUndo,
  faUniversity,
  faUserCheck,
  faUserCircle,
  faUsersCog,
  faCopy,
  faCoins,
} from '@fortawesome/free-solid-svg-icons';

library.add(
  faEye,
  faPen,
  faTrashAlt,
  faSpinner,
  faCaretDown,
  faCaretUp,
  faSearch,
  faFilter,
  faSignOutAlt,
  faTimes,
  faEyeSlash,
  faGooglePlusG,
  faPlus,
  faSlidersH,
  faDesktop,
  faUsersCog,
  faCreditCard,
  faClipboardList,
  faUniversity,
  faUndo,
  faChartBar,
  faCogs,
  faCoins,
  faLifeRing,
  faAngleUp,
  faAngleRight,
  faAngleDown,
  faTasks,
  faEnvelope,
  faSave,
  faStore,
  faUserCheck,
  faEnvelope,
  faInfoCircle,
  faExclamationCircle,
  faTimesCircle,
  faQuestionCircle,
  faCheckCircle,
  faCalendarAlt,
  faSyncAlt,
  faBell,
  faBars,
  faCalendarDay,
  faUserCircle,
  faPlusCircle,
  faMinusCircle,
  faFileExcel,
  faFileAlt,
  faFileCsv,
  faSort,
  faSortUp,
  faSortDown,
  faToggleOff,
  faToggleOn,
  faHome,
  faDatabase,
  faPaperPlane,
  faFileImport,
  faFileExport,
  faFileDownload,
  faChevronDown,
  faChevronRight,
  faPlayCircle,
  faPauseCircle,
  faArrowCircleDown,
  faCopy
);
