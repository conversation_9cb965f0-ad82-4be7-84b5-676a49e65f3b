import {
  ApolloClient,
  ApolloLink,
  Observable,
  InMemoryCache,
} from '@apollo/client';
import { onError } from 'apollo-link-error';
import createUploadLink from 'apollo-upload-client/createUploadLink.mjs';
import { removeTypenameFromVariables } from '@apollo/client/link/remove-typename';

const uri =
  window.__RUNTIME_CONFIG__?.VITE_REACT_APP_API_URL ||
  import.meta.env.VITE_REACT_APP_API_URL;
const NODE_ENV =
  window.__RUNTIME_CONFIG__?.VITE_NODE_ENV || import.meta.env.VITE_NODE_ENV;
const ENABLE_OPERATION_LOGGING =
  window.__RUNTIME_CONFIG__?.VITE_ENABLE_OPERATION_LOGGING ||
  import.meta.env.VITE_ENABLE_OPERATION_LOGGING;

const removeTypenameLink = removeTypenameFromVariables();

const request = operation => {
  if (operation.name !== 'login' || operation.name !== 'validateToken') {
    if (NODE_ENV && ENABLE_OPERATION_LOGGING === 'true') {
      console.log(operation);
    }
    const pswebtoken = window.localStorage.getItem('pswebtoken');
    const psaccesstoken = window.localStorage.getItem('okta-token-storage');
    const headers = {};
    if (pswebtoken && psaccesstoken) {
      headers.pswebtoken = pswebtoken;
      headers.psaccesstoken = psaccesstoken;
    }
    operation.setContext({
      headers,
    });
  }
};

const requestLink = new ApolloLink(
  (operation, forward) =>
    new Observable(observer => {
      let handle;
      Promise.resolve(operation)
        .then(oper => request(oper))
        .then(() => {
          handle = forward(operation).subscribe({
            next: observer.next.bind(observer),
            error: observer.error.bind(observer),
            complete: observer.complete.bind(observer),
          });
        })
        .catch(observer.error.bind(observer));

      return () => {
        if (handle) handle.unsubscribe();
      };
    })
);

const link = ApolloLink.from([
  removeTypenameLink,
  onError(({ graphQLErrors }) => {
    if (NODE_ENV === 'development') {
      if (graphQLErrors)
        graphQLErrors.forEach(({ message, locations, path }) => {
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          );
        });
    }
  }),
  requestLink,
  createUploadLink({
    uri,
  }),
]);

const keyBasedPagination = (keyArgs = false) => ({
  // use `filter` so that changing filters creates a new cache entry.
  keyArgs: ['filter'],

  merge(existing = { filteredData: [], lastKey: null }, incoming) {
    if (!incoming) return existing;

    const existingData = existing.filteredData || [];
    const incomingData = incoming.filteredData || [];

    return {
      lastKey: incoming.lastKey,
      filteredData: [...existingData, ...incomingData],
    };
  },
});

const cache = new InMemoryCache({
  typePolicies: {
    Query: {
      fields: {
        transactionLogs: keyBasedPagination(),
      },
    },
  },
});

const client = new ApolloClient({
  link,
  cache,
});

export default client;
