import addMonths from 'date-fns/add_months';
import endOfDay from 'date-fns/end_of_day';
import startOfDay from 'date-fns/start_of_day';
import { useState } from 'react';

const months = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
];

function useDateRange(monthOffset, monthsOnly = false, endDay = false) {
  const initialStartDate = startOfDay(addMonths(new Date(), -monthOffset));
  const initialEndDate = endDay ? endOfDay(new Date()) : startOfDay(new Date());

  const initialStart = monthsOnly
    ? months[initialStartDate.getMonth()]
    : initialStartDate;

  const initialEnd = monthsOnly
    ? months[initialEndDate.getMonth()]
    : initialEndDate;

  const [range, setRange] = useState({ start: initialStart, end: initialEnd });

  function validatedSetRange({ start, end }) {
    setRange({ start: start || initialStart, end: end || initialEnd });
  }

  return [range, validatedSetRange];
}

export default useDateRange;
