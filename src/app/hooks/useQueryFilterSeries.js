import { useEffect, useState } from 'react';
import { useQuery } from '@apollo/client';

function sortObject(unordered) {
  const ordered = {};
  Object.keys(unordered)
    .sort()
    .forEach(function (key) {
      ordered[key] = unordered[key];
    });
  return ordered;
}

function useQueryFilterSeries(query, path, values, extPath, hasLastKey = true) {
  const initFilter = values.filter ? values.filter : {};
  const [filter, setFilter] = useState(initFilter);
  const [cache, setCache] = useState({});

  let queryFilter = { ...filter };
  if (values && values.filter) {
    queryFilter = { ...queryFilter, ...values.filter };
  }

  const { data: queryData, refetch } = useQuery(query, {
    variables: {
      filter: queryFilter,
      reportType: values.reportType,
    },
    fetchPolicy: 'network-only',
  });

  const key = JSON.stringify({
    filter: sortObject(queryFilter),
  });

  const extKey = JSON.stringify({
    filter: sortObject(queryFilter),
    extPath: extPath,
  });

  useEffect(() => {
    if (queryData && queryData[path]) {
      let newData = queryData[path].filteredData;
      let wasCacheSet = false;

      if (!cache[key]) {
        wasCacheSet = true;
        setCache({
          ...cache,
          [key]: newData,
        });
      } else if (
        // check if was already previously added
        JSON.stringify(queryData[path].filteredData) !==
        JSON.stringify(cache[key].slice(-queryData[path].filteredData.length))
      ) {
        wasCacheSet = true;
        newData = [...queryData[path].filteredData];
        setCache({
          [key]: newData,
        });
      } else if (
        queryData[path].filteredData.length === 0 &&
        queryData[path].lastKey
      ) {
        wasCacheSet = true;
        newData = [...queryData[path].filteredData];
        setCache({
          [key]: newData,
        });
      }

      if (extPath) {
        let newExtData = queryData[path][extPath];
        wasCacheSet = false;

        if (!cache[extKey]) {
          wasCacheSet = true;
          setCache({
            ...cache,
            [key]: newData,
            [extKey]: newExtData,
          });
        } else if (
          // check if was already previously added
          JSON.stringify(queryData[path][extPath]) !==
          JSON.stringify(cache[extKey].slice(-queryData[path][extPath].length))
        ) {
          wasCacheSet = true;
          newExtData = [...queryData[path][extPath]];
          setCache({
            ...cache,
            [key]: newData,
            [extKey]: newExtData,
          });
        } else if (
          queryData[path][extPath].length === 0 &&
          queryData[path].lastKey
        ) {
          // eslint-disable-next-line no-unused-vars
          wasCacheSet = true;
          newExtData = [...queryData[path][extPath]];
          setCache({
            ...cache,
            [key]: newData,
            [extKey]: newExtData,
          });
        }
      }
    }
  }, [JSON.stringify(queryData), JSON.stringify(cache)]);

  const data = (cache[key] && cache[key]) || [];
  const extData = (cache[extKey] && cache[extKey]) || [];

  const loading =
    !(queryData && queryData[path]) ||
    (queryData[path].lastKey !== null && hasLastKey);

  function clearCache() {
    setCache({});
    refetch();
  }

  return {
    data,
    extData,
    setFilter,
    filter,
    loading,
    clearCache,
  };
}

export default useQueryFilterSeries;
