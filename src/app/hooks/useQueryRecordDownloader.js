import { useEffect, useState, useContext } from 'react';
import { useQuery } from '@apollo/client';
import NotificationContext from '../context/NotificationContext';

function useQueryRecordDownloader({ query, path, variables }, notifId) {
  const [pagination, setNewPagination] = useState(variables.pagination);
  const [isFetching, setIsFetching] = useState(true);
  const [consolidatedData, setConsolidatedData] = useState([]);
  const { error, data } = useQuery(query, {
    variables: {
      ...variables,
      pagination,
    },
    fetchPolicy: 'network-only',
  });

  const { getProgressDataById, updateNotifProgress, changeNotifType } =
    useContext(NotificationContext);
  let progressData = getProgressDataById(notifId);

  useEffect(() => {
    if (data?.[path] && isFetching) {
      const cursorIndex = data[path].cursors.indexOf(pagination.start);
      if (cursorIndex < data[path].cursors.length) {
        setConsolidatedData(consolidatedData.concat(data[path].filteredData));
      }

      if (cursorIndex + 1 === data[path].cursors.length) {
        updateNotifProgress(notifId, { ...progressData, progress: 1 });
      } else {
        setNewPagination({
          ...pagination,
          start: data[path].cursors[cursorIndex + 1],
        });
      }
    }
  }, [JSON.stringify(data), JSON.stringify(pagination), isFetching]);

  useEffect(() => {
    if (progressData.progress === 1) {
      changeNotifType(notifId, 'success');
    }
  }, [progressData]);

  useEffect(() => {
    if (error) {
      setIsFetching(false);
      changeNotifType(notifId, 'error');
    }
  }, [JSON.stringify(error)]);

  return {
    progress: progressData.progress,
    consolidatedData,
    isFetching,
    setIsFetching,
  };
}

export default useQueryRecordDownloader;
