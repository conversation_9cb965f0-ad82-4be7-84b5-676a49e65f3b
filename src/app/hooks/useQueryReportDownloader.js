import { useEffect, useState, useContext } from 'react';
import { useQuery } from '@apollo/client';
import NotificationContext from '../context/NotificationContext';

function useQueryReportDownloader(
  { query, path, variables, hasLastKey },
  notifId
) {
  const [pagination, setNewPagination] = useState(variables.pagination);
  const [isFetching, setIsFetching] = useState(true);
  const [consolidatedData, setConsolidatedData] = useState([]);
  const { error, data } = useQuery(query, {
    variables: {
      ...variables,
      pagination,
    },
    fetchPolicy: 'network-only',
  });

  const { getProgressDataById, updateNotifProgress, changeNotifType } =
    useContext(NotificationContext);
  let progressData = getProgressDataById(notifId);

  useEffect(() => {
    if (data && data[path] && isFetching) {
      if (
        !hasLastKey ||
        data[path].lastKey ||
        data[path].lastKey !== pagination.startKey
      ) {
        setConsolidatedData(consolidatedData.concat(data[path].filteredData));
      }

      if (!data[path].lastKey) {
        updateNotifProgress(notifId, { ...progressData, progress: 1 });
      } else {
        setNewPagination({ ...pagination, startKey: data[path].lastKey });
      }
    }
  }, [JSON.stringify(data), JSON.stringify(pagination), isFetching]);

  useEffect(() => {
    if (progressData.progress === 1) {
      changeNotifType(notifId, 'success');
    }
  }, [progressData]);

  useEffect(() => {
    if (error) {
      setIsFetching(false);
      changeNotifType(notifId, 'error');
    }
  }, [JSON.stringify(error)]);

  return {
    progress: progressData.progress,
    consolidatedData,
    isFetching,
    setIsFetching,
  };
}

export default useQueryReportDownloader;
