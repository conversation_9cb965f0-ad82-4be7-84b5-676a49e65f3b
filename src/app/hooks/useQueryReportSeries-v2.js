import { useState, useEffect } from 'react';
import { useQuery } from '@apollo/client';

function useQueryReportSeries(query, path, values) {
  const [filter, setFilter] = useState({});
  const [pagination, setNewPagination] = useState(values.pagination);
  const [page, setPage] = useState(1);

  let queryFilter = { ...filter };
  if (values && values.filter) {
    queryFilter = { ...queryFilter, ...values.filter };
  }

  const {
    data: queryData,
    error,
    refetch,
    fetchMore,
    loading: apolloLoading,
  } = useQuery(query, {
    variables: {
      pagination: {
        limit: pagination.limit,
        startKey: pagination.startKey,
      },
      filter: queryFilter,
    },
    fetchPolicy: 'cache-and-network',
    notifyOnNetworkStatusChange: true,
  });

  // Apollo's cache handles all the data merging automatically
  const queryResult = queryData?.[path];
  const allData = queryResult?.filteredData || [];

  function loadData() {
    refetch();
  }

  function loadDataOnError() {
    refetch();
  }

  // Reset to page 1 when filter or limit changes
  useEffect(() => {
    setPage(1);
    setNewPagination({
      ...pagination,
      startKey: values.pagination.startKey,
    });
  }, [filter, pagination.limit]);

  // Auto-fetch more data when navigating to pages that need it
  useEffect(() => {
    if (
      allData.length < page * pagination.limit &&
      queryResult?.lastKey &&
      !apolloLoading
    ) {
      fetchMore({
        variables: {
          pagination: {
            limit: pagination.limit,
            startKey: queryResult.lastKey,
          },
        },
      });
    }
  }, [
    allData.length,
    page,
    pagination.limit,
    queryResult?.lastKey,
    apolloLoading,
    fetchMore,
  ]);

  // Calculate current page data from Apollo's cached data
  const startIndex = (page - 1) * pagination.limit;
  const endIndex = startIndex + pagination.limit;
  const currentPageData = allData.slice(startIndex, endIndex);

  // Transform data here to flatten some info on settlementBreakdown
  const data = currentPageData;
  // const data =
  //   (path === 'reports' ||
  //     path === 'billingReports' ||
  //     path === 'installmentReport' ||
  //     path === 'transactionLogs') &&
  //   currentPageData.length > 0
  //     ? currentPageData.flatMap(transaction => {
  //         let oona = 0;
  //         let budgetProtect = 0;
  //         if (
  //           transaction.settlementBreakdown &&
  //           transaction.settlementBreakdown.length > 0
  //         ) {
  //           oona =
  //             transaction.settlementBreakdown.find(
  //               s => s.transactionType === 'O'
  //             )?.amountValue || 0;
  //           budgetProtect =
  //             transaction.settlementBreakdown.find(
  //               s => s.transactionType === 'S'
  //             )?.amountValue || 0;
  //         }
  //         return {
  //           ...transaction,
  //           oona,
  //           budgetProtect,
  //         };
  //       })
  //     : currentPageData;

  // Simple loading state based on Apollo's loading and data availability
  const loading = apolloLoading || !queryData;

  function clearCache() {
    setPage(1);
    setNewPagination(values.pagination);
    refetch();
  }

  const isLastPage = !queryResult?.lastKey && allData.length <= endIndex;

  return {
    refetch,
    data,
    setFilter,
    setNewPagination,
    pagination,
    filter,
    loading,
    page,
    setPage,
    error,
    isLastPage,
    clearCache,
    loadData,
    loadDataOnError,
  };
}

export default useQueryReportSeries;
