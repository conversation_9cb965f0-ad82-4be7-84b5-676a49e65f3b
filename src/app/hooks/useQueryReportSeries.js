import { useEffect, useState } from 'react';
import { useQuery } from '@apollo/client';

function sortObject(unordered) {
  const ordered = {};
  Object.keys(unordered)
    .sort()
    .forEach(function (key) {
      ordered[key] = unordered[key];
    });
  return ordered;
}

function useQueryReportSeries(query, path, values) {
  const [filter, setFilter] = useState({});
  const [pagination, setNewPagination] = useState(values.pagination);
  const [cache, setCache] = useState({});
  const [tempCache, setTempCache] = useState({});
  const [page, setPage] = useState(1);

  let queryFilter = { ...filter };
  if (values && values.filter) {
    queryFilter = { ...queryFilter, ...values.filter };
  }

  const {
    data: queryData,
    error,
    refetch,
  } = useQuery(query, {
    variables: {
      pagination: {
        limit: pagination.limit + 1,
        startKey: pagination.startKey,
      },
      filter: queryFilter,
    },
    fetchPolicy: 'network-only',
  });

  const key = JSON.stringify({
    filter: sortObject(filter),
    limit: pagination.limit,
  });

  function loadData() {
    setCache({});
  }

  function loadDataOnError() {
    setTempCache({ cache });
    setCache({ tempCache });
  }

  useEffect(() => {
    if (queryData && queryData[path]) {
      let newData = queryData[path].filteredData;
      let wasCacheSet = false;

      if (!cache[key]) {
        wasCacheSet = true;
        setCache({
          ...cache,
          [key]: newData,
        });
      } else if (
        cache[key].length < pagination.limit * page &&
        // check if was already previously added
        JSON.stringify(queryData[path].filteredData) !==
          JSON.stringify(cache[key].slice(-queryData[path].filteredData.length))
      ) {
        wasCacheSet = true;
        newData = [...cache[key], ...queryData[path].filteredData];
        setCache({
          ...cache,
          [key]: newData,
        });
      } else if (
        queryData[path].filteredData.length === 0 &&
        queryData[path].lastKey
      ) {
        wasCacheSet = true;
        newData = [...cache[key], ...queryData[path].filteredData];
        setCache({
          ...cache,
          [key]: newData,
        });
      }

      if (
        wasCacheSet &&
        newData.length < pagination.limit * page &&
        queryData[path].lastKey !== null
      ) {
        setNewPagination({
          ...pagination,
          startKey: queryData[path].lastKey,
        });
      }
    }
  }, [JSON.stringify(queryData), JSON.stringify(cache)]);

  useEffect(() => {
    setNewPagination({
      ...pagination,
      startKey: values.pagination.startKey,
    });
    setPage(1);
  }, [filter, pagination.limit]);

  const cachedData =
    (cache[key] &&
      cache[key].slice(
        (page - 1) * pagination.limit,
        page * pagination.limit
      )) ||
    [];

  // Transform data to flatten settlementBreakdown if needed
  const data =
    path === 'reports' &&
    cachedData.length > 0 &&
    cachedData[0].settlementBreakdown
      ? cachedData.flatMap(transaction => {
          if (
            transaction.settlementBreakdown &&
            transaction.settlementBreakdown.length > 0
          ) {
            return transaction.settlementBreakdown.map(settlement => ({
              ...transaction,
              settlementAccountNumber: settlement.accountNumber,
              settlementAccountType: settlement.accountType,
              settlementAmountValue: settlement.amountValue,
              settlementBrand: settlement.brand || '-',
              settlementEmailAddress: settlement.emailAddress || '-',
              settlementMobileNumber: settlement.mobileNumber || '-',
              settlementTransactionType: settlement.transactionType || '-',
              // Keep the original array for reference if needed
              // settlementBreakdown: undefined
            }));
          }
          return transaction;
        })
      : cachedData;

  const loading =
    !(queryData && queryData[path]) ||
    (queryData[path].lastKey !== null && cachedData.length < pagination.limit);

  function clearCache() {
    setCache({});
    if (page !== 1) setPage(1);
    if (JSON.stringify(values.pagination) !== JSON.stringify(pagination))
      setNewPagination(values.pagination);
    refetch();
  }

  return {
    refetch,
    data,
    setFilter,
    setNewPagination,
    pagination,
    filter,
    loading,
    page,
    setPage,
    error,
    isLastPage:
      !(queryData && queryData[path] && queryData[path].lastKey) &&
      !(
        !!cache[key] &&
        !!cache[key].slice(
          page * pagination.limit,
          (page + 1) * pagination.limit
        ).length
      ),
    clearCache,
    loadData,
    loadDataOnError,
  };
}

export default useQueryReportSeries;
