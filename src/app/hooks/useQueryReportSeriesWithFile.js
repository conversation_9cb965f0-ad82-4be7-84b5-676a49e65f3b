import { useEffect, useState } from 'react';
import { useQuery } from '@apollo/client';

function sortObject(unordered) {
  const ordered = {};
  Object.keys(unordered)
    .sort()
    .forEach(function (key) {
      ordered[key] = unordered[key];
    });
  return ordered;
}

function useQueryReportSeriesWithFile(query, path, values) {
  const [filter, setFilter] = useState({});
  const [pagination, setNewPagination] = useState(values.pagination);
  const [cache, setCache] = useState({});
  const [tempCache, setTempCache] = useState({});
  const [page, setPage] = useState(1);
  const [file, setFile] = useState(null);

  let queryFilter = { ...filter };
  if (values && values.filter) {
    queryFilter = { ...queryFilter, ...values.filter };
  }

  const {
    data: queryData,
    error,
    refetch,
  } = useQuery(query, {
    variables: {
      file: file,
      pagination: {
        limit: pagination.limit + 1,
        startKeys: pagination.startKeys,
      },
      filter: queryFilter,
    },
    fetchPolicy: 'network-only',
    skip: !file,
  });

  const key = JSON.stringify({
    file: file,
    filter: sortObject(filter),
    limit: pagination.limit,
  });

  function loadData() {
    setCache({});
  }

  function loadDataOnError() {
    setTempCache({ cache });
    setCache({ tempCache });
  }

  useEffect(() => {
    if (queryData && queryData[path]) {
      let newData = queryData[path].filteredData;
      let wasCacheSet = false;

      if (!cache[key]) {
        wasCacheSet = true;
        setCache({
          ...cache,
          [key]: newData,
        });
      } else if (
        cache[key].length < pagination.limit * page &&
        // check if was already previously added
        JSON.stringify(queryData[path].filteredData) !==
          JSON.stringify(cache[key].slice(-queryData[path].filteredData.length))
      ) {
        wasCacheSet = true;
        newData = [...cache[key], ...queryData[path].filteredData];
        setCache({
          ...cache,
          [key]: newData,
        });
      } else if (
        queryData[path].filteredData.length === 0 &&
        queryData[path].lastKey
      ) {
        wasCacheSet = true;
        newData = [...cache[key], ...queryData[path].filteredData];
        setCache({
          ...cache,
          [key]: newData,
        });
      }

      if (
        wasCacheSet &&
        newData.length < pagination.limit * page &&
        queryData[path].lastKey !== null
      ) {
        setNewPagination({
          ...pagination,
          startKeys: queryData[path].lastKey,
        });
      }
    }
  }, [JSON.stringify(queryData), JSON.stringify(cache)]);

  useEffect(() => {
    setNewPagination({
      ...pagination,
      startKeys: values.pagination.startKeys,
    });
    setPage(1);
  }, [filter, pagination.limit]);

  const data =
    (cache[key] &&
      cache[key].slice(
        (page - 1) * pagination.limit,
        page * pagination.limit
      )) ||
    [];

  useEffect(() => {
    if (
      page !== 1 &&
      data.length < pagination.limit &&
      queryData[path] &&
      queryData[path].lastKey !== null
    ) {
      setNewPagination({
        ...pagination,
        startKeys: queryData[path].lastKey,
      });
    }
  }, [page]);

  const loading =
    !(queryData && queryData[path]) ||
    (queryData[path].lastKey !== null && data.length < pagination.limit);

  function clearCache() {
    setCache({});
    if (page !== 1) setPage(1);
    if (JSON.stringify(values.pagination) !== JSON.stringify(pagination))
      setNewPagination(values.pagination);
    refetch();
  }

  return {
    refetch,
    data,
    setFile,
    file,
    setFilter,
    setNewPagination,
    pagination,
    filter,
    loading,
    page,
    setPage,
    error,
    isLastPage: !file
      ? false
      : !(queryData[path] && queryData[path].lastKey) &&
        !(
          !!cache[key] &&
          !!cache[key].slice(
            page * pagination.limit,
            (page + 1) * pagination.limit
          ).length
        ),
    clearCache,
    loadData,
    loadDataOnError,
  };
}

export default useQueryReportSeriesWithFile;
