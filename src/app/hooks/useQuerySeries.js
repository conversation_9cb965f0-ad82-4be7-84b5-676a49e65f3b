import { useEffect, useState } from 'react';
import { useQuery } from '@apollo/client';

function sortObject(unordered) {
  const ordered = {};
  Object.keys(unordered)
    .sort()
    .forEach(function (key) {
      ordered[key] = unordered[key];
    });
  return ordered;
}

function useQuerySeries(query, path, values) {
  const [filter, setFilter] = useState({});
  const [pagination, setPagination] = useState(values.pagination);
  const [cache, setCache] = useState({});
  const [page, setPage] = useState(1);

  let queryFilter = { ...filter };
  if (values && values.filter) {
    queryFilter = { ...queryFilter, ...values.filter };
  }

  const { data: queryData, refetch } = useQuery(query, {
    variables: {
      pagination,
      filter: queryFilter,
    },
    fetchPolicy: 'network-only',
  });

  const key = JSON.stringify({
    filter: sortObject(filter),
    limit: pagination.limit,
  });

  function loadData() {
    setCache({});
  }

  useEffect(() => {
    if (queryData && queryData[path]) {
      let newData = queryData[path].filteredData;
      let wasCacheSet = false;

      if (!cache[key]) {
        wasCacheSet = true;
        setCache({
          ...cache,
          [key]: newData,
        });
      } else if (
        cache[key].length < pagination.limit * page &&
        // check if was already previously added
        JSON.stringify(queryData[path].filteredData) !==
          JSON.stringify(cache[key].slice(-queryData[path].filteredData.length))
      ) {
        wasCacheSet = true;
        newData = [...cache[key], ...queryData[path].filteredData];
        setCache({
          ...cache,
          [key]: newData,
        });
      } else if (
        queryData[path].filteredData.length === 0 &&
        queryData[path].lastKey
      ) {
        wasCacheSet = true;
        newData = [...queryData[path].filteredData];
        setCache({
          [key]: newData,
        });
      }

      if (
        wasCacheSet &&
        newData.length < pagination.limit * page &&
        queryData[path].lastKey !== null
      ) {
        setPagination({
          ...pagination,
          start: queryData[path].lastKey,
        });
      }
    }
  }, [JSON.stringify(queryData), JSON.stringify(cache)]);

  useEffect(() => {
    setPagination({
      ...pagination,
      start: values.pagination.start,
    });
    setPage(1);
  }, [filter, pagination.limit]);

  const data =
    (cache[key] &&
      cache[key].slice(
        (page - 1) * pagination.limit,
        page * pagination.limit
      )) ||
    [];

  useEffect(() => {
    if (
      page !== 1 &&
      data.length < pagination.limit &&
      queryData[path] &&
      queryData[path].lastKey !== null
    ) {
      setPagination({
        ...pagination,
        start: queryData[path].lastKey,
      });
    }
  }, [page]);

  const loading =
    !(queryData && queryData[path]) ||
    (queryData[path] &&
      queryData[path].lastKey &&
      data.length < pagination.limit);

  function clearCache() {
    setCache({});
    if (page !== 1) setPage(1);
    if (JSON.stringify(values.pagination) !== JSON.stringify(pagination))
      setPagination(values.pagination);
    refetch();
  }

  return {
    data,
    setFilter,
    setPagination,
    pagination,
    filter,
    loading,
    page,
    setPage,
    isLastPage:
      !(queryData && queryData[path] && queryData[path].lastKey) &&
      !(
        !!cache[key] &&
        !!cache[key].slice(
          page * pagination.limit,
          (page + 1) * pagination.limit
        ).length
      ),
    clearCache,
    loadData,
  };
}

export default useQuerySeries;
