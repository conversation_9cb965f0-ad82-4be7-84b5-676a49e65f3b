import 'normalize.css';
import PropTypes from 'prop-types';
import React from 'react';
import { ApolloProvider } from '@apollo/client';
import { withA<PERSON>lo } from '@apollo/client/react/hoc';
import { ApolloProvider as ApolloHooksProvider } from '@apollo/client';
import 'react-datepicker/dist/react-datepicker.css';
import { createRoot } from 'react-dom/client';
import { ThemeProvider, StyleSheetManager } from 'styled-components';
import { unregister } from '../registerServiceWorker';
import App from './App';
import { AuthProvider } from './context/AuthContext';
import './fontAwesomeIcons';
import GlobalStyle from './GlobalStyle';
import client from './graphqlClient';
import theme from './theme';
import isPropValid from '@emotion/is-prop-valid';

// NEW: Okta imports
import { Security } from '@okta/okta-react';
import { OktaAuth, toRelativeUrl } from '@okta/okta-auth-js';

const APP_OKTA_ISSUER =
  window.__RUNTIME_CONFIG__?.VITE_REACT_APP_OKTA_ISSUER ||
  import.meta.env.VITE_REACT_APP_OKTA_ISSUER;
const APP_OKTA_CLIENT_ID =
  window.__RUNTIME_CONFIG__?.VITE_REACT_APP_OKTA_CLIENT_ID ||
  import.meta.env.VITE_REACT_APP_OKTA_CLIENT_ID;

const oktaConfig = {
  issuer: APP_OKTA_ISSUER,
  clientId: APP_OKTA_CLIENT_ID,
  redirectUri: window.location.origin + '/callback',
  scopes: ['openid', 'profile', 'email'],
  pkce: true,
};

const oktaAuth = new OktaAuth(oktaConfig);

const restoreOriginalUri = async (_oktaAuth, originalUri) => {
  window.location.replace(toRelativeUrl(originalUri || '', window.location.origin));
};

function ApolloHooksProviderWrapper({ client, children }) {
  return <ApolloHooksProvider client={client}>{children}</ApolloHooksProvider>;
}

ApolloHooksProviderWrapper.propTypes = {
  client: PropTypes.any,
  children: PropTypes.any,
};

const EnhancedApolloHooksProvider = withApollo(ApolloHooksProviderWrapper);

// This implements the default behavior from styled-components v5
function shouldForwardProp(propName, target) {
  if (typeof target === 'string') {
    // For HTML elements, forward the prop if it is a valid HTML attribute
    return isPropValid(propName);
  }
  // For other elements, forward all props
  return true;
}

createRoot(document.getElementById('root')).render(
  <ApolloProvider client={client}>
    <EnhancedApolloHooksProvider>
      <Security oktaAuth={oktaAuth} restoreOriginalUri={restoreOriginalUri}>
        <StyleSheetManager shouldForwardProp={shouldForwardProp}>
          <ThemeProvider theme={theme}>
            <AuthProvider>
              <>
                <GlobalStyle />
                <App />
              </>
            </AuthProvider>
          </ThemeProvider>
        </StyleSheetManager>
      </Security>
    </EnhancedApolloHooksProvider>
  </ApolloProvider>
);
// registerServiceWorker();
unregister();
