import PropTypes from 'prop-types';
import React, { useContext, useState } from 'react';
import globeLogo from '../../assets/globe-logo.png';
import Row from '../../components/Row';
import AuthContext from '../../context/AuthContext/AuthContext';
import ResponsiveContext from '../../context/ResponsiveContext';
import {
  BrandContainer,
  Description,
  HomeButton,
  LogoutButton,
  LogoutButtonContainer,
  Page404Container,
  PageColumn,
  PageSubTitle,
  PageTitle,
  SubDescription,
  TextContainer,
} from './styled';

const Page404 = ({ history }) => {
  const { isMobile } = useContext(ResponsiveContext);
  const { authUser, logout } = useContext(AuthContext);
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const Layout = isMobile ? PageColumn : Row;

  return (
    <Page404Container>
      <BrandContainer>
        <img src={globeLogo} alt="Globe Logo" />
        <p>PAYMENT SERVICE</p>
      </BrandContainer>

      {!isMobile && authUser && (
        <LogoutButtonContainer>
          <LogoutButton
            loading={isLoggingOut}
            onClick={() => {
              setIsLoggingOut(true);
              logout();
              history.push('/login');
            }}
            icon="sign-out-alt"
          >
            Logout
          </LogoutButton>
        </LogoutButtonContainer>
      )}
      <Layout>
        <PageColumn>
          <PageTitle>404</PageTitle>
          <PageSubTitle>PAGE NOT FOUND</PageSubTitle>
        </PageColumn>
        <TextContainer>
          <Description>Oops!</Description>
          <Description>Looks like you got lost!</Description>
          <SubDescription>
            {
              "It looks like you're trying to access a page that either has been deleted or never even existed."
            }
          </SubDescription>

          <HomeButton
            onClick={() => {
              history.push('/');
            }}
          >
            Take me Home
          </HomeButton>
          {isMobile && authUser && (
            <LogoutButton
              loading={isLoggingOut}
              onClick={() => {
                setIsLoggingOut(true);
                logout();
                history.push('/login');
              }}
            >
              Logout
            </LogoutButton>
          )}
        </TextContainer>
      </Layout>
    </Page404Container>
  );
};

Page404.propTypes = {
  history: PropTypes.object,
};

export default Page404;
