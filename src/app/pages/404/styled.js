import styled from 'styled-components';
import PrimaryButton from '../../components/Button/PrimaryButton';
import SecondaryButton from '../../components/Button/SecondaryButton';
import Column from '../../components/Column';

export const Page404Container = styled.div`
  display: flex;
  flex-direction: column;

  justify-content: center;
  align-items: center;

  width: 100vw;
  height: 100vh;
`;

export const BrandContainer = styled.div`
  position: absolute;
  text-align: center;
  top: 70px;

  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    position: initial;
  }

  img {
    height: 40px;
  }

  p {
    color: #244958;
    font-size: ${props => props.theme.fontSize.m};
    font-weight: 300;
    margin: 0;
    margin-top: 20px;
  }
`;

export const LogoutButtonContainer = styled.div`
  position: absolute;
  top: 20px;
  right: 20px;
`;

export const LogoutButton = styled(SecondaryButton)`
  color: rgba(51, 51, 51, 0.8);
`;

export const PageColumn = styled(Column)`
  flex-direction: column;
  align-items: center;

  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    flex: none;
  }
`;

export const TextContainer = styled(PageColumn)`
  align-items: flex-start;
  justify-content: center;
  margin-top: 0;

  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    align-items: center;
    margin-top: 20px;
  }
`;

export const PageTitle = styled.h1`
  color: #4a4a4a;
  font-weight: bold;
  font-size: 200px;
  line-height: 170px;
  margin: 0;

  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    font-size: 140px;
    line-height: 140px;
  }
`;

export const PageSubTitle = styled.h2`
  color: #333333;
  font-size: 30px;
  margin: 0;
  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    font-size: 25px;
  }
`;

export const Description = styled.div`
  font-size: 30px;
  color: #333333;
  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    font-size: 25px;
  }
`;

export const SubDescription = styled.div`
  font-size: 14px;
  margin: 20px 0;
  text-align: left;
  width: auto;

  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    text-align: center;
    width: 80%;
  }
`;

export const HomeButton = styled(PrimaryButton)`
  margin-bottom: 20px;
`;
