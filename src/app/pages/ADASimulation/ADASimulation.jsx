import PropTypes from 'prop-types';
import React, { useContext, useMemo, useState } from 'react';
import * as Yup from 'yup';
import useForm from '../../hooks/useForm';
import { useQuery } from '@apollo/client';
import Row from '../../components/Row';
import Page from '../../components/Page';
import Header from '../../components/Header';
import { AlertModal } from '../../components/Modal';
import DataHeader from '../../components/DataHeader';
import FormField from '../../components/Form/FormField';
import DataContainer from '../../components/DataContainer';
import { FIELD_TYPES } from '../../components/Form/constants';
import ResponsiveContext from '../../context/ResponsiveContext';
import PrimaryButton from '../../components/Button/PrimaryButton';
import {
  ButtonsContainer,
  PageSubsection,
  SubsectionTitle,
} from '../../components/InformationPage';
import { GET_CHANNEL_OPTIONS, GET_CHANNEL_INFORMATION } from './query';

export const shopperReferenceOptions = [
  { label: 'Any', value: null },
  {
    label: 'Total Amount Due',
    value: 'TAD',
  },
  {
    label: 'Current Charges',
    value: 'CC',
  },
  {
    label: 'Recurring Charges',
    value: 'RC',
  },
  {
    label: 'Total Balance',
    value: 'TB',
  },
  {
    label: ' Current Balance (MSF + usages)',
    value: 'CBMSF',
  },
  {
    label: 'Recurring balance (MSF)',
    value: 'RBMSF',
  },
];

const ADASimulation = ({ history }) => {
  const { isMobile } = useContext(ResponsiveContext);

  let api = import.meta.env.VITE_REACT_APP_PS_API_URL;

  const [apiUrl] = useState(`${api}/api/command`);

  const [state, setState] = useState({
    isEditing: true,
    loading: false,

    accessToken: null,
    payload: {},

    responseError: null,
    otherError: null,
  });

  const [isConfirm, setIsConfirm] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [isFailure, setIsFailure] = useState(false);
  const [theMessage, setTheMessage] = useState(null);
  const [paymentId, setPaymentId] = useState(null);

  const { data: channelData, loading: isLoadingChannels } = useQuery(
    GET_CHANNEL_OPTIONS,
    {
      fetchPolicy: 'network-only',
    }
  );

  const adaTypeOptions = [
    { label: 'Any', value: null },
    { label: 'Enrollment', value: 'ENROLLMENT' },
    { label: 'Pre-Auth', value: 'PREAUTH' },
    { label: 'Modification', value: 'MODIFICATION' },
    { label: 'Cancellation', value: 'CANCELLATION' },
  ];

  const channelOptions =
    !isLoadingChannels && channelData
      ? channelData.channelsLoose.map(channel => ({
          value: channel.id,
          label: channel.name,
        }))
      : [];

  channelOptions.unshift({
    value: null,
    label: 'Any',
  });

  // eslint-disable-next-line
  const { fields, initialValue } = useMemo(() => {
    const fields = {
      // AccessToken
      adaType: {},
      //For Enrollment and Pre Auth
      customerName: {
        validation: value =>
          value &&
          (value.adaType === adaTypeOptions[1].value ||
            value.adaType === adaTypeOptions[2].value)
            ? Yup.string().required('Please enter a value')
            : Yup.string().nullable(),
      },
      customerEmail: {
        validation: value =>
          value &&
          (value.adaType === adaTypeOptions[1].value ||
            value.adaType === adaTypeOptions[2].value)
            ? Yup.string().required('Please enter a value')
            : Yup.string().nullable(),
      },
      customerContactNumber: {
        validation: value =>
          value &&
          (value.adaType === adaTypeOptions[1].value ||
            value.adaType === adaTypeOptions[2].value)
            ? Yup.string().required('Please enter a value')
            : Yup.string().nullable(),
      },
      shopperReference: {
        validation: value =>
          value &&
          (value.adaType === adaTypeOptions[1].value ||
            value.adaType === adaTypeOptions[2].value)
            ? Yup.string().required('Please enter a value')
            : Yup.string().nullable(),
      },
      accountNumber: {
        validation: Yup.string().required('Please enter a value'),
      },
      mobileNumber: {
        validation: value =>
          value &&
          (value.adaType === adaTypeOptions[1].value ||
            value.adaType === adaTypeOptions[2].value)
            ? Yup.string().required('Please enter a value')
            : Yup.string().nullable(),
      },
      emailAddress: {
        validation: value =>
          value &&
          (value.adaType === adaTypeOptions[1].value ||
            value.adaType === adaTypeOptions[2].value)
            ? Yup.string().required('Please enter a value')
            : Yup.string().nullable(),
      },
      amountValue: {
        validation: value =>
          value &&
          (value.adaType === adaTypeOptions[1].value ||
            value.adaType === adaTypeOptions[2].value)
            ? Yup.string().required('Please enter a value')
            : Yup.string().nullable(),
      },
      //Modification and Cancellation
      channelId: {},
      //---> Account Number is Included
      tokenId: {
        validation: value =>
          value &&
          (value.adaType === adaTypeOptions[3].value ||
            value.adaType === adaTypeOptions[4].value)
            ? Yup.string().required('Please enter a value')
            : Yup.string().nullable(),
      },
      actionType: {
        validation: value =>
          value && value.adaType === adaTypeOptions[4].value
            ? Yup.string().required('Please enter a value')
            : Yup.string().nullable(),
      },
    };

    const initialValue = {};

    return { fields, initialValue };
  });

  const { values, onChange, onBlur, errors, onSubmit } = useForm(
    fields,
    values => {
      setIsConfirm(true);
      setState({
        ...state,
        payload:
          ((values.adaType === 'ENROLLMENT' ||
            values.adaType === 'PREAUTH') && {
            gatewayProcessor: 'generic',
            paymentInfo: {
              productDescription: 'Iphone XS',
              customerName: values.customerName,
              customerEmail: values.customerEmail,
              customerContactNumber: values.customerContactNumber,
              countryCode: 'PH',
              currency: 'PHP',
              platform: 'web',
              lang: 'UTF-8',
              responseURL: 'https://ups-fiona.free.beeceptor.com',
              browserInfo: {
                acceptHeader: 'application/json',
                userAgent: 'Mozilla',
              },
              shopperLocale: 'en-US',
              paymentMethod: 'card',
              remark: values.adaType === 'ENROLLMENT' ? 'ADA' : 'BC',
              saveTransactionDetails: true,
              shopperReference: values.shopperReference,
            },
            settlementInfos: [
              {
                accountNumber: values.accountNumber,
                mobileNumber: values.mobileNumber,
                emailAddress: values.emailAddress,
                amountValue: values.amountValue,
                transactionType: 'G',
              },
            ],
          }) ||
          (values.adaType === 'MODIFICATION' && {
            channelId: data.channel.clientId,
            accountNumber: values.accountNumber,
            tokenId: values.tokenId,
            redirectUrl: 'https://marjun-test.free.beeceptor.com',
          }) ||
          (values.adaType === 'CANCELLATION' && {
            channelId: data.channel.clientId,
            accountNumber: values.accountNumber,
            tokenId: values.tokenId,
            redirectUrl: 'https://marjun-test.free.beeceptor.com',
            actionType: 'CANCEL',
          }),
      });
    }
  );

  const { data, loading } = useQuery(GET_CHANNEL_INFORMATION, {
    variables: { where: { id: values.channelId } },
    skip: !values.channelId,
    fetchPolicy: 'network-only',
  });

  function GenerateAccessToken() {
    try {
      const clientSecret = encodeURIComponent(data.channel.clientSecret);
      fetch(
        `${api}/auth/api/v1/accesstoken?clientId=${data.channel.clientId}&clientSecret=${clientSecret}`
      )
        .then(response => response.json())
        .then(data =>
          setState({ ...state, accessToken: data.results.accessToken })
        )
        .catch(err => {
          setState({ ...state, accessToken: null });
          console.log('Error', err);
        });
    } catch (error) {
      console.log('Error:', error);
    }
  }

  function GetResponse() {
    if (
      state.accessToken &&
      (values.adaType === 'ENROLLMENT' || values.adaType === 'PREAUTH')
    ) {
      fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Request-Method': 'POST',
          Authorization: 'Bearer ' + state.accessToken,
        },
        body: JSON.stringify({
          command: {
            name: 'CreatePaymentSession',
            payload: state.payload,
          },
        }),
      })
        .then(result => result.json())
        .then(response => {
          setIsConfirm(false);
          setIsSuccess(response.error ? false : true);
          setIsFailure(response.error ? true : false);
          setTheMessage(response.message);
          setPaymentId(!response.error ? response.data.paymentId : paymentId);
          setState({
            ...state,
            responseError: response.error.details[0].message,
            otherError: response.error.details,
          });
        })
        .catch(error => {
          setState({ ...state, responseError: error });
          console.log('ERROR: ' + error);
        });
    }

    if (
      state.accessToken &&
      (values.adaType === 'CANCELLATION' || values.adaType === 'MODIFICATION')
    ) {
      fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Request-Method': 'POST',
          Authorization: 'Bearer ' + state.accessToken,
        },
        body: JSON.stringify({
          command: {
            name: 'AdaUpdateToken',
            payload: state.payload,
          },
        }),
      })
        .then(result => result.json())
        .then(response => {
          setIsConfirm(false);
          setIsSuccess(response.error ? false : true);
          setIsFailure(response.error ? true : false);
          setTheMessage(response.message);
          setState({
            ...state,
            responseError: response.error.details[0].message,
            otherError: response.error.details,
          });
        })
        .catch(error => {
          setState({ ...state, responseError: error });
          console.log('ERROR: ' + error);
        });
    }
  }

  const ButtonGroup = (
    <Row>
      <PrimaryButton
        icon="save"
        disabled={
          !state.isEditing ||
          (!values.adaType && !values.channelId) ||
          (values.adaType && !values.channelId) ||
          (!values.adaType && values.channelId)
        }
        onClick={() => {
          onSubmit();
        }}
      >
        Simulate
      </PrimaryButton>
    </Row>
  );

  const GenerateToken = (
    <center>
      <PrimaryButton
        disabled={
          !state.isEditing ||
          (!values.adaType && !values.channelId) ||
          (values.adaType && !values.channelId) ||
          (!values.adaType && values.channelId) ||
          loading
        }
        onClick={() => {
          GenerateAccessToken();
        }}
      >
        Generate AccessToken
      </PrimaryButton>
    </center>
  );

  return (
    <>
      <Page>
        <Header withHome title={'ADA Simulation'} path={['ADA Simulation']} />
        <DataContainer>
          <>
            <DataHeader>
              <DataHeader.Title>ADA Simulation</DataHeader.Title>
            </DataHeader>
            <PageSubsection>
              <FormField
                label="ADA Simulation Type"
                name="adaType"
                type={FIELD_TYPES.SELECT}
                options={adaTypeOptions}
                value={values.adaType}
                onChange={onChange.adaType}
                onBlur={onBlur.adaType}
                error={errors.adaType}
                readOnly={!state.isEditing}
                perRow={2}
                required
              />
              <FormField
                label="Channel"
                name="channelId"
                type={FIELD_TYPES.SELECT}
                options={channelOptions}
                value={values.channelId}
                onChange={onChange.channelId}
                onBlur={onBlur.channelId}
                error={errors.channelId}
                readOnly={!state.isEditing}
                perRow={2}
                required
              />
              <FormField
                label="Access Token"
                name="accessToken"
                type={FIELD_TYPES.TEXT}
                value={state.accessToken}
                onChange={onChange.accessToken}
                onBlur={onBlur.accessToken}
                error={errors.accessToken}
                readOnly={!state.isEditing}
                perRow={2}
                required
              />
            </PageSubsection>
            {GenerateToken}
            {values.adaType && values.channelId && state.accessToken && (
              <SubsectionTitle>{values.adaType}</SubsectionTitle>
            )}
            {(values.adaType === 'ENROLLMENT' ||
              values.adaType === 'PREAUTH') &&
              values.channelId &&
              state.accessToken && (
                <PageSubsection>
                  <FormField
                    label="Customer Name"
                    name="customerName"
                    type={FIELD_TYPES.TEXT}
                    value={values.customerName}
                    onChange={onChange.customerName}
                    onBlur={onBlur.customerName}
                    error={errors.customerName}
                    readOnly={!state.isEditing}
                    perRow={2}
                    required
                  />

                  <FormField
                    label="Customer Email"
                    name="customerEmail"
                    type={FIELD_TYPES.TEXT}
                    value={values.customerEmail}
                    onChange={onChange.customerEmail}
                    onBlur={onBlur.customerEmail}
                    error={errors.customerEmail}
                    readOnly={!state.isEditing}
                    perRow={2}
                    required
                  />

                  <FormField
                    label="Customer Contact Number"
                    name="customerContactNumber"
                    type={FIELD_TYPES.TEXT}
                    value={values.customerContactNumber}
                    onChange={onChange.customerContactNumber}
                    onBlur={onBlur.customerContactNumber}
                    error={errors.customerContactNumber}
                    readOnly={!state.isEditing}
                    perRow={2}
                    required
                  />

                  <FormField
                    label="Charges Type"
                    name="shopperReference"
                    type={FIELD_TYPES.SELECT}
                    options={shopperReferenceOptions}
                    value={values.shopperReference}
                    onChange={onChange.shopperReference}
                    onBlur={onBlur.shopperReference}
                    error={errors.shopperReference}
                    readOnly={!state.isEditing}
                    perRow={2}
                    required
                  />

                  <FormField
                    label="Account Number"
                    name="accountNumber"
                    type={FIELD_TYPES.TEXT}
                    value={values.accountNumber}
                    onChange={onChange.accountNumber}
                    onBlur={onBlur.accountNumber}
                    error={errors.accountNumber}
                    readOnly={!state.isEditing}
                    perRow={2}
                    required
                  />

                  <FormField
                    label="Mobile Number"
                    name="mobileNumber"
                    type={FIELD_TYPES.TEXT}
                    value={values.mobileNumber}
                    onChange={onChange.mobileNumber}
                    onBlur={onBlur.mobileNumber}
                    error={errors.mobileNumber}
                    readOnly={!state.isEditing}
                    perRow={2}
                    required
                  />

                  <FormField
                    label="Amount Value"
                    name="amountValue"
                    type={FIELD_TYPES.TEXT}
                    value={values.amountValue}
                    onChange={onChange.amountValue}
                    onBlur={onBlur.amountValue}
                    error={errors.amountValue}
                    readOnly={!state.isEditing}
                    perRow={2}
                    required
                  />

                  <FormField
                    label="Email Address"
                    name="emailAddress"
                    type={FIELD_TYPES.TEXT}
                    value={values.emailAddress}
                    onChange={onChange.emailAddress}
                    onBlur={onBlur.emailAddress}
                    error={errors.emailAddress}
                    readOnly={!state.isEditing}
                    perRow={2}
                    required
                  />
                </PageSubsection>
              )}
            {values.adaType === 'MODIFICATION' &&
              values.channelId &&
              state.accessToken && (
                <PageSubsection>
                  <FormField
                    label="Account Number"
                    name="accountNumber"
                    type={FIELD_TYPES.TEXT}
                    value={values.accountNumber}
                    onChange={onChange.accountNumber}
                    onBlur={onBlur.accountNumber}
                    error={errors.accountNumber}
                    readOnly={!state.isEditing}
                    perRow={2}
                    required
                  />

                  <FormField
                    label="Token ID"
                    name="tokenId"
                    type={FIELD_TYPES.TEXT}
                    value={values.tokenId}
                    onChange={onChange.tokenId}
                    onBlur={onBlur.tokenId}
                    error={errors.tokenId}
                    readOnly={!state.isEditing}
                    perRow={2}
                    required
                  />
                </PageSubsection>
              )}
            {values.adaType === 'CANCELLATION' &&
              values.channelId &&
              state.accessToken && (
                <PageSubsection>
                  <FormField
                    label="Account Number"
                    name="accountNumber"
                    type={FIELD_TYPES.TEXT}
                    value={values.accountNumber}
                    onChange={onChange.accountNumber}
                    onBlur={onBlur.accountNumber}
                    error={errors.accountNumber}
                    readOnly={!state.isEditing}
                    perRow={2}
                    required
                  />

                  <FormField
                    label="Token ID"
                    name="tokenId"
                    type={FIELD_TYPES.TEXT}
                    value={values.tokenId}
                    onChange={onChange.tokenId}
                    onBlur={onBlur.tokenId}
                    error={errors.tokenId}
                    readOnly={!state.isEditing}
                    perRow={2}
                    required
                  />

                  <FormField
                    label="Action Type"
                    name="actionType"
                    type={FIELD_TYPES.SELECT}
                    options={[
                      { label: 'Any', value: null },
                      { label: 'Cancel', value: 'CANCEL' },
                      { label: 'Deactivate', value: 'DEACTIVATE' },
                    ]}
                    value={values.actionType}
                    onChange={onChange.actionType}
                    onBlur={onBlur.actionType}
                    error={errors.actionType}
                    readOnly={!state.isEditing}
                    perRow={2}
                    required
                  />
                </PageSubsection>
              )}
            <PageSubsection>
              <ButtonsContainer>
                {isMobile ? (
                  <>
                    {values.adaType &&
                      values.channelId &&
                      state.accessToken &&
                      ButtonGroup}
                  </>
                ) : (
                  <>
                    {values.adaType &&
                      values.channelId &&
                      state.accessToken &&
                      ButtonGroup}
                  </>
                )}
              </ButtonsContainer>
            </PageSubsection>
          </>
        </DataContainer>
      </Page>

      <AlertModal
        isOpen={isConfirm}
        title={`${values.adaType}`}
        icon="exclamation-circle"
        variant="warn"
        header="ARE YOU SURE?"
        subHeader={`You are about to do a ${values.adaType}.`}
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        confirmText="Yes"
        handleConfirm={() => {
          GetResponse();
          setIsConfirm(false);
        }}
        handleClose={() => {
          setIsConfirm(false);
        }}
      />

      <AlertModal
        isOpen={isSuccess}
        title={`${values.adaType}`}
        handleClose={() => {
          setIsSuccess(false);
        }}
        icon="check-circle"
        variant="success"
        header={`${values.adaType === 'CANCELLATION' || values.adaType === 'MODIFICATION' ? 'SUCCESS' : theMessage}`}
        subHeader={`${values.adaType === 'CANCELLATION' || values.adaType === 'MODIFICATION' ? theMessage : paymentId}`}
        description={``}
        confirmText="Go Back"
        handleConfirm={() => {
          setIsSuccess(false);
          history.push('/ADAsimulation');
        }}
      />

      <AlertModal
        isOpen={isFailure}
        title={`${values.adaType}`}
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader={`${state.responseError ? 'Something went wrong!' : state.otherError}`}
        description="Please go back and try saving it again."
        handleClose={() => setIsFailure(false)}
        confirmText="Go Back"
        handleConfirm={() => {
          setIsFailure(false);
        }}
      />
    </>
  );
};

ADASimulation.propTypes = {
  location: PropTypes.object,
  history: PropTypes.object,
  match: PropTypes.object,
};

export default ADASimulation;
