import { gql } from '@apollo/client';

export const GET_VACANT_CHANNELS = gql`
  query getVacantChannels {
    nonBillNonRegisteredChannels {
      id
      name
    }
  }
`;

export const GET_CHANNEL_OPTIONS = gql`
  query getChannelOptions {
    channelsLoose {
      id
      name
      channelId
    }
  }
`;

export const GET_CHANNEL_INFORMATION = gql`
  query getChannelInformation($where: ChannelPrimary!) {
    channel(where: $where) {
      clientId
      clientSecret
    }
  }
`;
