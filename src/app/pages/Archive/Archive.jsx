import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import PropTypes from 'prop-types';
import React, { useState } from 'react';
import styled from 'styled-components';
import PrimaryButton from '../../components/Button/PrimaryButton';
import DataContainer from '../../components/DataContainer';
import Dropdown from '../../components/Dropdown';
import {
  DropdownButton,
  DropdownMenu,
} from '../../components/Dropdown/Dropdown';
import GlobalSearch from '../../components/GlobalSearch';
import Header from '../../components/Header';
import Page from '../../components/Page';
import Row from '../../components/Row';

const ArchiveItemContainer = styled.div`
  border: 1px solid rgba(165, 165, 165, 0.5);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  margin-right: 20px;
  margin-top: 20px;
  min-width: 300px;

  ${DropdownButton} {
    font-size: ${props => props.theme.fontSize.s};
    border: 0;
    border-bottom: 1px solid #979797;
  }

  ${DropdownMenu} {
    font-size: ${props => props.theme.fontSize.s};
    border: 1px solid #979797;
  }

  ${PrimaryButton} {
    margin-top: 10px;
    padding: 5px 10px;
    align-self: flex-end;
  }
`;

const ArchiveItemCol = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;

  &:first-child {
    margin-right: 20px;
  }
`;

const ArchiveItemTitle = styled.h1`
  margin: 0;
  margin-top: 10px;
  font-size: ${props => props.theme.fontSize.m};
`;

const ArchiveItemTextTitle = styled.h1`
  margin: 0;
  margin-top: 10px;
  font-size: ${props => props.theme.fontSize.m};
  text-align: center;
`;

const ArchiveItem = ({ title, value, options, history, onChange, isText }) => {
  return (
    <ArchiveItemContainer>
      <ArchiveItemCol>
        <FontAwesomeIcon icon="database" color="#009bdd" size="2x" />
        {isText ? (
          ''
        ) : (
          <ArchiveItemTitle>{title.toUpperCase()}</ArchiveItemTitle>
        )}
      </ArchiveItemCol>
      <ArchiveItemCol>
        {isText ? (
          <ArchiveItemTextTitle>{title}</ArchiveItemTextTitle>
        ) : (
          <Dropdown
            value={value}
            options={[{ label: '- Select Report -', value: null }, ...options]}
            onChange={onChange}
          />
        )}
        <PrimaryButton
          disabled={value === null}
          onClick={() => {
            history.push(value);
          }}
        >
          View
        </PrimaryButton>
      </ArchiveItemCol>
    </ArchiveItemContainer>
  );
};

ArchiveItem.propTypes = {
  title: PropTypes.string,
  isText: PropTypes.bool,
  value: PropTypes.any,
  options: PropTypes.array,
  history: PropTypes.object,
  onChange: PropTypes.func,
};

const Archive = ({ history }) => {
  const [selectedView, setSelectedView] = useState({
    rudyDb: null,
    oDb: null,
  });

  return (
    <Page>
      <Header withHome title="Archive" path={['System', 'Archive']} />
      <DataContainer>
        <GlobalSearch onSearch={() => {}} fields={[]} />
        <Row>
          <ArchiveItem
            title="Rudy DB"
            options={[
              {
                label: 'Transaction Tracking Logs',
                value: '/rudydb/transaction-logs',
              },
              {
                label: 'Paybill Reference Codes',
                value: '/rudydb/reference-codes',
              },
            ]}
            value={selectedView.rudyDb}
            onChange={rudyDb => setSelectedView({ ...selectedView, rudyDb })}
            history={history}
          />
          <ArchiveItem
            title="PS Transaction Logs"
            isText={true}
            value={'/archive/transaction-logs'}
            history={history}
          />
          <ArchiveItem
            title="Swipe Transaction Logs"
            isText={true}
            value={'/archive/swipe-transaction-logs'}
            history={history}
          />
          <ArchiveItem
            title="Webtool Audit Logs"
            isText={true}
            value={'/archive/audit-logs'}
            history={history}
          />
        </Row>
      </DataContainer>
    </Page>
  );
};

Archive.propTypes = {
  history: PropTypes.object,
};

export default Archive;
