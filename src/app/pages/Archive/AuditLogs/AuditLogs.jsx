import format from 'date-fns/format';
import React, { useEffect, useState } from 'react';
import { useQuery } from '@apollo/client';
import { ExportButton } from '../../../components/Button/ExportButton';
import DataContainer from '../../../components/DataContainer';
import DataTable from '../../../components/DataTable';
import { FIELD_TYPES } from '../../../components/Form/constants';
import GlobalSearch from '../../../components/GlobalSearch';
import { json2CSVYTD } from '../../../components/GlobalSearch/utils';
import Header from '../../../components/Header';
import { AlertModal } from '../../../components/Modal';
import Page from '../../../components/Page';
import useQuerySeries from '../../../hooks/useQuerySeries';
import {
  ColumnVisibilityDropdown,
  ResponsiveRow,
  TransactionLogHighlight,
} from '../../Reports/styled';
import { GET_AUDIT_LOGS_INFO, REPORT_PATH } from './query';

const AuditLogs = () => {
  const [isConfirmDownloadModalOpen, setIsConfirmDownloadModalOpen] =
    useState(false);
  const [isSuccessDownloadModalOpen, setIsSuccessDownloadModalOpen] =
    useState(false);

  const {
    pagination,
    setPagination,
    setFilter,
    filter,
    data,
    loading,
    page,
    setPage,
    isLastPage,
    loadData,
  } = useQuerySeries(GET_AUDIT_LOGS_INFO, REPORT_PATH, {
    pagination: {
      limit: 10,
      startKey: '',
    },
  });

  const fetchAuditLogs = useQuery(GET_AUDIT_LOGS_INFO, {
    variables: { pagination: { startKey: '' }, filter },
    skip: !isConfirmDownloadModalOpen,
    fetchPolicy: 'network-only',
  });

  const tableConfig = {
    id: {
      headerLabel: 'Id',
      sortable: true,
    },
    userId: {
      headerLabel: 'User Id',
      sortable: true,
    },
    userEmail: {
      headerLabel: 'User Email',
      sortable: true,
    },
    roleId: {
      headerLabel: 'Role Id',
      sortable: true,
    },
    roleName: {
      headerLabel: 'Role Name',
      sortable: true,
    },
    ipAddress: {
      headerLabel: 'IP Address',
      sortable: true,
    },
    userAgent: {
      headerLabel: 'User Agent',
      sortable: true,
    },
    category: {
      headerLabel: 'Category',
      sortable: true,
    },
    isViewed: {
      headerLabel: 'isViewed',
      sortable: true,
    },
    reasonToDelete: {
      headerLabel: 'reasonToDelete',
      sortable: true,
    },
    reasonToUpdate: {
      headerLabel: 'reasonToUpdate',
      sortable: true,
    },
    oldValue: {
      headerLabel: 'oldValue',
      sortable: true,
    },
    newValue: {
      headerLabel: 'newValue',
      sortable: true,
    },
    createdAt: {
      headerLabel: 'Date',
      sortable: true,
      renderAs: data => format(data.createdAt, 'MM/DD/YYYY - hh:mm:ss A'),
    },
  };

  const [visibleColumns, setVisibleColumns] = useState(
    Object.keys(tableConfig)
  );

  useEffect(() => {
    if (loading) {
      loadData();
    }
  }, [loading]);

  return (
    <>
      <Page>
        <Header
          withHome
          title="Webtool Audit Logs"
          path={['Archive', 'Webtool Audit Logs']}
        />
        <DataContainer>
          <DataTable
            minCellWidth={200}
            headerOptions={
              <>
                <GlobalSearch
                  onSearch={filter => {
                    setFilter(filter);
                    setPagination({ ...pagination, startKey: '' });
                  }}
                  fields={[
                    {
                      label: 'Id',
                      name: 'id',
                      type: FIELD_TYPES.TEXT,
                      isKey: true,
                    },
                    {
                      label: 'User Email',
                      name: 'userEmail',
                      type: FIELD_TYPES.TEXT,
                    },
                    // {
                    //   label: 'Date',
                    //   name: 'createdAt',
                    //   type: FIELD_TYPES.DATE_RANGE,
                    // },
                  ]}
                />
                <ResponsiveRow>
                  <ExportButton
                    icon="file-csv"
                    iconPosition="left"
                    disabled={loading}
                    onClick={() => setIsConfirmDownloadModalOpen(true)}
                  >
                    CSV
                  </ExportButton>

                  <ColumnVisibilityDropdown
                    multi
                    showMulti={false}
                    placeholder="Visible Columns"
                    value={visibleColumns}
                    options={Object.keys(tableConfig).map(key => ({
                      value: key,
                      label: tableConfig[key].headerLabel,
                    }))}
                    onChange={setVisibleColumns}
                  />
                </ResponsiveRow>
              </>
            }
            loading={loading}
            data={data}
            config={Object.keys(tableConfig).reduce((config, key) => {
              if (visibleColumns.includes(key)) {
                config[key] = tableConfig[key];
              }
              return config;
            }, {})}
            pagination={{
              ...pagination,
              count: 0,
              cursors: [],
              handleChange: pagination => {
                setPagination(pagination);
              },
            }}
            series={{
              page,
              setPage,
              isLastPage,
            }}
          />
        </DataContainer>
      </Page>
      <AlertModal
        isOpen={isConfirmDownloadModalOpen}
        title="Export Reports Alert"
        header="ARE YOU SURE?"
        variant="warn"
        icon="exclamation-circle"
        subHeader={
          <span>
            You are about to export all filtered records as .CSV File.
          </span>
        }
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() => setIsConfirmDownloadModalOpen(false)}
        handleCancel={() => setIsConfirmDownloadModalOpen(false)}
        confirmLoading={fetchAuditLogs.loading}
        confirmText="Yes"
        handleConfirm={async () => {
          setIsConfirmDownloadModalOpen(false);
          const archiveSwipeTransactionLogs = await json2CSVYTD(
            fetchAuditLogs.data.archiveAuditLogs &&
              fetchAuditLogs.data.archiveAuditLogs.filteredData,
            tableConfig
          );
          let csvData = `${archiveSwipeTransactionLogs}\n,\n`;
          const fileData = {
            mime: 'text/csv',
            filename: `archive-audit-logs.csv`,
            contents: csvData,
          };
          const blob = new Blob([fileData.contents], {
            type: fileData.mime,
          });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          document.body.appendChild(a);
          a.download = fileData.filename;
          a.href = url;
          a.click();
          document.body.removeChild(a);
        }}
      />
      <AlertModal
        isOpen={isSuccessDownloadModalOpen}
        title="Export Reports Alert"
        header="SUCCESS!"
        variant="success"
        icon="check-circle"
        subHeader={
          <span>
            You exported{' '}
            <TransactionLogHighlight>
              {data.length} row(s){' '}
            </TransactionLogHighlight>{' '}
            as .CSV File succesfully.
          </span>
        }
        description="Kindly check the downloaded file."
        confirmText="Back to All Reports"
        handleClose={() => setIsSuccessDownloadModalOpen(false)}
        handleConfirm={() => {
          setIsSuccessDownloadModalOpen(false);
        }}
      />
    </>
  );
};

AuditLogs.propTypes = {};

export default AuditLogs;
