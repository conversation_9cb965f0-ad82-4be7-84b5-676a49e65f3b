import { gql } from '@apollo/client';

export const GET_AUDIT_LOGS_INFO = gql`
  query getArchiveAuditLogs(
    $filter: SearchArchiveAuditLogFilterInput!
    $limit: Int
  ) {
    archiveAuditLogs(filter: $filter, limit: $limit) {
      filteredData {
        id
        userId
        userEmail
        userName
        roleId
        roleName
        ipAddress
        userAgent
        category
        isViewed
        reasonToDelete
        reasonToUpdate
        oldValue
        newValue
        createdAt
      }
    }
  }
`;

export const REPORT_PATH = 'archiveAuditLogs';
