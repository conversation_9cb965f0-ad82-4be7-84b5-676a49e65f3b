import { json2csv } from 'json-2-csv';
import React, { useContext, useState } from 'react';
import { useQuery } from '@apollo/client';
import { ExportButton } from '../../../components/Button/ExportButton';
import DataContainer from '../../../components/DataContainer';
import DataHeader from '../../../components/DataHeader';
import DataTable from '../../../components/DataTable';
import FilterNotification from '../../../components/FilterNotification/FilterNotification';
import { FIELD_TYPES } from '../../../components/Form/constants';
import GlobalSearch from '../../../components/GlobalSearch';
import Header from '../../../components/Header';
import Loader from '../../../components/Loader';
import { AlertModal } from '../../../components/Modal';
import Page from '../../../components/Page';
import AuthContext from '../../../context/AuthContext/AuthContext';
import { useMutation } from '@apollo/client';
import {
  ColumnVisibilityDropdown,
  ResponsiveRow,
  TransactionLogHighlight,
} from '../../Reports/styled';
import { EXPORT_ARCHIVE } from '../mutation';
import { GET_RUDYDB_PAYBILL_REFERENCE } from './query';

const RudyDBReferenceCodes = () => {
  const { permissions } = useContext(AuthContext);
  const [filter, setFilter] = useState({});
  const [isConfirmDownloadModalOpen, setIsConfirmDownloadModalOpen] =
    useState(false);
  const [isSuccessDownloadModalOpen, setIsSuccessDownloadModalOpen] =
    useState(false);

  const { data, loading } = useQuery(GET_RUDYDB_PAYBILL_REFERENCE, {
    variables: { filter },
    fetchPolicy: 'network-only',
    skip: !Object.keys(filter).length,
  });

  const [logExtraction] = useMutation(EXPORT_ARCHIVE);

  const tableConfig = {
    order_ref_code: {
      headerLabel: 'Reference ID',
      sortable: true,
    },
    payment_id: {
      headerLabel: 'Payment ID',
      sortable: true,
    },
    trans_id1: {
      headerLabel: 'Transaction ID 1',
      sortable: true,
    },
    trans_id2: {
      headerLabel: 'Transaction ID 2',
      sortable: true,
    },
    datetime_added: {
      headerLabel: 'Record Date',
      sortable: true,
    },
  };

  const [visibleColumns, setVisibleColumns] = useState(
    Object.keys(tableConfig)
  );

  return (
    <>
      <Page>
        <Header
          withHome
          title="RUDY DB Paybill Reference Codes"
          path={[
            'System',
            { label: 'Archive', to: '/archive' },
            'RUDY DB',
            'Paybill Reference',
          ]}
        />
        <DataContainer>
          <DataHeader>
            <GlobalSearch
              onSearch={filter => {
                setFilter(filter);
              }}
              fields={[
                //{
                //  label: 'Record Date',
                //  name: 'createdAt',
                //  type: FIELD_TYPES.DATE_RANGE,
                //},
                {
                  label: 'Reference ID',
                  name: 'orderRefCode',
                  type: FIELD_TYPES.TEXT,
                },
              ]}
              placeholder="Search entries here..."
            />
            <ResponsiveRow>
              {permissions.Archive.export && (
                <ExportButton
                  icon="file-csv"
                  iconPosition="left"
                  disabled={loading}
                  onClick={() => setIsConfirmDownloadModalOpen(true)}
                >
                  CSV
                </ExportButton>
              )}
              <ColumnVisibilityDropdown
                multi
                showMulti={false}
                placeholder="Visible Columns"
                value={visibleColumns}
                options={Object.keys(tableConfig).map(key => ({
                  value: key,
                  label: tableConfig[key].headerLabel,
                }))}
                onChange={setVisibleColumns}
              />
            </ResponsiveRow>
          </DataHeader>
          {!Object.keys(filter).length ? (
            <FilterNotification />
          ) : loading ? (
            <Loader />
          ) : (
            <DataTable
              minCellWidth={200}
              loading={loading}
              data={
                data && data.paybillReferenceCodes
                  ? data.paybillReferenceCodes.filteredData
                  : []
              }
              config={Object.keys(tableConfig).reduce((config, key) => {
                if (visibleColumns.includes(key)) {
                  config[key] = tableConfig[key];
                }
                return config;
              }, {})}
              pagination={null}
            />
          )}
        </DataContainer>
      </Page>
      <AlertModal
        isOpen={isConfirmDownloadModalOpen}
        title="Export Reports Alert"
        header="ARE YOU SURE?"
        variant="warn"
        icon="exclamation-circle"
        subHeader={
          <span>
            You are about to export{' '}
            <TransactionLogHighlight>
              {data &&
              data.paybillReferenceCodes &&
              data.paybillReferenceCodes.filteredData
                ? data.paybillReferenceCodes.filteredData.length
                : 0}{' '}
              rows{' '}
            </TransactionLogHighlight>{' '}
            as .CSV File.
          </span>
        }
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() => setIsConfirmDownloadModalOpen(false)}
        handleCancel={() => setIsConfirmDownloadModalOpen(false)}
        confirmText="Yes"
        handleConfirm={async () => {
          logExtraction({
            variables: {
              data: {
                type: 'paybill',
              },
            },
          });
          const csv = await json2csv(
            data && data.paybillReferenceCodes
              ? data.paybillReferenceCodes.filteredData
              : [],
            {
              keys: visibleColumns,
            }
          );

          const fileData = {
            mime: 'text/csv',
            filename: 'rudydb-paybill-reference-codes.csv',
            contents: csv,
          };
          const blob = new Blob([fileData.contents], {
            type: fileData.mime,
          });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          document.body.appendChild(a);
          a.download = fileData.filename;
          a.href = url;
          a.click();
          document.body.removeChild(a);
          setIsConfirmDownloadModalOpen(false);
          setIsSuccessDownloadModalOpen(true);
        }}
      />
      <AlertModal
        isOpen={isSuccessDownloadModalOpen}
        title="Export Reports Alert"
        header="SUCCESS!"
        variant="success"
        icon="check-circle"
        subHeader={
          <span>
            You exported{' '}
            <TransactionLogHighlight>
              {data &&
              data.paybillReferenceCodes &&
              data.paybillReferenceCodes.filteredData
                ? data.paybillReferenceCodes.filteredData.length
                : 0}{' '}
              row(s){' '}
            </TransactionLogHighlight>{' '}
            as .CSV File succesfully.
          </span>
        }
        description="Kindly check the downloaded file."
        confirmText="Back to All Reports"
        handleClose={() => setIsSuccessDownloadModalOpen(false)}
        handleConfirm={() => {
          setIsSuccessDownloadModalOpen(false);
        }}
      />
    </>
  );
};

RudyDBReferenceCodes.propTypes = {};

export default RudyDBReferenceCodes;
