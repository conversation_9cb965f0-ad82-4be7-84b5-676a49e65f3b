import { json2csv } from 'json-2-csv';
import React, { useContext, useState } from 'react';
import { useQuery } from '@apollo/client';
import { ExportButton } from '../../../components/Button/ExportButton';
import DataContainer from '../../../components/DataContainer';
import DataHeader from '../../../components/DataHeader';
import DataTable from '../../../components/DataTable';
import FilterNotification from '../../../components/FilterNotification/FilterNotification';
import { FIELD_TYPES } from '../../../components/Form/constants';
import GlobalSearch from '../../../components/GlobalSearch';
import Header from '../../../components/Header';
import Loader from '../../../components/Loader';
import { AlertModal } from '../../../components/Modal';
import Page from '../../../components/Page';
import AuthContext from '../../../context/AuthContext/AuthContext';
import { useMutation } from '@apollo/client';
import {
  ColumnVisibilityDropdown,
  ResponsiveRow,
  TransactionLogHighlight,
} from '../../Reports/styled';
import { EXPORT_ARCHIVE } from '../mutation';
import { GET_RUDYDB_TRANSACTION_LOGS } from './query';

const RudyDBTransactionLogs = () => {
  const { permissions } = useContext(AuthContext);
  const [filter, setFilter] = useState({});
  const [isConfirmDownloadModalOpen, setIsConfirmDownloadModalOpen] =
    useState(false);
  const [isSuccessDownloadModalOpen, setIsSuccessDownloadModalOpen] =
    useState(false);
  const { data, loading } = useQuery(GET_RUDYDB_TRANSACTION_LOGS, {
    variables: { filter },
    notifyOnNetworkStatusChange: true,
    fetchPolicy: 'network-only',
    skip: !Object.keys(filter).length,
  });

  const [logExtraction] = useMutation(EXPORT_ARCHIVE);

  const tableConfig = {
    order_ref_code: {
      headerLabel: 'Reference ID',
      sortable: true,
    },
    seller_name: {
      headerLabel: 'Channel Name',
      sortable: true,
    },
    payment_method: {
      headerLabel: 'Payment Method',
      sortable: true,
    },
    description: {
      headerLabel: 'Description',
      sortable: true,
    },
    status: {
      headerLabel: 'Status',
      sortable: true,
    },
    subs_lname: {
      headerLabel: 'Last Name',
      sortable: true,
    },
    subs_fname: {
      headerLabel: 'First Name',
      sortable: true,
    },
    subs_mname: {
      headerLabel: 'Middle Name',
      sortable: true,
    },
    subs_accnt_no: {
      headerLabel: 'Account Number',
      sortable: true,
    },
    subs_globe_no: {
      headerLabel: 'Globe No.',
      sortable: true,
    },
    subs_email_address: {
      headerLabel: 'Email Address',
      sortable: true,
    },
    datetime_added: {
      headerLabel: 'Time Added',
      sortable: true,
    },
    datetime_on_hold: {
      headerLabel: 'Time On Hold',
      sortable: true,
    },
    datetime_completed: {
      headerLabel: 'Time Completed',
      sortable: true,
    },
    sent_amount: {
      headerLabel: 'Amount Sent',
      sortable: true,
    },
    approved_amount: {
      headerLabel: 'Amount Approved',
      sortable: true,
    },
    remarks: {
      headerLabel: 'Remarks',
      sortable: true,
    },
    payment_id: {
      headerLabel: 'Payment ID',
      sortable: true,
    },
    trans_id1: {
      headerLabel: 'Transaction ID 1',
      sortable: true,
    },
    trans_id2: {
      headerLabel: 'Transaction ID 2',
      sortable: true,
    },
    account_type: {
      headerLabel: 'Account Type',
      sortable: true,
    },
    outstanding_balance: {
      headerLabel: 'Outstanding Balance',
      sortable: true,
    },
  };

  const [visibleColumns, setVisibleColumns] = useState(
    Object.keys(tableConfig)
  );

  return (
    <>
      <Page>
        <Header
          withHome
          title="RUDY DB Transaction Tracking Logs"
          path={[
            'System',
            { label: 'Archive', to: '/archive' },
            'RUDY DB',
            'Transaction Tracking Logs',
          ]}
        />
        <DataContainer>
          <DataHeader>
            <GlobalSearch
              onSearch={filter => {
                setFilter(filter);
              }}
              fields={[
                //{
                //  label: 'Record Date',
                //  name: 'createdAt',
                //  type: FIELD_TYPES.DATE_RANGE,
                //},
                {
                  label: 'Reference ID',
                  name: 'orderRefCode',
                  type: FIELD_TYPES.TEXT,
                },
              ]}
              placeholder="Search entries here..."
            />
            <ResponsiveRow>
              {permissions.Archive.export && (
                <ExportButton
                  icon="file-csv"
                  iconPosition="left"
                  disabled={loading}
                  onClick={() => setIsConfirmDownloadModalOpen(true)}
                >
                  CSV
                </ExportButton>
              )}
              <ColumnVisibilityDropdown
                multi
                showMulti={false}
                placeholder="Visible Columns"
                value={visibleColumns}
                options={Object.keys(tableConfig).map(key => ({
                  value: key,
                  label: tableConfig[key].headerLabel,
                }))}
                onChange={setVisibleColumns}
              />
            </ResponsiveRow>
          </DataHeader>
          {!Object.keys(filter).length ? (
            <FilterNotification />
          ) : loading ? (
            <Loader />
          ) : (
            <DataTable
              minCellWidth={200}
              loading={loading}
              data={
                data && data.paybillTransactionLogs
                  ? data.paybillTransactionLogs.filteredData
                  : []
              }
              config={Object.keys(tableConfig).reduce((config, key) => {
                if (visibleColumns.includes(key)) {
                  config[key] = tableConfig[key];
                }
                return config;
              }, {})}
              pagination={null}
            />
          )}
        </DataContainer>
      </Page>
      <AlertModal
        isOpen={isConfirmDownloadModalOpen}
        title="Export Reports Alert"
        header="ARE YOU SURE?"
        variant="warn"
        icon="exclamation-circle"
        subHeader={
          <span>
            You are about to export{' '}
            <TransactionLogHighlight>
              {data &&
              data.paybillTransactionLogs &&
              data.paybillTransactionLogs.filteredData
                ? data.paybillTransactionLogs.filteredData.length
                : 0}{' '}
              rows{' '}
            </TransactionLogHighlight>{' '}
            as .CSV File.
          </span>
        }
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() => setIsConfirmDownloadModalOpen(false)}
        handleCancel={() => setIsConfirmDownloadModalOpen(false)}
        confirmText="Yes"
        handleConfirm={async () => {
          logExtraction({
            variables: {
              data: {
                type: 'transaction',
              },
            },
          });
          const csv = await json2csv(
            data && data.paybillTransactionLogs
              ? data.paybillTransactionLogs.filteredData
              : [],
            {
              keys: visibleColumns,
            }
          );

          const fileData = {
            mime: 'text/csv',
            filename: 'rudydb-transaction-logs.csv',
            contents: csv,
          };
          const blob = new Blob([fileData.contents], {
            type: fileData.mime,
          });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          document.body.appendChild(a);
          a.download = fileData.filename;
          a.href = url;
          a.click();
          document.body.removeChild(a);
          setIsConfirmDownloadModalOpen(false);
          setIsSuccessDownloadModalOpen(true);
        }}
      />
      <AlertModal
        isOpen={isSuccessDownloadModalOpen}
        title="Export Reports Alert"
        header="SUCCESS!"
        variant="success"
        icon="check-circle"
        subHeader={
          <span>
            You exported{' '}
            <TransactionLogHighlight>
              {data &&
              data.paybillTransactionLogs &&
              data.paybillTransactionLogs.filteredData
                ? data.paybillTransactionLogs.filteredData.length
                : 0}{' '}
              row(s){' '}
            </TransactionLogHighlight>{' '}
            as .CSV File succesfully.
          </span>
        }
        description="Kindly check the downloaded file."
        confirmText="Back to All Reports"
        handleClose={() => setIsSuccessDownloadModalOpen(false)}
        handleConfirm={() => {
          setIsSuccessDownloadModalOpen(false);
        }}
      />
    </>
  );
};

RudyDBTransactionLogs.propTypes = {};

export default RudyDBTransactionLogs;
