import { gql } from '@apollo/client';

export const GET_RUDYDB_TRANSACTION_LOGS = gql`
  query getRudyDBTransactionLogs(
    $filter: SearchTransactionLogsGatewayFilterInput!
  ) {
    paybillTransactionLogs(filter: $filter) {
      filteredData {
        order_ref_code
        seller_name
        payment_method
        description
        status
        subs_lname
        subs_fname
        subs_mname
        subs_accnt_no
        subs_globe_no
        subs_email_address
        datetime_added
        datetime_on_hold
        datetime_completed
        sent_amount
        approved_amount
        remarks
        payment_id
        trans_id1
        trans_id2
        account_type
        outstanding_balance
      }
    }
  }
`;
