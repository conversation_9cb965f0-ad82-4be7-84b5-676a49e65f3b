import format from 'date-fns/format';
import React, { useEffect, useState } from 'react';
import { useQuery } from '@apollo/client';
import { ExportButton } from '../../../components/Button/ExportButton';
import DataContainer from '../../../components/DataContainer';
import DataTable from '../../../components/DataTable';
import { FIELD_TYPES } from '../../../components/Form/constants';
import GlobalSearch from '../../../components/GlobalSearch';
import {
  json2CSVYTD,
  numberWithCommas,
} from '../../../components/GlobalSearch/utils';
import Header from '../../../components/Header';
import { AlertModal } from '../../../components/Modal';
import Page from '../../../components/Page';
import useQuerySeries from '../../../hooks/useQuerySeries';
import {
  ColumnVisibilityDropdown,
  ResponsiveRow,
  TransactionLogHighlight,
} from '../../Reports/styled';
import { GET_SWIPETRANSACTION_LOGS_INFO, REPORT_PATH } from './query';

const SwipeTransactionLogs = () => {
  const [isConfirmDownloadModalOpen, setIsConfirmDownloadModalOpen] =
    useState(false);
  const [isSuccessDownloadModalOpen, setIsSuccessDownloadModalOpen] =
    useState(false);

  const {
    pagination,
    setPagination,
    setFilter,
    filter,
    data,
    loading,
    page,
    setPage,
    isLastPage,
    loadData,
  } = useQuerySeries(GET_SWIPETRANSACTION_LOGS_INFO, REPORT_PATH, {
    pagination: {
      limit: 10,
      startKey: '',
    },
  });

  const fetchSwipeTransactionLogs = useQuery(GET_SWIPETRANSACTION_LOGS_INFO, {
    variables: { pagination: { startKey: '' }, filter },
    skip: !isConfirmDownloadModalOpen,
    fetchPolicy: 'network-only',
  });

  const tableConfig = {
    psReferenceNo: {
      headerLabel: 'PS Reference No.',
      sortable: true,
    },
    gcashReferenceNo: {
      headerLabel: 'GCash Reference No.',
      sortable: true,
    },
    accountNo: {
      headerLabel: 'Account No.',
      sortable: true,
    },
    srn: {
      headerLabel: 'SRN',
      sortable: true,
    },
    channelName: {
      headerLabel: 'Channel Name',
      sortable: true,
    },
    paymentMethod: {
      headerLabel: 'Payment Method',
      sortable: true,
      renderAs: data => (data.paymentMethod === 'gcash' ? 'GCash' : ''),
    },
    paymentStatus: {
      headerLabel: 'Payment Status',
      sortable: true,
    },
    amount: {
      headerLabel: 'Amount',
      sortable: true,
      renderAs: data => numberWithCommas(data.amount, 2),
    },
    currency: {
      headerLabel: 'Currency',
      sortable: true,
    },
    date: {
      headerLabel: 'Date',
      sortable: true,
      renderAs: data => format(data.date, 'MM/DD/YYYY - hh:mm:ss A'),
    },
    msisdn: {
      headerLabel: 'MSISDN',
      sortable: true,
    },
    productDescription: {
      headerLabel: 'Product Description',
      sortable: true,
    },
    emailAddress: {
      headerLabel: 'Email Address',
      sortable: true,
    },
    paymentGateway: {
      headerLabel: 'Payment Gateway',
      sortable: true,
    },
    customerSegment: {
      headerLabel: 'Customer Segment',
      sortable: true,
    },
    customerSubType: {
      headerLabel: 'Customer Sub Type',
      sortable: true,
    },
    brand: {
      headerLabel: 'Brand',
      sortable: true,
    },
    entity: {
      headerLabel: 'Entity',
      sortable: true,
    },
    sku: {
      headerLabel: 'SKU',
      sortable: true,
    },
    modeOfPayment: {
      headerLabel: 'Mode of Payment',
      sortable: true,
    },
    subscriberType: {
      headerLabel: 'Sub Service / Subscriber Type',
      sortable: true,
    },
    contentPartnerShortName: {
      headerLabel: 'Content Partner Short Name',
      sortable: true,
    },
    voucherDispenseStatus: {
      headerLabel: 'Voucher Dispense Status',
      sortable: true,
    },
    refundStatus: {
      headerLabel: 'Refund Status',
      sortable: true,
    },
    refundAmount: {
      headerLabel: 'Refund Amount',
      sortable: true,
      renderAs: data => numberWithCommas(data.refundAmount, 2),
    },
  };

  const [visibleColumns, setVisibleColumns] = useState(
    Object.keys(tableConfig)
  );

  useEffect(() => {
    if (loading) {
      loadData();
    }
  }, [loading]);

  return (
    <>
      <Page>
        <Header
          withHome
          title="Swipe Transaction Logs"
          path={['Archive', 'Swipe Transaction Logs']}
        />
        <DataContainer>
          <DataTable
            minCellWidth={200}
            headerOptions={
              <>
                <GlobalSearch
                  onSearch={filter => {
                    setFilter(filter);
                    setPagination({ ...pagination, startKey: '' });
                  }}
                  fields={[
                    {
                      label: 'PS Reference No.',
                      name: 'id',
                      type: FIELD_TYPES.TEXT,
                      isKey: true,
                    },
                  ]}
                />
                <ResponsiveRow>
                  <ExportButton
                    icon="file-csv"
                    iconPosition="left"
                    disabled={loading}
                    onClick={() => setIsConfirmDownloadModalOpen(true)}
                  >
                    CSV
                  </ExportButton>

                  <ColumnVisibilityDropdown
                    multi
                    showMulti={false}
                    placeholder="Visible Columns"
                    value={visibleColumns}
                    options={Object.keys(tableConfig).map(key => ({
                      value: key,
                      label: tableConfig[key].headerLabel,
                    }))}
                    onChange={setVisibleColumns}
                  />
                </ResponsiveRow>
              </>
            }
            loading={loading}
            data={data}
            config={Object.keys(tableConfig).reduce((config, key) => {
              if (visibleColumns.includes(key)) {
                config[key] = tableConfig[key];
              }
              return config;
            }, {})}
            pagination={{
              ...pagination,
              count: 0,
              cursors: [],
              handleChange: pagination => {
                setPagination(pagination);
              },
            }}
            series={{
              page,
              setPage,
              isLastPage,
            }}
          />
        </DataContainer>
      </Page>
      <AlertModal
        isOpen={isConfirmDownloadModalOpen}
        title="Export Reports Alert"
        header="ARE YOU SURE?"
        variant="warn"
        icon="exclamation-circle"
        subHeader={
          <span>
            You are about to export all filtered records as .CSV File.
          </span>
        }
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() => setIsConfirmDownloadModalOpen(false)}
        handleCancel={() => setIsConfirmDownloadModalOpen(false)}
        confirmLoading={fetchSwipeTransactionLogs.loading}
        confirmText="Yes"
        handleConfirm={async () => {
          setIsConfirmDownloadModalOpen(false);
          const archiveSwipeTransactionLogs = await json2CSVYTD(
            fetchSwipeTransactionLogs.data.archiveSwipeLogs &&
              fetchSwipeTransactionLogs.data.archiveSwipeLogs.filteredData,
            tableConfig
          );
          let csvData = `${archiveSwipeTransactionLogs}\n,\n`;
          const fileData = {
            mime: 'text/csv',
            filename: `archive-swipe-transaction-logs.csv`,
            contents: csvData,
          };
          const blob = new Blob([fileData.contents], {
            type: fileData.mime,
          });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          document.body.appendChild(a);
          a.download = fileData.filename;
          a.href = url;
          a.click();
          document.body.removeChild(a);
        }}
      />
      <AlertModal
        isOpen={isSuccessDownloadModalOpen}
        title="Export Reports Alert"
        header="SUCCESS!"
        variant="success"
        icon="check-circle"
        subHeader={
          <span>
            You exported{' '}
            <TransactionLogHighlight>
              {data.length} row(s){' '}
            </TransactionLogHighlight>{' '}
            as .CSV File succesfully.
          </span>
        }
        description="Kindly check the downloaded file."
        confirmText="Back to All Reports"
        handleClose={() => setIsSuccessDownloadModalOpen(false)}
        handleConfirm={() => {
          setIsSuccessDownloadModalOpen(false);
        }}
      />
    </>
  );
};

SwipeTransactionLogs.propTypes = {};

export default SwipeTransactionLogs;
