import { gql } from '@apollo/client';

export const GET_SWIPETRANSACTION_LOGS_INFO = gql`
  query getArchiveSwipeLogs(
    $filter: SearchArchiveSwipeLogFilterInput!
    $limit: Int
  ) {
    archiveSwipeLogs(filter: $filter, limit: $limit) {
      filteredData {
        psReferenceNo
        gcashReferenceNo
        accountNo
        srn
        channelName
        channelId
        paymentMethod
        paymentStatus
        amount
        currency
        date
        msisdn
        productDescription
        emailAddress
        paymentGateway
        customerSegment
        customerSubType
        brand
        entity
        sku
        modeOfPayment
        subscriberType
        contentPartnerShortName
        voucherDispenseStatus
        refundStatus
        refundAmount
      }
    }
  }
`;

export const REPORT_PATH = 'archiveSwipeLogs';
