import format from 'date-fns/format';
import React, { useEffect, useState } from 'react';
import { useQuery } from '@apollo/client';
import { ExportButton } from '../../../components/Button/ExportButton';
import DataContainer from '../../../components/DataContainer';
import DataTable from '../../../components/DataTable';
import { FIELD_TYPES } from '../../../components/Form/constants';
import GlobalSearch from '../../../components/GlobalSearch';
import {
  json2CSVYTD,
  numberWithCommas,
} from '../../../components/GlobalSearch/utils';
import Header from '../../../components/Header';
import { AlertModal } from '../../../components/Modal';
import Page from '../../../components/Page';
import useQuerySeries from '../../../hooks/useQuerySeries';
import formatCurrency from '../../../utils/formatCurrency';
import {
  ColumnVisibilityDropdown,
  ResponsiveRow,
  TransactionLogHighlight,
} from '../../Reports/styled';
import { GET_TRANSACTION_LOGS_INFO, REPORT_PATH } from './query';

const PAYMENT_GATEWAYS = [
  { label: 'Any', value: null },
  { label: 'Adyen', value: 'adyen' },
  { label: 'GCash', value: 'gcash' },
  { label: 'iPay88', value: 'ipay88' },
  { value: 'bpi', label: 'BPI' },
];

const TransactionLogs = () => {
  const [isConfirmDownloadModalOpen, setIsConfirmDownloadModalOpen] =
    useState(false);
  const [isSuccessDownloadModalOpen, setIsSuccessDownloadModalOpen] =
    useState(false);

  const {
    pagination,
    setPagination,
    setFilter,
    filter,
    data,
    loading,
    page,
    setPage,
    isLastPage,
    loadData,
  } = useQuerySeries(GET_TRANSACTION_LOGS_INFO, REPORT_PATH, {
    pagination: {
      limit: 10,
      startKey: '',
    },
  });

  const fetchTransactionLogs = useQuery(GET_TRANSACTION_LOGS_INFO, {
    variables: { pagination: { startKey: '' }, filter },
    skip: !isConfirmDownloadModalOpen,
    fetchPolicy: 'network-only',
  });

  const tableConfig = {
    reference: {
      headerLabel: 'Reference No.',
      sortable: true,
    },
    accountNumber: {
      headerLabel: 'Account No.',
      sortable: true,
    },
    channelName: {
      headerLabel: 'Channel Name',
      sortable: true,
    },
    emailAddress: {
      headerLabel: 'Email Address',
      sortable: true,
    },
    prodDesc: {
      headerLabel: 'Product Description',
      sortable: true,
    },
    paymentMethod: {
      headerLabel: 'Payment Method',
      sortable: true,
    },
    mobileNumber: {
      headerLabel: 'MSISDN',
      sortable: true,
    },
    status: {
      headerLabel: 'Status',
      sortable: true,
    },
    paymentType: {
      headerLabel: 'Payment Type',
      sortable: true,
      renderAs: data => data.paymentType,
    },
    refundId: {
      headerLabel: 'Refund ID',
      sortable: true,
    },
    refundStatus: {
      headerLabel: 'Refund Status',
      sortable: true,
    },
    refundAmount: {
      headerLabel: 'Refund Amount',
      renderAs: data =>
        data.refundAmount ? numberWithCommas(data.refundAmount, 2) : '',
      sortable: true,
    },
    amountCurrency: {
      headerLabel: 'Currency',
      sortable: true,
    },
    amountValue: {
      headerLabel: 'Amount',
      sortable: true,
      renderAs: data => formatCurrency(data.amountValue, true),
      textAlign: 'right',
    },
    createdAt: {
      headerLabel: 'Date',
      sortable: true,
      renderAs: data => format(data.createdAt, 'MM/DD/YYYY - hh:mm:ss A'),
    },
    paymentGateway: {
      headerLabel: 'Payment Gateway',
      sortable: true,
      renderAs: data => {
        var renderData = PAYMENT_GATEWAYS.find(
          paymentGateway => paymentGateway.value === data.paymentGateway
        );

        return renderData ? renderData.label : '';
      },
    },
    splitPayment: {
      headerLabel: 'Split Payment',
      sortable: true,
      renderAs: data => (data.splitPayment ? 'Yes' : 'No'),
    },
    billType: {
      headerLabel: 'Bill Type',
      sortable: true,
    },
    fromBatchFile: {
      headerLabel: 'Batch File',
      sortable: true,
      renderAs: data => (data.fromBatchFile ? 'Yes' : 'No'),
    },
    costCenter: {
      headerLabel: 'Cost Center',
      sortable: true,
    },
    refusalReasonRaw: {
      headerLabel: 'Payment Reason',
      sortable: true,
    },
    postPaymentReason: {
      headerLabel: 'Post Payment Reason',
      sortable: true,
    },
    postPaymentEsbMessageId: {
      headerLabel: 'Post Payment ESB ID',
      sortable: true,
    },
  };

  const [visibleColumns, setVisibleColumns] = useState(
    Object.keys(tableConfig)
  );

  useEffect(() => {
    if (loading) {
      loadData();
    }
  }, [loading]);

  return (
    <>
      <Page>
        <Header
          withHome
          title="PS Transaction Logs"
          path={['Archive', 'PS Transaction Logs']}
        />
        <DataContainer>
          <DataTable
            minCellWidth={200}
            headerOptions={
              <>
                <GlobalSearch
                  onSearch={newFilter => {
                    setFilter(newFilter);
                    setPagination({ ...pagination, startKey: '' });
                  }}
                  fields={[
                    {
                      label: 'Reference No.',
                      name: 'id',
                      type: FIELD_TYPES.TEXT,
                      isKey: true,
                    },
                    {
                      label: 'Payment Gateway',
                      name: 'gatewayProcessor',
                      type: FIELD_TYPES.SELECT,
                      options: PAYMENT_GATEWAYS,
                      isKey: true,
                    },
                    {
                      label: 'Account No.',
                      name: 'accountNumber',
                      type: FIELD_TYPES.TEXT,
                      isKey: true,
                    },
                    {
                      label: 'Status',
                      name: 'status',
                      type: FIELD_TYPES.SELECT,
                      options: [
                        { value: null, label: 'Any' },
                        {
                          value: 'ADYEN_AUTHORISED',
                          label: 'PAYMENT_AUTHORIZED',
                        },
                        { value: 'ADYEN_REFUSED', label: 'PAYMENT_REFUSED' },
                        { value: 'POSTED', label: 'PAYMENT_POSTED' },
                        {
                          value: 'POSTING_FAILED',
                          label: 'PAYMENT_POSTED_FAILED',
                        },
                        {
                          value: 'POSTED_LUKE',
                          label: 'PAYMENT_POSTED_LUKE',
                        },
                        {
                          value: 'GCASH_AUTHORISED',
                          label: 'GCASH_AUTHORISED',
                        },
                        {
                          value: 'GCASH_REFUSED',
                          label: 'GCASH_REFUSED',
                        },
                      ],
                    },
                    // {
                    //   label: 'MSISDN',
                    //   name: 'mobileNumber',
                    //   type: FIELD_TYPES.TEXT,
                    // },
                    // {
                    //   label: 'Date',
                    //   name: 'createdAt',
                    //   type: FIELD_TYPES.DATE_RANGE,
                    // },
                  ]}
                />
                <ResponsiveRow>
                  <ExportButton
                    icon="file-csv"
                    iconPosition="left"
                    disabled={loading}
                    onClick={() => setIsConfirmDownloadModalOpen(true)}
                  >
                    CSV
                  </ExportButton>

                  <ColumnVisibilityDropdown
                    multi
                    showMulti={false}
                    placeholder="Visible Columns"
                    value={visibleColumns}
                    options={Object.keys(tableConfig).map(key => ({
                      value: key,
                      label: tableConfig[key].headerLabel,
                    }))}
                    onChange={setVisibleColumns}
                  />
                </ResponsiveRow>
              </>
            }
            loading={loading}
            data={data}
            config={Object.keys(tableConfig).reduce((config, key) => {
              if (visibleColumns.includes(key)) {
                config[key] = tableConfig[key];
              }
              return config;
            }, {})}
            pagination={{
              ...pagination,
              count: 0,
              cursors: [],
              handleChange: pagination => {
                setPagination(pagination);
              },
            }}
            series={{
              page,
              setPage,
              isLastPage,
            }}
          />
        </DataContainer>
      </Page>
      <AlertModal
        isOpen={isConfirmDownloadModalOpen}
        title="Export Reports Alert"
        header="ARE YOU SURE?"
        variant="warn"
        icon="exclamation-circle"
        subHeader={
          <span>
            You are about to export all filtered records as .CSV File.
          </span>
        }
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() => setIsConfirmDownloadModalOpen(false)}
        handleCancel={() => setIsConfirmDownloadModalOpen(false)}
        confirmLoading={fetchTransactionLogs.loading}
        confirmText="Yes"
        handleConfirm={async () => {
          {
            setIsConfirmDownloadModalOpen(false);
            const archiveTransactionLogs = await json2CSVYTD(
              fetchTransactionLogs.data.archiveTransactionLogs &&
                fetchTransactionLogs.data.archiveTransactionLogs.filteredData,
              tableConfig
            );
            let csvData = `${archiveTransactionLogs}\n,\n`;
            const fileData = {
              mime: 'text/csv',
              filename: `archive-transaction-logs.csv`,
              contents: csvData,
            };
            const blob = new Blob([fileData.contents], {
              type: fileData.mime,
            });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            document.body.appendChild(a);
            a.download = fileData.filename;
            a.href = url;
            a.click();
            document.body.removeChild(a);
          }
        }}
      />
      <AlertModal
        isOpen={isSuccessDownloadModalOpen}
        title="Export Reports Alert"
        header="SUCCESS!"
        variant="success"
        icon="check-circle"
        subHeader={
          <span>
            You exported{' '}
            <TransactionLogHighlight>
              {data.length} row(s){' '}
            </TransactionLogHighlight>{' '}
            as .CSV File succesfully.
          </span>
        }
        description="Kindly check the downloaded file."
        confirmText="Back to All Reports"
        handleClose={() => setIsSuccessDownloadModalOpen(false)}
        handleConfirm={() => {
          setIsSuccessDownloadModalOpen(false);
        }}
      />
    </>
  );
};

TransactionLogs.propTypes = {};

export default TransactionLogs;
