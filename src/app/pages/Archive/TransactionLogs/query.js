import { gql } from '@apollo/client';

export const GET_CHANNEL_OPTIONS = gql`
  query getChannelOptions {
    channelsLoose {
      id
      name
      channelId
    }
  }
`;

export const GET_TRANSACTION_LOGS_INFO = gql`
  query getArchiveTransactionLogs(
    $filter: SearchArchiveTransactionLogFilterInput!
    $limit: Int
  ) {
    archiveTransactionLogs(filter: $filter, limit: $limit) {
      filteredData {
        reference
        accountNumber
        channelName
        emailAddress
        prodDesc
        paymentMethod
        mobileNumber
        status
        paymentType
        refundId
        refundStatus
        refundAmount
        amountCurrency
        amountValue
        createdAt
        paymentGateway
        splitPayment
        billType
        fromBatchFile
        costCenter
        refusalReasonRaw
        postPaymentReason
        postPaymentEsbMessageId
      }
    }
  }
`;

export const REPORT_PATH = 'archiveTransactionLogs';
