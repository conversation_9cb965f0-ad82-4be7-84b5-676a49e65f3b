import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import format from 'date-fns/format';
import PropTypes from 'prop-types';
import React, { useState } from 'react';
import { useQuery } from '@apollo/client';
import styled from 'styled-components';
import ActionButtons from '../../components/ActionButtons';
import CreateButton from '../../components/Button/CreateButton';
import PrimaryButton from '../../components/Button/PrimaryButton';
import DataContainer from '../../components/DataContainer';
import DataTable from '../../components/DataTable';
import { FIELD_TYPES } from '../../components/Form/constants';
import GlobalSearch from '../../components/GlobalSearch';
import Header from '../../components/Header';
import AlertModal from '../../components/Modal/AlertModal';
import Page from '../../components/Page';
import useQuerySeries from '../../hooks/useQuerySeries';
import { GET_USER_INFORMATION } from '../UserInformation/query';
import { GET_AUDITS_INFORMATION } from './query';
import {
  UserModal,
  UserModalActiveLabel,
  UserModalIcon,
  UserModalLastLogin,
  UserModalLastLoginLabel,
  UserModalLastLoginRow,
  UserModalName,
  UserModalRole,
} from './styled';

const PreWrap = styled.span`
  white-space: pre-wrap;
`;

const AuditTrail = ({ history }) => {
  const {
    pagination,
    setPagination,
    setFilter,
    data,
    loading,
    page,
    setPage,
    isLastPage,
    clearCache,
  } = useQuerySeries(GET_AUDITS_INFORMATION, 'audits', {
    pagination: {
      start: { id: '', sortKey: '', createdAt: '' },
      limit: 10,
    },
  });

  const [selectedUser, setSelectedUser] = useState(null);

  const { data: selectedUserData, loading: isFetchingSelectedUser } = useQuery(
    GET_USER_INFORMATION,
    {
      variables: {
        where: { id: selectedUser ? selectedUser.id : '' },
      },
      skip: selectedUser === null,
      fetchPolicy: 'network-only',
    }
  );

  return (
    <>
      <Page>
        <Header withHome title="Audit Trail" path={['System', 'Audit Trail']} />
        <DataContainer>
          <DataTable
            data={data}
            loading={loading}
            headerOptions={
              <>
                <GlobalSearch
                  onSearch={filter => {
                    const newPagination = {
                      ...pagination,
                      start: { id: '', sortKey: '', createdAt: '' },
                    };
                    setFilter(filter);
                    setPagination(newPagination);
                  }}
                  fields={[
                    {
                      label: 'Record Date',
                      name: 'createdAt',
                      type: FIELD_TYPES.DATE_RANGE,
                    },
                    {
                      label: 'IP Address',
                      name: 'ipAddress',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Performing User',
                      name: 'userEmail',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'User Agent',
                      name: 'userAgent',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Category',
                      name: 'category',
                      type: FIELD_TYPES.TEXT,
                    },
                  ]}
                  placeholder="Search Audit Trail"
                />
                <CreateButton
                  icon="sync-alt"
                  onClick={() => {
                    clearCache();
                  }}
                  loading={loading}
                >
                  Refresh
                </CreateButton>
              </>
            }
            series={{ page, setPage, isLastPage }}
            pagination={{
              ...pagination,
              count: 0,
              cursors: [],
              handleChange: pagination => {
                setPagination(pagination);
              },
            }}
            config={{
              createdAt: {
                headerLabel: 'Record Date',
                sortable: true,
                renderAs: data =>
                  format(new Date(data.createdAt), 'MM/DD/YYYY - hh:mm:ss A'),
              },
              ipAddress: {
                headerLabel: 'IP Address',
                sortable: true,
              },
              userEmail: {
                headerLabel: 'Performing User',
                sortable: true,
                onClick: data => {
                  setSelectedUser({
                    id: data.userId,
                    name: data.userName,
                    email: data.userEmail,
                    roleId: data.roleId,
                  });
                },
              },
              userAgent: {
                headerLabel: 'User Agent',
                sortable: true,
              },
              category: {
                headerLabel: 'Category',
                sortable: true,
              },
              oldValue: {
                headerLabel: 'Old Value',
                sortable: true,
                // eslint-disable-next-line
                renderAs: data => (
                  <PreWrap>
                    {JSON.stringify(JSON.parse(data.oldValue), null, 1).slice(
                      0,
                      20
                    )}
                  </PreWrap>
                ),
              },
              newValue: {
                headerLabel: 'New Value',
                sortable: true,
                // eslint-disable-next-line
                renderAs: data => (
                  <PreWrap>
                    {JSON.stringify(JSON.parse(data.newValue), null, 1).slice(
                      0,
                      20
                    )}
                  </PreWrap>
                ),
              },
              actions: {
                // eslint-disable-next-line
                renderAs: data => (
                  <ActionButtons
                    handleView={() => {
                      history.push(`/audit-trail/${data.id}`);
                    }}
                  />
                ),
              },
            }}
          />
        </DataContainer>
      </Page>

      {selectedUser &&
        (!isFetchingSelectedUser &&
        selectedUserData &&
        (selectedUserData.user === null ||
          (selectedUserData.user &&
            (selectedUserData.user.email !== selectedUser.email ||
              selectedUserData.user.name !== selectedUser.name ||
              (selectedUserData.user.role &&
                selectedUserData.user.role.id !== selectedUser.roleId)))) ? (
          <AlertModal
            isOpen
            title="View Account Alert"
            variant="error"
            icon="times-circle"
            header="OH, SNAP!"
            subHeader="Cannot view the Account."
            description="Account is already edited after the notification occured. Go to Users Accounts and search the User."
            handleConfirm={() => {
              setSelectedUser(null);
            }}
            handleClose={() => {
              setSelectedUser(null);
            }}
            confirmText="Okay"
          />
        ) : (
          <UserModal
            isOpen={!isFetchingSelectedUser}
            handleClose={() => {
              setSelectedUser(null);
            }}
          >
            {isFetchingSelectedUser && <FontAwesomeIcon icon="spinner" spin />}
            {!isFetchingSelectedUser &&
              selectedUserData &&
              selectedUserData.user && (
                <>
                  <UserModalIcon icon="user-circle" />
                  <UserModalActiveLabel
                    isActive={selectedUserData.user.isActive}
                  >
                    {selectedUserData.user.isActive ? 'ACTIVE' : 'INACTIVE'}
                  </UserModalActiveLabel>
                  <UserModalName>{selectedUserData.user.name}</UserModalName>
                  <UserModalRole>
                    {selectedUserData.user.role &&
                      selectedUserData.user.role.name.toUpperCase()}
                  </UserModalRole>
                  <UserModalLastLoginRow>
                    <UserModalLastLoginLabel>
                      Last login
                    </UserModalLastLoginLabel>
                    <UserModalLastLogin>
                      {format(
                        selectedUserData.user.loginTime,
                        'MM/DD/YYYY - hh:mm:ss A'
                      )}
                    </UserModalLastLogin>
                  </UserModalLastLoginRow>
                  <PrimaryButton
                    onClick={() => {
                      history.push(
                        '/user-accounts/' + selectedUserData.user.id
                      );
                    }}
                  >
                    View Account
                  </PrimaryButton>
                </>
              )}
          </UserModal>
        ))}
    </>
  );
};

AuditTrail.propTypes = {
  history: PropTypes.object,
};

export default AuditTrail;
