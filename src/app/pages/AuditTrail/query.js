import { gql } from '@apollo/client';

export const GET_AUDITS_INFORMATION = gql`
  query getAuditsInformation(
    $filter: SearchAuditInput
    $pagination: AuditPaginationInput!
  ) {
    audits(filter: $filter, pagination: $pagination) {
      count
      lastKey {
        id
        sortKey
        createdAt
      }
      filteredData {
        id
        userId
        userName
        userEmail
        roleId
        ipAddress
        userAgent
        category
        oldValue
        newValue
        createdAt
      }
    }
  }
`;
