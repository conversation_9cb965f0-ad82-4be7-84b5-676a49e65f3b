import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import styled from 'styled-components';
import { Modal } from '../../components/Modal';
import { ModalContent } from '../../components/Modal/Modal';
import Row from '../../components/Row';

export const UserModal = styled(Modal)`
  ${ModalContent} {
    min-width: 280px;
    padding-top: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
`;

export const UserModalIcon = styled(FontAwesomeIcon)`
  color: #72cbf3;
  border-radius: 50%;
  font-size: 50px;
  margin-bottom: 10px;
`;

export const UserModalActiveLabel = styled.div`
  text-align: center;
  border-radius: 2px;
  padding: 5px 10px;
  color: white;
  margin-bottom: 5px;
  font-size: ${props => props.theme.fontSize.xs};
  background-color: ${props => (props.isActive ? '#2db44b' : '#FB1733')};
`;

export const UserModalEmail = styled.div`
  font-size: ${props => props.theme.fontSize.s};
  font-weight: lighter;
  color: #333333;
  margin-bottom: 20px;
`;

export const UserModalName = styled.div`
  font-size: ${props => props.theme.fontSize.m};
  color: #333333;
  margin-top: 10px;
  margin-bottom: 15px;
`;

export const UserModalRole = styled.div`
  color: #333333;
  font-size: ${props => props.theme.fontSize.s};
  font-weight: lighter;
  color: #333333;
  margin-bottom: 20px;
`;

export const UserModalLastLoginRow = styled(Row)`
  margin-bottom: 20px;
`;

export const UserModalLastLoginLabel = styled.span`
  font-size: ${props => props.theme.fontSize.xs};
  font-weight: 300;
  color: #333333;
`;

export const UserModalLastLogin = styled.span`
  margin-left: 20px;
  font-size: ${props => props.theme.fontSize.xs};
  color: #333333;
`;
