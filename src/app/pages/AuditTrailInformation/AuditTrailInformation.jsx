import PropTypes from 'prop-types';
import React from 'react';
import { useQuery } from '@apollo/client';
import SecondaryButton from '../../components/Button/SecondaryButton';
import DataContainer from '../../components/DataContainer';
import DataHeader from '../../components/DataHeader';
import FormField from '../../components/Form/FormField';
import Header from '../../components/Header';
import {
  ButtonsContainer,
  PageSubsection,
  SubsectionTitle,
} from '../../components/InformationPage';
import Loader from '../../components/Loader';
import Page from '../../components/Page';
import { GET_AUDIT_INFORMATION } from './query';

const AuditTrailInformation = ({ match, history }) => {
  const { data, loading } = useQuery(GET_AUDIT_INFORMATION, {
    fetchPolicy: 'network-only',
    variables: { where: { id: match.params.id } },
  });

  return (
    <Page>
      <Header
        withHome
        path={[
          'System',
          { label: 'Audit Trail', to: '/audit-trail' },
          'Audit Log  Transaction',
        ]}
        title="Audit Log Transaction"
      />
      <DataContainer loading={loading}>
        {loading && <Loader fullPage />}
        {!loading && data && data.audit && (
          <>
            <DataHeader>
              <DataHeader.Title>Audit Log Transaction</DataHeader.Title>
            </DataHeader>
            <SubsectionTitle>INFORMATION</SubsectionTitle>
            <PageSubsection>
              <FormField
                isStatic
                label="IP Address"
                value={data.audit.ipAddress}
                perRow={2}
              />
              <FormField
                isStatic
                label="User Agent"
                value={data.audit.userAgent}
                perRow={2}
              />
              <FormField
                isStatic
                label="Performing User"
                value={data.audit.user && data.audit.user.email}
                perRow={2}
              />
              <FormField
                isStatic
                label="Role"
                value={data.audit.roleName}
                perRow={2}
              />
              <FormField
                isStatic
                label="Channel"
                value={(data.audit.user && data.audit.user.channel) || 'None'}
                perRow={2}
              />
              <FormField
                isStatic
                label="User Agent"
                value={data.audit.userAgent}
                perRow={2}
              />
              <FormField
                isStatic
                label="Old Value"
                value={
                  data.audit.oldValue
                    ? JSON.stringify(JSON.parse(data.audit.oldValue), null, 2)
                    : 'NULL'
                }
                perRow={2}
              />
              <FormField
                isStatic
                label="New Value"
                value={
                  data.audit.newValue
                    ? JSON.stringify(JSON.parse(data.audit.newValue), null, 2)
                    : 'NULL'
                }
                perRow={2}
              />
            </PageSubsection>
            <PageSubsection>
              <ButtonsContainer>
                <SecondaryButton
                  onClick={() => {
                    history.push('/audit-trail');
                  }}
                >
                  Back to All Audit Logs
                </SecondaryButton>
              </ButtonsContainer>
            </PageSubsection>
          </>
        )}
      </DataContainer>
    </Page>
  );
};

AuditTrailInformation.propTypes = {
  match: PropTypes.object,
  history: PropTypes.object,
};

export default AuditTrailInformation;
