import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import React, { useContext, useState } from 'react';
import { useQuery } from '@apollo/client';
import * as Yup from 'yup';
import ActionButtons from '../../components/ActionButtons';
import CreateButton from '../../components/Button/CreateButton';
import FileButton from '../../components/Button/FileButton';
import DataContainer from '../../components/DataContainer';
import DataTable from '../../components/DataTable';
import { FIELD_TYPES } from '../../components/Form/constants';
import { Required } from '../../components/Form/FormField';
import GlobalSearch from '../../components/GlobalSearch';
import Header from '../../components/Header';
import { FormModal } from '../../components/Modal';
import AlertModal from '../../components/Modal/AlertModal';
import Page from '../../components/Page';
import AuthContext from '../../context/AuthContext/AuthContext';
import { useMutation } from '@apollo/client';
import getDiff from '../../utils/getDiff';
import { formatDescription, formatSubHeader } from '../../utils/getLabel';
import sanitize from '../../utils/sanitize';
import { ADD_BANK, DELETE_BANK, EDIT_BANK, IMPORT_BANKS } from './mutation';
import { GET_BANK_ACCOUNTS } from './query';

export const PAYMENT_GATEWAYS = [
  { value: null, label: 'Any' },
  { value: 'ipay88', label: 'iPay88' },
  { value: 'adyen', label: 'Adyen' },
  { value: 'bpi', label: 'BPI' },
  { value: 'xendit', label: 'Xendit' },
  { value: 'gcash', label: 'GCash' },
];

const BankCodeManagement = () => {
  const { permissions } = useContext(AuthContext);

  const [state, setState] = useState({
    isAddBankModalOpen: false,
    isConfirmAddBankModalOpen: false,
    isSuccessAddBankModalOpen: false,
    isFailureAddBankModalOpen: false,

    isLeavingPageWhileAdding: false,

    isEditBankModalOpen: false,
    isConfirmEditBankModalOpen: false,
    isSuccessEditBankModalOpen: false,
    isFailureEditBankModalOpen: false,

    isLeavingPageWhileEditing: false,

    isConfirmDeleteBankModalOpen: false,
    isSuccessDeleteBankModalOpen: false,
    isFailureDeleteBankModalOpen: false,

    isConfirmDeleteBanksModalOpen: false,
    isSuccessDeleteBanksModalOpen: false,

    isSuccessAddBanksModalOpen: false,
    isFailureAddBanksModalOpen: false,

    filter: {},
    pagination: {
      limit: 10,
      start: '',
    },

    selectedBankInitial: null,
    selectedBank: null,

    addBankError: null,
    editBankError: null,
    importFileError: null,
  });

  const { data, loading, refetch } = useQuery(GET_BANK_ACCOUNTS, {
    variables: {
      pagination: state.pagination,
      filter: state.filter,
    },
    fetchPolicy: 'network-only',
  });

  const [addBank, { loading: isAddingBank }] = useMutation(ADD_BANK, {
    onCompleted: () => {
      setState({
        ...state,
        isConfirmAddBankModalOpen: false,
        isSuccessAddBankModalOpen: true,
        isAddBankModalOpen: false,
        pagination: {
          ...state.pagination,
          start: '',
        },
      });
      refetch();
    },
    onError: err => {
      setState({
        ...state,
        addBankError: err.networkError.result
          ? err.networkError.result.message
          : null,
        isConfirmAddBankModalOpen: false,
        isFailureAddBankModalOpen: true,
      });
    },
  });

  const [editBank, { loading: isEditingBank }] = useMutation(EDIT_BANK, {
    onCompleted: () => {
      setState({
        ...state,
        isConfirmEditBankModalOpen: false,
        isSuccessEditBankModalOpen: true,
        isEditBankModalOpen: false,
        selectedBank: null,
      });
      refetch();
    },
    onError: err => {
      setState({
        ...state,
        editBankError: err.networkError.result
          ? err.networkError.result.message
          : null,
        isConfirmEditBankModalOpen: false,
        isFailureEditBankModalOpen: true,
      });
    },
  });

  const [deleteBank, { loading: isDeletingBank }] = useMutation(DELETE_BANK, {
    onCompleted: () => {
      setState({
        ...state,
        isConfirmDeleteBankModalOpen: false,
        isSuccessDeleteBankModalOpen: true,
        selectedBank: null,
      });
      refetch();
    },
    onError: () => {
      setState({
        ...state,
        isConfirmDeleteBankModalOpen: false,
        isFailureDeleteBankModalOpen: true,
      });
    },
  });

  const [importBanks, { loading: isImportingBanks }] = useMutation(
    IMPORT_BANKS,
    {
      onCompleted: async () => {
        refetch();
        setState({ ...state, isSuccessAddBanksModalOpen: true });
      },
      onError: async err => {
        setState({
          ...state,
          isFailureAddBanksModalOpen: true,
          importFileError: err.networkError.result
            ? err.networkError.result.message
            : null,
        });
      },
    }
  );

  return (
    <>
      <Page>
        <Header withHome title="BankCode" path={['BankCode']} />
        <DataContainer>
          <DataTable
            loading={loading}
            data={data && data.banks ? data.banks.filteredData : []}
            headerOptions={
              <>
                <GlobalSearch
                  onSearch={filter => {
                    setState({
                      ...state,
                      filter,
                      pagination: { ...state.pagination, start: '' },
                    });
                  }}
                  fields={[
                    {
                      label: 'Bank Name',
                      name: 'name',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Bank Code',
                      name: 'code',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Payment Gateway',
                      name: 'gateway',
                      type: FIELD_TYPES.SELECT,
                      options: PAYMENT_GATEWAYS,
                    },
                  ]}
                />

                <div style={{ display: 'flex' }}>
                  {(permissions.Bank.export || permissions.Bank.import) && (
                    <FileButton
                      icon="file-csv"
                      iconPosition="left"
                      disabled={loading}
                      loading={isImportingBanks}
                      onImport={
                        permissions.Bank.import &&
                        (file => {
                          importBanks({ variables: { file } });
                        })
                      }
                      onExport={
                        permissions.Bank.export &&
                        (() => {
                          setState({
                            ...state,
                            isConfirmDownloadModalOpen: true,
                          });
                        })
                      }
                      onTemplate={() => {
                        const fileData = {
                          mime: 'text/csv',
                          filename: 'bank-codes-template.csv',
                          contents:
                            '*Bank_Code,*Bank_Name,*Gateway\nBankCode1,BankName1,adyen\nBankCode2,BankName2,ipay88\n',
                        };
                        const blob = new Blob([fileData.contents], {
                          type: fileData.mime,
                        });
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        document.body.appendChild(a);
                        a.download = fileData.filename;
                        a.href = url;
                        a.click();
                        document.body.removeChild(a);
                      }}
                    >
                      CSV{' '}
                      <FontAwesomeIcon
                        style={{ marginLeft: 10 }}
                        icon="angle-down"
                      />
                    </FileButton>
                  )}
                  {permissions.Bank.create && (
                    <CreateButton
                      icon="plus"
                      onClick={() => {
                        setState({ ...state, isAddBankModalOpen: true });
                      }}
                    >
                      Add Bank
                    </CreateButton>
                  )}
                </div>
              </>
            }
            config={{
              name: {
                headerLabel: 'Bank Name',
                sortable: true,
              },
              code: {
                headerLabel: 'Bank Code',
                sortable: true,
              },
              gateway: {
                headerLabel: 'Payment Gateway',
                sortable: true,
                renderAs: data =>
                  PAYMENT_GATEWAYS.find(
                    paymentgw => paymentgw.value === data.gateway
                  ).label,
              },
              actions: {
                renderAs: data => (
                  <ActionButtons
                    disabled={{
                      edit: !permissions.Bank.update,
                      delete: !permissions.Bank.delete,
                    }}
                    handleEdit={() => {
                      setState({
                        ...state,
                        selectedBank: data,
                        selectedBankInitial: data,
                        isEditBankModalOpen: true,
                      });
                    }}
                    handleDelete={() => {
                      setState({
                        ...state,
                        selectedBank: data,
                        isConfirmDeleteBankModalOpen: true,
                      });
                    }}
                  />
                ),
              },
            }}
            pagination={{
              ...state.pagination,
              count: data && data.banks ? data.banks.count : 0,
              cursors: data && data.banks ? data.banks.cursors : [''],
              handleChange: pagination => {
                setState({ ...state, pagination });
              },
            }}
          />
        </DataContainer>
      </Page>

      {state.isAddBankModalOpen && (
        <FormModal
          isOpen={state.isAddBankModalOpen}
          width="600px"
          handleClose={() =>
            setState({ ...state, isLeavingPageWhileAdding: true })
          }
          title="Add New Bank"
          instructions={
            <span>
              To create new Bank, please fill out the required
              <Required>*</Required> fields.
            </span>
          }
          submitText="Create Bank"
          handleSubmit={values => {
            setState({
              ...state,
              selectedBank: values,
              isConfirmAddBankModalOpen: true,
            });
          }}
          fields={{
            name: {
              type: FIELD_TYPES.TEXT,
              label: 'Bank Name',
              placeholder: 'Bank Name',
              validation: Yup.string()
                .required('Please enter value')
                .max(100, 'Must not exceed 100 characters')
                .matches(/[^-\s]/, 'Must not be a whitespace')
                .matches(
                  /^(?![=,@,+,-])(.+)$/,
                  'Input must not begin with this special characters (=,@,+,-)'
                ),
              required: true,
              initialValue: '',
            },
            code: {
              type: FIELD_TYPES.TEXT,
              label: 'Bank Code',
              placeholder: 'Bank Code',
              validation: Yup.string()
                .matches(/^[0-9a-zA-Z]+$/, 'Must be alphanumeric')
                .max(10, 'Must not exceed 10 characters')
                .required('Please enter value'),
              required: true,
              initialValue: '',
            },
            gateway: {
              type: FIELD_TYPES.SELECT,
              options: PAYMENT_GATEWAYS,
              label: 'Payment Gateway',
              placeholder: 'Payment Gateway',
              validation: Yup.string().required('Please select a value'),
              required: true,
              initialValue: '',
            },
          }}
        />
      )}

      <AlertModal
        isOpen={state.isLeavingPageWhileAdding}
        title="New Bank Alert"
        icon="question-circle"
        variant="warn"
        header="SAVE BANK?"
        subHeader="You are about to leave without saving New Bank."
        description="Your entry will be lost if you don't save it"
        handleClose={() =>
          setState({ ...state, isLeavingPageWhileAdding: false })
        }
        cancelText="Discard Entry"
        confirmText="Go Back"
        handleCancel={() => {
          setState({
            ...state,
            isLeavingPageWhileAdding: false,
            isAddBankModalOpen: false,
          });
        }}
        handleConfirm={() => {
          setState({ ...state, isLeavingPageWhileAdding: false });
        }}
      />

      <AlertModal
        isOpen={state.isConfirmAddBankModalOpen}
        title="New Bank Alert"
        icon="exclamation-circle"
        variant="warn"
        header="ARE YOU SURE?"
        subHeader="You are about to create a new Bank."
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        confirmLoading={isAddingBank}
        confirmText="Yes"
        handleConfirm={() => {
          addBank({ variables: { data: sanitize(state.selectedBank) } });
        }}
        handleClose={() => {
          setState({
            ...state,
            isConfirmAddBankModalOpen: false,
          });
        }}
      />

      <AlertModal
        isOpen={state.isSuccessAddBankModalOpen}
        title="New Bank Alert"
        handleClose={() => {
          setState({ ...state, isSuccessAddBankModalOpen: false });
        }}
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader="Bank has been created successfully."
        description="Editing of Bank is now enabled."
        confirmText="Go to All Bank"
        handleConfirm={() => {
          setState({ ...state, isSuccessAddBankModalOpen: false });
        }}
      />

      <AlertModal
        isOpen={state.isFailureAddBankModalOpen}
        title="New Bank Alert"
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader={
          state.addBankError === 'BANK_NAME_ALREADY_EXIST'
            ? 'Current Bank Name already exists.'
            : state.addBankError === 'BANK_CODE_ALREADY_EXIST'
              ? 'Current Bank Code already exists'
              : 'There was a problem on saving New Bank.'
        }
        description="Please go back and try saving it again."
        handleClose={() =>
          setState({ ...state, isFailureAddBankModalOpen: false })
        }
        confirmText="Go Back"
        handleConfirm={() => {
          setState({ ...state, isFailureAddBankModalOpen: false });
        }}
      />

      {state.isEditBankModalOpen && (
        <FormModal
          isOpen={state.isEditBankModalOpen}
          width="600px"
          handleClose={() =>
            setState({ ...state, isLeavingPageWhileEditing: true })
          }
          title="Edit Bank"
          instructions={<span>Fill out the form with new fields.</span>}
          submitText="Update Bank"
          handleSubmit={values => {
            setState({
              ...state,
              selectedBank: values,
              isConfirmEditBankModalOpen: true,
            });
          }}
          fields={{
            name: {
              type: FIELD_TYPES.TEXT,
              label: 'Bank Name',
              placeholder: 'Bank Name',
              validation: Yup.string()
                .max(100, 'Must not exceed 100 characters')
                .required('Please enter value'),
              required: true,
              initialValue: state.selectedBank.name,
              readOnly: true,
            },
            code: {
              type: FIELD_TYPES.TEXT,
              label: 'Bank Code',
              placeholder: 'Bank Code',
              validation: Yup.string()
                .matches(/^[0-9a-zA-Z]+$/, 'Must be alphanumeric')
                .max(10, 'Must not exceed 10 characters')
                .required('Please enter value'),
              required: true,
              initialValue: state.selectedBank.code,
            },
            gateway: {
              type: FIELD_TYPES.SELECT,
              options: PAYMENT_GATEWAYS,
              label: 'Payment Gateway',
              placeholder: 'Payment Gateway',
              validation: Yup.string().required('Please select a value'),
              required: true,
              initialValue: state.selectedBank.gateway,
            },
          }}
        />
      )}

      <AlertModal
        isOpen={state.isLeavingPageWhileEditing}
        title="Edit Bank Alert"
        icon="question-circle"
        variant="warn"
        header="SAVE BANK?"
        subHeader="You are about to leave without saving Modified Bank."
        description="Your entry will be lost if you don't save it"
        handleClose={() =>
          setState({ ...state, isLeavingPageWhileEditing: false })
        }
        cancelText="Discard Entry"
        confirmText="Go Back"
        handleCancel={() => {
          setState({
            ...state,
            isLeavingPageWhileEditing: false,
            isEditBankModalOpen: false,
          });
        }}
        handleConfirm={() => {
          setState({ ...state, isLeavingPageWhileEditing: false });
        }}
      />

      <AlertModal
        isOpen={state.isConfirmEditBankModalOpen}
        title="Edit Bank Alert"
        icon="exclamation-circle"
        variant="warn"
        header="ARE YOU SURE?"
        subHeader="You are about to update an existing Bank."
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        confirmLoading={isEditingBank}
        confirmText="Yes"
        handleConfirm={() => {
          const bankname = state.selectedBank.name;
          const editedvalue = getDiff(
            state.selectedBankInitial,
            state.selectedBank
          );
          editBank({
            variables: {
              data: sanitize(editedvalue),
              where: { name: bankname },
            },
          });
        }}
        handleClose={() => {
          setState({
            ...state,
            isConfirmEditBankModalOpen: false,
          });
        }}
      />

      <AlertModal
        isOpen={state.isSuccessEditBankModalOpen}
        title="Edit Bank Alert"
        handleClose={() => {
          setState({ ...state, isSuccessEditBankModalOpen: false });
        }}
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader="Bank has been updated successfully."
        description="Changes are now reflected on the record."
        confirmText="Go to All Bank"
        handleConfirm={() => {
          setState({ ...state, isSuccessEditBankModalOpen: false });
        }}
      />

      <AlertModal
        isOpen={state.isFailureEditBankModalOpen}
        title="Edit Bank Alert"
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader={
          state.editBankError === 'BANK_CODE_ALREADY_EXIST'
            ? 'Current Bank Code already exists.'
            : 'There was a problem on saving Modified Bank.'
        }
        description="Please go back and try saving it again."
        handleClose={() =>
          setState({ ...state, isFailureEditBankModalOpen: false })
        }
        confirmText="Go Back"
        handleConfirm={() => {
          setState({ ...state, isFailureEditBankModalOpen: false });
        }}
      />

      <AlertModal
        isOpen={state.isConfirmDeleteBankModalOpen}
        title="Delete Bank Alert"
        icon="exclamation-circle"
        variant="warn"
        header="ARE YOU SURE?"
        subHeader="You are about to delete a Bank."
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() =>
          setState({ ...state, isConfirmDeleteBankModalOpen: false })
        }
        selectLabel="Reason"
        options={['No longer in use', 'Others'].map(reason => ({
          value: reason,
          label: reason,
        }))}
        confirmLoading={isDeletingBank}
        confirmText="Yes"
        handleConfirm={value => {
          deleteBank({
            variables: {
              data: { reasonToDelete: value },
              where: { name: state.selectedBank.name },
            },
          });
        }}
      />

      <AlertModal
        isOpen={state.isSuccessDeleteBankModalOpen}
        title="Delete Bank Alert"
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader="Bank has been deleted successfully."
        description="Deleted Bank is now no longer in use."
        handleClose={() =>
          setState({ ...state, isSuccessDeleteBankModalOpen: false })
        }
        confirmText="Go to All Bank"
        handleConfirm={() =>
          setState({ ...state, isSuccessDeleteBankModalOpen: false })
        }
      />

      <AlertModal
        isOpen={state.isFailureDeleteBankModalOpen}
        title="Delete Bank Alert"
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader="There was a problem on deleting Bank."
        description="Please go back and try deleting again."
        handleClose={() =>
          setState({ ...state, isFailureDeleteBankModalOpen: false })
        }
        confirmText="Go Back"
        handleConfirm={() => {
          setState({ ...state, isFailureDeleteBankModalOpen: false });
        }}
      />

      <AlertModal
        isOpen={state.isSuccessAddBanksModalOpen}
        title="Import Banks Alert"
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader="Banks has been created successfully."
        description="Created Banks can now be use."
        handleClose={() =>
          setState({ ...state, isSuccessAddBanksModalOpen: false })
        }
        confirmText="Go Back"
        handleConfirm={() =>
          setState({ ...state, isSuccessAddBanksModalOpen: false })
        }
      />

      <AlertModal
        isOpen={state.isFailureAddBanksModalOpen}
        title="Import Banks Alert"
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader={
          state.importFileError
            ? state.importFileError === 'BANK_ALREADY_EXISTS'
              ? 'The imported file contains data that is already existing'
              : formatSubHeader(state.importFileError)
            : ''
        }
        description={
          state.importFileError ? formatDescription(state.importFileError) : ''
        }
        handleClose={() =>
          setState({ ...state, isFailureAddBanksModalOpen: false })
        }
        confirmText="Go Back"
        handleConfirm={() => {
          setState({ ...state, isFailureAddBanksModalOpen: false });
        }}
      />
    </>
  );
};

export default BankCodeManagement;
