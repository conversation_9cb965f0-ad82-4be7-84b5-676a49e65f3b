import { gql } from '@apollo/client';

export const ADD_BANK = gql`
  mutation createBank($data: CreateBankInput!) {
    createBank(data: $data) {
      name
      code
      gateway
      createdAt
      updatedAt
    }
  }
`;

export const EDIT_BANK = gql`
  mutation editBank($data: UpdateBankInput!, $where: BankPrimary!) {
    updateBank(data: $data, where: $where) {
      name
    }
  }
`;

export const DELETE_BANK = gql`
  mutation deleteBank($data: DeleteBankInput!, $where: BankPrimary!) {
    deleteBank(data: $data, where: $where) {
      name
    }
  }
`;

export const EXPORT_BANKS = gql`
  mutation exportBanks {
    downloadBanks {
      id
      name
      roleName
      roleDescription
      group
      permission
      createdAt
      loginTime
      status
    }
  }
`;

export const IMPORT_BANKS = gql`
  mutation importBanks($file: Upload!) {
    uploadBanks(file: $file) {
      filename
    }
  }
`;
