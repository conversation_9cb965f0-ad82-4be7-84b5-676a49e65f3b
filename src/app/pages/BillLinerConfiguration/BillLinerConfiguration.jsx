import React, { useState, useContext } from 'react';
import * as Yup from 'yup';
import sanitize from '../../utils/sanitize';
import { FIELD_TYPES } from '../../components/Form/constants';
import Page from '../../components/Page';
import Header from '../../components/Header';
import FormField from '../../components/Form/FormField';
import styled from 'styled-components';
import { Required } from '../../components/Form/FormField';
import AlertModal from '../../components/Modal/AlertModal';
import DataContainer from '../../components/DataContainer';
import AuthContext from '../../context/AuthContext/AuthContext';
import { GET_BILL_LINER } from './query';
import CreateButton from '../../components/Button/CreateButton';
import { FormModal } from '../../components/Modal';
import { useMutation } from '@apollo/client';
import { useQuery } from '@apollo/client';
import {
  PageSubsection,
  SubsectionTitle,
} from '../../components/InformationPage';
import ResponsiveContext from '../../context/ResponsiveContext';
import { ADD_BILL_LINER, DELETE_BILL_LINER } from './mutation';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

const ActionFields = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;

  width: ${props => {
    const base = 100 / props.perRow;
    const margin = 20;
    return `calc(${base}% - ${margin}px)`;
  }};

  flex: 1;

  margin-left: ${props => (props.isMobile ? '0px' : '20px')};
  margin-bottom: 20px;
`;

const ActionIcon = styled(FontAwesomeIcon)`
  color: ${props => (props.disabled ? 'gray' : props.color)};
  font-size: 20px;
  cursor: pointer;

  margin-left: 10px;
  &:first-child {
    margin-left: 0;
  }
`;

const ActionButton = styled.button`
  background-color: ${props =>
    props.disabled ? 'gray' : props.backgroundColor};
  color: #fff;
  font-size: ${props => props.theme.fontSize.s};
  cursor: pointer;
  flex: 1;
  border-radius: 5px;
`;

const BillLinerConfiguration = () => {
  const { isMobile } = useContext(ResponsiveContext);
  const { permissions } = useContext(AuthContext);

  const [state, setState] = useState({
    isAddBillModalOpen: false,
    isConfirmAddBillModalOpen: false,
    isSuccessAddBillModalOpen: false,
    isFailureAddBillModalOpen: false,

    isLeavingPageWhileAdding: false,

    isLeavingPageWhileEditing: false,

    isConfirmDeleteBillModalOpen: false,
    isSuccessDeleteBillModalOpen: false,
    isFailureDeleteBillModalOpen: false,

    selectedBillInitial: null,
    selectedBill: null,

    addBillLinerError: null,
    editBillLinerError: null,
  });

  const {
    data,
    loading: billLinerLoading,
    refetch,
  } = useQuery(GET_BILL_LINER, {
    fetchPolicy: 'network-only',
  });

  const [deleteBill, { loading: isDeletingBill }] = useMutation(
    DELETE_BILL_LINER,
    {
      onCompleted: () => {
        setState({
          ...state,
          isConfirmDeleteBillModalOpen: false,
          isSuccessDeleteBillModalOpen: true,
          selectedBill: null,
        });
        refetch();
      },
      onError: () => {
        setState({
          ...state,
          isConfirmDeleteBillModalOpen: false,
          isFailureDeleteBillModalOpen: true,
        });
      },
    }
  );

  const [addBill, { loading: isAddingBill }] = useMutation(ADD_BILL_LINER, {
    onCompleted: () => {
      setState({
        ...state,
        isConfirmAddBillModalOpen: false,
        isSuccessAddBillModalOpen: true,
        isAddBillModalOpen: false,
        selectedBill: null,
      });
      refetch();
    },
    onError: err => {
      setState({
        ...state,
        addBillLinerError: err.networkError.result
          ? err.networkError.result.message
          : null,
        isConfirmAddBillModalOpen: false,
        isFailureAddBillModalOpen: true,
      });
    },
  });

  return (
    <>
      <Page>
        <Header withHome title="Bill Liner Config" path={['BillLiner']} />
        <DataContainer loading={billLinerLoading}>
          <>
            <div style={{ display: 'flex', margin: 20 }}>
              <SubsectionTitle>
                BILL LINER CONFIGURATION SETTINGS
              </SubsectionTitle>

              {permissions.BillLinerConfig.create && (
                <CreateButton
                  icon="plus"
                  onClick={() => {
                    setState({
                      ...state,
                      isAddBillModalOpen: true,
                    });
                  }}
                >
                  Add Bill Liner
                </CreateButton>
              )}
            </div>

            {!billLinerLoading &&
              data &&
              data.listBillLinerConfig &&
              data.listBillLinerConfig.map((billLiner, index) => (
                <PageSubsection key={billLiner.serviceType + '-' + index}>
                  <FormField
                    placeholder=""
                    label="Content"
                    name={`content-${billLiner.content}`}
                    type={FIELD_TYPES.TEXT}
                    value={billLiner.content}
                    readOnly={!state.isEditing}
                    perRow={2.5}
                    required
                  />
                  <FormField
                    placeholder=""
                    label="Adyen Merchant Account"
                    name={`adyenMerchantAccount-${billLiner.adyenMerchantAccount + '-' + index}`}
                    type={FIELD_TYPES.TEXT}
                    value={billLiner.adyenMerchantAccount}
                    readOnly={!state.isEditing}
                    perRow={2.5}
                    required
                  />
                  {permissions.BillLinerConfig.delete && (
                    <ActionFields
                      isMobile={isMobile}
                      fieldsPerRow={2.5}
                      perRow={5}
                    >
                      {isMobile ? (
                        <ActionButton
                          backgroundColor={billLinerLoading ? 'gray' : 'red'}
                          onClick={() => {
                            setState({
                              ...state,
                              isConfirmDeleteBillModalOpen: true,
                              selectedBill: billLiner.content,
                            });
                          }}
                        >
                          Delete
                        </ActionButton>
                      ) : (
                        <ActionIcon
                          icon={billLinerLoading ? 'spinner' : 'minus-circle'}
                          color={billLinerLoading ? 'gray' : 'red'}
                          onClick={() => {
                            setState({
                              ...state,
                              isConfirmDeleteBillModalOpen: true,
                              selectedBill: billLiner.content,
                            });
                          }}
                        />
                      )}
                    </ActionFields>
                  )}
                </PageSubsection>
              ))}
          </>
        </DataContainer>
      </Page>

      <AlertModal
        isOpen={state.isConfirmDeleteBillModalOpen}
        title="Delete Bill Liner Liner Alert"
        icon="exclamation-circle"
        variant="warn"
        header="ARE YOU SURE?"
        subHeader="You are about to delete a Bill Liner."
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() =>
          setState({ ...state, isConfirmDeleteBillModalOpen: false })
        }
        selectLabel="Reason"
        options={['No longer in use', 'Others'].map(reason => ({
          value: reason,
          label: reason,
        }))}
        confirmLoading={isDeletingBill}
        confirmText="Yes"
        handleConfirm={value => {
          deleteBill({
            variables: {
              data: { reasonToDelete: value },
              where: state.selectedBill,
            },
          });
        }}
      />

      <AlertModal
        isOpen={state.isSuccessDeleteBillModalOpen}
        title="Delete Bill Liner Alert"
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader="Bill Liner has been deleted successfully."
        description="Deleted Bill Liner is now no longer in use."
        handleClose={() =>
          setState({ ...state, isSuccessDeleteBillModalOpen: false })
        }
        confirmText="Go to All Bill Liner"
        handleConfirm={() =>
          setState({ ...state, isSuccessDeleteBillModalOpen: false })
        }
      />

      <AlertModal
        isOpen={state.isFailureDeleteBillModalOpen}
        title="Delete Bill Liner Alert"
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader="There was a problem on deleting Bill Liner."
        description="Please go back and try deleting again."
        handleClose={() =>
          setState({ ...state, isFailureDeleteBillModalOpen: false })
        }
        confirmText="Go Back"
        handleConfirm={() => {
          setState({ ...state, isFailureDeleteBillModalOpen: false });
        }}
      />

      {state.isAddBillModalOpen && (
        <FormModal
          isOpen={state.isAddBillModalOpen}
          width="600px"
          handleClose={() =>
            setState({
              ...state,
              isLeavingPageWhileAdding: true,
              isAddBillModalOpen: false,
            })
          }
          title="Add New Bill Liner"
          instructions={
            <span>
              To create new Bill Liner, please fill out the required
              <Required>*</Required> fields.
            </span>
          }
          submitText="Create Bill Liner"
          handleSubmit={values => {
            setState({
              ...state,
              selectedBill: values,
              isConfirmAddBillModalOpen: true,
            });
          }}
          fields={{
            content: {
              type: FIELD_TYPES.TEXT,
              label: 'Content',
              placeholder: 'Content',
              validation: dataValue =>
                Yup.string()
                  .required('Please enter value')
                  .min(1, 'Minimum must be 1 character')
                  .max(50, 'Must not exceed 50 characters')
                  .matches(
                    /^(?!.*[0-9]-[0-9])[A-Za-z0-9]+(-[A-Za-z0-9]+)?$/,
                    'Special character allowed between words is hypen (-) only'
                  )
                  .matches(/[^-\s]/, 'Must not be a whitespace')
                  .matches(
                    /^(?![=,@,+,-])(.+)$/,
                    'Input must not begin with this special characters (=,@,+,-)'
                  )
                  .test(
                    'Term Validation',
                    'This bill liner already exist',
                    () => {
                      const exist =
                        data &&
                        data.listBillLinerConfig.find(
                          data => data.content === dataValue.content
                        );
                      if (exist) {
                        return false;
                      }
                      return true;
                    }
                  ),
              required: true,
              initialValue: '',
            },
            adyenMerchantAccount: {
              type: FIELD_TYPES.TEXT,
              label: 'Adyen Merchant Account',
              placeholder: 'Adyen Merchant Account',
              validation: Yup.string()
                .required('Please enter value')
                .min(1, 'Minimum must be 1 character')
                .max(50, 'Must not exceed 50 characters')
                .matches(
                  /^(?!.*[0-9]-[0-9])[A-Za-z0-9]+(-[A-Za-z0-9]+)?$/,
                  'Special character allowed between words is hypen (-) only'
                )
                .matches(/[^-\s]/, 'Must not be a whitespace')
                .matches(
                  /^(?![=,@,+,-])(.+)$/,
                  'Input must not begin with this special characters (=,@,+,-)'
                ),
              required: true,
              initialValue: '',
            },
          }}
        />
      )}

      <AlertModal
        isOpen={state.isConfirmAddBillModalOpen}
        title="New Bill Liner Alert"
        icon="exclamation-circle"
        variant="warn"
        header="ARE YOU SURE?"
        subHeader="You are about to create a new Bill Liner."
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        confirmLoading={isAddingBill}
        confirmText="Yes"
        handleConfirm={() => {
          addBill({ variables: { data: sanitize(state.selectedBill) } });
        }}
        handleClose={() => {
          setState({
            ...state,
            isConfirmAddBillModalOpen: false,
          });
        }}
      />

      <AlertModal
        isOpen={state.isSuccessAddBillModalOpen}
        title="Add Bill Liner Alert"
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader="Bill Liner has been created successfully."
        description="Created Bill Liner can now be use."
        handleClose={() =>
          setState({ ...state, isSuccessAddBillModalOpen: false })
        }
        confirmText="Go Back"
        handleConfirm={() =>
          setState({ ...state, isSuccessAddBillModalOpen: false })
        }
      />

      <AlertModal
        isOpen={state.isFailureAddBillModalOpen}
        title="New Bill Liner Alert"
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader={
          state.addBillLinerError === 'BILL_LINER_ALREADY_EXIST'
            ? 'Current Bill Liner Content already exists.'
            : 'There was a problem on saving New Bill Liner.'
        }
        description="Please go back and try saving it again."
        handleClose={() =>
          setState({ ...state, isFailureAddBillModalOpen: false })
        }
        confirmText="Go Back"
        handleConfirm={() => {
          setState({ ...state, isFailureAddBillModalOpen: false });
        }}
      />
    </>
  );
};

export default BillLinerConfiguration;
