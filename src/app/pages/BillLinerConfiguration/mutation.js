import { gql } from '@apollo/client';

export const ADD_BILL_LINER = gql`
  mutation createBillLinerConfig($data: CreateBillLinerConfig) {
    createBillLinerConfig(data: $data) {
      content
      adyenMerchantAccount
    }
  }
`;

export const DELETE_BILL_LINER = gql`
  mutation deleteBillLinerConfig($data: DeleteBillLinerInput, $where: String!) {
    deleteBillLinerConfig(data: $data, where: $where) {
      content
      adyenMerchantAccount
    }
  }
`;
