import format from 'date-fns/format';
import PropTypes from 'prop-types';
import React, { useState, useEffect } from 'react';
import PrimaryButton from '../../../components/Button/PrimaryButton';
import SecondaryButton from '../../../components/Button/SecondaryButton';
import DataContainer from '../../../components/DataContainer';
import DataTable from '../../../components/DataTable';
import { FIELD_TYPES } from '../../../components/Form/constants';
import GlobalSearch from '../../../components/GlobalSearch';
import Header from '../../../components/Header';
import { AlertModal, FormModal } from '../../../components/Modal';
import {
  AlertModalIcon,
  ApprovalButtonContainer,
  ListHistoryContainer,
} from '../styled';
import Page from '../../../components/Page';
import { useMutation } from '@apollo/client';
import sanitize from '../../../utils/sanitize';
import { useQuery } from '@apollo/client';
import { UPDATE_REFUND } from './mutation';
import useQueryReportSeries from '../../../hooks/useQueryReportSeries';
import { ColumnVisibilityDropdown, ResponsiveRow } from '../../Reports/styled';
import {
  GET_CARDREFUNDAPPROVAL,
  GET_CARDREFUNDHISTORY,
  REPORT_PATH,
  GET_CHANNEL_OPTIONS,
} from './query';
import { numberWithCommas } from '../../../components/GlobalSearch/utils';

const CardRefundApproval = ({ history }) => {
  const [state, setState] = useState({
    isApproveRefundModalOpen: false,
    isRejectRefundModalOpen: false,
    isConfirmApproveRefundModalOpen: false,
    isSuccesApproveRefundOpen: false,
    isFailureApproveRefundOpen: false,
    approve: false,

    referenceValue: null,
    amountPaid: 0,
    refundAmount: 0,
    refundReason: null,
    approverRemarks: '',

    isLeavingPageWhileAdding: false,
    filter: {},
    pagination: {
      startKeys: '',
      limit: 1000,
    },

    selectedApproveRefund: null,
    nextLocation: null,
    approveRefundError: null,
  });

  const {
    pagination,
    setNewPagination,
    setFilter,
    data,
    loading,
    page,
    setPage,
    isLastPage,
    loadData,
    refetch,
    loadDataOnError,
  } = useQueryReportSeries(GET_CARDREFUNDAPPROVAL, REPORT_PATH, {
    pagination: {
      startKeys: '',
      limit: 10,
    },
  });

  const { data: dataForm } = useQuery(GET_CARDREFUNDHISTORY, {
    variables: {
      pagination: state.pagination,
      filter: state.filter,
    },
    fetchPolicy: 'network-only',
  });

  const { data: channelData, loading: isLoadingChannels } = useQuery(
    GET_CHANNEL_OPTIONS,
    {
      fetchPolicy: 'network-only',
    }
  );

  const channelOptions =
    !isLoadingChannels && channelData
      ? channelData.channelsLoose.map(channel => ({
          value: channel.id,
          label: channel.name,
        }))
      : [];

  channelOptions.unshift({
    value: null,
    label: 'Any',
  });

  useEffect(() => {
    if (isApprovingRefund && !state.approve && !state.requestRefundError) {
      loadData();
    } else if (
      isApprovingRefund &&
      state.approve &&
      !state.requestRefundError
    ) {
      loadData();
    }
  });

  useEffect(() => {
    const unblock = history.block(location => {
      if (state.isLeavingPageWhileAdding || !state.isRequestRefundModalOpen) {
        return true;
      }
      setState({
        ...state,
        nextLocation: location,
        isLeavingPageWhileAdding: true,
      });
      return false;
    });

    return () => {
      unblock();
    };
  }, [state.isLeavingPageWhileAdding, state.isRequestRefundModalOpen]);

  const [updateRefund, { loading: isApprovingRefund }] = useMutation(
    UPDATE_REFUND,
    {
      onCompleted: () => {
        setState({
          ...state,
          isConfirmApproveRefundModalOpen: false,
          isSuccesApproveRefundOpen: true,
          isApproveRefundModalOpen: false,
          isRejectRefundModalOpen: false,
          approverRemarks: '',
          filter: {},
        });
        refetch();
      },
      onError: err => {
        setState({
          ...state,
          requestRefundError: err.networkError.result
            ? err.networkError.result.message
            : null,
          isConfirmApproveRefundModalOpen: false,
          isFailureApproveRefundOpen: true,
        });
        loadDataOnError();
      },
    }
  );

  const tableConfig = {
    reference: {
      headerLabel: 'Reference No.',
      sortable: true,
    },
    accountNumber: {
      headerLabel: 'Account No.',
      sortable: true,
    },
    channelName: {
      headerLabel: 'Channel',
      sortable: true,
    },
    timestamp: {
      headerLabel: 'Transaction Date',
      sortable: true,
      renderAs: data => format(data.timestamp, 'MM/DD/YYYY - hh:mm:ss A'),
    },
    postedTimestamp: {
      headerLabel: 'Date Posted/Authorised',
      sortable: true,
      renderAs: data => format(data.postedTimestamp, 'MM/DD/YYYY - hh:mm:ss A'),
    },
    amountValue: {
      headerLabel: 'Amount',
      sortable: true,
      renderAs: data =>
        numberWithCommas(
          !data.finalAmount ? data.amountValue : data.finalAmount,
          2
        ),
    },
    status: {
      headerLabel: 'Payment Status',
      sortable: true,
    },
    refundReason: {
      headerLabel: 'Refund Reason',
      sortable: true,
    },
    refundAmount: {
      headerLabel: 'Refund Amount',
      sortable: true,
      renderAs: data =>
        data.refundAmount === null
          ? ''
          : numberWithCommas(data.refundAmount, 2),
    },
    actions: {
      headerLabel: 'Approve / Reject',
      renderAs: data => (
        <ApprovalButtonContainer>
          <SecondaryButton
            onClick={() => {
              setState({
                referenceValue: data.reference,
                amountPaid: !data.finalAmount
                  ? data.amountValue
                  : data.finalAmount,
                refundAmount: data.refundAmount,
                refundReason: data.refundReason,
                filter: {
                  reference: data.reference,
                },
                pagination: {
                  startKeys: '',
                  limit: 1000,
                },
                isApproveRefundModalOpen: true,
                isRejectRefundModalOpen: false,
                approve: true,
              });
            }}
          >
            Approve
          </SecondaryButton>
          <PrimaryButton
            onClick={() =>
              setState({
                referenceValue: data.reference,
                amountPaid: !data.finalAmount
                  ? data.amountValue
                  : data.finalAmount,
                refundAmount: data.refundAmount,
                refundReason: data.refundReason,
                filter: {
                  reference: data.reference,
                },
                pagination: {
                  startKeys: '',
                  limit: 1000,
                },
                isRejectRefundModalOpen: true,
                isApproveRefundModalOpen: false,
              })
            }
          >
            Reject
          </PrimaryButton>
        </ApprovalButtonContainer>
      ),
    },
  };

  const [visibleColumns, setVisibleColumns] = useState(
    Object.keys(tableConfig)
  );

  return (
    <>
      <Page>
        <Header
          withHome
          title="Adyen Refund Approval"
          path={['Adyen Refund', 'Adyen Refund Approval']}
        />
        <DataContainer>
          <DataTable
            loading={loading}
            data={data}
            minCellWidth={200}
            headerOptions={
              <>
                <GlobalSearch
                  onSearch={filter => {
                    const newPagination = {
                      ...pagination,
                      startKeys: '',
                    };
                    setFilter(filter);
                    setNewPagination(newPagination);
                  }}
                  fields={[
                    {
                      label: 'Reference No.',
                      name: 'reference',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Channel',
                      name: 'channelId',
                      type: FIELD_TYPES.SELECT,
                      options: channelOptions,
                      isKey: true,
                    },
                    {
                      label: 'Status',
                      name: 'status',
                      type: FIELD_TYPES.SELECT,
                      options: [
                        { value: null, label: 'Any' },
                        { value: 'POSTED', label: 'PAYMENT_POSTED' },
                        { value: 'POSTED_LUKE', label: 'PAYMENT_POSTED_LUKE' },
                        {
                          value: 'ADYEN_AUTHORISED',
                          label: 'PAYMENT_AUTHORIZED',
                        },
                      ],
                    },
                    {
                      label: 'Date Range',
                      name: 'createdAt',
                      type: FIELD_TYPES.DATE_RANGE,
                    },
                  ]}
                  placeholder="Search entries here..."
                />
                <ResponsiveRow>
                  <ColumnVisibilityDropdown
                    multi
                    showMulti={false}
                    placeholder="Visible Columns"
                    value={visibleColumns}
                    options={Object.keys(tableConfig).map(key => ({
                      value: key,
                      label: tableConfig[key].headerLabel,
                    }))}
                    onChange={setVisibleColumns}
                  />
                </ResponsiveRow>
              </>
            }
            config={Object.keys(tableConfig).reduce((config, key) => {
              if (visibleColumns.includes(key)) {
                config[key] = tableConfig[key];
              }
              return config;
            }, {})}
            pagination={{
              ...pagination,
              startKeys: pagination.startKeys,
              withStartKeys: true,
              count: 0,
              cursors: [],
              handleChange: setNewPagination,
            }}
            series={{ page, setPage, isLastPage }}
          />
        </DataContainer>
      </Page>
      {state.isApproveRefundModalOpen ? (
        <FormModal
          isOpen={state.isApproveRefundModalOpen}
          width="450px"
          handleClose={() =>
            setState({
              ...state,
              isLeavingPageWhileAdding: true,
              isApproveRefundModalOpen: false,
            })
          }
          title="REFUND CONFIRMATION"
          instructions={
            <span>
              <center>
                <AlertModalIcon icon="exclamation-circle" variant="warn" />
              </center>
              <b style={{ fontSize: 20 }}>
                <center>ARE YOU SURE?</center>
              </b>
              <center style={{ fontSize: 17 }}>
                You are about to submit a transaction.
              </center>
              <b style={{ fontSize: 15 }}>
                <br />
                <center>
                  Amount Paid: {numberWithCommas(state.amountPaid, 2)}
                </center>
                <center>
                  {' '}
                  Refund Amount: {numberWithCommas(state.refundAmount, 2)}
                </center>
                <center>Refund Reason: {state.refundReason}</center>
              </b>
              <br />
              <span style={{ marginLeft: 20 }}>History</span>
              <span>
                {dataForm &&
                  dataForm.cardRefundApprovalModuleHistory &&
                  dataForm.cardRefundApprovalModuleHistory.filteredData
                    .slice(0, 3)
                    .map((data, index) => (
                      <ListHistoryContainer key={index}>
                        <li style={{ fontSize: 13 }}>
                          {format(
                            data.refundRejectedTimestamp,
                            'MM/DD/YYYY - hh:mm:ss A'
                          )}
                          {''} : {data.refundReason}
                        </li>
                      </ListHistoryContainer>
                    ))}
              </span>
            </span>
          }
          submitText="Yes"
          changeListener={(name, values, setValues) => {
            if (name === 'channelName') {
              let { reference, action, approverRemarks } = values;

              setValues({
                ...values,
                reference,
                action,
                approverRemarks,
              });
            }
          }}
          handleSubmit={values => {
            setState({
              ...state,
              selectedApproveRefund: values,
              isConfirmApproveRefundModalOpen: true,
            });
          }}
          fields={{
            reference: {
              initialValue: state.referenceValue,
            },
            action: {
              initialValue: 'approve',
            },
            approverRemarks: {
              type: FIELD_TYPES.TEXT,
              label: 'Approver Remarks',
              placeholder: '',
              initialValue: state.approverRemarks,
              perRow: 1.1,
            },
          }}
        />
      ) : (
        <FormModal
          isOpen={state.isRejectRefundModalOpen}
          width="450px"
          handleClose={() =>
            setState({
              ...state,
              isLeavingPageWhileAdding: true,
              isRejectRefundModalOpen: false,
            })
          }
          title="REFUND CONFIRMATION"
          instructions={
            <span>
              <center>
                <AlertModalIcon icon="exclamation-circle" variant="warn" />
              </center>
              <b style={{ fontSize: 20 }}>
                <center>ARE YOU SURE?</center>
              </b>
              <center style={{ fontSize: 17 }}>
                You are about to submit a transaction.
              </center>
              <b style={{ fontSize: 15 }}>
                <br />
                <center>
                  Amount Paid: {numberWithCommas(state.amountPaid, 2)}
                </center>
                <center>
                  {' '}
                  Refund Amount: {numberWithCommas(state.refundAmount, 2)}
                </center>
                <center>Refund Reason: {state.refundReason}</center>
              </b>
              <br />
              <span style={{ marginLeft: 20 }}>History</span>
              <span>
                {dataForm &&
                  dataForm.cardRefundApprovalModuleHistory &&
                  dataForm.cardRefundApprovalModuleHistory.filteredData
                    .slice(0, 3)
                    .map((data, index) => (
                      <ListHistoryContainer key={index}>
                        <li style={{ fontSize: 13 }}>
                          {format(
                            data.refundRejectedTimestamp,
                            'MM/DD/YYYY - hh:mm:ss A'
                          )}
                          {''} : {data.refundReason}
                        </li>
                      </ListHistoryContainer>
                    ))}
              </span>
            </span>
          }
          submitText="Yes"
          changeListener={(name, values, setValues) => {
            if (name === 'channelName') {
              let { reference, action, approverRemarks } = values;

              setValues({
                ...values,
                reference,
                action,
                approverRemarks,
              });
            }
          }}
          handleSubmit={values => {
            setState({
              ...state,
              selectedApproveRefund: values,
              isConfirmApproveRefundModalOpen: true,
            });
          }}
          fields={{
            reference: {
              initialValue: state.referenceValue,
            },
            action: {
              initialValue: 'reject',
            },
            approverRemarks: {
              type: FIELD_TYPES.TEXT,
              label: 'Approver Remarks',
              placeholder: '',
              initialValue: state.approverRemarks,
              perRow: 1.1,
            },
          }}
        />
      )}

      <AlertModal
        isOpen={state.isConfirmApproveRefundModalOpen}
        title="Approval Refund Alert"
        icon="exclamation-circle"
        variant="warn"
        header="ARE YOU SURE?"
        subHeader={`You are about to ${state.isApproveRefundModalOpen ? 'Approve' : 'Reject'} a Refund.`}
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        confirmLoading={isApprovingRefund}
        confirmText="Yes"
        handleConfirm={async () => {
          updateRefund({
            variables: {
              data: sanitize(state.selectedApproveRefund),
            },
          });
        }}
        handleClose={() => {
          setState({
            ...state,
            isConfirmApproveRefundModalOpen: false,
          });
        }}
      />

      <AlertModal
        isOpen={state.isSuccesApproveRefundOpen}
        title={
          state.approve ? 'Approve Refund Success' : 'Reject Refund Success'
        }
        handleClose={() => {
          setState({ ...state, isSuccesApproveRefundOpen: false });
        }}
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader={
          state.approve
            ? 'Refund has been approved successfully.'
            : 'Refund has been rejected successfully.'
        }
        description=""
        confirmText="Go to Refund Approval Module"
        handleConfirm={() => {
          setState({ ...state, isSuccesApproveRefundOpen: false });
        }}
      />

      <AlertModal
        isOpen={state.isFailureApproveRefundOpen}
        title="Request Refund Alert"
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader={`Refund ${state.approve ? 'approve' : 'reject'} error!`}
        description={
          state.approveRefundError
            ? 'Please refresh the page to see updated results.'
            : `Please go back and try to ${state.approve ? 'approve' : 'reject'} again.` ||
              'Something went wrong!'
        }
        handleClose={() =>
          setState({ ...state, isFailureApproveRefundOpen: false })
        }
        confirmText="Go Back"
        handleConfirm={() => {
          setState({ ...state, isFailureApproveRefundOpen: false });
        }}
      />
    </>
  );
};

CardRefundApproval.propTypes = {
  history: PropTypes.object,
};

export default CardRefundApproval;
