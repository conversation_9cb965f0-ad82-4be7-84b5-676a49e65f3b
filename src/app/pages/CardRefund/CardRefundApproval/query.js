import { gql } from '@apollo/client';

export const GET_CARDREFUNDAPPROVAL = gql`
  query getRefundApprovalModule(
    $filter: SearchCardRefundInput!
    $pagination: PaginationReportInput!
  ) {
    cardRefundApprovalModule(filter: $filter, pagination: $pagination) {
      lastKey
      filteredData {
        reference
        accountNumber
        channelName
        timestamp
        postedTimestamp
        amountValue
        status
        refundAmount
        refundReason
        refundApprovalStatus
        finalAmount
      }
    }
  }
`;

export const GET_CARDREFUNDHISTORY = gql`
  query getCardRefundApprovalModuleHistory(
    $filter: SearchCardRefundInput!
    $pagination: PaginationReportInput!
  ) {
    cardRefundApprovalModuleHistory(filter: $filter, pagination: $pagination) {
      lastKey
      filteredData {
        reference
        accountNumber
        channelName
        timestamp
        postedTimestamp
        amountValue
        status
        refundAmount
        refundReason
        refundApprovalStatus
        refundRejectedTimestamp
      }
    }
  }
`;

export const GET_CHANNEL_OPTIONS = gql`
  query getChannelOptions {
    channelsLoose {
      id
      name
      channelId
    }
  }
`;

export const GET_REFUND_REASON = gql`
  query getCardRefundReason {
    getCardRefundReason {
      reason
    }
  }
`;

export const REPORT_PATH = 'cardRefundApprovalModule';
