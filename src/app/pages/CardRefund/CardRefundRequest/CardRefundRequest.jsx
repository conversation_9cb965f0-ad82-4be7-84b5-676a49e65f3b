import format from 'date-fns/format';
import PropTypes from 'prop-types';
import * as Yup from 'yup';
import React, { useState, useEffect } from 'react';
import PrimaryButton from '../../../components/Button/PrimaryButton';
import DataContainer from '../../../components/DataContainer';
import DataTable from '../../../components/DataTable';
import { FIELD_TYPES } from '../../../components/Form/constants';
import GlobalSearch from '../../../components/GlobalSearch';
import Header from '../../../components/Header';
import { AlertModal, FormModal } from '../../../components/Modal';
import { AlertModalIcon, RequestButtonContainer } from '../styled';
import Page from '../../../components/Page';
import { useMutation } from '@apollo/client';
import sanitize from '../../../utils/sanitize';
import { useQuery } from '@apollo/client';
import { REQUEST_REFUND } from './mutation';
import useQueryReportSeries from '../../../hooks/useQueryReportSeries';
import { ColumnVisibilityDropdown, ResponsiveRow } from '../../Reports/styled';
import {
  GET_CARDREFUNDREQUEST,
  REPORT_PATH,
  GET_CHANNEL_OPTIONS,
  GET_REFUND_REASON,
} from './query';
import { numberWithCommas } from '../../../components/GlobalSearch/utils';

const CardRefundRequest = ({ history }) => {
  const [state, setState] = useState({
    isRequestRefundModalOpen: false,
    isConfirmRequestRefundModalOpen: false,
    isSuccesRequestRefundOpen: false,
    isFailureRequestRefundOpen: false,

    days: 0,

    referenceValue: null,
    amountPaid: 0,

    isLeavingPageWhileAdding: false,

    selectedRequestRefund: null,
    nextLocation: null,
    requestRefundError: null,
  });

  const {
    pagination,
    setNewPagination,
    setFilter,
    data,
    loading,
    page,
    setPage,
    isLastPage,
    refetch,
    loadData,
    loadDataOnError,
  } = useQueryReportSeries(GET_CARDREFUNDREQUEST, REPORT_PATH, {
    pagination: {
      startKeys: '',
      limit: 10,
    },
  });

  const { data: channelData, loading: isLoadingChannels } = useQuery(
    GET_CHANNEL_OPTIONS,
    {
      fetchPolicy: 'network-only',
    }
  );

  const { data: refundReasonData, loading: refundReasonLoading } = useQuery(
    GET_REFUND_REASON,
    {
      fetchPolicy: 'network-only',
    }
  );

  useEffect(() => {
    if (isRequestingRefund) {
      loadData();
    }
  });

  const channelOptions =
    !isLoadingChannels && channelData && channelData.channelsLoose !== undefined
      ? channelData.channelsLoose.map(channel => ({
          value: channel.id,
          label: channel.name,
        }))
      : [];

  channelOptions.unshift({
    value: null,
    label: 'Any',
  });

  const refundReasonOptions =
    !refundReasonLoading &&
    refundReasonData &&
    refundReasonData.getCardRefundReason !== undefined
      ? refundReasonData.getCardRefundReason.map(reason => ({
          value: reason.reason,
          label: reason.reason,
        }))
      : [];

  refundReasonOptions.unshift({
    value: null,
    label: '-Select-',
  });

  refundReasonOptions.push({
    value: 'Others',
    label: 'Others',
  });

  useEffect(() => {
    const unblock = history.block(location => {
      if (state.isLeavingPageWhileAdding || !state.isRequestRefundModalOpen) {
        return true;
      }
      setState({
        ...state,
        nextLocation: location,
        isLeavingPageWhileAdding: true,
      });
      return false;
    });

    return () => {
      unblock();
    };
  }, [state.isLeavingPageWhileAdding, state.isRequestRefundModalOpen]);

  const [requestRefund, { loading: isRequestingRefund }] = useMutation(
    REQUEST_REFUND,
    {
      onCompleted: () => {
        setState({
          ...state,
          isConfirmRequestRefundModalOpen: false,
          isSuccesRequestRefundOpen: true,
          isRequestRefundModalOpen: false,
        });
        refetch();
      },
      onError: err => {
        setState({
          ...state,
          requestRefundError: err.networkError.result
            ? err.networkError.result.message
            : null,
          isConfirmRequestRefundModalOpen: false,
          isFailureRequestRefundOpen: true,
        });
        loadDataOnError();
      },
    }
  );

  const tableConfig = {
    reference: {
      headerLabel: 'Reference No.',
      sortable: true,
    },
    accountNumber: {
      headerLabel: 'Account No.',
      sortable: true,
    },
    channelName: {
      headerLabel: 'Channel',
      sortable: true,
    },
    timestamp: {
      headerLabel: 'Transaction Date',
      sortable: true,
      renderAs: data => format(data.timestamp, 'MM/DD/YYYY - hh:mm:ss A'),
    },
    postedTimestamp: {
      headerLabel: 'Date Posted/Authorised',
      sortable: true,
      renderAs: data => format(data.postedTimestamp, 'MM/DD/YYYY - hh:mm:ss A'),
    },
    amountValue: {
      headerLabel: 'Amount Paid',
      sortable: true,
      renderAs: data =>
        numberWithCommas(
          !data.finalAmount ? data.amountValue : data.finalAmount,
          2
        ),
    },
    status: {
      headerLabel: 'Payment Status',
      sortable: true,
    },
    refundStatus: {
      headerLabel: 'Refund Status',
      sortable: true,
    },
    refundAmount: {
      headerLabel: 'Refund Amount',
      sortable: true,
      renderAs: data =>
        data.refundAmount === null
          ? ''
          : numberWithCommas(data.refundAmount, 2),
    },
    refundReason: {
      headerLabel: 'Refund Reason',
      sortable: true,
    },
    refundDate: {
      headerLabel: 'Refund Date',
      sortable: true,
      renderAs: data =>
        data.refundDate
          ? format(data.refundDate, 'MM/DD/YYYY - hh:mm:ss A')
          : '',
    },
    refundApprovalStatus: {
      renderAs: data => (
        <RequestButtonContainer>
          <PrimaryButton
            onClick={() =>
              setState({
                referenceValue: data.reference,
                amountPaid: !data.finalAmount
                  ? data.amountValue
                  : data.finalAmount,
                isRequestRefundModalOpen: true,
              })
            }
            disabled={
              data.refundApprovalStatus === 'For Approval' ||
              data.refundApprovalStatus === 'Approved'
            }
          >
            {(data.refundApprovalStatus === 'For Approval' && 'For Approval') ||
              (data.refundApprovalStatus === 'Approved' && 'Approved') ||
              'Request Refund'}
          </PrimaryButton>
        </RequestButtonContainer>
      ),
    },
  };

  const [visibleColumns, setVisibleColumns] = useState(
    Object.keys(tableConfig)
  );

  return (
    <>
      <Page>
        <Header
          withHome
          title="Adyen Refund Request"
          path={['Adyen Refund', 'Adyen Refund Request']}
        />
        <DataContainer>
          <DataTable
            loading={loading}
            data={data}
            minCellWidth={200}
            headerOptions={
              <>
                <GlobalSearch
                  onSearch={filter => {
                    const newPagination = {
                      ...pagination,
                      startKeys: '',
                    };
                    setFilter(filter);
                    setNewPagination(newPagination);
                  }}
                  fields={[
                    {
                      label: 'Reference No.',
                      name: 'reference',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Channel',
                      name: 'channelId',
                      type: FIELD_TYPES.SELECT,
                      options: channelOptions,
                      isKey: true,
                    },
                    {
                      label: 'Refund Status',
                      name: 'refundStatus',
                      type: FIELD_TYPES.SELECT,
                      options: [
                        { value: null, label: 'Any' },
                        { value: 'PartialRefund', label: 'Partial Refund' },
                        { value: 'FullRefund', label: 'Full Refund' },
                        {
                          value: 'ForApproval',
                          label: 'For Approval',
                        },
                        {
                          value: 'Processing',
                          label: 'Processing',
                        },
                        {
                          value: 'Rejected',
                          label: 'Rejected',
                        },
                      ],
                    },
                    {
                      label: 'Payment Status',
                      name: 'status',
                      type: FIELD_TYPES.SELECT,
                      options: [
                        { value: null, label: 'Any' },
                        { value: 'POSTED', label: 'PAYMENT_POSTED' },
                        { value: 'POSTED_LUKE', label: 'PAYMENT_POSTED_LUKE' },
                        {
                          value: 'ADYEN_AUTHORISED',
                          label: 'PAYMENT_AUTHORIZED',
                        },
                      ],
                    },
                    {
                      label: 'Date Range',
                      name: 'createdAt',
                      type: FIELD_TYPES.DATE_RANGE,
                    },
                  ]}
                  placeholder="Search entries here..."
                />
                <ResponsiveRow>
                  <ColumnVisibilityDropdown
                    multi
                    showMulti={false}
                    placeholder="Visible Columns"
                    value={visibleColumns}
                    options={Object.keys(tableConfig).map(key => ({
                      value: key,
                      label: tableConfig[key].headerLabel,
                    }))}
                    onChange={setVisibleColumns}
                  />
                </ResponsiveRow>
              </>
            }
            config={Object.keys(tableConfig).reduce((config, key) => {
              if (visibleColumns.includes(key)) {
                config[key] = tableConfig[key];
              }
              return config;
            }, {})}
            pagination={{
              ...pagination,
              startKeys: pagination.startKeys,
              withStartKeys: true,
              count: 0,
              cursors: [],
              handleChange: setNewPagination,
            }}
            series={{ page, setPage, isLastPage }}
          />
        </DataContainer>
      </Page>
      {state.isRequestRefundModalOpen && (
        <FormModal
          isOpen={state.isRequestRefundModalOpen}
          width="450px"
          handleClose={() =>
            setState({
              ...state,
              isLeavingPageWhileAdding: true,
              isRequestRefundModalOpen: false,
            })
          }
          title="REFUND CONFIRMATION"
          instructions={
            <span>
              <center>
                <AlertModalIcon icon="exclamation-circle" variant="warn" />
              </center>
              <b style={{ fontSize: 20 }}>
                <center>ARE YOU SURE?</center>
              </b>
              <center style={{ fontSize: 17 }}>
                You are about to submit a transaction.
              </center>
              <b style={{ fontSize: 15 }}>
                <br />
                <center>
                  Paid Amount: {numberWithCommas(state.amountPaid, 2)}
                </center>
              </b>
            </span>
          }
          submitText="Yes"
          changeListener={(name, data, values, setValues) => {
            if (name === 'channelName') {
              let { reference, refundAmount, refundReason } = values;

              setValues({
                ...values,
                reference,
                refundAmount,
                refundReason,
              });
            }
          }}
          handleSubmit={values => {
            setState({
              ...state,
              selectedRequestRefund: {
                reference: values.reference,
                refundAmount: values.refundAmount,
                refundReason:
                  values.refundReason === 'Others'
                    ? values.others
                    : values.refundReason,
              },
              isConfirmRequestRefundModalOpen: true,
            });
          }}
          fields={{
            reference: {
              initialValue: state.referenceValue,
            },
            refundAmount: {
              type: FIELD_TYPES.TEXT,
              label: 'Refund Amount',
              placeholder: 'Refund Amount',
              validation: Yup.string()
                .matches(
                  /^\d*(\.\d+)?$/,
                  'Invalid input! Allowed characters are positive number or number with decimal placed only'
                )
                .required('Please enter a value')
                .test(
                  'AmountValidation',
                  'Value should not be less than 0 and not greater than the amount paid',
                  value => {
                    const amount = state.amountPaid;
                    if (value > Number(amount)) {
                      return false;
                    }
                    return true;
                  }
                ),
              required: true,
              perRow: 1.1,
            },
            refundReason: {
              type: FIELD_TYPES.SELECT,
              label: 'Refund Reason',
              placeholder: 'Refund Reason',
              options: refundReasonOptions,
              validation: Yup.string()
                .nullable()
                .required('Please select a value'),
              required: true,
              perRow: 1.1,
            },
            others: {
              typeWhen: data =>
                data.refundReason ===
                refundReasonOptions[refundReasonOptions.length - 1].value
                  ? FIELD_TYPES.TEXT
                  : '',
              labelWhen: data =>
                data.refundReason ===
                refundReasonOptions[refundReasonOptions.length - 1].value
                  ? 'Others'
                  : '',
              placeholder: '',
              disableWhen: data =>
                !(
                  data.refundReason ===
                  refundReasonOptions[refundReasonOptions.length - 1].value
                ),
              requiredWhen: data =>
                data.refundReason ===
                refundReasonOptions[refundReasonOptions.length - 1].value,
              validation: data =>
                data.refundReason ===
                refundReasonOptions[refundReasonOptions.length - 1].value
                  ? Yup.string()
                      .min(3, 'Minimum should be 3 characters')
                      .max(50, 'Must not exceed 50 characters')
                      .required('Please enter value')
                  : Yup.string().nullable(),
              perRow: 1.1,
              initialValue: '',
            },
          }}
        />
      )}

      <AlertModal
        isOpen={state.isConfirmRequestRefundModalOpen}
        title="Request Refund Alert"
        icon="exclamation-circle"
        variant="warn"
        header="ARE YOU SURE?"
        subHeader="You are about to Request a Refund."
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        confirmLoading={isRequestingRefund}
        confirmText="Yes"
        handleConfirm={() => {
          requestRefund({
            variables: {
              data: sanitize(state.selectedRequestRefund),
            },
          });
        }}
        handleClose={() => {
          setState({
            ...state,
            isConfirmRequestRefundModalOpen: false,
          });
        }}
      />

      <AlertModal
        isOpen={state.isSuccesRequestRefundOpen}
        title="Request Refund Success"
        handleClose={() => {
          setState({ ...state, isSuccesRequestRefundOpen: false });
        }}
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader="Refund has been requested successfully."
        description=""
        confirmText="Go to Refund Request Module"
        handleConfirm={() => {
          setState({ ...state, isSuccesRequestRefundOpen: false });
        }}
      />

      <AlertModal
        isOpen={state.isFailureRequestRefundOpen}
        title="Request Refund Alert"
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader={
          (state.requestRefundError === 'Request for refund already filed' &&
            'Request for refund already filed.') ||
          (state.requestRefundError === 'Request for refund already approved' &&
            'Request for refund already approved') ||
          (state.requestRefundError ===
            'Refund Amount is greater than Payment amount' &&
            'Refund Amount is greater than Payment amount') ||
          state.requestRefundError === ''
        }
        description={
          (state.requestRefundError === 'Request for refund already filed' &&
            'Request for refund already filed.') ||
          (state.requestRefundError === 'Request for refund already approved' &&
            'Request for refund already approved')
            ? 'Please refresh the page to see updated results.'
            : 'Please go back and try requesting it again.'
        }
        handleClose={() =>
          setState({ ...state, isFailureRequestRefundOpen: false })
        }
        confirmText="Go Back"
        handleConfirm={() => {
          setState({ ...state, isFailureRequestRefundOpen: false });
        }}
      />
    </>
  );
};

CardRefundRequest.propTypes = {
  history: PropTypes.object,
};

export default CardRefundRequest;
