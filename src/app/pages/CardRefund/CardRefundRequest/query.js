import { gql } from '@apollo/client';

export const GET_CARDREFUNDREQUEST = gql`
  query getCardRefundRequestModule(
    $filter: SearchCardRefundInput!
    $pagination: PaginationReportInput!
  ) {
    cardRefundRequestModule(filter: $filter, pagination: $pagination) {
      lastKey
      filteredData {
        reference
        accountNumber
        channelName
        timestamp
        postedTimestamp
        amountValue
        status
        refundAmount
        refundReason
        refundApprovalStatus
        refundStatus
        refundDate
        finalAmount
      }
    }
  }
`;

export const GET_CHANNEL_OPTIONS = gql`
  query getChannelOptions {
    channelsLoose {
      id
      name
      channelId
    }
  }
`;

export const GET_REFUND_REASON = gql`
  query getCardRefundReason {
    getCardRefundReason {
      reason
    }
  }
`;

export const REPORT_PATH = 'cardRefundRequestModule';
