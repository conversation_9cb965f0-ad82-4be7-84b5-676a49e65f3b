import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

export const AlertModalIcon = styled(FontAwesomeIcon)`
  margin-bottom: 20px;
  font-size: 4em;
  color: ${props =>
    props.variant === 'warn'
      ? '#FF9933'
      : props.variant === 'success'
        ? '#57A74F'
        : '#FB1733'};
`;

export const ApprovalButtonContainer = styled.div`
  display: flex;
  flex-direction: row;
  max-width: 100px;
`;

export const RequestButtonContainer = styled.div`
  display: flex;
  flex-direction: row;
`;

export const ListHistoryContainer = styled.ul`
  list-style: none;
`;
