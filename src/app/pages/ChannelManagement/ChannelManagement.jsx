import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import PropTypes from 'prop-types';
import React, { useContext, useEffect, useState } from 'react';
import { useQuery } from '@apollo/client';
import <PERSON>lickOutHandler from '@stratpoint-fe/react-onclickout';
import * as Yup from 'yup';
import CreateButton from '../../components/Button/CreateButton';
import DataContainer from '../../components/DataContainer';
import { FIELD_TYPES } from '../../components/Form/constants';
import { Required } from '../../components/Form/FormField';
import GlobalSearch from '../../components/GlobalSearch';
import Header from '../../components/Header/Header';
import Loader from '../../components/Loader';
import AlertModal from '../../components/Modal/AlertModal';
import FormModal from '../../components/Modal/FormModal';
import Page from '../../components/Page';
import AuthContext from '../../context/AuthContext/AuthContext';
import { useMutation } from '@apollo/client';
import sanitize from '../../utils/sanitize';
import { ADD_CHANNEL, DELETE_CHANNEL, DELETE_CHANNELS } from './mutation';
import { GET_CHANNELS_INFORMATION } from './query';
import sellerLogo from './seller-logo.png';
import {
  AddChannelButton,
  AddChannelButtonIcon,
  ChannelCard,
  ChannelCardId,
  ChannelCardPicture,
  ChannelCardSettingsButton,
  ChannelCardSettingsButtonContainer,
  ChannelCardSettingsCheckbox,
  ChannelCardSettingsIcon,
  ChannelCardSettingsMenu,
  ChannelCardSettingsMenuItem,
  ChannelCardSettingsMenuItemIcon,
  ChannelCardSettingsRow,
  ChannelCardTitle,
  ChannelsContainer,
  CredentialsContainer,
  CredentialRow,
  CredentialLabel,
  CredentialValue,
  DownloadButton,
} from './styled';
import { BILL_TYPE_OPTIONS as BILL_TYPES } from '../../utils/constants';
import { CHANNEL_ERROR_MESSAGES as ERROR_MESSAGES } from '../../utils/errorMessages';

// Utility function to download credentials as JSON
const downloadCredentialsAsJson = (clientId, clientSecret, channelName) => {
  const credentials = {
    clientId,
    clientSecret,
    timestamp: new Date().toISOString(),
  };

  const blob = new Blob([JSON.stringify(credentials, null, 2)], {
    type: 'application/json',
  });

  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  document.body.appendChild(a);
  a.download = `${channelName.replace(/\s+/g, '-').toLowerCase()}-credentials.json`;
  a.href = url;
  a.click();
  document.body.removeChild(a);
};

const ChannelManagement = ({ history }) => {
  const { permissions } = useContext(AuthContext);

  const [state, setState] = useState({
    isAddChannelModalOpen: false,

    isConfirmAddChannelModalOpen: false,
    isSuccessAddChannelModalOpen: false,
    isFailureAddChannelModalOpen: false,
    isLeavingPageWhileAdding: false,

    isConfirmDeleteChannelModalOpen: false,
    isSuccessDeleteChannelModalOpen: false,
    isFailureDeleteChannelModalOpen: false,

    isConfirmDeleteChannelsModalOpen: false,
    isSuccessDeleteChannelsModalOpen: false,
    isFailureDeleteChannelsModalOpen: false,

    addChannelError: null,
    deleteChannelError: null,
    deleteChannelsError: null,

    isChannelMenuOpen: null,
    selectedChannel: null,

    filter: {},

    selectedChannels: [],
  });

  useEffect(() => {
    const unblock = history.block(location => {
      if (state.isLeavingPageWhileAdding || !state.isAddChannelModalOpen) {
        return true;
      }
      setState({
        ...state,
        nextLocation: location,
        isLeavingPageWhileAdding: true,
      });
      return false;
    });

    return () => {
      unblock();
    };
  }, [state.isLeavingPageWhileAdding, state.isAddChannelModalOpen]);

  const { data, loading, refetch } = useQuery(GET_CHANNELS_INFORMATION, {
    variables: {
      filter: state.filter,
    },
    fetchPolicy: 'network-only',
  });

  const [addChannel, { loading: isAddingChannel }] = useMutation(ADD_CHANNEL, {
    onCompleted: data => {
      setState({
        ...state,
        isConfirmAddChannelModalOpen: false,
        isSuccessAddChannelModalOpen: true,
        isAddChannelModalOpen: false,
        selectedChannel: {
          ...state.selectedChannel,
          id: data.createChannel.id,
          clientId: data.createChannel.clientId,
          clientSecret: data.createChannel.clientSecret,
        },
      });
      refetch();
    },
    onError: err => {
      console.log('err', err.networkError);
      setState({
        ...state,
        addChannelError: err.networkError.result
          ? err.networkError.result.message
          : null,
        isConfirmAddChannelModalOpen: false,
        isFailureAddChannelModalOpen: true,
      });
    },
  });

  const [deleteChannel, { loading: isDeletingChannel }] = useMutation(
    DELETE_CHANNEL,
    {
      onCompleted: () => {
        const selectedChannels = { ...state.selectedChannels };
        if (selectedChannels[state.selectedChannel.id]) {
          delete selectedChannels[state.selectedChannel.id];
        }
        setState({
          ...state,
          isConfirmDeleteChannelModalOpen: false,
          isSuccessDeleteChannelModalOpen: true,
          selectedChannel: null,
          selectedChannels,
        });
        refetch();
      },
      onError: err => {
        setState({
          ...state,
          deleteChannelError: err.networkError.result
            ? err.networkError.result.message
            : null,
          isConfirmDeleteChannelModalOpen: false,
          isFailureDeleteChannelModalOpen: true,
        });
      },
    }
  );

  const [deleteChannels, { loading: isDeletingChannels }] = useMutation(
    DELETE_CHANNELS,
    {
      onCompleted: () => {
        setState({
          ...state,
          isConfirmDeleteChannelsModalOpen: false,
          isSuccessDeleteChannelsModalOpen: true,
          selectedChannels: {},
        });
        refetch();
      },
      onError: err => {
        setState({
          ...state,
          deleteChannelsError: err.networkError.result
            ? err.networkError.result.message
            : null,
          isConfirmDeleteChannelsModalOpen: false,
          isFailureDeleteChannelsModalOpen: true,
        });
      },
    }
  );

  useEffect(() => {
    setState({ ...state, selectedChannels: {} });
  }, [state.filter]);

  const dataContainerProps = loading ? { loading } : {};

  return (
    <>
      <Page>
        <Header title="Channels" withHome path={['Channel Mgt.']} />
        <DataContainer {...dataContainerProps}>
          {loading && <Loader />}
          {!loading && (
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <GlobalSearch
                onSearch={filter => {
                  setState({
                    ...state,
                    filter,
                  });
                }}
                placeholder="Search Channels"
                fields={[
                  {
                    type: FIELD_TYPES.TEXT,
                    name: 'name',
                    label: 'Channel Name',
                  },
                  {
                    type: FIELD_TYPES.TEXT,
                    name: 'id',
                    label: 'Channel ID',
                  },
                ]}
              />
              {permissions.Channel.delete && (
                <CreateButton
                  disabled={!Object.keys(state.selectedChannels).length}
                  style={{ marginLeft: 10 }}
                  icon="trash-alt"
                  onClick={() => {
                    setState({
                      ...state,
                      isConfirmDeleteChannelsModalOpen: true,
                    });
                  }}
                >
                  {Object.keys(state.selectedChannels).length} selected
                </CreateButton>
              )}
            </div>
          )}

          <ChannelsContainer>
            {!loading && permissions.Channel.create && (
              <AddChannelButton
                onClick={() => {
                  setState({ ...state, isAddChannelModalOpen: true });
                }}
              >
                Add New Channel
                <AddChannelButtonIcon icon="plus" />
              </AddChannelButton>
            )}
            {!loading &&
              data &&
              data.channels &&
              data.channels.filteredData &&
              data.channels.filteredData.map(channel => (
                <ChannelCard
                  key={channel.id}
                  selected={!!state.selectedChannels[channel.id]}
                >
                  <ChannelCardSettingsRow>
                    <FontAwesomeIcon
                      icon={
                        channel.isVerified ? 'check-circle' : 'minus-circle'
                      }
                      color={channel.isVerified ? '#2DB44B' : '#7F7F7F'}
                    />
                    <ChannelCardSettingsButtonContainer>
                      <ChannelCardSettingsButton
                        onClick={() => {
                          setState({ ...state, isChannelMenuOpen: channel.id });
                        }}
                      >
                        <ChannelCardSettingsIcon icon="sliders-h" />
                      </ChannelCardSettingsButton>
                      {state.isChannelMenuOpen === channel.id && (
                        <ClickOutHandler
                          onClickOut={() => {
                            setState({ ...state, isChannelMenuOpen: null });
                          }}
                        >
                          <ChannelCardSettingsMenu>
                            <ChannelCardSettingsMenuItem
                              disabled={!permissions.Channel.update}
                              onClick={event => {
                                event.preventDefault();
                                const selectedChannels = {
                                  ...state.selectedChannels,
                                };
                                if (selectedChannels[channel.id]) {
                                  delete selectedChannels[channel.id];
                                } else {
                                  selectedChannels[channel.id] = {
                                    ...channel,
                                  };
                                }
                                setState({ ...state, selectedChannels });
                              }}
                            >
                              <ChannelCardSettingsCheckbox
                                name={`select-channel-${channel.id}`}
                                checked={!!state.selectedChannels[channel.id]}
                                disableClick
                              />
                              Select
                            </ChannelCardSettingsMenuItem>
                            <ChannelCardSettingsMenuItem
                              onClick={() => {
                                history.push(
                                  '/channels-management/' + channel.id
                                );
                              }}
                            >
                              <ChannelCardSettingsMenuItemIcon icon="eye" />
                              View
                            </ChannelCardSettingsMenuItem>
                            <ChannelCardSettingsMenuItem
                              onClick={() => {
                                history.push(
                                  '/channels-management/' + channel.id,
                                  {
                                    isEditing: true,
                                  }
                                );
                              }}
                              disabled={
                                !permissions.Channel.update ||
                                !channel.isVerified
                              }
                            >
                              <ChannelCardSettingsMenuItemIcon icon="pen" />
                              Edit
                            </ChannelCardSettingsMenuItem>
                            <ChannelCardSettingsMenuItem
                              onClick={() => {
                                setState({
                                  ...state,
                                  isConfirmDeleteChannelModalOpen: true,
                                  selectedChannel: channel,
                                });
                              }}
                              disabled={
                                !permissions.Channel.delete ||
                                !channel.isVerified
                              }
                            >
                              <ChannelCardSettingsMenuItemIcon icon="trash-alt" />
                              Delete
                            </ChannelCardSettingsMenuItem>
                          </ChannelCardSettingsMenu>
                        </ClickOutHandler>
                      )}
                    </ChannelCardSettingsButtonContainer>
                  </ChannelCardSettingsRow>
                  <ChannelCardPicture src={sellerLogo} alt="Channel Logo" />
                  <ChannelCardTitle title={channel.name}>
                    {channel.name}
                  </ChannelCardTitle>
                  <ChannelCardId title={channel.channelCode}>
                    {channel.channelCode}
                  </ChannelCardId>
                </ChannelCard>
              ))}
          </ChannelsContainer>
        </DataContainer>
      </Page>
      {state.isAddChannelModalOpen && (
        <FormModal
          isOpen={state.isAddChannelModalOpen}
          width="600px"
          handleClose={() =>
            setState({ ...state, isLeavingPageWhileAdding: true })
          }
          title="Add New Channel"
          instructions={
            <span>
              To create new channel, please fill out the required
              <Required>*</Required> fields.
            </span>
          }
          submitText="Create Channel"
          handleSubmit={values => {
            setState({
              ...state,
              isConfirmAddChannelModalOpen: true,
              selectedChannel: {
                ...values,
              },
            });
          }}
          fields={{
            name: {
              type: FIELD_TYPES.TEXT,
              label: 'Channel Name',
              placeholder: 'Channel name',
              validation: Yup.string()
                .min(1, 'Minimum should be 1 character')
                .max(32, 'Must not exceed 32 characters')
                .required('Please enter a value')
                .matches(/[^-\s]/, 'Must not be a whitespace')
                .matches(
                  /^(?![=,@,+,-])(.+)$/,
                  'Input must not begin with this special characters (=,@,+,-)'
                ),
              required: true,
              initialValue: '',
            },
            email: {
              type: FIELD_TYPES.EMAIL,
              label: 'Email',
              placeholder: 'Enter valid email address',
              validation: Yup.string()
                .email('Must be an email')
                .max(64, 'Must not exceed 64 characters')
                .required('Please enter a value'),
              required: true,
              initialValue: '',
            },
            channelCode: {
              type: FIELD_TYPES.TEXT,
              label: 'Channel Code',
              placeholder: 'Channel Code (e.g., GFP, GLA)',
              validation: Yup.string()
                .min(1, 'Minimum should be 1 character')
                .max(5, 'Must not exceed 5 characters')
                .required('Please enter a value')
                .matches(/[^-\s]/, 'Must not be a whitespace')
                .matches(
                  /^(?![=,@,+,-])(.+)$/,
                  'Input must not begin with this special characters (=,@,+,-)'
                ),
              required: true,
              initialValue: '',
            },
            billType: {
              type: FIELD_TYPES.SELECT,
              options: BILL_TYPES,
              label: 'Bill Type',
              placeholder: 'Bill Type',
              validation: Yup.string().required('Please select a value'),
              required: true,
              initialValue: '',
            },

            ipAddress: {
              type: FIELD_TYPES.TEXT,
              label: 'IP Address',
              placeholder: 'x.x.x.x',
              validation: Yup.string()
                // .required('Please enter a value')
                .test('is-ipAddress', 'Invalid format', value => {
                  if (!value) return true;
                  const split = value.split('.');
                  if (split.length !== 4) return false;
                  for (const i of split) {
                    if (String(+i).length !== i.length) return false;
                    if (+i < 0 || +i > 255) return false;
                  }
                  return true;
                }),
              required: false,
              initialValue: '',
            },
            xApiKey: {
              type: FIELD_TYPES.TEXT,
              label: 'API Key',
              placeholder: 'API Key',
              validation: Yup.string()
                .max(250, 'Must not exceed 250 characters')
                .matches(
                  /^$|^(?![=,@,+,-])(.+)$/,
                  'Input must not begin with this special characters (=,@,+,-)'
                )
                .nullable(),
              required: false,
            },
            merchantCode: {
              type: FIELD_TYPES.TEXT,
              label: 'Merchant Code',
              placeholder: 'Merchant Code',
              validation: Yup.string()
                .max(250, 'Must not exceed 250 characters')
                .matches(
                  /^$|^(?![=,@,+,-])(.+)$/,
                  'Input must not begin with this special characters (=,@,+,-)'
                )
                .nullable(),
              required: false,
            },
            merchantKey: {
              type: FIELD_TYPES.TEXT,
              label: 'Merchant Key',
              placeholder: 'Merchant Key',
              validation: Yup.string()
                .max(250, 'Must not exceed 250 characters')
                .matches(
                  /^$|^(?![=,@,+,-])(.+)$/,
                  'Input must not begin with this special characters (=,@,+,-)'
                )
                .nullable(),
              required: false,
            },
            isForPayByLink: {
              type: FIELD_TYPES.CHECKBOX,
              label: 'Activate Pay-by-Link',
              initialValue: false,
              perRow: 0,
              row: true,
              verticalGap: 60,
            },
          }}
        />
      )}
      <AlertModal
        isOpen={state.isConfirmAddChannelModalOpen}
        title="New Channel Alert"
        icon="exclamation-circle"
        variant="warn"
        header="ARE YOU SURE?"
        subHeader="You are about to create a new Channel."
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        confirmLoading={isAddingChannel}
        confirmText="Yes"
        handleConfirm={() => {
          addChannel({
            variables: {
              data: sanitize(state.selectedChannel),
            },
          });
        }}
        handleClose={() => {
          setState({
            ...state,
            isAddChannelModalOpen: false,
            isConfirmAddChannelModalOpen: false,
          });
        }}
      />
      <AlertModal
        isOpen={state.isSuccessAddChannelModalOpen}
        title="New Channel Alert"
        handleClose={() => {
          setState({ ...state, isSuccessAddChannelModalOpen: false });
        }}
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader="Channel has been created successfully."
        description="Instructions will be sent to Channel's email address and or mobile number to activate the account."
        confirmText="Go to All Channels"
        handleConfirm={() => {
          setState({ ...state, isSuccessAddChannelModalOpen: false });
        }}
        content={
          state.selectedChannel ? (
            <>
              <CredentialsContainer>
                <div
                  style={{
                    fontWeight: 'bold',
                    marginBottom: '10px',
                    color: '#d14',
                  }}
                >
                  Important: Save these credentials now. The client secret will
                  be encrypted and cannot be retrieved later.
                </div>
                <CredentialRow>
                  <CredentialLabel>Client ID:</CredentialLabel>
                  <CredentialValue>
                    {state.selectedChannel.clientId}
                  </CredentialValue>
                </CredentialRow>
                <CredentialRow>
                  <CredentialLabel>Client Secret:</CredentialLabel>
                  <CredentialValue>
                    {state.selectedChannel.clientSecret}
                  </CredentialValue>
                </CredentialRow>
                <DownloadButton
                  onClick={e => {
                    e.stopPropagation();
                    downloadCredentialsAsJson(
                      state.selectedChannel.clientId,
                      state.selectedChannel.clientSecret,
                      state.selectedChannel.name
                    );
                  }}
                >
                  <FontAwesomeIcon
                    icon="download"
                    style={{ marginRight: '8px' }}
                  />
                  Download Credentials
                </DownloadButton>
              </CredentialsContainer>
            </>
          ) : null
        }
      />
      <AlertModal
        isOpen={state.isLeavingPageWhileAdding}
        title="New Channel Alert"
        icon="question-circle"
        variant="warn"
        header="SAVE CHANNEL?"
        subHeader="You are about to leave without saving New Channel."
        description="Your entry will be lost if you don't save it."
        handleClose={() =>
          setState({ ...state, isLeavingPageWhileAdding: false })
        }
        cancelText="Discard Entry"
        confirmText="Go Back"
        handleCancel={() => {
          if (state.nextLocation) {
            history.push(state.nextLocation);
          } else {
            setState({
              ...state,
              isLeavingPageWhileAdding: false,
              isAddChannelModalOpen: false,
            });
          }
        }}
        handleConfirm={() => {
          setState({ ...state, isLeavingPageWhileAdding: false });
        }}
      />
      <AlertModal
        isOpen={state.isFailureAddChannelModalOpen}
        title="New Channel Status"
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader={
          ERROR_MESSAGES.subHeader[state.addChannelError] ||
          ERROR_MESSAGES.subHeader.default
        }
        description={
          ERROR_MESSAGES.description[state.addChannelError] ||
          ERROR_MESSAGES.description.default
        }
        handleClose={() =>
          setState({
            ...state,
            isFailureAddChannelModalOpen: false,
            addChannelError: null,
          })
        }
        confirmText="Go Back"
        handleConfirm={() => {
          setState({
            ...state,
            isFailureAddChannelModalOpen: false,
            addChannelError: null,
          });
        }}
      />
      <AlertModal
        isOpen={state.isConfirmDeleteChannelModalOpen}
        title="Delete Channel Alert"
        icon="exclamation-circle"
        variant="warn"
        header="ARE YOU SURE?"
        subHeader="You are about to delete a Channel."
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() =>
          setState({
            ...state,
            isConfirmDeleteChannelModalOpen: false,
          })
        }
        selectLabel="Reason"
        options={['No longer in use', 'Others'].map(reason => ({
          value: reason,
          label: reason,
        }))}
        confirmLoading={isDeletingChannel}
        confirmText="Yes"
        handleConfirm={reason => {
          deleteChannel({
            variables: {
              where: { id: state.selectedChannel.id },
              data: { reasonToDelete: reason },
            },
          });
        }}
      />
      <AlertModal
        isOpen={state.isSuccessDeleteChannelModalOpen}
        title="Delete Channel Alert"
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader="Channel has been deleted successfully."
        description="Notifications will be sent to the Channel's email address."
        handleClose={() =>
          setState({ ...state, isSuccessDeleteChannelModalOpen: false })
        }
        confirmText="Go to All Channels"
        handleConfirm={() =>
          setState({ ...state, isSuccessDeleteChannelModalOpen: false })
        }
      />
      <AlertModal
        isOpen={state.isFailureDeleteChannelModalOpen}
        title="Delete Channel Alert"
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader={
          state.deleteChannelError === 'CHANNEL_IS_NOT_VERIFIED'
            ? 'Channel could not be deleted.'
            : 'There was a problem on deleting the Channel.'
        }
        description={
          state.deleteChannelError === 'CHANNEL_IS_NOT_VERIFIED'
            ? 'Please verify channel first before deleting.'
            : 'Please go back and try deleting again.'
        }
        handleClose={() =>
          setState({ ...state, isFailureDeleteChannelModalOpen: false })
        }
        confirmText="Go Back"
        handleConfirm={() => {
          setState({ ...state, isFailureDeleteChannelModalOpen: false });
        }}
      />
      <AlertModal
        isOpen={state.isConfirmDeleteChannelsModalOpen}
        title="Delete Channel Alert"
        icon="exclamation-circle"
        variant="warn"
        header="ARE YOU SURE?"
        subHeader={`You are about to delete ${Object.keys(state.selectedChannels).length} Channel(s).`}
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() =>
          setState({
            ...state,
            isConfirmDeleteChannelsModalOpen: false,
          })
        }
        selectLabel="Reason"
        options={['No longer in use', 'Others'].map(reason => ({
          value: reason,
          label: reason,
        }))}
        confirmLoading={isDeletingChannels}
        confirmText="Yes"
        handleConfirm={reason => {
          deleteChannels({
            variables: {
              where: { ids: Object.keys(state.selectedChannels) },
              data: { reasonToDelete: reason },
            },
          });
        }}
      />
      <AlertModal
        isOpen={state.isSuccessDeleteChannelsModalOpen}
        title="Delete Channel Alert"
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader="Channel(s) has been deleted successfully."
        description="Notifications will be sent to each Channel(s)' email address."
        handleClose={() =>
          setState({ ...state, isSuccessDeleteChannelsModalOpen: false })
        }
        confirmText="Go to All Channels"
        handleConfirm={() =>
          setState({ ...state, isSuccessDeleteChannelsModalOpen: false })
        }
      />
      <AlertModal
        isOpen={state.isFailureDeleteChannelsModalOpen}
        title="Delete Channel Alert"
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader={
          state.deleteChannelError === 'CHANNEL_IS_NOT_VERIFIED'
            ? 'Channel could not be deleted.'
            : 'There was a problem on deleting the Channel(s).'
        }
        description={
          state.deleteChannelError === 'CHANNEL_IS_NOT_VERIFIED'
            ? 'Please verify channel first before deleting.'
            : 'Please go back and try deleting again.'
        }
        handleClose={() =>
          setState({ ...state, isFailureDeleteChannelsModalOpen: false })
        }
        confirmText="Go Back"
        handleConfirm={() => {
          setState({ ...state, isFailureDeleteChannelsModalOpen: false });
        }}
      />
    </>
  );
};

ChannelManagement.propTypes = {
  history: PropTypes.object,
};

export default ChannelManagement;
