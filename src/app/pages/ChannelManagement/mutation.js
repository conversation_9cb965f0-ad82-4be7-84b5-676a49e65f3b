import { gql } from '@apollo/client';

export const ADD_CHANNEL = gql`
  mutation addChannel($data: CreateChannel!) {
    createChannel(data: $data) {
      id
      clientId
      clientSecret
    }
  }
`;

export const DELETE_CHANNEL = gql`
  mutation deleteChannel($data: DeleteChannel!, $where: ChannelFilter!) {
    deleteChannel(data: $data, where: $where) {
      id
    }
  }
`;

export const DELETE_CHANNELS = gql`
  mutation deleteChannels($data: DeleteChannel!, $where: IDArray!) {
    deleteChannels(data: $data, where: $where) {
      unprocessedItems
    }
  }
`;
