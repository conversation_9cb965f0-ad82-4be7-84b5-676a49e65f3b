import { gql } from '@apollo/client';

export const GET_CHANNELS_INFORMATION = gql`
  query getChannelInformation($filter: ChannelFilter) {
    channels(filter: $filter) {
      count
      filteredData {
        id
        name
        channelCode
        isVerified
      }
    }
  }
`;

export const GET_SUBMERCHANTS = gql`
  query getSubMerchants($where: MerchantPrimary) {
    subMerchants(where: $where) {
      serviceType
      subMerchantId
      merchant
    }
  }
`;
