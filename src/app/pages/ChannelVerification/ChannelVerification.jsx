import PropTypes from 'prop-types';
import React, { useState, useEffect } from 'react';
import { withRouter } from 'react-router-dom';
import Checkbox from '../../components/Checkbox';
import { AlertModal } from '../../components/Modal';
import { useMutation } from '@apollo/client';
import { VERIFY_CHANNEL } from './mutation';
import {
  ChannelVerificationButton,
  ChannelVerificationContainer,
  ChannelVerificationIcon,
  ChannelVerificationInput,
  ChannelVerificationInstructions,
  ChannelVerificationLicense,
  ChannelVerificationLink,
  ChannelVerificationPage,
  ChannelVerificationRow,
  ChannelVerificationTitle,
} from './styled';

const ChannelVerification = ({ match, history, location }) => {
  const [isVerificationSuccess, setIsVerificationSuccess] = useState(false);
  const [isVerificationFailure, setIsVerificationFailure] = useState(false);
  const [agreed, setAgreed] = useState(false);
  const [pin, setPin] = useState('');
  const [channelId, setChannelId] = useState('');

  useEffect(() => {
    // Parse query parameters
    const queryParams = new URLSearchParams(location.search);
    const channelIdParam = queryParams.get('channelId');
    if (channelIdParam) {
      setChannelId(channelIdParam);
    }
  }, [location]);

  const [verifyChannel, { loading: isVerifyingChannel }] = useMutation(
    VERIFY_CHANNEL,
    {
      onCompleted: () => setIsVerificationSuccess(true),
      onError: () => setIsVerificationFailure(true),
    }
  );

  return (
    <>
      <ChannelVerificationPage>
        <ChannelVerificationContainer>
          <ChannelVerificationIcon icon="question-circle" />
          <ChannelVerificationTitle>VERIFY CHANNEL?</ChannelVerificationTitle>
          <ChannelVerificationInstructions>
            Please enter the 6 digit PIN to proceed.
          </ChannelVerificationInstructions>
          <ChannelVerificationInput
            type="text"
            name="pin"
            onChange={event => {
              setPin(event.target.value);
            }}
            value={pin}
            maxLength={6}
          />
          <ChannelVerificationRow>
            <Checkbox
              name="agreed"
              checked={agreed}
              onChange={() => {
                setAgreed(!agreed);
              }}
            />
            <ChannelVerificationLicense>
              I have read and accepted the Payment Service{' '}
              <ChannelVerificationLink to="/license-and-agreement">
                User License and Agreement
              </ChannelVerificationLink>
              .
            </ChannelVerificationLicense>
          </ChannelVerificationRow>

          <ChannelVerificationButton
            disabled={!agreed || pin.length !== 6}
            loading={isVerifyingChannel}
            onClick={async () => {
              console.log(match);
              console.log(location);
              verifyChannel({
                variables: {
                  data: {
                    confirmationCode: pin,
                    email: match.params.email,
                    channelId: channelId || undefined,
                  },
                },
              });
            }}
          >
            Verify
          </ChannelVerificationButton>
        </ChannelVerificationContainer>
      </ChannelVerificationPage>
      <AlertModal
        isOpen={isVerificationSuccess}
        handleClose={() => {
          history.push('/');
        }}
        title="Verification Alert"
        header="SUCCESS!"
        subHeader="Channel is now verified."
        description="Notification will be sent to the Channel's email address regarding the confirmation."
        icon="check-circle"
        variant="success"
        handleConfirm={() => {
          history.push('/channels-management');
        }}
        confirmText="Go to All Channels"
      />
      <AlertModal
        isOpen={isVerificationFailure}
        handleClose={() => {
          setIsVerificationFailure(false);
        }}
        title="Verification Alert"
        header="OH SNAP!"
        subHeader="There was a problem verifying the channel."
        description="Please go back and re-enter the PIN."
        icon="times-circle"
        variant="error"
        handleConfirm={() => {
          setIsVerificationFailure(false);
        }}
        confirmText="Go Back"
      />
    </>
  );
};

ChannelVerification.propTypes = {
  match: PropTypes.object,
  history: PropTypes.object,
  location: PropTypes.object, // Add location to propTypes
};

export default withRouter(ChannelVerification);
