import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import PrimaryButton from '../../components/Button/PrimaryButton';
import Row from '../../components/Row';

export const ChannelVerificationPage = styled.div`
  height: 100vh;
  width: 100vw;
  display: flex;
  justify-content: center;
  align-items: center;
`;

export const ChannelVerificationContainer = styled.div`
  color: #333333;
  background-color: white;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.5);
`;

export const ChannelVerificationIcon = styled(FontAwesomeIcon)`
  color: #ff9718;
  font-size: 50px;
`;

export const ChannelVerificationTitle = styled.h1`
  font-size: 18px;
  margin: 0;
  margin-top: 20px;
`;

export const ChannelVerificationInstructions = styled.p`
  font-size: 18px;
  margin: 0;
  margin-top: 10px;
  font-weight: lighter;
`;

export const ChannelVerificationRow = styled(Row)`
  margin-top: 10px;
  width: 80%;
`;

export const ChannelVerificationInput = styled.input`
  margin-top: 20px;
  border: 0;
  border-bottom: 1px solid #979797;
  text-align: center;
  padding: 5px 0;
  width: 90%;
  font-size: 25px;
  letter-spacing: 10px;
`;

export const ChannelVerificationLicense = styled.div`
  font-size: 12px;
  margin-top: 10px;
  margin-left: 20px;
  flex: 1;
`;

export const ChannelVerificationLink = styled(Link)`
  color: #009cde;
`;

export const ChannelVerificationButton = styled(PrimaryButton)`
  margin-top: 40px;
`;
