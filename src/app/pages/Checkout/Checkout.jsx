import React, { Component } from 'react';

class Checkout extends Component {
  state = {
    isDropIn: false,
    paymentSession: '',
    payload: '',
    MerchantCode: 'PH00067',
    ActionType: 'BT',
    PaymentId: '1',
    RefNo: 'A00000001',
    Amount: '50.00',
    Currency: 'PHP',
    ProdDesc: 'Photo Print',
    UserName: '<PERSON>',
    UserEmail: '<EMAIL>',
    UserContact: '0126500100',
    Remark: '',
    Lang: 'UTF-8',
    Signature: 'HTz0zTp/nxsfeuCU2sKXn725LRM=',
    ResponseURL: 'https://ipaytest.free.beeceptor.com',
    BackendURL:
      'https://b48kobbagd.execute-api.ap-southeast-1.amazonaws.com/test/api/ipay88/callback',
    TokenID: '',

    CardDetailsURL:
      'https://payment.ipay88.com.ph/epayment/MerchantTokenization/manageCard.asp',
  };

  handleChangeDropIn = () => {
    if (!this.state.isDropIn) {
      this.setState({ ...this.state, isDropIn: true });
    } else if (this.state.isDropIn) {
      this.setState({ ...this.state, isDropIn: false });
    }
  };

  handleChange = event => {
    this.setState({
      [event.target.name]: event.target.value,
    });
  };

  handleURLChange = event => {
    this.setState({
      ...this.state,
      CardDetailsURL: event.target.value,
    });
  };

  handleSubmit = event => {
    if (event.target.name === 'paymentSession') {
      if (event.key !== 'Enter') {
        return;
      }
    }
    const sdkConfigObj = {
      context: 'test',
    };

    // Initiate the Checkout form.
    window.chckt.checkout(
      { paymentSession: this.state.paymentSession },
      '#adyen',
      sdkConfigObj
    );

    window.chckt.hooks.beforeComplete = (node, paymentData) => {
      this.setState({
        payload: paymentData.payload,
      });

      return true;
    };
  };

  onChange = event => {
    this.setState({
      [event.target.name]: event.target.value,
    });
  };

  render() {
    return (
      <React.Fragment>
        <div style={{ display: 'flex' }}>
          <div>
            <label>Payment Session</label>
            <br />
            <textarea
              name="paymentSession"
              onChange={this.handleChange}
              onKeyPress={this.handleSubmit}
              value={this.state.paymentSesssion}
              cols="80"
              rows="10"
            />
            <br />
            <button type="button" onClick={this.handleSubmit}>
              Submit
            </button>
          </div>

          <div>
            <label>Payment Payload</label>
            <br />
            <textarea cols="80" rows="10" value={this.state.payload} readOnly />
          </div>
        </div>

        <hr />

        <br />
        <button type="button" onClick={this.handleChangeDropIn}>
          {!this.state.isDropIn ? 'Open DropIn Form' : 'Hide DropIn Form'}
        </button>
        <br />
        <br />

        {this.state.isDropIn ? (
          <>
            <h2>Dropin Form</h2>

            <form
              action="https://checkoutshopper-test.adyen.com/checkoutshopper/threeDS2.shtml"
              method="POST"
            >
              MD:
              <input type="text" name="MD" /> <br />
              Pareq:
              <input type="text" name="PaReq" /> <br />
              TermUrl:
              <input type="text" name="TermUrl" />
              <input type="hidden" name="form_submitted" value="1" />
              <br />
              <input type="submit" value="Submit" />
            </form>
            <br />
            <br />
          </>
        ) : (
          ''
        )}

        <div id="adyen" />

        <div>
          <form
            method="post"
            name="ePayment"
            action="https://sandbox.ipay88.com.ph/epayment/entry.asp"
          >
            <label>MerchantCode</label>
            <input
              type="text"
              name="MerchantCode"
              value={this.state.MerchantCode}
              onChange={this.onChange}
            />

            <br />
            <label>ActionType</label>
            <input
              type="text"
              name="ActionType"
              value={this.state.ActionType}
              onChange={this.onChange}
            />
            <br />

            <label>PaymentId</label>
            <input
              type="text"
              name="PaymentId"
              value={this.state.PaymentId}
              onChange={this.onChange}
            />
            <br />

            <label>RefNo</label>
            <input
              type="text"
              name="RefNo"
              value={this.state.RefNo}
              onChange={this.onChange}
            />
            <br />

            <label>Amount</label>
            <input
              type="text"
              name="Amount"
              value={this.state.Amount}
              onChange={this.onChange}
            />
            <br />

            <label>Currency</label>
            <input
              type="text"
              name="Currency"
              value={this.state.Currency}
              onChange={this.onChange}
            />
            <br />

            <label>ProdDesc</label>
            <input
              type="text"
              name="ProdDesc"
              value={this.state.ProdDesc}
              onChange={this.onChange}
            />
            <br />

            <label>UserName</label>
            <input
              type="text"
              name="UserName"
              value={this.state.UserName}
              onChange={this.onChange}
            />
            <br />

            <label>UserEmail</label>
            <input
              type="text"
              name="UserEmail"
              value={this.state.UserEmail}
              onChange={this.onChange}
            />
            <br />

            <label>UserContact</label>
            <input
              type="text"
              name="UserContact"
              value={this.state.UserContact}
              onChange={this.onChange}
            />
            <br />

            <label>Remark</label>
            <input
              type="text"
              name="Remark"
              value={this.state.Remark}
              onChange={this.onChange}
            />
            <br />

            <label>Lang</label>
            <input
              type="text"
              name="Lang"
              value={this.state.Lang}
              onChange={this.onChange}
            />
            <br />

            <label>Signature</label>
            <input
              type="text"
              name="Signature"
              value={this.state.Signature}
              onChange={this.onChange}
            />
            <br />

            <label>ResponseURL</label>
            <input
              type="text"
              name="ResponseURL"
              value={this.state.ResponseURL}
              onChange={this.onChange}
            />
            <br />

            <label>BackendURL</label>
            <input
              type="text"
              name="BackendURL"
              value={this.state.BackendURL}
              onChange={this.onChange}
            />
            <br />

            <label>TokenID</label>
            <input
              type="text"
              name="TokenID"
              value={this.state.TokenID}
              onChange={this.onChange}
            />
            <br />

            <input type="submit" value="Proceed with Payment" name="Submit" />
          </form>
        </div>
        <br />
        <div>
          <form
            method="post"
            name="eCardDetails"
            action={this.state.CardDetailsURL}
          >
            <label>URL</label>
            <input
              type="text"
              name="URL"
              value={this.state.CardDetailsURL}
              onChange={this.handleURLChange}
            />
            <br />

            <label>MerchantCode</label>
            <input
              type="text"
              name="MerchantCode"
              value={this.state.MerchantCode}
              onChange={this.onChange}
            />
            <br />

            <label>Signature</label>
            <input
              type="text"
              name="Signature"
              value=""
              onChange={this.onChange}
            />
            <br />

            <label>RedirectURL</label>
            <input
              type="text"
              name="RedirectURL"
              value={this.state.RedirectURL}
              onChange={this.onChange}
            />
            <br />

            <label>TokenID</label>
            <input
              type="text"
              name="TokenID"
              value=""
              onChange={this.onChange}
            />
            <br />

            <input type="submit" value="Get card details" name="Submit" />
          </form>
        </div>
      </React.Fragment>
    );
  }
}

export default Checkout;
