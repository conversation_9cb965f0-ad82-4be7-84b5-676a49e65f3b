import format from 'date-fns/format';
import React, { useContext, useState } from 'react';
import { useQuery, useMutation } from '@apollo/client';
import * as Yup from 'yup';
import ActionButtons from '../../components/ActionButtons';
import CreateButton from '../../components/Button/CreateButton';
import DataContainer from '../../components/DataContainer';
import DataTable from '../../components/DataTable';
import { FIELD_TYPES } from '../../components/Form/constants';
import { Required } from '../../components/Form/FormField';
import Header from '../../components/Header';
import { FormModal } from '../../components/Modal';
import AlertModal from '../../components/Modal/AlertModal';
import Page from '../../components/Page';
import AuthContext from '../../context/AuthContext/AuthContext';
import sanitize from '../../utils/sanitize';
import {
  ADD_CONVENIENCE_FEE,
  DELETE_CONVENIENCE_FEE,
  EDIT_CONVENIENCE_FEE,
} from './mutation';
import { GET_CONVENIENCE_FEES } from './query';
import { BILL_TYPES } from '../MID/MID';
import { GET_CHANNEL_OPTIONS } from '../ADASimulation/query';
import GlobalSearch from '../../components/GlobalSearch';
import { GET_CONVENIENCE_FEE_BRANDS } from '../ConvenienceFeeBrands/query';

export const PAYMENT_GATEWAYS = [
  { value: 'adyen', label: 'Adyen' },
  { value: 'xendit', label: 'Xendit' },
  { value: 'gcash', label: 'GCash' },
];

export const PaymentMethodOptions = {
  adyen: [
    { value: 'default', label: 'default' },
    { value: 'dropin', label: 'dropin' },
    { value: 'paybylink', label: 'paybylink' },
  ],
  gcash: [{ value: 'dragonpay_gcash', label: 'dragonpay_gcash' }],
  xendit: [
    { value: 'CC_DC', label: 'CC_DC' },
    { value: 'PAYMAYA', label: 'PAYMAYA' },
    { value: 'SHOPEEPAY', label: 'SHOPEEPAY' },
    { value: 'GRABPAY', label: 'GRABPAY' },
    { value: 'BPI', label: 'BPI' },
    { value: 'UBP', label: 'UBP' },
    { value: 'RCBC', label: 'RCBC' },
  ],
};

const ConvenienceFee = () => {
  const { permissions } = useContext(AuthContext);

  const [convenienceFeeId, setConvenienceFeeId] = useState();
  const [state, setState] = useState({
    isAddConvenieceFeeModalOpen: false,
    isConfirmAddConvenieceFeeModalOpen: false,
    isSuccessAddConvenieceFeeModalOpen: false,
    isFailureAddConvenieceFeeModalOpen: false,

    isLeavingPageWhileAdding: false,

    isEditConvenieceFeeModalOpen: false,
    isConfirmEditConvenieceFeeModalOpen: false,
    isSuccessEditConvenieceFeeModalOpen: false,
    isFailureEditConvenienceFeeModalOpen: false,

    isConfirmDeleteConvenienceFeeModalOpen: false,
    isSuccessDeleteConvenienceFeeModalOpen: false,
    isFailureDeleteConvenienceFeeModalOpen: false,

    isLeavingPageWhileEditing: false,
    showTieredSchemeField: false,

    filter: {},
    pagination: {
      limit: 10,
      startKey: '',
    },

    selectedConvenienceFeeInitial: null,
    selectedConvenienceFee: null,

    addConvenienceFeeError: null,
    editConvenienceFeeError: null,

    bulkCreationResult: null,
  });

  const { data, loading, refetch } = useQuery(GET_CONVENIENCE_FEES, {
    variables: {
      pagination: state.pagination,
      filter: state.filter,
    },
    fetchPolicy: 'network-only',
  });

  const { data: channelData, loading: isLoadingChannels } = useQuery(
    GET_CHANNEL_OPTIONS,
    {
      variables: {
        filter: {},
      },
      fetchPolicy: 'network-only',
    }
  );

  const { data: brandData, loading: isLoadingBrands } = useQuery(
    GET_CONVENIENCE_FEE_BRANDS,
    {
      variables: {
        filter: {},
        pagination: { limit: 100, startKey: '' },
      },
      fetchPolicy: 'network-only',
    }
  );

  const channelOptions =
    !isLoadingChannels && channelData
      ? channelData.channelsLoose.map(channel => ({
          value: channel.channelId,
          label: channel.name,
        }))
      : [];
  const brandOptions =
    !isLoadingBrands && brandData
      ? brandData.convenienceFeeBrands.filteredData
          .map(brand => ({
            value: brand.name,
            label: brand.name,
          }))
          .sort((a, b) => b.label - a.label)
      : [];

  const [addConvenienceFee, { loading: isAddingConvenienceFee }] = useMutation(
    ADD_CONVENIENCE_FEE,
    {
      onCompleted: (result) => {
        setState({
          ...state,
          isConfirmAddConvenieceFeeModalOpen: false,
          isSuccessAddConvenieceFeeModalOpen: true,
          isAddConvenieceFeeModalOpen: false,
          bulkCreationResult: result.createConvenienceFee,
          pagination: {
            ...state.pagination,
            startKey: '',
          },
        });
        refetch();
      },
      onError: err => {
        setState({
          ...state,
          addConvenienceFeeError: err.networkError.result
            ? err.networkError.result.message
            : null,
          isConfirmAddConvenieceFeeModalOpen: false,
          isFailureAddConvenieceFeeModalOpen: true,
        });
      },
    }
  );

  const [deleteConvenienceFee, { loading: isDeletingConvenienceFee }] =
    useMutation(DELETE_CONVENIENCE_FEE, {
      onCompleted: () => {
        setConvenienceFeeId(null);
        setState({
          ...state,
          isConfirmDeleteConvenienceFeeModalOpen: false,
          isSuccessDeleteConvenienceFeeModalOpen: true,
          selectedConvenienceFee: null,
        });
        refetch();
      },
      onError: () => {
        setConvenienceFeeId(null);
        setState({
          ...state,
          isConfirmDeleteConvenienceFeeModalOpen: false,
          isFailureDeleteConvenienceFeeModalOpen: true,
        });
      },
    });

  const [editConvenienceFee, { loading: isEditingConvenienceFEe }] =
    useMutation(EDIT_CONVENIENCE_FEE, {
      onCompleted: () => {
        setConvenienceFeeId(null);
        setState({
          ...state,
          isConfirmEditConvenieceFeeModalOpen: false,
          isSuccessEditConvenieceFeeModalOpen: true,
          isEditConvenieceFeeModalOpen: false,
          selectedConvenienceFee: null,
        });
        refetch();
      },
      onError: err => {
        setState({
          ...state,
          editConvenienceFeeError: err.networkError.result
            ? err.networkError.result.message
            : null,
          isConfirmEditConvenieceFeeModalOpen: false,
          isFailureEditConvenienceFeeModalOpen: true,
        });
      },
    });

  return (
    <>
      <Page>
        <Header withHome title="Convenience Fee" path={['ConvenienceFee']} />
        <DataContainer>
          <DataTable
            loading={loading}
            data={
              data && data.convenienceFees
                ? data.convenienceFees.filteredData
                : []
            }
            headerOptions={
              <>
                <GlobalSearch
                  onSearch={filter => {
                    setState({
                      ...state,
                      filter,
                      pagination: { ...state.pagination, startKey: '' },
                    });
                  }}
                  fields={[
                    {
                      label: 'Channel',
                      name: 'channelId',
                      type: FIELD_TYPES.DROP,
                      options: channelOptions,
                      isKey: true,
                    },
                    {
                      label: 'Brand',
                      name: 'brand',
                      type: FIELD_TYPES.SELECT,
                      options: brandOptions,
                    },
                  ]}
                  placeholder="Search entries here..."
                />
                <div />
                <div style={{ display: 'flex' }}>
                  {permissions.ConvenienceFee.create && (
                    <CreateButton
                      icon="plus"
                      onClick={() => {
                        setState({
                          ...state,
                          isAddConvenieceFeeModalOpen: true,
                        });
                      }}
                    >
                      Add ConvenienceFee
                    </CreateButton>
                  )}
                </div>
              </>
            }
            config={{
              name: {
                headerLabel: 'Channel',
                sortable: true,
              },
              brand: {
                headerLabel: 'Brand',
                sortable: true,
              },
              gatewayProcessor: {
                headerLabel: 'Gateway Processor',
                sortable: true,
              },
              paymentMethod: {
                headerLabel: 'Payment Method',
                sortable: true,
              },
              transactionType: {
                headerLabel: 'Transaction Type',
                sortable: true,
              },
              convenienceFeeType: {
                headerLabel: 'Conv Fee Type',
                sortable: true,
              },
              convenienceFeeValue: {
                headerLabel: 'Conv Fee Value',
                sortable: true,
              },
              convenienceFeeThreshold: {
                headerLabel: 'Threshold',
                sortable: true,
              },
              convenienceFeeTieredScheme: {
                headerLabel: 'Tiered Scheme',
                sortable: true,
                renderAs: data =>
                  data.convenienceFeeType !== 'Tiered_Scheme'
                    ? ''
                    : data.convenienceFeeTieredScheme,
              },
              createdAt: {
                headerLabel: 'Create At',
                sortable: true,
                renderAs: data =>
                  format(data.createdAt, 'MM/DD/YYYY - hh:mm:ss A'),
              },
              updatedAt: {
                headerLabel: 'Updated At',
                sortable: true,
                renderAs: data =>
                  format(data.updatedAt, 'MM/DD/YYYY - hh:mm:ss A'),
              },

              actions: {
                renderAs: data => (
                  <ActionButtons
                    disabled={{
                      edit: !permissions.ConvenienceFee.update,
                      delete: !permissions.ConvenienceFee.delete,
                    }}
                    handleEdit={() => {
                      setConvenienceFeeId(data.id);
                      setState({
                        ...state,
                        selectedConvenienceFee: data,
                        selectedConvenienceFeeInitial: data,
                        isEditConvenieceFeeModalOpen: true,
                      });
                    }}
                    handleDelete={() => {
                      setConvenienceFeeId(data.id);
                      setState({
                        ...state,
                        isConfirmDeleteConvenienceFeeModalOpen: true,
                      });
                    }}
                  />
                ),
              },
            }}
            pagination={{
              ...state.pagination,
              count:
                data && data.convenienceFees ? data.convenienceFees.count : 0,
              cursors:
                data && data.convenienceFees
                  ? data.convenienceFees.cursors
                  : [''],
              handleChange: pagination => {
                setState({ ...state, pagination });
              },
            }}
          />
        </DataContainer>
      </Page>
      {state.isAddConvenieceFeeModalOpen && (
        <FormModal
          isOpen={state.isAddConvenieceFeeModalOpen}
          width="600px"
          handleClose={() =>
            setState({ ...state, isLeavingPageWhileAdding: true })
          }
          title="Add New ConvenienceFee"
          instructions={
            <span>
              To create new ConvenienceFee, please fill out the required
              <Required>*</Required> fields.
            </span>
          }
          submitText="Create ConvenienceFee"
          handleSubmit={values => {
            setState({
              ...state,
              selectedConvenienceFee: values,
              isConfirmAddConvenieceFeeModalOpen: true,
            });
          }}
          changeListener={(name, data, values, setValues) => {
            if (name === 'convenienceFeeType' && data !== 'Tiered_Scheme') {
              setValues(prevState => ({
                ...prevState,
                convenienceFeeTieredScheme: '',
              }));
            }

            if (
              name === 'gatewayProcessor' &&
              data !== values.gatewayProcessor
            ) {
              setValues({
                ...values,
                gatewayProcessor: data,
                paymentMethods: [],
              });
            }
          }}
          fields={{
            channelId: {
              type: FIELD_TYPES.SELECT,
              label: 'Channel',
              options: channelOptions,
              placeholder: 'Channel',
              validation: Yup.string()
                .required('Please select a value')
                .nullable(),
              required: true,
              initialValue: '',
            },
            brands: {
              type: FIELD_TYPES.MULTISELECT,
              label: 'Brands',
              options: brandOptions,
              placeholder: 'Select Brands',
              validation: Yup.array()
                .min(1, 'Please select at least one brand')
                .required('Please select a value')
                .nullable(),
              required: true,
              initialValue: [],
            },
            gatewayProcessor: {
              type: FIELD_TYPES.SELECT,
              options: PAYMENT_GATEWAYS,
              label: 'Gateway Processor',
              placeholder: 'Gateway Processor',
              validation: Yup.string()
                .required('Please select a value')
                .nullable(),
              required: true,
              initialValue: '',
            },
            paymentMethods: {
              type: FIELD_TYPES.MULTISELECT,
              dynamicOptions: value =>
                (value.gatewayProcessor === 'adyen' &&
                  PaymentMethodOptions.adyen) ||
                (value.gatewayProcessor === 'gcash' &&
                  PaymentMethodOptions.gcash) ||
                (value.gatewayProcessor === 'xendit' &&
                  PaymentMethodOptions.xendit) ||
                [],
              label: 'Payment Method',
              placeholder: 'Payment Method',
              validation: Yup.array()
                .min(1, 'Please select at least one payment method')
                .required('Please select a value')
                .nullable(),
              required: true,
              disableWhen: value => !value.gatewayProcessor,
              initialValue: [],
            },
            transactionType: {
              type: FIELD_TYPES.SELECT,
              options: BILL_TYPES,
              label: 'Transaction Type',
              placeholder: 'Transaction Type',
              validation: Yup.string()
                .required('Please select a value')
                .nullable(),
              required: true,
              initialValue: '',
            },
            convenienceFeeType: {
              type: FIELD_TYPES.SELECT,
              options: [
                { label: 'Off', value: 'Off' },
                { label: 'Flat', value: 'Flat' },
                { label: 'Percent', value: 'Percent' },
                { label: 'Tiered Scheme', value: 'Tiered_Scheme' },
              ],
              label: 'ConvenienceFee Type',
              placeholder: 'ConvenienceFee Type',
              validation: Yup.string().nullable(),
              initialValue: 'Off',
            },
            convenienceFeeValue: {
              type: FIELD_TYPES.TEXT,
              label: 'ConvenienceFee Value',
              placeholder: 'ConvenienceFee Value',
              validation: value =>
                value.convenienceFeeType !== 'Off'
                  ? Yup.string()
                      .matches(
                        /^\d*(\.\d{0,2})?$/,
                        'Invalid input! Allowed are positive number or number with no more than 2 decimal places only'
                      )
                      .required('Please enter a value')
                  : Yup.string().matches(
                      /^\d*(\.\d{0,2})?$/,
                      'Invalid input! Allowed are positive number or number with no more than 2 decimal places only'
                    ),
              requiredWhen: value => value.convenienceFeeType !== 'Off',
              initialValueWhen: data =>
                data.convenienceFeeType === 'Off' ? '' : '',
            },
            convenienceFeeThreshold: {
              type: FIELD_TYPES.TEXT,
              label: 'ConvenienceFee Threshold',
              placeholder: 'ConvenienceFee Threshold',
              validation: value =>
                value.convenienceFeeType !== 'Off'
                  ? Yup.string()
                      .matches(
                        /^\d*(\.\d{0,2})?$/,
                        'Invalid input! Allowed are positive number or number with no more than 2 decimal places only'
                      )
                      .required('Please enter a value')
                  : Yup.string().matches(
                      /^\d*(\.\d{0,2})?$/,
                      'Invalid input! Allowed are positive number or number with no more than 2 decimal places only'
                    ),
              requiredWhen: value => value.convenienceFeeType !== 'Off',
              initialValueWhen: data =>
                data.convenienceFeeType === 'Off' ? '' : '',
            },
            convenienceFeeTieredScheme: {
              type: FIELD_TYPES.TEXT,
              label: 'ConvenienceFee % (Tiered Scheme)',
              placeholder: 'ConvenienceFee % (Tiered Scheme)',
              validation: value =>
                value.convenienceFeeType === 'Tiered_Scheme'
                  ? Yup.string()
                      .matches(
                        /^\d*(\.\d{0,2})?$/,
                        'Invalid input! Allowed are positive number or number with no more than 2 decimal places only'
                      )
                      .required('Please enter a value')
                  : Yup.string().matches(
                      /^\d*(\.\d{0,2})?$/,
                      'Invalid input! Allowed are positive number or number with no more than 2 decimal places only'
                    ),
              requiredWhen: value =>
                value.convenienceFeeType === 'Tiered_Scheme',
              disableWhen: data => data.convenienceFeeType !== 'Tiered_Scheme',
            },
          }}
        />
      )}

      <AlertModal
        isOpen={state.isLeavingPageWhileAdding}
        title="New ConvenienceFee Alert"
        icon="question-circle"
        variant="warn"
        header="SAVE CONVENIENCEFEE?"
        subHeader="You are about to leave without saving New ConvenienceFee."
        description="Your entry will be lost if you don't save it"
        handleClose={() =>
          setState({ ...state, isLeavingPageWhileAdding: false })
        }
        cancelText="Discard Entry"
        confirmText="Go Back"
        handleCancel={() => {
          setState({
            ...state,
            isLeavingPageWhileAdding: false,
            isAddConvenieceFeeModalOpen: false,
          });
        }}
        handleConfirm={() => {
          setState({ ...state, isLeavingPageWhileAdding: false });
        }}
      />

      <AlertModal
        isOpen={state.isConfirmAddConvenieceFeeModalOpen}
        title="New ConvenienceFee Alert"
        icon="exclamation-circle"
        variant="warn"
        header="ARE YOU SURE?"
        subHeader=
        { state.selectedConvenienceFee?.brands?.length > 1 ||
          state.selectedConvenienceFee?.paymentMethods?.length > 1 
          ? `You are about to create ${
            (state.selectedConvenienceFee?.brands?.length || 0) * 
            (state.selectedConvenienceFee?.paymentMethods?.length || 0)
          } ConvenienceFee entries.`
        : "You are about to create a new ConvenienceFee."
    }
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        confirmLoading={isAddingConvenienceFee}
        confirmText="Yes"
        handleConfirm={() => {
          addConvenienceFee({
            variables: { data: sanitize(state.selectedConvenienceFee) },
          });
        }}
        handleClose={() => {
          setState({
            ...state,
            isConfirmAddConvenieceFeeModalOpen: false,
          });
        }}
      />

      <AlertModal
        isOpen={state.isConfirmDeleteConvenienceFeeModalOpen}
        title="Delete Convenience Fee Alert"
        icon="exclamation-circle"
        variant="warn"
        header="ARE YOU SURE?"
        subHeader="You are about to delete a Convenience Fee."
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() =>
          setState({ ...state, isConfirmDeleteConvenienceFeeModalOpen: false })
        }
        selectLabel="Reason"
        options={['No longer in use', 'Others'].map(reason => ({
          value: reason,
          label: reason,
        }))}
        confirmLoading={isDeletingConvenienceFee}
        confirmText="Yes"
        handleConfirm={value => {
          deleteConvenienceFee({
            variables: {
              data: { reasonToDelete: value },
              where: { id: convenienceFeeId },
            },
          });
        }}
      />

      <AlertModal
        isOpen={state.isSuccessDeleteConvenienceFeeModalOpen}
        title="Delete Convenience Fee Alert"
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader="Convenience Fee has been deleted successfully."
        description="Deleted Convenience Fee is now no longer in use."
        handleClose={() =>
          setState({ ...state, isSuccessDeleteConvenienceFeeModalOpen: false })
        }
        confirmText="Go to All Convenience Fee"
        handleConfirm={() =>
          setState({ ...state, isSuccessDeleteConvenienceFeeModalOpen: false })
        }
      />

      <AlertModal
        isOpen={state.isFailureDeleteConvenienceFeeModalOpen}
        title="Delete Convenience Fee Alert"
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader="There was a problem on deleting Convenience Fee."
        description="Please go back and try deleting again."
        handleClose={() =>
          setState({ ...state, isFailureDeleteConvenienceFeeModalOpen: false })
        }
        confirmText="Go Back"
        handleConfirm={() => {
          setState({ ...state, isFailureDeleteConvenienceFeeModalOpen: false });
        }}
      />

      <AlertModal
        isOpen={state.isSuccessAddConvenieceFeeModalOpen}
        title="New ConvenienceFee Alert"
        handleClose={() => {
          setState({ ...state, isSuccessAddConvenieceFeeModalOpen: false , bulkCreationResult: null, });
        }}
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader={
          state.bulkCreationResult
            ? state.bulkCreationResult.skipped > 0
              ? `${state.bulkCreationResult.created} ConvenienceFee entries created successfully. ${state.bulkCreationResult.skipped} entries already exist.`
              : `${state.bulkCreationResult.created} ConvenienceFee entries have been created successfully.`
            : "ConvenienceFee has been created successfully."
        }
        description={
          state.bulkCreationResult?.skipped > 0
            ? "Multiple ConvenienceFee entries are now available for editing."
            : "Editing of ConvenienceFee is now enabled."
        }
        confirmText="Go to All ConvenienceFee"
        handleConfirm={() => {
          setState({ ...state, isSuccessAddConvenieceFeeModalOpen: false , bulkCreationResult:null, });
        }}
      />

      <AlertModal
        isOpen={state.isFailureAddConvenieceFeeModalOpen}
        title="New ConvenienceFee Alert"
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader={
          state.addConvenienceFeeError?.includes('All Convenience Fee combinations already exist')
            ? 'All Convenience Fee combinations already exist'
            : state.addConvenienceFeeError?.includes('CONVENIENCE_FEE_BULK_CONFLICTS')
            ? 'Some Convenience Fee combinations already exist'
            : state.addConvenienceFeeError === 'CONVENIENCE_FEE_ALREADY_EXISTS'
            ? 'Convenience Fee already exist unable to add convenience fee'
            : 'There was a problem on saving New ConvenienceFee.'
        }
        description={
          state.addConvenienceFeeError?.includes('All Convenience Fee combinations already exist')
            ? "All selected combinations already exist. Please choose different ones."
            : state.addConvenienceFeeError?.includes('CONVENIENCE_FEE_BULK_CONFLICTS')
            ? "Some combinations already exist. Please review your selections."
            : "Please go back and try saving it again."
        }
        handleClose={() =>
          setState({ ...state, isFailureAddConvenieceFeeModalOpen: false })
        }
        confirmText="Go Back"
        handleConfirm={() => {
          setState({ ...state, isFailureAddConvenieceFeeModalOpen: false });
        }}
      />

      {state.isEditConvenieceFeeModalOpen && (
        <FormModal
          isOpen={state.isEditConvenieceFeeModalOpen}
          width="600px"
          handleClose={() =>
            setState({ ...state, isLeavingPageWhileEditing: true })
          }
          title="Edit ConvenienceFee"
          instructions={<span>Fill out the form with new fields.</span>}
          submitText="Update ConvenienceFee"
          handleSubmit={values => {
            setState({
              ...state,
              selectedConvenienceFee: values,
              isConfirmEditConvenieceFeeModalOpen: true,
            });
          }}
          changeListener={(name, data, values, setValues) => {
            if (name === 'convenienceFeeType' && data !== 'Tiered_Scheme') {
              setValues(prevState => ({
                ...prevState,
                convenienceFeeTieredScheme: '',
              }));
            }

            if (
              name === 'gatewayProcessor' &&
              data !== values.gatewayProcessor
            ) {
              setValues({
                ...values,
                gatewayProcessor: data,
                paymentMethod: '',
              });
            }
          }}
          fields={{
            channelId: {
              type: FIELD_TYPES.SELECT,
              label: 'Channel',
              options: channelOptions,
              placeholder: 'Channel',
              validation: Yup.string()
                .required('Please select a value')
                .nullable(),
              required: true,
              readOnly: true,
              initialValue: state.selectedConvenienceFee.channelId,
            },
            brand: {
              type: FIELD_TYPES.SELECT,
              label: 'Brand',
              options: brandOptions,
              placeholder: 'Brand',
              validation: Yup.string()
                .required('Please select a value')
                .nullable(),
              required: true,
              initialValue: state.selectedConvenienceFee.brand,
            },
            gatewayProcessor: {
              type: FIELD_TYPES.SELECT,
              options: PAYMENT_GATEWAYS,
              label: 'Gateway Processor',
              placeholder: 'Gateway Processor',
              validation: Yup.string()
                .required('Please select a value')
                .nullable(),
              required: true,
              initialValue: state.selectedConvenienceFee.gatewayProcessor,
            },
            paymentMethod: {
              type: FIELD_TYPES.SELECT,
              dynamicOptions: value =>
                (value.gatewayProcessor === 'adyen' &&
                  PaymentMethodOptions.adyen) ||
                (value.gatewayProcessor === 'gcash' &&
                  PaymentMethodOptions.gcash) ||
                (value.gatewayProcessor === 'xendit' &&
                  PaymentMethodOptions.xendit) ||
                [],
              label: 'Payment Method',
              placeholder: 'Payment Method',
              validation: Yup.string()
                .required('Please select a value')
                .nullable(),
              required: true,
              disableWhen: value => !value.gatewayProcessor,
              initialValue: state.selectedConvenienceFee.paymentMethod,
            },
            transactionType: {
              type: FIELD_TYPES.SELECT,
              options: BILL_TYPES,
              label: 'Transaction Type',
              placeholder: 'Transaction Type',
              validation: Yup.string()
                .required('Please select a value')
                .nullable(),
              required: true,
              initialValue: state.selectedConvenienceFee.transactionType,
            },
            convenienceFeeType: {
              type: FIELD_TYPES.SELECT,
              options: [
                { label: 'Off', value: 'Off' },
                { label: 'Flat', value: 'Flat' },
                { label: 'Percent', value: 'Percent' },
                { label: 'Tiered Scheme', value: 'Tiered_Scheme' },
              ],
              label: 'ConvenienceFee Type',
              placeholder: 'ConvenienceFee Type',
              validation: Yup.string().nullable(),
              initialValue: state.selectedConvenienceFee.convenienceFeeType,
            },
            convenienceFeeValue: {
              type: FIELD_TYPES.TEXT,
              label: 'ConvenienceFee Value',
              placeholder: 'ConvenienceFee Value',
              validation: value =>
                value.convenienceFeeType !== 'Off'
                  ? Yup.string()
                      .matches(
                        /^\d*(\.\d{0,2})?$/,
                        'Invalid input! Allowed are positive number or number with no more than 2 decimal places only'
                      )
                      .required('Please enter a value')
                  : Yup.string().matches(
                      /^\d*(\.\d{0,2})?$/,
                      'Invalid input! Allowed are positive number or number with no more than 2 decimal places only'
                    ),
              requiredWhen: value => value.convenienceFeeType !== 'Off',
              initialValue: state.selectedConvenienceFee.convenienceFeeValue,
            },
            convenienceFeeThreshold: {
              type: FIELD_TYPES.TEXT,
              label: 'ConvenienceFee Threshold',
              placeholder: 'ConvenienceFee Threshold',
              validation: value =>
                value.convenienceFeeType !== 'Off'
                  ? Yup.string()
                      .matches(
                        /^\d*(\.\d{0,2})?$/,
                        'Invalid input! Allowed are positive number or number with no more than 2 decimal places only'
                      )
                      .required('Please enter a value')
                  : Yup.string().matches(
                      /^\d*(\.\d{0,2})?$/,
                      'Invalid input! Allowed are positive number or number with no more than 2 decimal places only'
                    ),
              requiredWhen: value => value.convenienceFeeType !== 'Off',
              initialValue:
                state.selectedConvenienceFee.convenienceFeeThreshold,
            },
            convenienceFeeTieredScheme: {
              type: FIELD_TYPES.TEXT,
              label: 'ConvenienceFee % (Tiered Scheme)',
              placeholder: 'ConvenienceFee % (Tiered Scheme)',
              validation: value =>
                value.convenienceFeeType === 'Tiered_Scheme'
                  ? Yup.string()
                      .matches(
                        /^\d*(\.\d{0,2})?$/,
                        'Invalid input! Allowed are positive number or number with no more than 2 decimal places only'
                      )
                      .required('Please enter a value')
                  : Yup.string().matches(
                      /^\d*(\.\d{0,2})?$/,
                      'Invalid input! Allowed are positive number or number with no more than 2 decimal places only'
                    ),
              requiredWhen: value =>
                value.convenienceFeeType === 'Tiered_Scheme',
              disableWhen: data => data.convenienceFeeType !== 'Tiered_Scheme',
              initialValue:
                state.selectedConvenienceFee.convenienceFeeType ===
                'Tiered_Scheme'
                  ? state.selectedConvenienceFee.convenienceFeeTieredScheme
                  : '',
            },
          }}
        />
      )}

      <AlertModal
        isOpen={state.isLeavingPageWhileEditing}
        title="Edit ConvenienceFee Alert"
        icon="question-circle"
        variant="warn"
        header="SAVE CONVENIENCEFEE?"
        subHeader="You are about to leave without saving Modified ConvenienceFee."
        description="Your entry will be lost if you don't save it"
        handleClose={() =>
          setState({ ...state, isLeavingPageWhileEditing: false })
        }
        cancelText="Discard Entry"
        confirmText="Go Back"
        handleCancel={() => {
          setState({
            ...state,
            isLeavingPageWhileEditing: false,
            isEditConvenieceFeeModalOpen: false,
          });
        }}
        handleConfirm={() => {
          setState({ ...state, isLeavingPageWhileEditing: false });
        }}
      />

      <AlertModal
        isOpen={state.isConfirmEditConvenieceFeeModalOpen}
        title="Edit ConvenienceFee Alert"
        icon="exclamation-circle"
        variant="warn"
        header="ARE YOU SURE?"
        subHeader="You are about to update an existing ConvenienceFee."
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        confirmLoading={isEditingConvenienceFEe}
        confirmText="Yes"
        handleConfirm={() => {
          const payload = {
            ...state.selectedConvenienceFee,
            ...(state.selectedConvenienceFee.convenienceFeeType !==
              'Tiered_Scheme' && { convenienceFeeTieredScheme: '0' }),
          };

          editConvenienceFee({
            variables: {
              data: sanitize(payload),
              where: { id: convenienceFeeId },
            },
          });
        }}
        handleClose={() => {
          setState({
            ...state,
            isConfirmEditConvenieceFeeModalOpen: false,
          });
        }}
      />

      <AlertModal
        isOpen={state.isFailureEditConvenienceFeeModalOpen}
        title="Edit ConvenienceFee Alert"
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader={
          state.editConvenienceFeeError === 'CONVENIENCE_FEE_ALREADY_EXISTS'
            ? 'Convenience Fee already exist unable to edit conveniece fee'
            : 'There was a problem on saving ConvenienceFee.'
        }
        description="Please go back and try saving it again."
        handleClose={() =>
          setState({ ...state, isFailureEditConvenienceFeeModalOpen: false })
        }
        confirmText="Go Back"
        handleConfirm={() => {
          setState({ ...state, isFailureEditConvenienceFeeModalOpen: false });
        }}
      />

      <AlertModal
        isOpen={state.isSuccessEditConvenieceFeeModalOpen}
        title="Edit ConvenienceFee Alert"
        handleClose={() => {
          setState({ ...state, isSuccessEditConvenieceFeeModalOpen: false });
        }}
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader="ConvenienceFee has been updated successfully."
        description="Changes are now reflected on the record."
        confirmText="Go to All ConvenienceFee"
        handleConfirm={() => {
          setState({ ...state, isSuccessEditConvenieceFeeModalOpen: false });
        }}
      />
    </>
  );
};

export default ConvenienceFee;
