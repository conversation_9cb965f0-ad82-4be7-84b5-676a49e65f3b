import { gql } from '@apollo/client';

export const ADD_CONVENIENCE_FEE = gql`
  mutation createConvenienceFee($data: CreateConvenienceFeeInput!) {
    createConvenienceFee(data: $data) {
      created
      skipped
      entries {
        channelId
        brand
        gatewayProcessor
        paymentMethod
        transactionType
        convenienceFeeType
        convenienceFeeValue
        convenienceFeeThreshold
        convenienceFeeTieredScheme
        createdAt
        updatedAt
      }
    }
  }
`;

export const EDIT_CONVENIENCE_FEE = gql`
  mutation editConvenienceFee(
    $data: updateConvenienceFeeInput!
    $where: ConvenienceFeePrimary!
  ) {
    updateConvenienceFee(data: $data, where: $where) {
      channelId
      brand
      gatewayProcessor
      paymentMethod
      transactionType
      convenienceFeeType
      convenienceFeeValue
      convenienceFeeThreshold
      convenienceFeeTieredScheme
    }
  }
`;

export const DELETE_CONVENIENCE_FEE = gql`
  mutation deleteConvenienceFee(
    $data: DeleteConvenienceFeeInput!
    $where: ConvenienceFeePrimary!
  ) {
    deleteConvenienceFee(data: $data, where: $where) {
      id
    }
  }
`;
