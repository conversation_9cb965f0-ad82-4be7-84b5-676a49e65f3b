import { gql } from '@apollo/client';

export const GET_CONVENIENCE_FEES = gql`
  query getConvenienceFees(
    $filter: SearchConvenienceFeeInput
    $pagination: PaginationInput!
  ) {
    convenienceFees(filter: $filter, pagination: $pagination) {
      cursors
      count
      filteredData {
        id
        channelId
        name
        brand
        gatewayProcessor
        paymentMethod
        transactionType
        convenienceFeeType
        convenienceFeeValue
        convenienceFeeThreshold
        convenienceFeeTieredScheme
        createdAt
        updatedAt
      }
    }
  }
`;
