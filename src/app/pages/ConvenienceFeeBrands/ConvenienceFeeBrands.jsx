import React, { useState, useContext } from 'react';
import format from 'date-fns/format';
import { useQuery, useMutation } from '@apollo/client';
import Header from '../../components/Header';
import Page from '../../components/Page';
import DataContainer from '../../components/DataContainer';
import AlertModal from '../../components/Modal/AlertModal';
import DataTable from '../../components/DataTable';
import CreateButton from '../../components/Button/CreateButton';
import ActionButtons from '../../components/ActionButtons';
import AuthContext from '../../context/AuthContext/AuthContext';
import { FormModal } from '../../components/Modal';
import { Required } from '../../components/Form/FormField';
import * as Yup from 'yup';
import { GET_CONVENIENCE_FEE_BRANDS } from './query';
import {
  ADD_CONVENIENCE_FEE_BRAND,
  EDIT_CONVENIENCE_FEE_BRAND,
  DELETE_CONVENIENCE_FEE_BRAND,
} from './mutation';
import { FIELD_TYPES } from '../../components/Form/constants';
import sanitize from '../../utils/sanitize';

const ConvenienceFeeBrands = () => {
  const { permissions } = useContext(AuthContext);

  const [state, setState] = useState({
    filter: {},
    pagination: {
      limit: 10,
      startKey: '',
    },
    isLeavingPageWhileAdding: false,
    isAddBrandModalOpen: false,
    isConfirmAddBrandModalOpen: false,
    isSuccessAddBrandModalOpen: false,
    isEditBrandModalOpen: false,
    isConfirmEditBrandModalOpen: false,
    isSuccessEditBrandModalOpen: false,
    isDeleteBrandModalOpen: false,
    isConfirmDeleteBrandModalOpen: false,
    isSuccessDeleteBrandModalOpen: false,
    isFailureAddBrandModalOpen: false,
    isFailureEditBrandModalOpen: false,
    isFailureDeleteBrandModalOpen: false,
    brandData: {},
    isLeavingPageWhileEditing: false,
  });

  const { data, loading, refetch } = useQuery(GET_CONVENIENCE_FEE_BRANDS, {
    variables: {
      pagination: state.pagination,
      filter: state.filter,
    },
    fetchPolicy: 'network-only',
  });

  const [addConvenienceFeeBrand, { loading: isAddingConvenienceFeeBrand }] =
    useMutation(ADD_CONVENIENCE_FEE_BRAND, {
      onCompleted: () => {
        setState({
          ...state,
          isConfirmAddBrandModalOpen: false,
          isSuccessAddBrandModalOpen: true,
          isAddBrandModalOpen: false,
          pagination: {
            ...state.pagination,
            startKey: '',
          },
        });
        refetch();
      },
      onError: err => {
        setState({
          ...state,
          addBrandError: err.networkError.result
            ? err.networkError.result.message
            : null,
          isConfirmAddBrandModalOpen: false,
          isFailureAddBrandModalOpen: true,
        });
      },
    });

  const [editConvenienceFeeBrand, { loading: isEditingConvenienceFeeBrand }] =
    useMutation(EDIT_CONVENIENCE_FEE_BRAND, {
      onCompleted: () => {
        setState({
          ...state,
          isConfirmEditBrandModalOpen: false,
          isSuccessEditBrandModalOpen: true,
          isEditBrandModalOpen: false,
          pagination: {
            ...state.pagination,
            startKey: '',
          },
        });
        refetch();
      },
      onError: err => {
        setState({
          ...state,
          addBrandError: err.networkError.result
            ? err.networkError.result.message
            : null,
          isConfirmAddBrandModalOpen: false,
          isFailureAddBrandModalOpen: true,
        });
      },
    });

  const [
    deleteConvenienceFeeBrand,
    { loading: isDeletingConvenienceFeeBrand },
  ] = useMutation(DELETE_CONVENIENCE_FEE_BRAND, {
    onCompleted: () => {
      setState({
        ...state,
        isConfirmDeleteBrandModalOpen: false,
        isSuccessDeleteBrandModalOpen: true,
      });
      refetch();
    },
    onError: () => {
      setState({
        ...state,
        isConfirmDeleteBrandModalOpen: false,
        isFailureDeleteBrandModalOpen: true,
      });
    },
  });

  return (
    <>
      <Page>
        <Header
          withHome
          title="Convenience Fee Brands"
          path={['ConvenienceFeeBrands']}
        />
        <DataContainer>
          <DataTable
            loading={loading}
            data={data?.convenienceFeeBrands?.filteredData ?? []}
            headerOptions={
              <>
                <div />
                <div style={{ display: 'flex' }}>
                  {permissions.ConvenienceFeeBrand.create && (
                    <CreateButton
                      icon="plus"
                      onClick={() => {
                        setState({
                          ...state,
                          isAddBrandModalOpen: true,
                        });
                      }}
                    >
                      Add New Brand
                    </CreateButton>
                  )}
                </div>
              </>
            }
            config={{
              name: {
                headerLabel: 'Brand Name',
                sortable: true,
              },
              createdAt: {
                headerLabel: 'Created At',
                sortable: true,
                renderAs: data =>
                  format(data.createdAt, 'MM/DD/YYYY - hh:mm:ss A'),
              },
              updatedAt: {
                headerLabel: 'Updated At',
                sortable: true,
                renderAs: data =>
                  format(data.updatedAt, 'MM/DD/YYYY - hh:mm:ss A'),
              },

              actions: {
                renderAs: data => (
                  <ActionButtons
                    disabled={{
                      edit: !permissions.ConvenienceFeeBrand.update,
                      delete: !permissions.ConvenienceFeeBrand.delete,
                    }}
                    handleEdit={() =>
                      setState(prevState => ({
                        ...prevState,
                        brandData: data,
                        isEditBrandModalOpen: true,
                      }))
                    }
                    handleDelete={() =>
                      setState(prevState => ({
                        ...prevState,
                        brandData: data,
                        isConfirmDeleteBrandModalOpen: true,
                      }))
                    }
                  />
                ),
              },
            }}
            pagination={{
              ...state.pagination,
              count: data?.convenienceFeeBrands?.count ?? 0,
              cursors: data?.convenienceFeeBrands?.cursors ?? [''],
              handleChange: pagination => {
                setState({ ...state, pagination });
              },
            }}
          />
        </DataContainer>
      </Page>

      {state.isAddBrandModalOpen && (
        <FormModal
          isOpen={state.isAddBrandModalOpen}
          width="600px"
          handleClose={() =>
            setState(prevState => ({
              ...prevState,
              isAddBrandModalOpen: false,
            }))
          }
          title="Add New Brand"
          instructions={
            <span>
              To create new Brand, please fill out the required
              <Required>*</Required> fields.
            </span>
          }
          submitText="Create Convenience Fee Brand"
          handleSubmit={values => {
            setState({
              ...state,
              brandData: values,
              isConfirmAddBrandModalOpen: true,
            });
          }}
          fields={{
            name: {
              type: FIELD_TYPES.TEXT,
              label: 'Name',
              placeholder: 'Name',
              validation: Yup.string()
                .max(64, 'Must be at most 64 chars long')
                .required('This field is required'),
              required: true,
              initialValue: '',
            },
          }}
        />
      )}
      {state.isEditBrandModalOpen && (
        <FormModal
          isOpen={state.isEditBrandModalOpen}
          width="600px"
          handleClose={() =>
            setState({ ...state, isLeavingPageWhileEditing: true })
          }
          title="Edit Convenience Fee Brand"
          instructions={<span>Fill out the form with new fields.</span>}
          submitText="Update Convenience Fee Brand"
          handleSubmit={values => {
            setState({
              ...state,
              brandData: {
                ...state.brandData,
                name: values.name,
              },
              isConfirmEditBrandModalOpen: true,
            });
          }}
          fields={{
            name: {
              type: FIELD_TYPES.TEXT,
              label: 'Name',
              placeholder: 'Name',
              validation: Yup.string()
                .max(64, 'Must be at most 64 chars long')
                .required('This field is required'),
              required: true,
              initialValue: state.brandData?.name,
            },
          }}
        />
      )}

      <AlertModal
        isOpen={state.isConfirmAddBrandModalOpen}
        title="New Brand Alert"
        icon="exclamation-circle"
        variant="warn"
        header="ARE YOU SURE?"
        subHeader="You are about to create a new Convenience Fee Brand."
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        confirmLoading={isAddingConvenienceFeeBrand}
        confirmText="Yes"
        handleConfirm={() => {
          addConvenienceFeeBrand({
            variables: { data: sanitize(state.brandData) },
          });
        }}
        handleClose={() => {
          setState({
            ...state,
            isConfirmAddBrandModalOpen: false,
          });
        }}
      />
      <AlertModal
        isOpen={state.isConfirmEditBrandModalOpen}
        title="Edit Brand Alert"
        icon="exclamation-circle"
        variant="warn"
        header="ARE YOU SURE?"
        subHeader="You are about to update the Convenience Fee Brand."
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        confirmLoading={isEditingConvenienceFeeBrand}
        confirmText="Yes"
        handleConfirm={() => {
          editConvenienceFeeBrand({
            variables: {
              data: { name: state.brandData?.name },
              where: { id: state.brandData?.id },
            },
          });
        }}
        handleClose={() => {
          setState({
            ...state,
            isConfirmEditBrandModalOpen: false,
          });
        }}
      />
      <AlertModal
        isOpen={state.isSuccessAddBrandModalOpen}
        title="New Convenience Fee Brand Alert"
        handleClose={() => {
          setState({ ...state, isSuccessAddBrandModalOpen: false });
        }}
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader="New convenience fee brand has been created successfully."
        description="Editing of Convenience Fee Brand is now enabled."
        confirmText="Go to All Convenience Fee Brands"
        handleConfirm={() => {
          setState({ ...state, isSuccessAddBrandModalOpen: false });
        }}
      />
      <AlertModal
        isOpen={state.isSuccessEditBrandModalOpen}
        title="Edit Convenience Fee Brand Alert"
        handleClose={() => {
          setState({ ...state, isSuccessEditBrandModalOpen: false });
        }}
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader="Convenience fee brand has been updated successfully."
        description="Changes are now reflected on the record."
        confirmText="Go to All Convenience Fee Brands"
        handleConfirm={() => {
          setState({ ...state, isSuccessEditBrandModalOpen: false });
        }}
      />
      <AlertModal
        isOpen={state.isConfirmDeleteBrandModalOpen}
        title="Delete Convenience Fee Brand Alert"
        icon="exclamation-circle"
        variant="warn"
        header="ARE YOU SURE?"
        subHeader="You are about to delete a Convenience Fee Brand."
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() =>
          setState({ ...state, isConfirmDeleteBrandModalOpen: false })
        }
        confirmLoading={isDeletingConvenienceFeeBrand}
        confirmText="Yes"
        handleConfirm={() => {
          deleteConvenienceFeeBrand({
            variables: {
              where: { id: state.brandData?.id },
            },
          });
        }}
      />

      <AlertModal
        isOpen={state.isSuccessDeleteBrandModalOpen}
        title="Delete Convenience Fee Brand Alert"
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader="Convenience Fee Brand has been deleted successfully."
        description="Deleted Convenience Fee Brand is now no longer in use."
        handleClose={() =>
          setState({ ...state, isSuccessDeleteBrandModalOpen: false })
        }
        confirmText="Go to All Convenience Fee Brands"
        handleConfirm={() =>
          setState({ ...state, isSuccessDeleteBrandModalOpen: false })
        }
      />

      <AlertModal
        isOpen={state.isFailureAddBrandModalOpen}
        title="New Convenience Fee Brand Alert"
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader={
          state.addBrandError === 'CONVENIENCE_FEE_BRAND_ALREADY_EXISTS'
            ? 'Convenience fee brand already exists. Unable to add conveniece fee brand.'
            : 'There was a problem on saving new convenience fee brand.'
        }
        description="Please go back and try saving it again."
        handleClose={() =>
          setState({
            ...state,
            isFailureAddBrandModalOpen: false,
          })
        }
        confirmText="Go Back"
        handleConfirm={() => {
          setState({
            ...state,
            isFailureAddBrandModalOpen: false,
          });
        }}
      />
      <AlertModal
        isOpen={state.isFailureEditBrandModalOpen}
        title="Edit Convenience Fee Brand Alert"
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader={
          state.editBrandError === 'CONVENIENCE_FEE_BRAND_ALREADY_EXISTS'
            ? 'Convenience fee brand already exists. Unable to edit conveniece fee brand.'
            : 'There was a problem on saving convenience fee brand.'
        }
        description="Please go back and try saving it again."
        handleClose={() =>
          setState({
            ...state,
            isFailureEditBrandModalOpen: false,
          })
        }
        confirmText="Go Back"
        handleConfirm={() => {
          setState({
            ...state,
            isFailureEditBrandModalOpen: false,
          });
        }}
      />
      <AlertModal
        isOpen={state.isFailureDeleteBrandModalOpen}
        title="Delete Convenience Fee Brand Alert"
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader="There was a problem on deleting Convenience Fee Brand."
        description="Please go back and try deleting again."
        handleClose={() =>
          setState({
            ...state,
            isFailureDeleteBrandModalOpen: false,
          })
        }
        confirmText="Go Back"
        handleConfirm={() => {
          setState({
            ...state,
            isFailureDeleteBrandModalOpen: false,
          });
        }}
      />

      <AlertModal
        isOpen={state.isLeavingPageWhileEditing}
        title="Edit Brand Alert"
        icon="question-circle"
        variant="warn"
        header="SAVE BRAND?"
        subHeader="You are about to leave without saving Modified Brand."
        description="Your entry will be lost if you don't save it"
        handleClose={() =>
          setState({ ...state, isLeavingPageWhileEditing: false })
        }
        cancelText="Discard Entry"
        confirmText="Go Back"
        handleCancel={() => {
          setState({
            ...state,
            isLeavingPageWhileEditing: false,
            isEditBrandModalOpen: false,
          });
        }}
        handleConfirm={() => {
          setState({ ...state, isLeavingPageWhileEditing: false });
        }}
      />
    </>
  );
};

export default ConvenienceFeeBrands;
