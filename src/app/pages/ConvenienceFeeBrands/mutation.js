import { gql } from '@apollo/client';

export const ADD_CONVENIENCE_FEE_BRAND = gql`
  mutation createConvenienceFeeBrand($data: CreateConvenienceFeeBrandInput!) {
    createConvenienceFeeBrand(data: $data) {
      id
      name
    }
  }
`;

export const EDIT_CONVENIENCE_FEE_BRAND = gql`
  mutation updateConvenienceFeeBrand(
    $data: updateConvenienceFeeBrandInput!
    $where: ConvenienceFeeBrandPrimary!
  ) {
    updateConvenienceFeeBrand(data: $data, where: $where) {
      id
      name
    }
  }
`;

export const DELETE_CONVENIENCE_FEE_BRAND = gql`
  mutation deleteConvenienceFeeBrand($where: ConvenienceFeeBrandPrimary!) {
    deleteConvenienceFeeBrand(where: $where) {
      id
      name
    }
  }
`;
