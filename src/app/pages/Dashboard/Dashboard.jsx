import PropTypes from 'prop-types';
import React, { useEffect, useContext } from 'react';
import { useQuery } from '@apollo/client';
import styled from 'styled-components';
import Button from '../../components/Button/Button';
import Column from '../../components/Column';
import Header from '../../components/Header';
import Page from '../../components/Page';
import Row from '../../components/Row';
import DashboardContainer from './components/DashboardContainer';
import AdjyenMonitorPanel from './modules/AdyenMonitorPanel';
import ChannelOverallTransactionPanel from './modules/ChannelOverallTransactionPanel';
import ChannelPaymentStatusPanel from './modules/ChannelPaymentStatusPanel';
import ChannelRevenuePanel from './modules/ChannelRevenuePanel';
import PerformancePanel from './modules/PerformancePanel';
import OverallTransactionPercentagePanel from './modules/OverallTransactionPercentagePanel';
import OverallGcashTransactionsPanel from './modules/OverallGcashTransactionsPanel';
import OverallTransactionsPanel from './modules/OverallTransactionsPanel';
import SystemNotificationsPanel from './modules/SystemNotificationsPanel';
import UserManagementPanel from './modules/UserManagementPanel';
import { GET_CHANNEL_OPTIONS, GET_LAST_UPDATED } from './query';
import AuthContext from '../../context/AuthContext/AuthContext';
import ChannelAmountTransactionsPanel from './modules/ChannelAmountTransactionsPanel';
import ChannelCountTransactionsPanel from './modules/ChannelCountTransactionsPanel';

export const ViewAllButton = styled(Button)`
  padding: 5px;
  margin: 5px;
  color: #0072ce;
`;

export const ScrollableContainer = styled.div`
  overflow-y: auto;
  max-height: ${props => props.maxHeight};
`;

export const PushedRow = styled(Row)`
  justify-content: flex-end;
`;

export const StretchedRow = styled(Row)`
  align-items: initial;
`;

export const ColumnPanel = styled(Column)`
  flex: initial;
  width: 30%;
  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    width: 100%;
  }
`;

export const months = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
];

const Dashboard = ({ history }) => {
  const { dashboardPermissions } = useContext(AuthContext);

  const { data: channelData, loading: channelLoading } = useQuery(
    GET_CHANNEL_OPTIONS,
    {
      fetchPolicy: 'network-only',
      notifyOnNetworkStatusChange: true,
    }
  );

  const { data: dashboardData } = useQuery(GET_LAST_UPDATED, {
    fetchPolicy: 'network-only',
  });

  useEffect(() => {
    const searchParams = new URLSearchParams(window.location.search);
    const to = searchParams.get('to');

    if (to) {
      history.replace(to);
    }
  }, []);

  const hiddenHorizontalTable =
    !dashboardPermissions.revenuePerChannel ||
    !dashboardPermissions.transactionsPerChannel ||
    !dashboardPermissions.gatewayStatus;

  return (
    <Page>
      <Header
        title="Welcome to Globe Unified Payment Service"
        path={['My Dashboard']}
        refreshTime={
          dashboardData?.overallLastUpdatedAt &&
          dashboardData?.overallLastUpdatedAt?.lastUpdatedAt
        }
      />
      <DashboardContainer>
        {dashboardPermissions.transactions && (
          <Row>
            <OverallTransactionsPanel />
          </Row>
        )}
        {dashboardPermissions.onlineCCGCash && (
          <Row>
            <OverallGcashTransactionsPanel />
          </Row>
        )}
        {hiddenHorizontalTable && (
          <StretchedRow>
            {dashboardPermissions.notifications && <SystemNotificationsPanel />}
            {dashboardPermissions.transactionsPercentage && (
              <OverallTransactionPercentagePanel />
            )}
            {(dashboardPermissions.performance ||
              dashboardPermissions.adyen) && (
              <ColumnPanel>
                {dashboardPermissions.performance && <PerformancePanel />}
                {dashboardPermissions.adyen && <AdjyenMonitorPanel />}
              </ColumnPanel>
            )}
          </StretchedRow>
        )}
        {dashboardPermissions.revenuePerChannel && (
          <StretchedRow>
            <ChannelRevenuePanel
              channelData={channelData}
              channelLoading={channelLoading}
            />
            {dashboardPermissions.notifications && !hiddenHorizontalTable && (
              <ColumnPanel>
                <SystemNotificationsPanel />
              </ColumnPanel>
            )}
          </StretchedRow>
        )}
        {dashboardPermissions.transactionsPerChannel && (
          <StretchedRow>
            <ChannelOverallTransactionPanel
              channelData={channelData}
              channelLoading={channelLoading}
            />
            {dashboardPermissions.transactionsPercentage &&
              !hiddenHorizontalTable && (
                <ColumnPanel>
                  <OverallTransactionPercentagePanel />
                </ColumnPanel>
              )}
          </StretchedRow>
        )}
        {dashboardPermissions.gatewayStatus && (
          <StretchedRow>
            <ChannelPaymentStatusPanel />
            {(dashboardPermissions.performance || dashboardPermissions.adyen) &&
              !hiddenHorizontalTable && (
                <ColumnPanel>
                  {dashboardPermissions.performance && <PerformancePanel />}
                  {dashboardPermissions.adyen && <AdjyenMonitorPanel />}
                </ColumnPanel>
              )}
          </StretchedRow>
        )}
        {dashboardPermissions.channelTransaction && (
          <Row>
            <ChannelAmountTransactionsPanel
              channelData={channelData}
              channelLoading={channelLoading}
            />
          </Row>
        )}
        {dashboardPermissions.channelTransaction && (
          <Row>
            <ChannelCountTransactionsPanel
              channelData={channelData}
              channelLoading={channelLoading}
            />
          </Row>
        )}
        {dashboardPermissions.userMgmt && (
          <Row>
            <UserManagementPanel />
          </Row>
        )}
      </DashboardContainer>
    </Page>
  );
};

Dashboard.propTypes = {
  history: PropTypes.object,
};

export default Dashboard;
