import differenceInMinutes from 'date-fns/difference_in_minutes';
import distanceInWords from 'date-fns/distance_in_words';
import PropTypes from 'prop-types';
import React from 'react';
import styled from 'styled-components';
import Row from '../../../components/Row';

const StyledChannelStatus = styled(Row)`
  border-radius: 5px;
  padding: 10px;
  margin: 10px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.11);
  background-color: ${props => (props.recent ? '#e9ffe0' : 'white')};
`;

const ChannelStatusIcon = styled.div`
  min-width: 20px;
  min-height: 20px;
  border-radius: 50%;
  background-color: ${props => (props.active ? '#2cb44b' : '#ff0050')};
  margin-right: 20px;
`;

const ChannelStatusFlex = styled.div`
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    flex-direction: column;
    align-items: flex-start;
  }
`;

const ChannelStatusName = styled.div`
  color: #333333;
  flex: 1;
  max-width: 200px;
  font-size: ${props => props.theme.fontSize.s};
`;

const ChannelStatusPaymentMethod = styled.div`
  color: #0072ce;
  flex: 1;
  font-size: ${props => props.theme.fontSize.xs};
`;

const ChannelStatusLastTransaction = styled.div`
  color: #969696;
  text-align: right;
  font-size: ${props => props.theme.fontSize.xs};
`;

const ChannelStatus = ({ channel, status, paymentMethod, updatedAt }) => {
  const recent = differenceInMinutes(new Date(), new Date(updatedAt)) < 10;

  return (
    <StyledChannelStatus recent={recent}>
      <ChannelStatusIcon active={status} />
      <ChannelStatusFlex>
        <ChannelStatusName>{channel && channel.name}</ChannelStatusName>
        <ChannelStatusPaymentMethod>
          {paymentMethod && paymentMethod.toUpperCase()}
        </ChannelStatusPaymentMethod>
        <ChannelStatusLastTransaction>
          {`Last transaction ${distanceInWords(new Date(), updatedAt)}`}
        </ChannelStatusLastTransaction>
      </ChannelStatusFlex>
    </StyledChannelStatus>
  );
};

ChannelStatus.propTypes = {
  channel: PropTypes.object.isRequired,
  paymentMethod: PropTypes.string.isRequired,
  status: PropTypes.bool,
  updatedAt: PropTypes.string,
};

export default ChannelStatus;
