import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import PropTypes from 'prop-types';
import React, { useContext, useState } from 'react';
import styled from 'styled-components';
import Button from '../../../components/Button/Button';
import Row from '../../../components/Row';
import ResponsiveContext from '../../../context/ResponsiveContext';

const DashboardPanelContainer = styled.div`
  border: 1px solid #e1e1e1;
  border-radius: 2px;
  background-color: #ffffff;
  margin-bottom: 20px;
  align-self: stretch;
  margin-right: ${props => (props.noMargin ? 0 : 16)}px;
  display: flex;
  flex-direction: column;
  font-size: ${props => props.theme.fontSize.m};

  min-width: ${props => props.width || 'auto'};
  max-width: ${props => props.width || 'auto'};
  flex: ${props => (props.width ? 'initial' : 1)};

  &:last-child {
    margin-right: 0;
  }

  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    margin-bottom: 0;
    min-width: 100%;
    max-width: 100%;
    flex: initial;
  }
`;

const DashboardPanelHeader = styled(Row)`
  padding: 10px;
  border-bottom: 1px solid #e1e1e1;

  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    padding: 5px 10px;
    border-bottom: ${props => (props.active ? '1px solid #e1e1e1' : 0)};
  }
`;

const Stretch = styled.div`
  flex: 1;
`;

const DashboardPanel = ({ title, children, width, noMargin }) => {
  const { isMobile } = useContext(ResponsiveContext);

  const [isShowingContent, setIsShowingContent] = useState(false);

  return (
    <DashboardPanelContainer width={width} noMargin={noMargin}>
      <DashboardPanelHeader active={isShowingContent}>
        <Stretch>{title}</Stretch>
        {isMobile && (
          <Button
            onClick={() => {
              setIsShowingContent(!isShowingContent);
            }}
          >
            <FontAwesomeIcon
              icon={isShowingContent ? 'angle-down' : 'angle-right'}
              size="2x"
            />
          </Button>
        )}
      </DashboardPanelHeader>

      {(isShowingContent || !isMobile) && children}
    </DashboardPanelContainer>
  );
};

DashboardPanel.propTypes = {
  title: PropTypes.string,
  children: PropTypes.element,
  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  noMargin: PropTypes.bool,
};

export default DashboardPanel;
