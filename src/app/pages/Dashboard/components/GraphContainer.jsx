import PropTypes from 'prop-types';
import React from 'react';
import styled from 'styled-components';

const StyledGraphContainer = styled.div`
  overflow-x: auto;
  overflow-y: auto;
`;

const GraphInnerContainer = styled.div`
  height: 350px;
  min-width: 0px;

  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    min-width: 500px;
  }
`;

const GraphContainer = ({ children }) => {
  return (
    <StyledGraphContainer>
      <GraphInnerContainer>{children}</GraphInnerContainer>
    </StyledGraphContainer>
  );
};

GraphContainer.propTypes = {
  children: PropTypes.element,
};

export default GraphContainer;
