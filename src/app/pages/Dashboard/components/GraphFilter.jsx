import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import addMonths from 'date-fns/add_months';
import subMonths from 'date-fns/sub_months';
import PropTypes from 'prop-types';
import React from 'react';
import styled from 'styled-components';
import DateRange, {
  StyledDatePicker,
} from '../../../components/DateRange/DateRange';
import Dropdown from '../../../components/Dropdown';
import {
  DropdownButton,
  DropdownContainer,
} from '../../../components/Dropdown/Dropdown';
import Row from '../../../components/Row';

const StyledGraphFilter = styled(Row)`
  height: 40px;
  padding-right: 5px;
  background-color: #fbfbfb;
  justify-content: flex-end;
  flex-wrap: inherit;

  ${DropdownContainer} {
    width: 130px;
    margin-right: 5px;
    font-size: 12px;
    @media (max-width: ${props => props.theme.breakpoint.phone}) {
      margin-top: 10px;
    }
  }

  ${DropdownButton} {
    border: 0;
    border-bottom: 1px solid #6a6a6a;
    padding: 5px 24px 5px 12px;
    min-height: 0;
    background-color: #fbfbfb;
  }

  ${StyledDatePicker} {
    font-size: ${props => props.theme.fontSize.xs};
  }
`;

const GraphFilterDropdown = styled(Dropdown)`
  && {
    width: auto;
  }
`;

const months = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
];

const GraphFilter = ({
  range,
  dateRange,
  handleChangeDate,
  channelOptions,
  handleChangeChannels,
  monthsOnly = false,
  channels,
  className,
}) => {
  const now = new Date();

  const options = [now];
  for (let i = 1; i <= range; ++i) {
    options.unshift(addMonths(now, -i));
  }

  const isYearSplit =
    options[0].getFullYear() !== options[options.length - 1].getFullYear();

  return (
    <StyledGraphFilter className={className}>
      <FontAwesomeIcon icon="filter" color="#0090E1" />
      <Row style={{ flexWrap: 'inherit' }}>
        {monthsOnly ? (
          <>
            <Dropdown
              onChange={month => {
                handleChangeDate({ ...dateRange, start: month });
              }}
              value={dateRange.start}
              options={options.map(date => ({
                label: isYearSplit
                  ? `${months[date.getMonth()]} ${date.getFullYear()}`
                  : `${months[date.getMonth()]} ${date.getFullYear()}`,
                value: months[date.getMonth()],
              }))}
            />
            <Dropdown
              onChange={month => {
                handleChangeDate({ ...dateRange, end: month });
              }}
              value={dateRange.end}
              options={options.map(date => ({
                label: isYearSplit
                  ? `${months[date.getMonth()]} ${date.getFullYear()}`
                  : `${months[date.getMonth()]} ${date.getFullYear()}`,
                value: months[date.getMonth()],
              }))}
            />
          </>
        ) : (
          <DateRange
            handleChange={handleChangeDate}
            hasDelimiter
            value={dateRange}
            minDate={subMonths(new Date(), range)}
            maxDate={new Date()}
          />
        )}
      </Row>
      {handleChangeChannels && channelOptions && (
        <GraphFilterDropdown
          showMulti={false}
          placeholder="Selected Channels"
          multi
          value={channels}
          onChange={handleChangeChannels}
          options={channelOptions.map(channel => ({
            value: channel.id,
            label: channel.name,
          }))}
        />
      )}
    </StyledGraphFilter>
  );
};

GraphFilter.propTypes = {
  monthsOnly: PropTypes.bool,
  range: PropTypes.number,
  dateRange: PropTypes.object,
  handleChangeDate: PropTypes.func.isRequired,
  handleChangeChannels: PropTypes.func,
  channels: PropTypes.array,
  channelOptions: PropTypes.array,
  className: PropTypes.string,
};

export default GraphFilter;
