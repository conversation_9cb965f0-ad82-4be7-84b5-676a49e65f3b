import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import differenceInMinutes from 'date-fns/difference_in_minutes';
import distanceInWords from 'date-fns/distance_in_words';
import PropTypes from 'prop-types';
import React from 'react';
import styled from 'styled-components';
import Row from '../../../components/Row';

const StyledNotificationMessage = styled(Row)`
  padding: 10px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.11);
  border-radius: 5px;
  margin: 0 10px;
  margin-bottom: 10px;

  background-color: ${props => (props.recent ? '#ebffdf' : 'white')};
`;

const NotificationMessageIcon = styled(FontAwesomeIcon)`
  color: #72cbf3;
  background-color: white;
  font-size: 32px;
  align-self: flex-start;
  margin-right: 10px;
`;

const NotificationMessageText = styled.div`
  font-size: ${props => props.theme.fontSize.s};
`;

const NotificationMessageCategory = styled.div`
  margin: 5px 0;
  color: #0072ce;
  font-size: ${props => props.theme.fontSize.xs};
`;

const NotificationMessageTime = styled.div`
  font-size: ${props => props.theme.fontSize.xs};
  color: #969696;
`;

const NotificationMessage = ({ userName, roleName, category, createdAt }) => {
  return (
    <StyledNotificationMessage
      recent={differenceInMinutes(new Date(), new Date(createdAt)) < 10}
    >
      <NotificationMessageIcon icon="user-circle" />
      <div>
        <NotificationMessageText>
          {userName && userName.split(/\s+/)[0]}
          {` - ${roleName}`}
        </NotificationMessageText>
        <NotificationMessageCategory>- {category}</NotificationMessageCategory>
        <NotificationMessageTime>
          {distanceInWords(new Date(createdAt), new Date())} ago
        </NotificationMessageTime>
      </div>
    </StyledNotificationMessage>
  );
};

NotificationMessage.propTypes = {
  userName: PropTypes.string,
  roleName: PropTypes.string,
  isViewed: PropTypes.bool,
  category: PropTypes.string.isRequired,
  createdAt: PropTypes.string.isRequired,
};

export default NotificationMessage;
