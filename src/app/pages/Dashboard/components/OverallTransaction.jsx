import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import format from 'date-fns/format';
import PropTypes from 'prop-types';
import React from 'react';
import styled from 'styled-components';
import commafy from '../../../utils/commafy';

const StyledOverallTranscation = styled.div`
  flex: 1;
  display: flex;
  color: #333333;
  padding: 20px;
  margin: 10px;
  background-color: ${props => props.backgroundColor};

  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    min-width: calc(100% - 40px);
    padding: 10px 20px;

    &:not(:first-child) {
      margin-top: 0;
    }
  }
`;

const OverallTransactionIcon = styled.div`
  align-items: center;
  display: flex;
  font-size: 50px;
  padding-right: 20px;
`;

const OverallTransactionImage = styled.div`
  align-items: center;
  display: flex;
  font-size: 50px;
  padding-right: 20px;

  img {
    height: 50px;
  }
`;

const OverallTransactionContent = styled.div`
  margin: 0px;
`;

const OverallTransactionTitle = styled.div`
  font-size: ${props => props.theme.fontSize.s};
`;

const OverallTransactionDate = styled.div`
  font-size: ${props => props.theme.fontSize.s};
  color: #4a4a4a;
`;

const OverallTransactionValue = styled.div`
  font-size: ${props => props.theme.fontSize.l};
  margin: 10px 0;
`;

const OverallTransaction = ({
  icon,
  image,
  title,
  value,
  filter,
  backgroundColor = 'transparent',
}) => {
  return (
    <StyledOverallTranscation backgroundColor={backgroundColor}>
      {icon && (
        <OverallTransactionIcon>
          <FontAwesomeIcon color="#2980B9" icon={icon} />
        </OverallTransactionIcon>
      )}
      {image && (
        <OverallTransactionImage>
          <img src={image} alt="overall" />
        </OverallTransactionImage>
      )}
      <OverallTransactionContent>
        <OverallTransactionTitle>{title}</OverallTransactionTitle>
        <OverallTransactionValue>
          {value && commafy(value)}
        </OverallTransactionValue>
        <OverallTransactionDate>
          {format(filter.start, 'MMM. DD')} - {format(filter.end, 'MMM. DD')}
        </OverallTransactionDate>
      </OverallTransactionContent>
    </StyledOverallTranscation>
  );
};

OverallTransaction.propTypes = {
  icon: PropTypes.string,
  image: PropTypes.any,
  title: PropTypes.string.isRequired,
  value: PropTypes.number.isRequired,
  filter: PropTypes.shape({
    start: PropTypes.instanceOf(Date).isRequired,
    end: PropTypes.instanceOf(Date).isRequired,
  }),
  backgroundColor: PropTypes.string,
};

export default OverallTransaction;
