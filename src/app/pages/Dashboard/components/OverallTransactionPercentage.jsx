import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import PropTypes from 'prop-types';
import React from 'react';
import styled from 'styled-components';
import Row from '../../../components/Row';
import commafy from '../../../utils/commafy';

const StyledOverallTransactionPercentage = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;

  border-radius: 2px;
  color: #333333;
  margin: 20px;
  padding: 20px;
  flex: 1;
  background-color: ${props => (props.success ? '#eafbe2' : '#fbe0e5')};

  &:last-child {
    margin-top: 0;
  }
`;

const OverallTransactionPercentageText = styled.h2`
  margin: 0;
  font-size: 40px;
  font-weight: normal;
  text-align: right;
`;

const OverallTransactionPercentageTotal = styled.div`
  margin-top: 10px;
  color: #6e6e6e;
  text-align: center;
  font-size: ${props => props.theme.fontSize.s};
`;

const OverallTransactionPercentageRow = styled(Row)`
  div {
    flex: 1;
  }

  ${Row} {
    span {
      margin-left: 10px;
      font-size: ${props => props.theme.fontSize.m};
    }
  }
`;

const OverallTransactionPercentage = ({ success = false, value, total }) => {
  const percent = (value / total) * 100;

  return (
    <StyledOverallTransactionPercentage success={success}>
      <OverallTransactionPercentageRow>
        <OverallTransactionPercentageRow>
          <FontAwesomeIcon
            icon={success ? 'check-circle' : 'times-circle'}
            color={success ? '#2CB44B' : '#DB0006'}
            style={{ fontSize: 20 }}
          />
          <span>{success ? 'Success' : 'Failed'}</span>
        </OverallTransactionPercentageRow>

        <div>
          <OverallTransactionPercentageText>
            {isNaN(percent) ? '0' : percent.toFixed(2)}%
          </OverallTransactionPercentageText>
        </div>
      </OverallTransactionPercentageRow>
      <OverallTransactionPercentageTotal>
        {`${commafy(value)} out of ${commafy(total)}`}
      </OverallTransactionPercentageTotal>
    </StyledOverallTransactionPercentage>
  );
};

OverallTransactionPercentage.propTypes = {
  success: PropTypes.bool,
  value: PropTypes.number.isRequired,
  total: PropTypes.number.isRequired,
};

export default OverallTransactionPercentage;
