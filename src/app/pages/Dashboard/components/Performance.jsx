import PropTypes from 'prop-types';
import React from 'react';
import styled from 'styled-components';

const StyledPerformance = styled.div`
  display: flex;
  flex-direction: column;
  width: 40%;
  padding: 10px 0;
  margin: 5%;
  text-align: center;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.11);
  border-radius: 5px;
  justify-content: space-evenly;

  div {
    &:first-child {
      font-size: 25px;
    }

    &:last-child {
      font-size: ${props => props.theme.fontSize.m};
    }
  }
`;

const Performance = ({ label, value }) => {
  return (
    <StyledPerformance>
      <div>{value}</div>
      <div>{label}</div>
    </StyledPerformance>
  );
};

Performance.propTypes = {
  label: PropTypes.string.isRequired,
  value: PropTypes.string.isRequired,
};

export default Performance;
