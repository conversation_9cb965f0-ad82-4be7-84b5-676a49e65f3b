import React, { useContext } from 'react';
import { useQuery } from '@apollo/client';
import styled from 'styled-components';
import Row from '../../../components/Row';
import ConfigContext from '../../../context/ConfigContext/ConfigContext';
import DashboardPanel from '../components/DashboardPanel';
import { GET_UPTIME } from '../query';

const MonitorRow = styled(Row)`
  height: 100%;
  margin: 10px;
  justify-content: center;
`;

const MonitorIcon = styled.div`
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-right: 10px;

  background-color: ${props => (props.isOnline ? '#2cb44b' : '#ff0050')};
`;

const MonitorText = styled.span`
  color: #333333;
  font-size: 20px;
  span {
    font-weight: bold;
  }
`;

const AdjyenMonitorPanel = () => {
  const { config } = useContext(ConfigContext);
  const { data, loading } = useQuery(GET_UPTIME, {
    notifyOnNetworkStatusChange: true,
    fetchPolicy: 'network-only',
    pollInterval: 1000 * 60 * config.refreshTime || 0,
    variables: { isPolling: true },
  });

  return (
    <DashboardPanel title="Adyen Monitoring Uptime" noMargin>
      {loading || !data || !data.pgUptimeMonitor ? null : (
        <MonitorRow>
          <MonitorIcon isOnline={data.pgUptimeMonitor.online} />
          <MonitorText>
            Adyen is{' '}
            <span>{data.pgUptimeMonitor.online ? 'ONLINE' : 'OFFLINE'}</span>
          </MonitorText>
        </MonitorRow>
      )}
    </DashboardPanel>
  );
};

export default AdjyenMonitorPanel;
