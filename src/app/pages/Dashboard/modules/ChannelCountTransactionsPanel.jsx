import React, { useContext, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { useQuery } from '@apollo/client';
import { ResponsiveContaine<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'recharts';
import Loader from '../../../components/Loader';
import ConfigContext from '../../../context/ConfigContext/ConfigContext';
import useDateRange from '../../../hooks/useDateRange';
import DashboardPanel from '../components/DashboardPanel';
import GraphContainer from '../components/GraphContainer';
import GraphFilter from '../components/GraphFilter';
import { GET_CHANNEL_TRANSACTIONS } from '../query';
import { numberWithCommas } from '../../../components/GlobalSearch/utils';

const ChannelTransactionsPanel = ({ channelData }) => {
  const { config } = useContext(ConfigContext);
  const [transactionSummaryFilter, setTransactionSummaryFilter] = useDateRange(
    1,
    true
  );
  const [transactionChannelsFilter, setTransactionChannelsFilter] = useState(
    []
  );
  const [pieData, setPieData] = useState({
    channelName: {
      amount: [],
      count: [],
    },
    gateway: {
      amount: [],
      count: [],
    },
    fundingSource: {
      amount: [],
      count: [],
    },
  });

  const { data } = useQuery(GET_CHANNEL_TRANSACTIONS, {
    fetchPolicy: 'network-only',
    pollInterval: 1000 * 60 * config.refreshTime || 0,
    variables: {
      filter: {
        month: {
          start: transactionSummaryFilter.start,
          end: transactionSummaryFilter.end,
        },
        year: {
          start:
            transactionSummaryFilter.start === 'December'
              ? new Date().getFullYear() - 1
              : new Date().getFullYear(),
          end: new Date().getFullYear(),
        },
        channelIds: transactionChannelsFilter,
      },
      variables: { isPolling: true },
    },
  });

  useEffect(() => {
    if (channelData && channelData.channelsLoose) {
      const value = channelData.channelsLoose.map(({ id }) => id);
      setTransactionChannelsFilter(value);
    }
  }, [JSON.stringify(channelData)]);

  useEffect(() => {
    if (data && data.channelReports && data.channelReports.filteredData) {
      const pieData = [...(data.channelReports.filteredData || [])]
        .sort((prev, curr) => {
          if (prev.channelId > curr.channelId) {
            return -1;
          }

          if (prev.channelId < curr.channelId) {
            return 1;
          }

          return 0;
        })
        .sort((prev, curr) => {
          if (prev.channelId === curr.channelId) {
            if (prev.gateway.toLowerCase() > curr.gateway.toLowerCase()) {
              return -1;
            }

            if (prev.gateway.toLowerCase() < curr.gateway.toLowerCase()) {
              return 1;
            }
          }

          return 0;
        })
        .reduce(
          (pieValue, current) => {
            const {
              channelId,
              channelName,
              gateway,
              fundingSource,
              transAmount,
              transCount,
            } = current;

            const channelIndex = pieValue.channelName.amount.findIndex(
              pie => pie.channelId === channelId
            );
            const gatewayIndex = pieValue.gateway.amount.findIndex(
              pie => pie.name === gateway && pie.channelId === channelId
            );

            if (channelIndex >= 0) {
              pieValue.channelName.amount[channelIndex].value +=
                parseFloat(transAmount);
              pieValue.channelName.count[channelIndex].value +=
                parseInt(transCount);
            } else {
              pieValue.channelName.amount.push({
                channelId: channelId,
                name: channelName,
                value: parseFloat(transAmount),
              });
              pieValue.channelName.count.push({
                channelId: channelId,
                name: channelName,
                value: parseInt(transCount),
              });
            }

            if (gatewayIndex >= 0) {
              pieValue.gateway.amount[gatewayIndex].value +=
                parseFloat(transAmount);
              pieValue.gateway.count[gatewayIndex].value +=
                parseInt(transCount);
            } else {
              pieValue.gateway.amount.push({
                channelId: channelId,
                name: gateway,
                value: parseFloat(transAmount),
              });
              pieValue.gateway.count.push({
                channelId: channelId,
                channelName: channelName,
                name: gateway,
                value: parseInt(transCount),
              });
            }

            pieValue.fundingSource.amount.push({
              channelId: channelId,
              gateway: gateway,
              name: fundingSource || 'GCASH',
              value: parseFloat(transAmount),
            });
            pieValue.fundingSource.count.push({
              channelId: channelId,
              channelName: channelName,
              gateway: gateway,
              name: fundingSource || 'GCASH',
              value: parseInt(transCount),
            });

            return pieValue;
          },
          {
            channelName: {
              amount: [],
              count: [],
            },
            gateway: {
              amount: [],
              count: [],
            },
            fundingSource: {
              amount: [],
              count: [],
            },
          }
        );

      setPieData(pieData);
    }
  }, [data]);

  const amountPieData = [];
  pieData.channelName.count.map(data => {
    return amountPieData.push({
      pieLayer: 'channelName',
      name: data.name,
      value: data.value,
    });
  });
  pieData.gateway.count.map(data => {
    return amountPieData.push({
      pieLayer: 'gateway',
      channelName: data.channelName,
      name: data.name,
      value: data.value,
    });
  });
  pieData.fundingSource.count.map(data => {
    return amountPieData.push({
      pieLayer: 'fundingSource',
      channelName: data.channelName,
      gateway: data.gateway,
      name: data.name,
      value: data.value,
    });
  });

  return (
    <DashboardPanel title="Channel Count Transactions">
      <>
        <GraphFilter
          range={1}
          channels={transactionChannelsFilter}
          monthsOnly
          dateRange={transactionSummaryFilter}
          handleChangeChannels={setTransactionChannelsFilter}
          handleChangeDate={setTransactionSummaryFilter}
          channelOptions={
            channelData && channelData.channelsLoose
              ? channelData.channelsLoose
              : []
          }
        />
        {!data || !data.channelReports ? (
          <Loader />
        ) : (
          <GraphContainer>
            <ResponsiveContainer>
              <PieChart>
                <Pie
                  data={pieData.channelName.count}
                  paddingAngle={1}
                  minAngle={5}
                  dataKey="value"
                  isAnimationActive={false}
                  outerRadius={60}
                  fill="#0088FE"
                />
                <Pie
                  data={pieData.gateway.count}
                  paddingAngle={1}
                  minAngle={5}
                  dataKey="value"
                  isAnimationActive={false}
                  innerRadius={60}
                  outerRadius={90}
                  fill="#00C49F"
                />
                <Pie
                  data={pieData.fundingSource.count}
                  label={data => numberWithCommas(data.value)}
                  paddingAngle={1}
                  minAngle={5}
                  dataKey="value"
                  isAnimationActive={false}
                  innerRadius={90}
                  outerRadius={120}
                  fill="#FFBB27"
                />
                <Tooltip label={value => numberWithCommas(value, 2)} />
                <Legend
                  payload={amountPieData.map(data => ({
                    id: data.name,
                    type: 'square',
                    value: `${
                      data.channelName === undefined
                        ? ''
                        : `${data.channelName}-`
                    }${data.gateway === undefined ? '' : `${data.gateway}-`}${data.name} (${data.value})`,
                    color:
                      data.pieLayer === 'channelName'
                        ? '#0088FE'
                        : null || data.pieLayer === 'gateway'
                          ? '#00C49F'
                          : null || data.pieLayer === 'fundingSource'
                            ? '#FFBB27'
                            : null,
                  }))}
                  layout="vertical"
                  verticalAlign
                  align="right"
                />
              </PieChart>
            </ResponsiveContainer>
          </GraphContainer>
        )}
      </>
    </DashboardPanel>
  );
};

ChannelTransactionsPanel.propTypes = {
  channelData: PropTypes.object,
};

export default ChannelTransactionsPanel;
