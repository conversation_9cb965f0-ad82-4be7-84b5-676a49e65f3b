import PropTypes from 'prop-types';
import React, { useContext, useEffect, useMemo, useState } from 'react';
import { useQuery } from '@apollo/client';
import {
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';
import Loader from '../../../components/Loader';
import ConfigContext from '../../../context/ConfigContext/ConfigContext';
import useDateRange from '../../../hooks/useDateRange';
import randomColor from '../../../utils/randomColor';
import DashboardPanel from '../components/DashboardPanel';
import GraphContainer from '../components/GraphContainer';
import GraphFilter from '../components/GraphFilter';
import { GET_TRANSACTIONS } from '../query';

const ChannelOverallTransactionPanel = ({ channelData, channelLoading }) => {
  const { config } = useContext(ConfigContext);
  const [overallTransactionsFilter, setOverallTransactionsFilter] =
    useDateRange(6, true);

  const [overallTransactionsChannels, setOverallTransactionsChannels] =
    useState([]);

  useEffect(() => {
    if (channelData && channelData.channelsLoose) {
      const value = channelData.channelsLoose.map(({ id }) => id);
      setOverallTransactionsChannels(value);
    }
  }, [JSON.stringify(channelData)]);

  const { data: transactionsData } = useQuery(GET_TRANSACTIONS, {
    pollInterval: 1000 * 60 * config.refreshTime || 0,
    fetchPolicy: 'network-only',
    variables: {
      where: {
        startMonth: overallTransactionsFilter.start,
        endMonth: overallTransactionsFilter.end,
      },
      channelIds: overallTransactionsChannels,
      variables: { isPolling: true },
    },
  });

  const graphData = useMemo(() => {
    const graphData = [];
    if (transactionsData && transactionsData.transactions) {
      for (const transaction of transactionsData.transactions) {
        const data = {
          month: transaction.month.slice(0, 3),
        };
        for (const payload of transaction.payload) {
          if (
            payload &&
            overallTransactionsChannels.includes(payload.channelId)
          ) {
            const channel = channelData.channelsLoose.find(
              channel => channel.id === payload.channelId
            );
            data[channel.name] = payload.transactions;
          }
        }
        graphData.push(data);
      }
    }
    return graphData;
  }, [
    JSON.stringify(overallTransactionsChannels),
    JSON.stringify(transactionsData),
    JSON.stringify(channelData),
  ]);

  const graphColors = useMemo(() => {
    return overallTransactionsChannels.reduce((colorMap, channelId) => {
      colorMap[channelId] = randomColor(channelId);
      return colorMap;
    }, {});
  }, [
    JSON.stringify(overallTransactionsChannels),
    JSON.stringify(transactionsData),
    JSON.stringify(channelData),
  ]);

  return (
    <DashboardPanel title="Overall Transaction per Channel">
      <>
        <GraphFilter
          range={6}
          monthsOnly
          dateRange={overallTransactionsFilter}
          handleChangeDate={setOverallTransactionsFilter}
          handleChangeChannels={setOverallTransactionsChannels}
          channelOptions={
            channelData && channelData.channelsLoose
              ? channelData.channelsLoose
              : []
          }
          channels={overallTransactionsChannels}
        />
        {!channelLoading &&
        transactionsData &&
        transactionsData.transactions &&
        channelData &&
        channelData.channelsLoose ? (
          <>
            <GraphContainer>
              <ResponsiveContainer>
                <LineChart
                  style={{ width: '0' }}
                  margin={{
                    top: 50,
                    right: 60,
                    bottom: 50,
                    left: 60,
                  }}
                  data={graphData}
                >
                  <XAxis dataKey="month" />
                  <YAxis />
                  <CartesianGrid strokeDasharray="3 3" />
                  <Tooltip />
                  <Legend />
                  {channelData &&
                    channelData.channelsLoose &&
                    channelData.channelsLoose
                      .filter(channel =>
                        overallTransactionsChannels.includes(channel.id)
                      )
                      .map(channel => (
                        <Line
                          key={channel.id}
                          type="monotone"
                          dataKey={channel.name}
                          stroke={graphColors[channel.id]}
                        />
                      ))}
                </LineChart>
              </ResponsiveContainer>
            </GraphContainer>
          </>
        ) : (
          <Loader />
        )}
      </>
    </DashboardPanel>
  );
};

ChannelOverallTransactionPanel.propTypes = {
  channelData: PropTypes.object,
  channelLoading: PropTypes.bool,
};

export default ChannelOverallTransactionPanel;
