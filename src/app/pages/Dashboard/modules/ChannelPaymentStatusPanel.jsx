import PropTypes from 'prop-types';
import React, { useContext } from 'react';
import { useQuery } from '@apollo/client';
import { withRouter } from 'react-router-dom';
import styled from 'styled-components';
import Loader from '../../../components/Loader';
import ConfigContext from '../../../context/ConfigContext/ConfigContext';
import ChannelStatus from '../components/ChannelStatus';
import DashboardPanel from '../components/DashboardPanel';
import { PushedRow, ViewAllButton } from '../Dashboard';
import { GET_CHANNEL_AND_GATEWAY_STATUS } from '../query';

const ChannelStatusContainer = styled.div`
  height: 200px;
  overflow-y: auto;
`;

const ChannelPaymentStatusPanel = ({ history }) => {
  const { config } = useContext(ConfigContext);
  const { data: statusData, loading: statusLoading } = useQuery(
    GET_CHANNEL_AND_GATEWAY_STATUS,
    {
      notifyOnNetworkStatusChange: true,
      pollInterval: 1000 * 60 * config.refreshTime || 0,
      fetchPolicy: 'network-only',
      variables: { isPolling: true },
    }
  );

  return (
    <DashboardPanel title="Channel and Payment Method Status">
      {statusLoading || !statusData || !statusData.channelAndGatewayStatus ? (
        <Loader />
      ) : (
        <>
          <PushedRow>
            <ViewAllButton
              onClick={() => {
                history.push('/payment-status');
              }}
            >
              View All
            </ViewAllButton>
          </PushedRow>
          <ChannelStatusContainer>
            {statusData.channelAndGatewayStatus.filteredData.map(
              (status, index) => (
                <ChannelStatus {...status} key={index} />
              )
            )}
          </ChannelStatusContainer>
        </>
      )}
    </DashboardPanel>
  );
};

ChannelPaymentStatusPanel.propTypes = {
  history: PropTypes.object,
};

export default withRouter(ChannelPaymentStatusPanel);
