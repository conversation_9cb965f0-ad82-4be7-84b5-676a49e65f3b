import PropTypes from 'prop-types';
import React, { useContext, useEffect, useMemo, useState } from 'react';
import { useQuery } from '@apollo/client';
import {
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';
import Loader from '../../../components/Loader';
import ConfigContext from '../../../context/ConfigContext/ConfigContext';
import useDateRange from '../../../hooks/useDateRange';
import randomColor from '../../../utils/randomColor';
import DashboardPanel from '../components/DashboardPanel';
import GraphContainer from '../components/GraphContainer';
import GraphFilter from '../components/GraphFilter';
import { GET_REVENUES } from '../query';

const ChannelRevenuePanel = ({ channelData }) => {
  const [revenueChannels, setRevenueChannels] = useState([]);
  const [revenueFilter, setRevenueFilter] = useDateRange(6, true);

  useEffect(() => {
    if (channelData && channelData.channelsLoose) {
      const value = channelData.channelsLoose.map(({ id }) => id);
      setRevenueChannels(value);
    }
  }, [JSON.stringify(channelData)]);

  const { config } = useContext(ConfigContext);
  const { data: revenueData } = useQuery(GET_REVENUES, {
    fetchPolicy: 'network-only',
    pollInterval: 1000 * 60 * config.refreshTime || 0,
    variables: {
      where: {
        startMonth: revenueFilter.start,
        endMonth: revenueFilter.end,
      },
      channelIds: revenueChannels,
      variables: { isPolling: true },
    },
  });

  const graphData = useMemo(() => {
    const graphData = [];
    if (revenueData && revenueData.transactions) {
      for (const revenue of revenueData.transactions) {
        const data = {
          month: revenue.month.slice(0, 3),
        };
        for (const payload of revenue.payload) {
          if (payload && revenueChannels.includes(payload.channelId)) {
            const channel = channelData.channelsLoose.find(
              channel => channel.id === payload.channelId
            );
            data[channel.name] = payload.revenue;
          }
        }
        graphData.push(data);
      }
    }
    return graphData;
  }, [
    JSON.stringify(revenueChannels),
    JSON.stringify(revenueData),
    JSON.stringify(channelData),
  ]);

  const graphColors = useMemo(() => {
    return revenueChannels.reduce((colorMap, channelId) => {
      colorMap[channelId] = randomColor(channelId);
      return colorMap;
    }, {});
  }, [
    JSON.stringify(revenueChannels),
    JSON.stringify(revenueData),
    JSON.stringify(channelData),
  ]);

  return (
    <DashboardPanel title="Revenue per Channel">
      <>
        {channelData && channelData.channelsLoose && (
          <GraphFilter
            range={6}
            dateRange={revenueFilter}
            monthsOnly
            handleChangeDate={setRevenueFilter}
            handleChangeChannels={setRevenueChannels}
            channelOptions={channelData.channelsLoose}
            channels={revenueChannels}
          />
        )}
        {revenueData && revenueData.transactions ? (
          <GraphContainer>
            <ResponsiveContainer>
              <LineChart
                style={{ width: '0' }}
                margin={{
                  top: 50,
                  right: 60,
                  bottom: 50,
                  left: 60,
                }}
                data={graphData}
              >
                <XAxis dataKey="month" />
                <YAxis />
                <CartesianGrid strokeDasharray="3 3" />
                <Tooltip />
                <Legend />
                {channelData &&
                  channelData.channelsLoose &&
                  channelData.channelsLoose
                    .filter(channel => revenueChannels.includes(channel.id))
                    .map(channel => (
                      <Line
                        key={channel.id}
                        type="monotone"
                        dataKey={channel.name}
                        stroke={graphColors[channel.id]}
                      />
                    ))}
              </LineChart>
            </ResponsiveContainer>
          </GraphContainer>
        ) : (
          <Loader />
        )}
      </>
    </DashboardPanel>
  );
};

ChannelRevenuePanel.propTypes = {
  channelData: PropTypes.object,
};

export default ChannelRevenuePanel;
