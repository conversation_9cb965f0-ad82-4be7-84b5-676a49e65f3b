import React, { useState } from 'react';
import {
  Bar,
  Bar<PERSON>hart,
  CartesianGrid,
  Legend,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';
import useDateRange from '../../../hooks/useDateRange';
import randomColor from '../../../utils/randomColor';
import DashboardPanel from '../components/DashboardPanel';
import GraphContainer from '../components/GraphContainer';
import GraphFilter from '../components/GraphFilter';

const DisputeRatesPanel = () => {
  const [disputeRatesFilter, setDisputeRatesFilter] = useDateRange(6);

  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'Jun', 'Jul'];

  const [graphData] = useState(
    months.map(month => ({
      month,
      success: Math.floor(Math.random() * 1000),
      failed: Math.floor(Math.random() * 1000),
    }))
  );

  return (
    <DashboardPanel title="Disputes Rates">
      <>
        <GraphFilter
          range={6}
          dateRange={disputeRatesFilter}
          handleChangeDate={setDisputeRatesFilter}
        />
        <GraphContainer>
          <ResponsiveContainer>
            <BarChart
              margin={{
                top: 50,
                right: 60,
                bottom: 50,
                left: 60,
              }}
              data={graphData}
            >
              <XAxis dataKey="month" />
              <YAxis />
              <CartesianGrid strokeDasharray="3 3" />
              <Tooltip />
              <Legend />
              <Bar
                dataKey="success"
                fill={randomColor('disputerates-success')}
              />
              <Bar dataKey="failed" fill={randomColor('disputerates-failed')} />
            </BarChart>
          </ResponsiveContainer>
        </GraphContainer>
      </>
    </DashboardPanel>
  );
};

export default DisputeRatesPanel;
