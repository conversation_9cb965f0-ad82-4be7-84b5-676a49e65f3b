import React from 'react';
import { useQuery } from '@apollo/client';
import Row from '../../../components/Row';
import useDateRange from '../../../hooks/useDateRange';
import Loader from '../../../components/Loader';
import DashboardPanel from '../components/DashboardPanel';
import OverallTransaction from '../components/OverallTransaction';
import GraphFilter from '../components/GraphFilter';
import { GET_OVERALL_TRANSACTIONS } from '../query';
import gcashLogo from '../../../assets/gcash-logo.png';

const OverallGcashTransactionsPanel = () => {
  const [overallTransactionsFilter, setOverallTransactionsFilter] =
    useDateRange(6, false, true);

  const { data: overallTransactionsData } = useQuery(GET_OVERALL_TRANSACTIONS, {
    fetchPolicy: 'network-only',
    variables: {
      where: {
        startMonth: overallTransactionsFilter.start,
        endMonth: overallTransactionsFilter.end,
      },
    },
  });

  return (
    <DashboardPanel title="Online CC and GCash">
      <>
        <GraphFilter
          range={6}
          dateRange={overallTransactionsFilter}
          handleChangeDate={setOverallTransactionsFilter}
        />
        <Row>
          {overallTransactionsData &&
          overallTransactionsData.overallTransactions ? (
            <>
              <OverallTransaction
                title="Total Count for Online CC"
                icon="credit-card"
                value={
                  overallTransactionsData.overallTransactions.adyen
                    ? overallTransactionsData.overallTransactions.adyen
                    : 0
                }
                filter={{
                  start: overallTransactionsFilter.start,
                  end: overallTransactionsFilter.end,
                }}
                backgroundColor="#E8FFE1"
              />

              <OverallTransaction
                title="Total Count for GCash"
                image={gcashLogo}
                value={
                  overallTransactionsData.overallTransactions.gcash
                    ? overallTransactionsData.overallTransactions.gcash
                    : 0
                }
                filter={{
                  start: overallTransactionsFilter.start,
                  end: overallTransactionsFilter.end,
                }}
                backgroundColor="#E8FFE1"
              />
            </>
          ) : (
            <Loader />
          )}
        </Row>
      </>
    </DashboardPanel>
  );
};

export default OverallGcashTransactionsPanel;
