import React from 'react';
import { useQuery } from '@apollo/client';
import styled from 'styled-components';
import { StyledDatePicker } from '../../../components/DateRange/DateRange';
import useDateRange from '../../../hooks/useDateRange';
import Loader from '../../../components/Loader';
import DashboardPanel from '../components/DashboardPanel';
import OverallTransactionPercentage from '../components/OverallTransactionPercentage';
import GraphFilter from '../components/GraphFilter';
import { GET_OVERALL_TRANSACTIONS } from '../query';

const OverallTransactionPercentageGraphFilter = styled(GraphFilter)`
  ${StyledDatePicker} {
    padding: 5px;
    max-width: 96px;
  }
`;

const OverallTransactionPercentagePanel = () => {
  const [
    overallTransactionPercentageFilter,
    setOverallTransactionPercentageFilter,
  ] = useDateRange(6, false, true);

  const { data: percentageData } = useQuery(GET_OVERALL_TRANSACTIONS, {
    fetchPolicy: 'network-only',
    variables: {
      where: {
        startMonth: overallTransactionPercentageFilter.start,
        endMonth: overallTransactionPercentageFilter.end,
      },
    },
  });

  return (
    <DashboardPanel title="Transactions Percentage">
      <>
        <OverallTransactionPercentageGraphFilter
          range={6}
          dateRange={overallTransactionPercentageFilter}
          handleChangeDate={setOverallTransactionPercentageFilter}
        />

        {percentageData && percentageData.overallTransactions ? (
          <>
            <OverallTransactionPercentage
              success
              value={percentageData.overallTransactions.success}
              total={
                percentageData.overallTransactions.success +
                percentageData.overallTransactions.failed
              }
            />
            <OverallTransactionPercentage
              value={percentageData.overallTransactions.failed}
              total={
                percentageData.overallTransactions.success +
                percentageData.overallTransactions.failed
              }
            />
          </>
        ) : (
          <Loader />
        )}
      </>
    </DashboardPanel>
  );
};

export default OverallTransactionPercentagePanel;
