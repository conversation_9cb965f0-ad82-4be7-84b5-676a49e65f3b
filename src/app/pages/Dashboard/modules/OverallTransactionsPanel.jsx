import React, { useEffect, useState } from 'react';
import { useQuery } from '@apollo/client';
import Row from '../../../components/Row';
import useDateRange from '../../../hooks/useDateRange';
import Loader from '../../../components/Loader';
import DashboardPanel from '../components/DashboardPanel';
import OverallTransaction from '../components/OverallTransaction';
import GraphFilter from '../components/GraphFilter';
import { GET_OVERALL_TRANSACTIONS } from '../query';

const OverallTransactionsPanel = () => {
  const [totalTransactions, setTotalTransactions] = useState(null);
  const [overallTransactionsFilter, setOverallTransactionsFilter] =
    useDateRange(6, false, true);

  const { data: overallTransactionsData } = useQuery(GET_OVERALL_TRANSACTIONS, {
    fetchPolicy: 'network-only',
    variables: {
      where: {
        startMonth: overallTransactionsFilter.start,
        endMonth: overallTransactionsFilter.end,
      },
    },
  });

  useEffect(() => {
    if (
      overallTransactionsData &&
      overallTransactionsData.overallTransactions
    ) {
      const value =
        overallTransactionsData.overallTransactions.success +
        overallTransactionsData.overallTransactions.failed +
        overallTransactionsData.overallTransactions.refused;
      setTotalTransactions(value);
    }
  }, [JSON.stringify(overallTransactionsData)]);

  return (
    <DashboardPanel title="Overall Transactions">
      <>
        <GraphFilter
          range={6}
          dateRange={overallTransactionsFilter}
          handleChangeDate={setOverallTransactionsFilter}
        />
        <Row>
          {overallTransactionsData &&
          overallTransactionsData.overallTransactions ? (
            <>
              <OverallTransaction
                title="Total Transactions"
                value={totalTransactions ? totalTransactions : 0}
                filter={{
                  start: overallTransactionsFilter.start,
                  end: overallTransactionsFilter.end,
                }}
              />

              <OverallTransaction
                title="Total Success"
                value={
                  overallTransactionsData &&
                  overallTransactionsData.overallTransactions
                    ? overallTransactionsData.overallTransactions.success
                    : 0
                }
                filter={{
                  start: overallTransactionsFilter.start,
                  end: overallTransactionsFilter.end,
                }}
                backgroundColor="#E8FFE1"
              />

              <OverallTransaction
                title="Total Failed"
                value={
                  overallTransactionsData &&
                  overallTransactionsData.overallTransactions
                    ? overallTransactionsData.overallTransactions.failed
                    : 0
                }
                filter={{
                  start: overallTransactionsFilter.start,
                  end: overallTransactionsFilter.end,
                }}
                backgroundColor="#FFEAEA"
              />

              <OverallTransaction
                title="Total Refused"
                value={
                  overallTransactionsData &&
                  overallTransactionsData.overallTransactions
                    ? overallTransactionsData.overallTransactions.refused
                    : 0
                }
                filter={{
                  start: overallTransactionsFilter.start,
                  end: overallTransactionsFilter.end,
                }}
                backgroundColor="#FFEAEA"
              />
            </>
          ) : (
            <Loader />
          )}
        </Row>
      </>
    </DashboardPanel>
  );
};

export default OverallTransactionsPanel;
