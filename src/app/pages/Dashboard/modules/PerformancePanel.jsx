import React, { useContext } from 'react';
import { useQuery } from '@apollo/client';
import styled from 'styled-components';
import Loader from '../../../components/Loader';
import Row from '../../../components/Row';
import ConfigContext from '../../../context/ConfigContext/ConfigContext';
import DashboardPanel from '../components/DashboardPanel';
import Performance from '../components/Performance';
import { GET_PERFORMANCE } from '../query';

const PerformanceContainer = styled(Row)`
  justify-content: center;
  color: #333333;
  flex: 1;
`;

const PerformancePanel = () => {
  const { config } = useContext(ConfigContext);
  const { data, loading } = useQuery(GET_PERFORMANCE, {
    notifyOnNetworkStatusChange: true,
    pollInterval: 1000 * 60 * config.refreshTime || 0,
    fetchPolicy: 'network-only',
    variables: { isPolling: true },
  });

  return (
    <DashboardPanel title="Performance" noMargin>
      {loading || !data || !data.performance ? (
        <Loader />
      ) : (
        <PerformanceContainer>
          <Performance label="CPU" value={data.performance.cpu} />
          <Performance label="Used Memory" value={data.performance.memory} />
          {/* <Performance label="Disk" value="2.1*" />
                <Performance label="Network" value="234*" /> */}
        </PerformanceContainer>
      )}
    </DashboardPanel>
  );
};

export default PerformancePanel;
