import PropTypes from 'prop-types';
import React, { useContext, useEffect } from 'react';
import { useQuery } from '@apollo/client';
import { withRouter } from 'react-router-dom';
import Loader from '../../../components/Loader';
import ConfigContext from '../../../context/ConfigContext/ConfigContext';
import { useMutation } from '@apollo/client';
import DashboardPanel from '../components/DashboardPanel';
import NotificationMessage from '../components/NotificationMesage';
import { PushedRow, ScrollableContainer, ViewAllButton } from '../Dashboard';
import { UPDATE_NOTIFICATIONS } from '../mutation';
import { GET_NOTIFICATIONS } from '../query';

const SystemNotificationsPanel = ({ history }) => {
  const { config } = useContext(ConfigContext);
  const { data, loading } = useQuery(GET_NOTIFICATIONS, {
    fetchPolicy: 'network-only',
    pollInterval: 1000 * 60 * config.refreshTime || 0,
    variables: {
      data: { limit: 10 },
      isPolling: true,
    },
  });

  const [updateNotification] = useMutation(UPDATE_NOTIFICATIONS, {
    ignoreResults: true,
  });

  useEffect(() => {
    if (data && data.notifications && data.notifications.filteredData) {
      const id = data.notifications.filteredData
        .filter(({ isViewed }) => !isViewed)
        .map(({ id }) => id);
      if (id.length) {
        updateNotification({
          variables: {
            data: { id },
          },
        });
      }
    }
  }, [JSON.stringify(data)]);

  return (
    <DashboardPanel title="System Notifications">
      <>
        <PushedRow>
          <ViewAllButton
            onClick={() => {
              history.push('/notifications');
            }}
          >
            View All
          </ViewAllButton>
        </PushedRow>

        {loading && <Loader />}

        {!loading &&
          data &&
          data.notifications &&
          data.notifications.filteredData && (
            <ScrollableContainer maxHeight="300px">
              {data.notifications.filteredData.map((notification, index) => (
                <NotificationMessage {...notification} key={index} />
              ))}
            </ScrollableContainer>
          )}
      </>
    </DashboardPanel>
  );
};

SystemNotificationsPanel.propTypes = {
  history: PropTypes.object,
};

export default withRouter(SystemNotificationsPanel);
