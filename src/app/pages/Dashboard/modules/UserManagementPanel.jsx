import addDays from 'date-fns/add_days';
import React, { useContext, useMemo } from 'react';
import { useQuery } from '@apollo/client';
import {
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';
import Loader from '../../../components/Loader';
import ConfigContext from '../../../context/ConfigContext/ConfigContext';
import useDateRange from '../../../hooks/useDateRange';
import randomColor from '../../../utils/randomColor';
import DashboardPanel from '../components/DashboardPanel';
import GraphContainer from '../components/GraphContainer';
import GraphFilter from '../components/GraphFilter';
import { GET_USERS_SUMMARY } from '../query';

const UserManagementPanel = () => {
  const { config } = useContext(ConfigContext);
  const [usersSummaryFilter, setUsersSummaryFilter] = useDateRange(6);
  const { data } = useQuery(GET_USERS_SUMMARY, {
    fetchPolicy: 'network-only',
    pollInterval: 1000 * 60 * config.refreshTime || 0,
    variables: {
      range: {
        ...usersSummaryFilter,
        end: addDays(usersSummaryFilter.end, 1),
      },
      variables: { isPolling: true },
    },
  });

  const graphData = useMemo(() => {
    let graphData = [];
    const months = {};
    if (data && data.usersSummary) {
      for (const summary of data.usersSummary.activeUsers) {
        if (!months[summary.month]) months[summary.month] = {};
        months[summary.month]['Active Users'] = summary.count;
      }
      for (const summary of data.usersSummary.inactiveUsers) {
        if (!months[summary.month]) months[summary.month] = {};
        months[summary.month]['Inactive Users'] = summary.count;
      }
      graphData = Object.keys(months).map(month => ({
        month,
        ...months[month],
      }));
    }
    return graphData;
  }, [data]);

  const graphColors = useMemo(() => {
    return ['Active Users', 'Inactive Users'].reduce((colorMap, key) => {
      colorMap[key] = randomColor(key);
      return colorMap;
    }, {});
  }, [data]);

  return (
    <DashboardPanel title="User Management Summary">
      <>
        <GraphFilter
          range={6}
          dateRange={usersSummaryFilter}
          handleChangeDate={setUsersSummaryFilter}
        />
        {!data || !data.usersSummary ? (
          <Loader />
        ) : (
          <GraphContainer>
            <ResponsiveContainer>
              <LineChart
                margin={{
                  top: 50,
                  right: 60,
                  bottom: 50,
                  left: 60,
                }}
                data={graphData}
              >
                <XAxis dataKey="month" />
                <YAxis />
                <CartesianGrid strokeDasharray="3 3" />
                <Tooltip />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="Active Users"
                  stroke={graphColors['Active Users']}
                />
                <Line
                  type="monotone"
                  dataKey="Inactive Users"
                  stroke={graphColors['Inactive Users']}
                />
              </LineChart>
            </ResponsiveContainer>
          </GraphContainer>
        )}
      </>
    </DashboardPanel>
  );
};

export default UserManagementPanel;
