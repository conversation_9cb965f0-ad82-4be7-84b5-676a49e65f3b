import { gql } from '@apollo/client';

export const GET_PERFORMANCE = gql`
  query getPerformance($isPolling: Boolean) {
    performance(isPolling: $isPolling) {
      cpu
      memory
    }
  }
`;

export const GET_NOTIFICATIONS = gql`
  query getNotifications($data: NotificationInput!, $isPolling: Boolean) {
    notifications(data: $data, isPolling: $isPolling) {
      count
      filteredData {
        id
        userName
        roleName
        category
        createdAt
        isViewed
      }
    }
  }
`;

export const GET_REVENUES = gql`
  query revenues($where: monthInput, $isPolling: Boolean) {
    transactions(where: $where, isPolling: $isPolling) {
      month
      year
      type
      payload {
        channelId
        channelName
        revenue
      }
    }
  }
`;

export const GET_TRANSACTIONS = gql`
  query transactions($where: monthInput, $isPolling: Boolean) {
    transactions(where: $where, isPolling: $isPolling) {
      month
      year
      type
      payload {
        channelId
        channelName
        transactions
      }
    }
  }
`;

export const GET_USERS_SUMMARY = gql`
  query getUsersSummary($range: DateRange!, $isPolling: Boolean) {
    usersSummary(range: $range, isPolling: $isPolling) {
      activeUsers {
        month
        count
      }
      inactiveUsers {
        month
        count
      }
    }
  }
`;

export const GET_CHANNEL_TRANSACTIONS = gql`
  query getChannelGraph(
    $filter: channelReportFilterInput!
    $isPolling: Boolean
  ) {
    channelReports(filter: $filter, isPolling: $isPolling) {
      filteredData {
        channelId
        channelName
        gateway
        fundingSource
        transAmount
        transCount
      }
    }
  }
`;

export const GET_CHANNEL_OPTIONS = gql`
  query getChannelOptions {
    channelsLoose {
      id
      name
    }
  }
`;

export const GET_UPTIME = gql`
  query getUptime($isPolling: Boolean) {
    pgUptimeMonitor(isPolling: $isPolling) {
      online
    }
  }
`;

export const GET_CHANNEL_AND_GATEWAY_STATUS = gql`
  query getChannelAndGatewayStatus($isPolling: Boolean) {
    channelAndGatewayStatus(
      filter: {}
      pagination: { start: { channelId: "", paymentMethod: "" }, limit: 10 }
      isPolling: $isPolling
    ) {
      cursors {
        channelId
        paymentMethod
      }
      count
      filteredData {
        channel {
          id
          name
        }
        paymentMethod
        status
        updatedAt
      }
    }
  }
`;

export const GET_OVERALL_TRANSACTIONS = gql`
  query getOverallTransactions($where: monthInput) {
    overallTransactions(where: $where) {
      success
      failed
      refused
      adyen
      gcash
    }
  }
`;

export const GET_LAST_UPDATED = gql`
  query getLastUpdated {
    overallLastUpdatedAt {
      lastUpdatedAt
    }
  }
`;
