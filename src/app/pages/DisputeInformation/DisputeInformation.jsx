import PropTypes from 'prop-types';
import React, { useState } from 'react';
import FormField from '../../components/Form/FormField';
import Header from '../../components/Header';
import Page from '../../components/Page';
import DataContainer from '../../components/DataContainer';
import DataHeader from '../../components/DataHeader';
import formatCurrency from '../../utils/formatCurrency';
import { AlertModal } from '../../components/Modal';
import {
  ButtonsContainer,
  PageSubsection,
} from '../../components/InformationPage';
import Loader from '../../components/Loader';
import NotFound from '../../components/NotFound/NotFound';
import PrimaryButton from '../../components/Button/PrimaryButton';
import SecondaryButton from '../../components/Button/SecondaryButton';
import { useQuery } from '@apollo/client';
import { GET_GCASH_REFUND_INFORMATION } from './query';

const DisputeInformation = ({ history, match }) => {
  const [state, setState] = useState({
    isFailureRefundModalOpen: false,
    isSuccessRefundModalOpen: false,
    isConfirmRefundModalOpen: false,
  });

  const { data, loading } = useQuery(GET_GCASH_REFUND_INFORMATION, {
    variables: {
      where: {
        id: match.params.referenceId,
        transactionId: match.params.transactionId,
      },
    },
    fetchPolicy: 'network-only',
  });

  return (
    <>
      <Page>
        <Header
          title={data && data.gcashRefund ? data.gcashRefund.refundId : ''}
          path={[
            'Disputes',
            'GCash',
            { label: 'Refunds', to: '/disputes' },
            data && data.gcashRefund ? data.gcashRefund.refundId : '',
          ]}
        />
        <DataContainer loading={loading}>
          {loading && <Loader fullPage />}
          {!loading && !data.gcashRefund && <NotFound />}
          {!loading && data.gcashRefund && (
            <>
              <DataHeader>
                <DataHeader.Title>Transaction Information</DataHeader.Title>
              </DataHeader>

              <PageSubsection>
                <FormField
                  isStatic
                  label="Merchant ID"
                  value={
                    data.gcashRefund.merchantId
                      ? data.gcashRefund.merchantId
                      : ''
                  }
                  perRow={2}
                />
                <FormField
                  isStatic
                  label="Merchant Trans ID"
                  value={
                    data.gcashRefund.merchantTransID
                      ? data.gcashRefund.merchantTransID
                      : ''
                  }
                  perRow={2}
                />
                <FormField
                  isStatic
                  label="Refund ID"
                  value={
                    data.gcashRefund.refundId ? data.gcashRefund.refundId : ''
                  }
                  perRow={2}
                />
                <FormField
                  isStatic
                  label="Amount"
                  value={
                    data.gcashRefund.refundAmount
                      ? formatCurrency(data.gcashRefund.refundAmount)
                      : ''
                  }
                  perRow={2}
                />
                <FormField
                  isStatic
                  label="Acquire ID"
                  value={
                    data.gcashRefund.acquirementId
                      ? data.gcashRefund.acquirementId
                      : ''
                  }
                  perRow={2}
                />
                <FormField
                  isStatic
                  label="Refund Status"
                  value={
                    data.gcashRefund.refundStatus
                      ? data.gcashRefund.refundStatus
                      : ''
                  }
                  perRow={2}
                />
              </PageSubsection>
              <PageSubsection>
                <ButtonsContainer style={{ marginTop: '10%' }}>
                  <SecondaryButton
                    onClick={() => {
                      history.push('/disputes');
                    }}
                  >
                    Back to All Transactions
                  </SecondaryButton>
                  {data.gcashRefund &&
                  (data.gcashRefund.refundStatus !== 'PENDING' ||
                    data.gcashRefund.refundStatus !== 'AUTHORISED') ? (
                    <PrimaryButton
                      icon="undo"
                      onClick={() =>
                        setState({ ...state, isConfirmRefundModalOpen: true })
                      }
                    >
                      Refund
                    </PrimaryButton>
                  ) : null}
                </ButtonsContainer>
              </PageSubsection>
            </>
          )}
        </DataContainer>
      </Page>
      <AlertModal
        isOpen={state.isSuccessRefundModalOpen}
        title="Refund Transaction Alert"
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader="This transaction request is now for Approval."
        description={[
          'GCash will be notified and will approve or',
          'reject this request.',
        ]}
        confirmText="Go back"
        handleConfirm={() => {
          history.push('/disputes');
        }}
        handleClose={() =>
          setState({ ...state, isSuccessRefundModalOpen: false })
        }
      />
      <AlertModal
        isOpen={state.isFailureRefundModalOpen}
        title="Refund Transaction Alert"
        variant="error"
        icon="times-circle"
        header="OH, SNAP!"
        subHeader="Something went wrong."
        description="Please go back and retry the action."
        confirmText="Try Again"
        handleConfirm={() => {
          history.push('/disputes');
        }}
        handleClose={() =>
          setState({ ...state, isFailureRefundModalOpen: false })
        }
      />
      <AlertModal
        isOpen={state.isConfirmRefundModalOpen}
        title="Refund Transaction Alert"
        icon="question-circle"
        variant="warn"
        header="ARE YOU SURE?"
        subHeader="You're about to Refund this transaction."
        description={[
          'This action will notify and request approval',
          'from GCash.',
        ]}
        confirmText="Yes"
        handleConfirm={() => {
          setState({
            ...state,
            isConfirmRefundModalOpen: false,
            isSuccessRefundModalOpen: true,
          });
        }}
        handleClose={() =>
          setState({ ...state, isConfirmRefundModalOpen: false })
        }
      />
    </>
  );
};

DisputeInformation.propTypes = {
  history: PropTypes.object,
  match: PropTypes.object,
};

export default DisputeInformation;
