import PropTypes from 'prop-types';
import { json2csv } from 'json-2-csv';
import format from 'date-fns/format';
import React from 'react';
import ActionButtons from '../../components/ActionButtons';
import DataContainer from '../../components/DataContainer';
import DataTable from '../../components/DataTable';
import { FIELD_TYPES } from '../../components/Form/constants';
import GlobalSearch from '../../components/GlobalSearch';
import Header from '../../components/Header/Header';
import Page from '../../components/Page';
import useQuerySeries from '../../hooks/useQuerySeries';
import { GET_GCASH_REFUND } from './query';
import formatCurrency from '../../utils/formatCurrency';
import { ExportButton } from '../../components/Button/ExportButton';

const Disputes = ({ history }) => {
  const {
    pagination,
    setPagination,
    setFilter,
    data,
    loading,
    page,
    setPage,
    isLastPage,
  } = useQuerySeries(GET_GCASH_REFUND, 'gcashRefunds', {
    pagination: {
      start: { id: '', transactionId: '' },
      limit: 10,
    },
  });

  const tableConfig = {
    requestId: {
      headerLabel: 'Request ID',
      sortable: true,
      onClick: data => {
        history.push(
          '/disputes/' + data.referenceId + '/' + data.transactionId
        );
      },
    },
    merchantId: {
      headerLabel: 'Merchant ID',
      sortable: true,
    },
    merchantTransID: {
      headerLabel: 'Merchant Trans ID',
      sortable: true,
    },
    refundAmount: {
      headerLabel: 'Refund Amount',
      sortable: true,
      renderAs: data => formatCurrency(data.refundAmount),
    },
    acquirementId: {
      headerLabel: 'Acquire ID',
      sortable: true,
    },
    refundId: {
      headerLabel: 'Refund ID',
      sortable: true,
    },
    refundStatus: {
      headerLabel: 'Refund Status',
      sortable: true,
    },
    actions: {
      renderAs: data => (
        <ActionButtons
          handleView={() =>
            history.push(`/disputes/${data.referenceId}/${data.transactionId}`)
          }
        />
      ),
    },
  };

  return (
    <>
      <Page>
        <Header
          withHome
          title="GCash Refunds"
          path={['Disputes', 'GCash', 'Refunds']}
        />
        <DataContainer>
          <DataTable
            loading={loading}
            minCellWidth={200}
            data={data}
            headerOptions={
              <>
                <GlobalSearch
                  onSearch={filter => {
                    const newPagination = {
                      ...pagination,
                      start: { id: '', transactionId: '' },
                    };
                    setFilter(filter);
                    setPagination(newPagination);
                  }}
                  fields={[
                    {
                      label: 'Request ID',
                      name: 'requestId',
                      type: FIELD_TYPES.TEXT,
                      disabled: ({
                        merchantId,
                        merchantTransID,
                        refundAmount,
                        refundStatus,
                      }) =>
                        !!(
                          merchantId ||
                          merchantTransID ||
                          refundAmount ||
                          refundStatus
                        ),
                    },
                    {
                      label: 'Merchant ID',
                      name: 'merchantId',
                      type: FIELD_TYPES.TEXT,
                      disabled: ({
                        requestId,
                        merchantTransID,
                        refundAmount,
                        refundStatus,
                      }) =>
                        !!(
                          requestId ||
                          merchantTransID ||
                          refundAmount ||
                          refundStatus
                        ),
                    },
                    {
                      label: 'Merchant Trans ID',
                      name: 'merchantTransID',
                      type: FIELD_TYPES.TEXT,
                      disabled: ({
                        requestId,
                        merchantId,
                        refundAmount,
                        refundStatus,
                      }) =>
                        !!(
                          requestId ||
                          merchantId ||
                          refundAmount ||
                          refundStatus
                        ),
                    },
                    {
                      label: 'Refund Amount',
                      name: 'refundAmount',
                      type: FIELD_TYPES.NUMBER,
                      noOperator: true,
                      disabled: ({
                        requestId,
                        merchantId,
                        merchantTransID,
                        refundStatus,
                      }) =>
                        !!(
                          requestId ||
                          merchantId ||
                          merchantTransID ||
                          refundStatus
                        ),
                    },
                    {
                      label: 'Refund Status',
                      name: 'refundStatus',
                      type: FIELD_TYPES.SELECT,
                      disabled: ({
                        requestId,
                        merchantId,
                        merchantTransID,
                        refundAmount,
                      }) =>
                        !!(
                          requestId ||
                          merchantId ||
                          merchantTransID ||
                          refundAmount
                        ),
                      options: [
                        { value: null, label: '' },
                        {
                          value: 'GCASH_AUTHORISED',
                          label: 'GCASH_AUTHORISED',
                        },
                        {
                          value: 'GCASH_PENDING',
                          label: 'GCASH_PENDING',
                        },
                        {
                          value: 'GCASH_REFUSED',
                          label: 'GCASH_REFUSED',
                        },
                      ],
                    },
                  ]}
                />
                <div style={{ display: 'flex' }}>
                  <ExportButton
                    style={{ marginRight: '0px' }}
                    icon="file-csv"
                    iconPosition="left"
                    disabled={loading}
                    onClick={async () => {
                      const csv = await json2csv(
                        data.map(datum => {
                          const obj = {};
                          for (const key in datum) {
                            const config = tableConfig[key];
                            if (config) {
                              obj[config.headerLabel] = config.renderAs
                                ? config.renderAs(datum)
                                : datum[key];
                            }
                          }
                          return obj;
                        })
                      );
                      const fileData = {
                        mime: 'text/csv',
                        filename: `gcash-refund_${format(new Date(), 'MMDDYYYY')}.csv`,
                        contents: csv,
                      };
                      const blob = new Blob([fileData.contents], {
                        type: fileData.mime,
                      });
                      const url = URL.createObjectURL(blob);
                      const a = document.createElement('a');
                      document.body.appendChild(a);
                      a.download = fileData.filename;
                      a.href = url;
                      a.click();
                      document.body.removeChild(a);
                    }}
                  >
                    CSV
                  </ExportButton>
                </div>
              </>
            }
            config={tableConfig}
            pagination={{
              ...pagination,
              count: 0,
              cursors: [],
              handleChange: setPagination,
            }}
            series={{ page, setPage, isLastPage }}
          />
        </DataContainer>
      </Page>
    </>
  );
};

Disputes.propTypes = {
  history: PropTypes.object,
};

export default Disputes;
