import { gql } from '@apollo/client';

export const GET_GCASH_REFUND = gql`
  query getGCashRefundInfo(
    $filter: SearchGCashRefund!
    $pagination: PaginationReportInput!
  ) {
    gcashRefunds(filter: $filter, pagination: $pagination) {
      lastKey {
        id
        transactionId
      }
      count
      filteredData {
        referenceId
        transactionId
        requestId
        merchantId
        merchantTransID
        refundAmount
        acquirementId
        refundId
        refundStatus
      }
    }
  }
`;
