import PropTypes from 'prop-types';
import React, { useMemo, useState } from 'react';
import * as Yup from 'yup';
import useForm from '../../hooks/useForm';
import { useQuery } from '@apollo/client';
import Page from '../../components/Page';
import Header from '../../components/Header';
import { AlertModal } from '../../components/Modal';
import DataHeader from '../../components/DataHeader';
import FormField from '../../components/Form/FormField';
import DataContainer from '../../components/DataContainer';
import { FIELD_TYPES } from '../../components/Form/constants';
import PrimaryButton from '../../components/Button/PrimaryButton';
import SecondaryButton from '../../components/Button/SecondaryButton';
import { PageSubsection } from '../../components/InformationPage';
import { GET_CHANNEL_OPTIONS, GET_CHANNEL_INFORMATION } from './query';
import { GET_PSORPAYMENTTYPE_OPTIONS } from '../Reports/LoadORReport/query';
import AdyenCheckout from '@adyen/adyen-web';
import '@adyen/adyen-web/dist/adyen.css';

const DropInSimulation = ({ history }) => {
  let clientKey = import.meta.env.VITE_REACT_APP_CLIENT_KEY;
  let api = import.meta.env.VITE_REACT_APP_PS_API_URL;

  const [state, setState] = useState({
    isEditing: true,
    loading: false,

    accessToken: null,

    responseError: null,
    otherError: null,

    otpURL: '',
    paymentMethodResponse: '',
  });

  const [apiUrl] = useState(`${api}/api/command`);
  const [formOn, setFormOn] = useState(false);
  const [isAccessTokenSuccess, setIsAccessTokenSuccess] = useState(false);
  const [isAccessTokenFailed, setIsAccessTokenFailed] = useState(false);
  const [isConfirm, setIsConfirm] = useState(false);
  const [isInitiateSuccess, setIsInitiateSuccess] = useState(false);
  const [isCheckoutSuccess, setIsCheckoutSuccess] = useState(false);
  const [isInitiateFailure, setIsInitiateFailure] = useState(false);
  const [isCheckoutFailure, setIsCheckoutFailure] = useState(false);
  const [theMessage, setTheMessage] = useState(null);
  const [paymentId, setPaymentId] = useState(null);
  const [accessTokenGenerated, setAccessTokenGenerated] = useState(false);

  const { data: channelData, loading: isLoadingChannels } = useQuery(
    GET_CHANNEL_OPTIONS,
    {
      fetchPolicy: 'network-only',
    }
  );

  const channelOptions =
    !isLoadingChannels && channelData
      ? channelData.channelsLoose.map(channel => ({
          value: channel.id,
          label: channel.name,
        }))
      : [];

  channelOptions.unshift({
    value: null,
    label: 'Any',
  });

  const billTypeOptions = [
    { label: 'Any', value: null },
    { label: 'Bill', value: 'Bill' },
    { label: 'NonBill', value: 'NonBill' },
  ];

  // eslint-disable-next-line
  const { fields, initialValue } = useMemo(() => {
    const fields = {
      billType: { validation: Yup.string().required('Please select a value') },
      mobileNumber: {
        validation: values =>
          values && values.billType === billTypeOptions[1].value
            ? Yup.string().nullable()
            : Yup.string()
                .required('Please enter a value')
                .matches(
                  /^(09)\d{9}$/,
                  'Invalid mobile number format! Must start at (09)'
                ),
      },
      accountNumber: {
        validation: values =>
          values && values.billType === billTypeOptions[1].value
            ? Yup.string().required('Please enter a value')
            : Yup.string().nullable(),
      },
      emailAddress: {
        validation: Yup.string()
          .required('Please enter a value')
          .email('Must be an email'),
      },
      amount: {
        validation: Yup.string()
          .min(1, 'Minimum should be 1 digits')
          .max(7, 'Must not exceed 7 digits')
          .matches(
            /^\d*(\.\d+)?$/,
            'Invalid input! Allowed characters are positive number or number with decimal placed only'
          )
          .test(
            'Amount Checker',
            'Invalid Input! Amount is not allowed to be 0',
            value => {
              if (value > -1 && value < 1) {
                return false;
              }
              return true;
            }
          )
          .required('Please enter a value'),
      },
      transactionType: {
        validation: Yup.string().required('Please select a value'),
      },
      responseUrl: {
        validation: Yup.string()
          .required('Please a enter value')
          .url('Must be url format'),
      },
      paymentId: {
        validation: Yup.string().required('Please enter a value'),
      },
      merchantAccount: {
        validation: Yup.string().required('Please enter a value'),
      },
      entityType: {},
      shopperReference: {},
      channelReference: {},
      channelId: {},
    };

    const initialValue = {};

    return { fields, initialValue };
  });

  const { values, onChange, onBlur, errors, onSubmit } = useForm(fields, () => {
    DropInCard();
  });

  const { data, loading } = useQuery(GET_CHANNEL_INFORMATION, {
    variables: { where: { id: values.channelId } },
    skip: !values.channelId,
    fetchPolicy: 'network-only',
  });

  const { data: psORData, loading: isLoadingPSOR } = useQuery(
    GET_PSORPAYMENTTYPE_OPTIONS,
    {
      fetchPolicy: 'network-only',
    }
  );

  const psOROptions =
    !isLoadingPSOR && psORData
      ? psORData.listPSORPaymentType.map(psOR => ({
          value: psOR.or,
          label: psOR.or,
        }))
      : [];

  psOROptions.unshift(
    {
      value: null,
      label: 'Any',
    },
    {
      value: 'RETAILER',
      label: 'RETAILER',
    },
    {
      value: 'CONSUMER',
      label: 'CONSUMER',
    }
  );

  function GenerateAccessToken() {
    try {
      const clientSecret = encodeURIComponent(
        data.channelDropInSimulator.clientSecret
      );
      fetch(
        `${api}/auth/api/v1/accesstoken?clientId=${data.channelDropInSimulator.clientId}&clientSecret=${clientSecret}`
      )
        .then(response => response.json())
        .then(data => {
          setState({ ...state, accessToken: data.results.accessToken });
          setAccessTokenGenerated(true);
          setIsAccessTokenSuccess(true);
        })
        .catch(err => {
          setState({ ...state, accessToken: null });
          setIsAccessTokenFailed(true);
          console.log('Error', err);
        });
    } catch (error) {
      console.log('Error:', error);
    }
  }

  function handleDropInForm() {
    if (formOn) {
      setFormOn(false);
    } else if (!formOn) {
      setFormOn(true);
    }
  }

  function DropInInitiate() {
    try {
      let paymentInfo = {};
      if (
        values.billType === 'NonBill' &&
        values.shopperReference &&
        !values.entityType &&
        !values.channelReference
      ) {
        paymentInfo = {
          currency: 'PHP',
          countryCode: 'PH',
          platform: 'web',
          shopperLocale: 'en-US',
          paymentMethod: 'dropin',
          responseURL: values.responseUrl,
          shopperReference: values.shopperReference,
          browserInfo: {
            acceptHeader: 'application/json',
            userAgent: 'Mozilla',
          },
        };
      } else if (
        values.billType === 'NonBill' &&
        values.entityType &&
        !values.shopperReference &&
        !values.channelReference
      ) {
        paymentInfo = {
          currency: 'PHP',
          countryCode: 'PH',
          platform: 'web',
          shopperLocale: 'en-US',
          paymentMethod: 'dropin',
          responseURL: values.responseUrl,
          entityType: values.entityType,
          browserInfo: {
            acceptHeader: 'application/json',
            userAgent: 'Mozilla',
          },
        };
      } else if (
        values.billType === 'NonBill' &&
        values.entityType &&
        values.shopperReference &&
        !values.channelReference
      ) {
        paymentInfo = {
          currency: 'PHP',
          countryCode: 'PH',
          platform: 'web',
          shopperLocale: 'en-US',
          paymentMethod: 'dropin',
          responseURL: values.responseUrl,
          shopperReference: values.shopperReference,
          entityType: values.entityType,
          browserInfo: {
            acceptHeader: 'application/json',
            userAgent: 'Mozilla',
          },
        };
      } else if (
        values.billType === 'Bill' &&
        values.shopperReference &&
        !values.entityType
      ) {
        paymentInfo = {
          currency: 'PHP',
          countryCode: 'PH',
          platform: 'web',
          shopperLocale: 'en-US',
          paymentMethod: 'dropin',
          responseURL: values.responseUrl,
          shopperReference: values.shopperReference,
          browserInfo: {
            acceptHeader: 'application/json',
            userAgent: 'Mozilla',
          },
        };
      } else if (
        values.billType === 'Bill' &&
        values.entityType &&
        !values.shopperReference
      ) {
        paymentInfo = {
          currency: 'PHP',
          countryCode: 'PH',
          platform: 'web',
          shopperLocale: 'en-US',
          paymentMethod: 'dropin',
          responseURL: values.responseUrl,
          entityType: values.entityType,
          browserInfo: {
            acceptHeader: 'application/json',
            userAgent: 'Mozilla',
          },
        };
      } else if (
        values.billType === 'Bill' &&
        values.entityType &&
        values.shopperReference
      ) {
        paymentInfo = {
          currency: 'PHP',
          countryCode: 'PH',
          platform: 'web',
          shopperLocale: 'en-US',
          paymentMethod: 'dropin',
          responseURL: values.responseUrl,
          shopperReference: values.shopperReference,
          entityType: values.entityType,
          browserInfo: {
            acceptHeader: 'application/json',
            userAgent: 'Mozilla',
          },
        };
      } else if (
        values.billType === 'NonBill' &&
        !values.shopperReference &&
        !values.entityType &&
        values.channelReference
      ) {
        paymentInfo = {
          currency: 'PHP',
          countryCode: 'PH',
          platform: 'web',
          shopperLocale: 'en-US',
          paymentMethod: 'dropin',
          responseURL: values.responseUrl,
          channelReference: values.channelReference,
          browserInfo: {
            acceptHeader: 'application/json',
            userAgent: 'Mozilla',
          },
        };
      } else if (
        values.billType === 'NonBill' &&
        !values.entityType &&
        values.shopperReference &&
        values.channelReference
      ) {
        paymentInfo = {
          currency: 'PHP',
          countryCode: 'PH',
          platform: 'web',
          shopperLocale: 'en-US',
          paymentMethod: 'dropin',
          responseURL: values.responseUrl,
          shopperReference: values.shopperReference,
          channelReference: values.channelReference,
          browserInfo: {
            acceptHeader: 'application/json',
            userAgent: 'Mozilla',
          },
        };
      } else if (
        values.billType === 'NonBill' &&
        values.entityType &&
        !values.shopperReference &&
        values.channelReference
      ) {
        paymentInfo = {
          currency: 'PHP',
          countryCode: 'PH',
          platform: 'web',
          shopperLocale: 'en-US',
          paymentMethod: 'dropin',
          responseURL: values.responseUrl,
          channelReference: values.channelReference,
          entityType: values.entityType,
          browserInfo: {
            acceptHeader: 'application/json',
            userAgent: 'Mozilla',
          },
        };
      } else if (
        values.billType === 'NonBill' &&
        values.shopperReference &&
        values.entityType &&
        values.channelReference
      ) {
        paymentInfo = {
          currency: 'PHP',
          countryCode: 'PH',
          platform: 'web',
          shopperLocale: 'en-US',
          paymentMethod: 'dropin',
          responseURL: values.responseUrl,
          shopperReference: values.shopperReference,
          channelReference: values.channelReference,
          entityType: values.entityType,
          browserInfo: {
            acceptHeader: 'application/json',
            userAgent: 'Mozilla',
          },
        };
      } else {
        paymentInfo = {
          currency: 'PHP',
          countryCode: 'PH',
          platform: 'web',
          shopperLocale: 'en-US',
          paymentMethod: 'dropin',
          responseURL: values.responseUrl,
          browserInfo: {
            acceptHeader: 'application/json',
            userAgent: 'Mozilla',
          },
        };
      }

      fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Request-Method': 'POST',
          Authorization: 'Bearer ' + state.accessToken,
        },
        body: JSON.stringify({
          command: {
            name: 'CreatePaymentSession',
            payload: {
              gatewayProcessor: 'generic',
              paymentInfo: paymentInfo,
              settlementInfos:
                values.billType === 'Bill'
                  ? [
                      {
                        accountNumber: values.accountNumber,
                        emailAddress: values.emailAddress,
                        amountValue: values.amount,
                        transactionType: values.transactionType,
                      },
                    ]
                  : [
                      {
                        mobileNumber: values.mobileNumber,
                        emailAddress: values.emailAddress,
                        amountValue: values.amount,
                        transactionType: values.transactionType,
                      },
                    ],
            },
          },
        }),
      })
        .then(result => result.json())
        .then(response => {
          setIsInitiateSuccess(response.error ? false : true);
          setIsInitiateFailure(response.error ? true : false);
          setTheMessage(response.message);
          setPaymentId(!response.error ? response.data.paymentId : paymentId);
          setState({
            ...state,
            responseError: response.error.details[0].message,
            otherError: response.error.details,
          });
        })
        .catch(error => {
          setState({ ...state, responseError: error });
          console.log('ERROR: ' + error);
        });
    } catch (error) {
      console.log('Error:', error);
    }
  }

  //DropInPayment
  function DropInCheckout(encryptedValue) {
    try {
      //Condition for Payment Payload
      const paymentMethodData = encryptedValue.data.paymentMethod;
      let dropInCheckoutPayload = {};
      if (encryptedValue.data.storePaymentMethod && values.shopperReference) {
        dropInCheckoutPayload = {
          paymentId: values.paymentId,
          paymentMethod: {
            type: 'scheme',
            encryptedCardNumber: paymentMethodData.encryptedCardNumber,
            encryptedExpiryMonth: paymentMethodData.encryptedExpiryMonth,
            encryptedExpiryYear: paymentMethodData.encryptedExpiryYear,
            encryptedSecurityCode: paymentMethodData.encryptedSecurityCode,
            holderName: paymentMethodData.holderName,
            brand: paymentMethodData.brand,
          },
          merchantAccount: values.merchantAccount,
          shopperReference: values.shopperReference,
          storePaymentMethod: true,
        };
      } else if (
        paymentMethodData.storedPaymentMethodId &&
        paymentMethodData.holderName &&
        values.shopperReference
      ) {
        dropInCheckoutPayload = {
          paymentId: values.paymentId,
          paymentMethod: {
            type: 'scheme',
            storedPaymentMethodId: paymentMethodData.storedPaymentMethodId,
            encryptedSecurityCode: paymentMethodData.encryptedSecurityCode,
            holderName: paymentMethodData.holderName,
            brand: paymentMethodData.brand,
          },
          merchantAccount: values.merchantAccount,
          shopperReference: values.shopperReference,
          storePaymentMethod: true,
        };
      } else if (
        paymentMethodData.storedPaymentMethodId &&
        !paymentMethodData.holderName &&
        values.shopperReference
      ) {
        dropInCheckoutPayload = {
          paymentId: values.paymentId,
          paymentMethod: {
            type: 'scheme',
            storedPaymentMethodId: paymentMethodData.storedPaymentMethodId,
            encryptedSecurityCode: paymentMethodData.encryptedSecurityCode,
            brand: paymentMethodData.brand,
          },
          merchantAccount: values.merchantAccount,
          shopperReference: values.shopperReference,
          storePaymentMethod: true,
        };
      } else if (!values.shopperReference || values.shopperReference) {
        dropInCheckoutPayload = {
          paymentId: values.paymentId,
          paymentMethod: {
            type: 'scheme',
            encryptedCardNumber: paymentMethodData.encryptedCardNumber,
            encryptedExpiryMonth: paymentMethodData.encryptedExpiryMonth,
            encryptedExpiryYear: paymentMethodData.encryptedExpiryYear,
            encryptedSecurityCode: paymentMethodData.encryptedSecurityCode,
            holderName: paymentMethodData.holderName,
            brand: paymentMethodData.brand,
          },
          merchantAccount: values.merchantAccount,
        };
      }

      fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Request-Method': 'POST',
          Authorization: 'Bearer ' + state.accessToken,
        },
        body: JSON.stringify({
          command: {
            name: 'DropinPaymentDataCreated',
            payload: dropInCheckoutPayload,
          },
        }),
      })
        .then(result => result.json())
        .then(response => {
          setIsCheckoutSuccess(response.error ? false : true);
          setIsCheckoutFailure(response.error ? true : false);
          setTheMessage(response.message);
          setPaymentId(!response.error ? response.data.paymentId : paymentId);
          setState({
            ...state,
            responseError: response.error.details[0].message,
            otherError: response.error.details,
          });
        })
        .catch(error => {
          setState({ ...state, responseError: error });
          console.log('ERROR: ' + error);
        });
    } catch (error) {
      console.log('Error:', error);
    }
  }

  const Simulate = (
    <center>
      <PrimaryButton
        disabled={
          !state.isEditing ||
          loading ||
          !values.channelId ||
          !values.merchantAccount ||
          !values.paymentId ||
          errors.merchantAccount ||
          errors.paymentId ||
          !state.paymentMethodResponse
        }
        onClick={() => {
          onSubmit();
        }}
      >
        Simulate
      </PrimaryButton>
    </center>
  );

  const GenerateToken = (
    <center>
      <PrimaryButton
        disabled={!state.isEditing || loading || !values.channelId}
        onClick={() => {
          GenerateAccessToken();
        }}
      >
        Generate AccessToken
      </PrimaryButton>
    </center>
  );

  const CreatePaymentSession = (
    <center>
      <PrimaryButton
        disabled={
          !state.isEditing ||
          loading ||
          !accessTokenGenerated ||
          errors.billType ||
          errors.amount ||
          errors.responseUrl ||
          errors.emailAddress ||
          errors.transactionType ||
          (values.billType === 'Bill' && errors.accountNumber) ||
          (values.billType === 'NonBill' && errors.mobileNumber) ||
          !values.billType ||
          !values.amount ||
          !values.responseUrl ||
          !values.emailAddress ||
          !values.transactionType ||
          (values.billType === 'Bill' && !values.accountNumber) ||
          (values.billType === 'NonBill' && !values.mobileNumber)
        }
        onClick={() => {
          setIsConfirm(true);
        }}
      >
        Create PaymentSession
      </PrimaryButton>
    </center>
  );

  //DropInInitiate
  function DropInCard() {
    try {
      const paymentMethodResponseValue = JSON.parse(
        `{${state.paymentMethodResponse}}`
      );
      const configuration = {
        paymentMethodsResponse: paymentMethodResponseValue,
        clientKey: clientKey,
        environment: 'test',
        locale: 'en_US',
        showPayButton: true,
        showStoredPaymentMethods: true,
        paymentMethodsConfiguration: {
          ideal: {
            showImage: true,
          },
          card: {
            hasHolderName: true,
            holderNameRequired: true,
            enableStoreDetails: true,
            name: 'Credit or debit card',
            amount: {
              value: values.amount * 100,
              currency: 'PHP',
            },
          },
        },

        onSubmit: state => {
          if (state.isValid) {
            DropInCheckout(state);
          }
        },
      };

      const checkout = new AdyenCheckout(configuration);
      checkout.create('dropin').mount('#dropin');
    } catch (error) {
      console.log(error);
    }
  }

  function handleChangeOTPUrl(event) {
    setState({ ...state, otpURL: event.target.value });
  }

  function handlePaymentMethodResponse(event) {
    setState({
      ...state,
      paymentMethodResponse: event.target.value,
    });
  }

  const FormButton = (
    <>
      <SecondaryButton onClick={handleDropInForm}>
        {formOn ? 'Hide OTP Form' : 'Show OTP Form'}
      </SecondaryButton>
      <br />
      <br />
    </>
  );

  return (
    <>
      <Page>
        <Header
          withHome
          title={'DropIn Simulation'}
          path={['DropIn Simulation']}
        />
        <DataContainer>
          <>
            <DataHeader>
              <DataHeader.Title>DropIn Simulation</DataHeader.Title>
            </DataHeader>
            {FormButton}
            {formOn ? (
              <>
                <h2>OTP Form</h2>

                <form action={`${state.otpURL}`} target="_blank" method="POST">
                  MD:
                  <input type="text" name="MD" /> <br />
                  Pareq:
                  <input type="text" name="PaReq" /> <br />
                  TermUrl:
                  <input type="text" name="TermUrl" /> <br />
                  otpURL:
                  <input
                    type="text"
                    name="otpURL"
                    value={state.otpURL}
                    onChange={handleChangeOTPUrl}
                  />
                  <input type="hidden" name="form_submitted" value="1" />
                  <br />
                  <input type="submit" value="Submit" />
                </form>
                <br />
                <br />
              </>
            ) : (
              ''
            )}

            <PageSubsection>
              <FormField
                label="Channel"
                name="channelId"
                type={FIELD_TYPES.SELECT}
                options={channelOptions}
                value={values.channelId}
                onChange={onChange.channelId}
                onBlur={onBlur.channelId}
                error={errors.channelId}
                readOnly={!state.isEditing}
                perRow={2}
                required
              />
              <FormField
                label="Access Token"
                name="accessToken"
                type={FIELD_TYPES.TEXT}
                value={state.accessToken}
                onChange={onChange.accessToken}
                onBlur={onBlur.accessToken}
                error={errors.accessToken}
                readOnly={state.isEditing}
                perRow={2}
                required
              />
            </PageSubsection>
            {GenerateToken}
            <br />
            <br />
            {state.accessToken && (
              <PageSubsection>
                <FormField
                  label="Bill Type"
                  name="billType"
                  type={FIELD_TYPES.SELECT}
                  options={billTypeOptions}
                  value={values.billType}
                  onChange={onChange.billType}
                  onBlur={onBlur.billType}
                  error={errors.billType}
                  readOnly={!state.isEditing}
                  perRow={2}
                  required
                />
                {values.billType ? (
                  values.billType === 'Bill' ? (
                    <FormField
                      label="Account Number"
                      name="accountNumber"
                      type={FIELD_TYPES.TEXT}
                      value={values.accountNumber}
                      onChange={onChange.accountNumber}
                      onBlur={onBlur.accountNumber}
                      error={errors.accountNumber}
                      readOnly={!state.isEditing}
                      perRow={2}
                      required
                    />
                  ) : (
                    <FormField
                      label="Mobile Number"
                      name="mobileNumber"
                      type={FIELD_TYPES.TEXT}
                      value={values.mobileNumber}
                      onChange={onChange.mobileNumber}
                      onBlur={onBlur.mobileNumber}
                      error={errors.mobileNumber}
                      readOnly={!state.isEditing}
                      perRow={2}
                      required
                    />
                  )
                ) : (
                  ''
                )}
                <FormField
                  label="Email Address"
                  name="emailAddress"
                  type={FIELD_TYPES.TEXT}
                  value={values.emailAddress}
                  onChange={onChange.emailAddress}
                  onBlur={onBlur.emailAddress}
                  error={errors.emailAddress}
                  readOnly={!state.isEditing}
                  perRow={2}
                  required
                />
                <FormField
                  label="Amount"
                  name="amount"
                  type={FIELD_TYPES.TEXT}
                  value={values.amount}
                  onChange={onChange.amount}
                  onBlur={onBlur.amount}
                  error={errors.amount}
                  readOnly={!state.isEditing}
                  perRow={2}
                  required
                />
                <FormField
                  label="Transaction Type"
                  name="transactionType"
                  type={FIELD_TYPES.SELECT}
                  value={values.transactionType}
                  options={[
                    { label: 'Any', value: null },
                    { label: 'I', value: 'I' },
                    { label: 'G', value: 'G' },
                    { label: 'N', value: 'N' },
                  ]}
                  onChange={onChange.transactionType}
                  onBlur={onBlur.transactionType}
                  error={errors.transactionType}
                  readOnly={!state.isEditing}
                  perRow={2}
                  required
                />
                <FormField
                  label="Response URL"
                  name="responseUrl"
                  type={FIELD_TYPES.TEXT}
                  value={values.responseUrl}
                  onChange={onChange.responseUrl}
                  onBlur={onBlur.responseUrl}
                  error={errors.responseUrl}
                  readOnly={!state.isEditing}
                  perRow={2}
                  required
                />
                <FormField
                  label="Shopper Reference"
                  name="shoppeReference"
                  type={FIELD_TYPES.TEXT}
                  value={values.shopperReference}
                  onChange={onChange.shopperReference}
                  onBlur={onBlur.shopperReference}
                  error={errors.shopperReference}
                  readOnly={!state.isEditing}
                  perRow={2}
                />
                {values.billType === 'NonBill' && (
                  <FormField
                    label="Channel Reference"
                    name="channelReference"
                    type={FIELD_TYPES.TEXT}
                    value={values.channelReference}
                    onChange={onChange.channelReference}
                    onBlur={onBlur.channelReference}
                    error={errors.channelReference}
                    readOnly={!state.isEditing}
                    perRow={2}
                  />
                )}
                <FormField
                  label="Entity Type"
                  name="entityType"
                  type={FIELD_TYPES.SELECT}
                  options={psOROptions}
                  value={values.entityType}
                  onChange={onChange.entityType}
                  onBlur={onBlur.entityType}
                  error={errors.entityType}
                  readOnly={!state.isEditing}
                  perRow={2}
                />
              </PageSubsection>
            )}
            {state.accessToken && CreatePaymentSession}
            {paymentId && state.accessToken && (
              <PageSubsection>
                <FormField
                  label="PaymentId"
                  name="paymentId"
                  type={FIELD_TYPES.TEXT}
                  value={values.paymentId}
                  onChange={onChange.paymentId}
                  onBlur={onBlur.paymentId}
                  error={errors.paymentId}
                  readOnly={!state.isEditing}
                  perRow={1}
                  required
                />
                <FormField
                  label="Merchant Account"
                  name="merchantAccount"
                  type={FIELD_TYPES.TEXT}
                  value={values.merchantAccount}
                  onChange={onChange.merchantAccount}
                  onBlur={onBlur.merchantAccount}
                  error={errors.merchantAccount}
                  readOnly={!state.isEditing}
                  perRow={1}
                  required
                />
                <div>
                  <label>Payment Method Response</label>
                  <br />
                  <br />
                  <textarea
                    label="Payment Method Response"
                    name="paymentMethodResponse"
                    value={state.paymentMethodResponse}
                    onChange={handlePaymentMethodResponse}
                    cols="80"
                    rows="10"
                  />
                  <br />
                  <br />
                </div>
              </PageSubsection>
            )}

            <PageSubsection>
              {paymentId && state.accessToken && Simulate}
              <br />
              <br />
            </PageSubsection>

            <PageSubsection>
              <div id="dropin"></div>
            </PageSubsection>
          </>
        </DataContainer>
      </Page>

      <AlertModal
        isOpen={isConfirm}
        title={'Dropin Confirmation'}
        icon="exclamation-circle"
        variant="warn"
        header="ARE YOU SURE?"
        subHeader={`You are about to do a dropin confirmation`}
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        confirmText="Yes"
        handleConfirm={() => {
          DropInInitiate();
          setIsConfirm(false);
        }}
        handleClose={() => {
          setIsConfirm(false);
        }}
      />

      <AlertModal
        isOpen={isAccessTokenSuccess}
        title={'Generate Token Success'}
        handleClose={() => {
          setIsAccessTokenSuccess(false);
        }}
        icon="check-circle"
        variant="success"
        header={'SUCCESS'}
        subHeader={'Accesstoken generate success'}
        description={``}
        confirmText="Ok"
        handleConfirm={() => {
          setIsAccessTokenSuccess(false);
          history.push('/DropInSimulation');
        }}
      />
      <AlertModal
        isOpen={isAccessTokenFailed}
        title={'Generate Token Failed'}
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader={`Failed to generate token on this channel`}
        description="Please go back and try generating again."
        handleClose={() => setIsAccessTokenFailed(false)}
        confirmText="Ok"
        handleConfirm={() => {
          setIsAccessTokenFailed(false);
        }}
      />

      <AlertModal
        isOpen={isInitiateSuccess}
        title={'DROPIN INITAITE SUCCESS'}
        handleClose={() => {
          setIsInitiateSuccess(false);
        }}
        icon="check-circle"
        variant="success"
        header={theMessage}
        subHeader={paymentId}
        description={``}
        confirmText="Go Back"
        handleConfirm={() => {
          setIsInitiateSuccess(false);
          history.push('/DropInSimulation');
        }}
      />
      <AlertModal
        isOpen={isInitiateFailure}
        title={'DROPIN INITAITE FAILED'}
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader={`${state.responseError ? 'Something went wrong!' : state.otherError}`}
        description="Please go back and try saving it again."
        handleClose={() => setIsInitiateFailure(false)}
        confirmText="Go Back"
        handleConfirm={() => {
          setIsInitiateFailure(false);
        }}
      />

      <AlertModal
        isOpen={isCheckoutSuccess}
        title={'DROPIN CHECKOUT SUCCESS'}
        handleClose={() => {
          setIsCheckoutSuccess(false);
          window.location.reload();
        }}
        icon="check-circle"
        variant="success"
        header={theMessage}
        subHeader={paymentId}
        description={``}
        confirmText="Go Back"
        handleConfirm={() => {
          setIsCheckoutSuccess(false);
          window.location.reload();
        }}
      />

      <AlertModal
        isOpen={isCheckoutFailure}
        title={'DROPIN CHECKOUT FAILED'}
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader={`${state.responseError ? 'Something went wrong!' : state.otherError}`}
        description="Please go back and try saving it again."
        handleClose={() => setIsCheckoutFailure(false)}
        confirmText="Go Back"
        handleConfirm={() => {
          setIsCheckoutFailure(false);
          window.location.reload();
        }}
      />
    </>
  );
};

DropInSimulation.propTypes = {
  location: PropTypes.object,
  history: PropTypes.object,
  match: PropTypes.object,
};

export default DropInSimulation;
