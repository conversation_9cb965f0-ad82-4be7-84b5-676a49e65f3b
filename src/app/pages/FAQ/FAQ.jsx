import React, { Fragment, useState } from 'react';
import Button from '../../components/Button/Button';
import DataContainer from '../../components/DataContainer';
import DataHeader from '../../components/DataHeader';
import GlobalSearch from '../../components/GlobalSearch';
import Header from '../../components/Header';
import Page from '../../components/Page';
import questions from './questions';
import {
  FAQAnswerContainer,
  FAQAnswerLabel,
  FAQAnswerText,
  FAQAnswerTextContainer,
  FAQAnswerTexts,
  FAQContainer,
  FAQDataHeader,
  FAQDivider,
  FAQQuestionCollapseIcon,
  FAQQuestionContainer,
  FAQQuestionLabel,
  FAQQuestionText,
  FAQQuestionTextContainer,
  FAQSearchHightlight,
} from './styled';

function withHighlight(highlighted, text) {
  const index = text.toLowerCase().indexOf(highlighted.toLowerCase());
  return (
    <>
      <span>{text.slice(0, index)}</span>
      <FAQSearchHightlight>
        {text.slice(index, index + highlighted.length)}
      </FAQSearchHightlight>
      <span>{text.slice(index + highlighted.length, text.length)}</span>
    </>
  );
}

const FAQ = () => {
  const [isQuestionCollapsed, setIsQuestionCollapsed] = useState(
    questions.reduce((acc, curr) => ({ ...acc, [curr.question]: false }), {})
  );

  const [questionFilter, setQuestionFilter] = useState('');
  return (
    <Page>
      <Header
        withHome
        path={['Help & Support']}
        title="Frequently Asked Questions"
      />
      <DataContainer>
        <GlobalSearch
          placeholder="Search entries here..."
          onSearch={questionFilter => {
            setQuestionFilter(questionFilter);
          }}
        />
        <FAQDataHeader>
          <DataHeader.Title>How may we help you?</DataHeader.Title>
        </FAQDataHeader>
        {questions
          .filter(
            ({ question }) =>
              questionFilter === '' ||
              question.toLowerCase().includes(questionFilter.toLowerCase())
          )
          .map(({ question, answer }, index) => {
            return (
              <Fragment key={index}>
                <FAQContainer>
                  <FAQQuestionContainer>
                    <FAQQuestionTextContainer>
                      <FAQQuestionLabel>Q.</FAQQuestionLabel>
                      <FAQQuestionText>
                        {questionFilter
                          ? withHighlight(questionFilter, question)
                          : question}
                      </FAQQuestionText>
                    </FAQQuestionTextContainer>
                    <Button
                      onClick={() => {
                        setIsQuestionCollapsed({
                          ...isQuestionCollapsed,
                          [question]: !isQuestionCollapsed[question],
                        });
                      }}
                    >
                      <FAQQuestionCollapseIcon
                        icon={`angle-${isQuestionCollapsed[question] ? 'up' : 'down'}`}
                      />
                    </Button>
                  </FAQQuestionContainer>
                  {isQuestionCollapsed[question] && (
                    <FAQAnswerContainer>
                      <FAQAnswerTextContainer>
                        <FAQAnswerLabel>A.</FAQAnswerLabel>
                        <FAQAnswerTexts>
                          {typeof answer === 'string' ? (
                            <FAQAnswerText>{answer}</FAQAnswerText>
                          ) : (
                            answer.map((a, answerIndex) => (
                              <FAQAnswerText key={answerIndex}>
                                {answerIndex + 1}. {a}
                              </FAQAnswerText>
                            ))
                          )}
                        </FAQAnswerTexts>
                      </FAQAnswerTextContainer>
                    </FAQAnswerContainer>
                  )}
                </FAQContainer>

                {index !== questions.length - 1 && <FAQDivider />}
              </Fragment>
            );
          })}
      </DataContainer>
    </Page>
  );
};

FAQ.propTypes = {};

export default FAQ;
