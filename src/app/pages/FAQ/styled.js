import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import styled from 'styled-components';
import DataHeader from '../../components/DataHeader';

export const FAQDataHeader = styled(DataHeader)`
  margin-top: 25px;
`;

export const FAQContainer = styled.div`
  margin-left: 30px;
  width: 800px;
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;

  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    width: auto;
    margin-left: 0;
  }
`;

export const FAQQuestionContainer = styled.div`
  border-left: 3px solid #26aeef;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f9f9f9;
  padding: 15px 10px;
`;

export const FAQQuestionTextContainer = styled.div`
  display: flex;
  align-items: center;
`;

export const FAQQuestionLabel = styled.div`
  font-size: 30px;
  color: #0072ce;
  margin-right: 20px;
`;

export const FAQQuestionText = styled.div`
  color: #333333;
  font-size: 16px;
`;

export const FAQQuestionCollapseIcon = styled(FontAwesomeIcon)`
  color: #0072ce;
  font-size: 28px;
`;

export const FAQAnswerContainer = styled.div`
  border-left: 3px solid #eeeeee;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: white;
  padding: 15px 10px;
`;

export const FAQAnswerTextContainer = styled.div`
  display: flex;
`;

export const FAQAnswerLabel = styled.div`
  font-size: 30px;
  color: #4a4a4a;
  margin-right: 20px;
`;

export const FAQAnswerTexts = styled.div`
  display: flex;
  flex-direction: column;
`;

export const FAQAnswerText = styled.div`
  font-weight: 300;
  color: #333333;
  font-size: 14px;
  margin-bottom: 5px;

  &:last-child {
    margin-bottom: 0;
  }
`;

export const FAQDivider = styled.div`
  margin-left: 30px;
  width: 800px;
  margin-bottom: 20px;
  border-bottom: 1px solid #eaeaea;
`;

export const FAQSearchHightlight = styled.span`
  background-color: #f1faa8;
`;
