import React, { useContext, useState } from 'react';
import format from 'date-fns/format';
import { ExportButton } from '../../components/Button/ExportButton';
import DataContainer from '../../components/DataContainer';
import DataTable from '../../components/DataTable';
import Header from '../../components/Header';
import GlobalSearch from '../../components/GlobalSearch';
import { AlertModal } from '../../components/Modal';
import Page from '../../components/Page';
import PrimaryButton from '../../components/Button/PrimaryButton';
import AuthContext from '../../context/AuthContext/AuthContext';
import { EXPORT_REPORTS, EDIT_GCASH_BINDING } from './mutation';
import useQueryReportSeries from '../../hooks/useQueryReportSeries';
import {
  ResponsiveRow,
  TransactionLogHighlight,
  ColumnVisibilityDropdown,
} from './styled';
import { GET_GCASH_ENROLLED_PAYMENT_METHODS, REPORT_PATH } from './query';
import { useMutation } from '@apollo/client';
import { FIELD_TYPES } from '../../components/Form/constants';
import NotificationContext from '../../context/NotificationContext';

const GCashEnrolledPaymentMethods = () => {
  const { permissions } = useContext(AuthContext);

  const { addNotif } = useContext(NotificationContext);

  const [state, setState] = useState({});

  const [isConfirmDownloadModalOpen, setIsConfirmDownloadModalOpen] =
    useState(false);

  const [isSuccessDownloadModalOpen, setIsSuccessDownloadModalOpen] =
    useState(false);

  const [logExtraction] = useMutation(EXPORT_REPORTS);

  const tableConfig = {
    channel: {
      headerLabel: 'Channel',
      sortable: true,
    },
    uuid: {
      headerLabel: 'UUID',
      sortable: true,
    },
    bindingRequestID: {
      headerLabel: 'Binding Request ID',
      sortable: true,
    },
    bindingTokenId: {
      headerLabel: 'Binding Token ID',
      sortable: true,
    },
    status: {
      headerLabel: 'Status',
      sortable: true,
    },
    phoneNumber: {
      headerLabel: 'Phone Number',
      sortable: true,
    },
    validUntil: {
      headerLabel: 'Valid Until',
      renderAs: data =>
        data.validUntil
          ? format(data.validUntil, 'MM/DD/YYYY - hh:mm:ss A')
          : '',
      sortable: true,
    },
    unbindDate: {
      headerLabel: 'Unbind Date',
      renderAs: data =>
        data.unbindDate
          ? format(data.unbindDate, 'MM/DD/YYYY - hh:mm:ss A')
          : '',
      sortable: true,
    },
    unbindAction: {
      headerLabel: '',
      renderAs: data => (
        <>
          {permissions.GCashBindingReport?.update && (
            <PrimaryButton
              disabled={data.status !== 'ACTIVE'}
              onClick={() =>
                setState(prevState => ({
                  ...prevState,
                  isConfirmEditGCashBindingModalOpen: true,
                  selectedBindingRequestId: data.bindingRequestID,
                }))
              }
            >
              Unbind
            </PrimaryButton>
          )}
        </>
      ),
    },
  };

  const [visibleColumns, setVisibleColumns] = useState(
    Object.keys(tableConfig)
  );

  const {
    pagination,
    setNewPagination,
    filter,
    setFilter,
    data,
    loading,
    page,
    setPage,
    isLastPage,
    refetch,
  } = useQueryReportSeries(
    GET_GCASH_ENROLLED_PAYMENT_METHODS,
    'gCashBindingReport',
    {
      pagination: {
        startKeys: '',
        limit: 10,
      },
    }
  );

  const [editGCashBinding, { loading: isEditingGCashBinding }] = useMutation(
    EDIT_GCASH_BINDING,
    {
      onCompleted: () => {
        setState(prevState => ({
          ...prevState,
          selectedBindingRequestId: null,
          isConfirmEditGCashBindingModalOpen: false,
          isSuccessEditGCashBindingModalOpen: true,
          isEditing: false,
        }));
        refetch();
      },
      onError: err =>
        setState(prevState => ({
          ...prevState,
          editGCashBindingError: err.networkError.result
            ? err.networkError.result.message
            : null,
          selectedBindingRequestId: null,
          isConfirmEditGCashBindingModalOpen: false,
          isFailureEditGCashBindingModalOpen: true,
        })),
      refetchQueries: [
        {
          query: GET_GCASH_ENROLLED_PAYMENT_METHODS,
          variables: {
            filter,
            pagination,
          },
        },
      ],
    }
  );

  return (
    <>
      <Page>
        <Header
          withHome
          title="GCash Enrolled Payment Methods"
          path={['GCash Enrolled Payment Methods']}
        />
        <DataContainer>
          <DataTable
            loading={loading}
            data={data}
            minCellWidth={200}
            headerOptions={
              <>
                <GlobalSearch
                  onSearch={filter => {
                    const newPagination = {
                      ...pagination,
                      startKeys: '',
                    };
                    setFilter(filter);
                    setNewPagination(newPagination);
                  }}
                  fields={[
                    {
                      label: 'Channel',
                      name: 'channel',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'UUID',
                      name: 'uuid',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Binding Request ID',
                      name: 'bindingRequestID',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Status',
                      name: 'status',
                      type: FIELD_TYPES.SELECT,
                      options: [
                        { value: 'ACTIVE', label: 'ACTIVE' },
                        { value: 'EXPIRED', label: 'EXPIRED' },
                        { value: 'UNBIND', label: 'UNBIND' },
                        { value: 'INACTIVE', label: 'INACTIVE' },
                        { value: 'PROCESSING', label: 'PROCESSING' },
                      ],
                    },
                    {
                      label: 'Valid Until',
                      name: 'validUntil',
                      type: FIELD_TYPES.DATE_RANGE_NO_MAX,
                    },
                  ]}
                />
                <ResponsiveRow>
                  {permissions.GCashBindingReport?.export && (
                    <ExportButton
                      icon="file-csv"
                      iconPosition="left"
                      disabled={false}
                      onClick={() => {
                        setIsConfirmDownloadModalOpen(true);
                      }}
                    >
                      CSV
                    </ExportButton>
                  )}
                  <ColumnVisibilityDropdown
                    multi
                    showMulti={false}
                    placeholder="Visible Columns"
                    value={visibleColumns}
                    options={Object.keys(tableConfig)
                      .filter(key => tableConfig[key].headerLabel !== '')
                      .map(key => ({
                        value: key,
                        label: tableConfig[key].headerLabel,
                      }))}
                    onChange={setVisibleColumns}
                  />
                </ResponsiveRow>
              </>
            }
            config={Object.keys(tableConfig).reduce((config, key) => {
              if (visibleColumns.includes(key)) {
                config[key] = tableConfig[key];
              }
              return config;
            }, {})}
            pagination={{
              ...pagination,
              startKeys: pagination.startKeys,
              withStartKeys: true,
              count: 0,
              cursors: [],
              handleChange: setNewPagination,
            }}
            series={{ page, setPage, isLastPage }}
          />
        </DataContainer>
      </Page>
      <AlertModal
        isOpen={isConfirmDownloadModalOpen}
        title="Export Reports Alert"
        header="ARE YOU SURE?"
        variant="warn"
        icon="exclamation-circle"
        subHeader={
          <span>
            You are about to export all filtered records as .CSV File.
          </span>
        }
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() => {
          setIsConfirmDownloadModalOpen(false);
        }}
        handleCancel={() => {
          setIsConfirmDownloadModalOpen(false);
        }}
        confirmText="Yes"
        handleConfirm={async () => {
          const notifTime = new Date().getTime();
          setIsConfirmDownloadModalOpen(false);
          addNotif({
            id: 'GCashOneClickEnrolledPaymentMethodsReport-' + notifTime,
            notifTime,
            type: 'info',
            title: 'Downloading Report File',
            message:
              'Downloading GCash One-Click Enrolled Payment Methods Report',
            isProgress: true,
            progressData: {
              progress: 0,
              isProgressive: false,
            },
            isLocal: true,
            data: {},
            reportDLParams: {
              query: GET_GCASH_ENROLLED_PAYMENT_METHODS,
              path: REPORT_PATH,
              variables: {
                filter,
                pagination: {
                  startKeys: '',
                  limit: 10,
                },
              },
              onDownload: () => {
                logExtraction({
                  variables: {
                    data: {
                      type: 'gCashBindingReport',
                    },
                  },
                });
              },
              tableConfig,
              fileName: `Enrolled-Payment-Methods Report ${format(new Date(), 'MMDDYYYY')}.csv`,
            },
          });
        }}
      />
      <AlertModal
        isOpen={isSuccessDownloadModalOpen}
        title="Export Reports Alert"
        header="SUCCESS!"
        variant="success"
        icon="check-circle"
        subHeader={
          <span>
            You exported{' '}
            <TransactionLogHighlight>
              {data.length} row(s){' '}
            </TransactionLogHighlight>{' '}
            as .CSV File succesfully.
          </span>
        }
        description="Kindly check the downloaded file."
        confirmText="Back to All Reports"
        handleClose={() => {
          setIsSuccessDownloadModalOpen(false);
        }}
        handleConfirm={() => {
          setIsSuccessDownloadModalOpen(false);
        }}
      />
      <AlertModal
        isOpen={state.isConfirmEditGCashBindingModalOpen}
        title="Save Changes Alert"
        variant="warn"
        icon="exclamation-circle"
        header="ARE YOU SURE?"
        subHeader="You are about to unbind the selected payment method."
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone',
        ]}
        confirmText="Yes"
        confirmLoading={isEditingGCashBinding}
        handleConfirm={() => {
          editGCashBinding({
            variables: {
              where: { bindingRequestID: state.selectedBindingRequestId },
            },
          });
        }}
        handleClose={() =>
          setState(prevState => ({
            ...prevState,
            isConfirmEditGCashBindingModalOpen: false,
          }))
        }
      />
      <AlertModal
        isOpen={state.isSuccessEditGCashBindingModalOpen}
        title="Save Changes Alert"
        variant="success"
        icon="check-circle"
        header="SUCCESS!"
        subHeader="Changes have been saved successfully."
        description={[]}
        confirmText="Go to All GCash Enrolled Payment Methods"
        handleConfirm={() => {
          setState({ ...state, isSuccessEditGCashBindingModalOpen: false });
        }}
        handleClose={() =>
          setState({ ...state, isSuccessEditGCashBindingModalOpen: false })
        }
      />
      <AlertModal
        isOpen={state.isFailureEditGCashBindingModalOpen}
        title="Save Changes Alert"
        variant="error"
        icon="times-circle"
        header="OH, SNAP!"
        subHeader={''}
        description={
          state.editGCashBindingError
            ? state.editGCashBindingError
            : 'There was a problem with unbinding the payment method.'
        }
        confirmText="Go back"
        handleConfirm={() => {
          setState({
            ...state,
            isFailureEditGCashBindingModalOpen: false,
            editGCashBindingError: null,
          });
        }}
        handleClose={() =>
          setState({
            ...state,
            isFailureEditGCashBindingModalOpen: false,
            editGCashBindingError: null,
          })
        }
      />
    </>
  );
};

GCashEnrolledPaymentMethods.propTypes = {};

export default GCashEnrolledPaymentMethods;
