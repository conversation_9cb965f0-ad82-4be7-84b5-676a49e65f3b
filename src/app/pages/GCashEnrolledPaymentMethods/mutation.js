import { gql } from '@apollo/client';

export const EXPORT_REPORTS = gql`
  mutation downloadReports($data: DownloadReportInput!) {
    downloadReports(data: $data) {
      authorized
    }
  }
`;

export const EDIT_GCASH_BINDING = gql`
  mutation updategCashBindingReport($where: UpdateGCashBindingWhere!) {
    updategCashBindingReport(where: $where) {
      uuid
      status
      bindingRequestID
    }
  }
`;
