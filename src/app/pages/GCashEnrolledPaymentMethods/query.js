import { gql } from '@apollo/client';

export const GET_GCASH_ENROLLED_PAYMENT_METHODS = gql`
  query getGCashBindingReport(
    $filter: GCashBindingReportInput
    $pagination: PaginationReportInput!
  ) {
    gCashBindingReport(filter: $filter, pagination: $pagination) {
      lastKey
      filteredData {
        uuid
        bindingRequestID
        bindingTokenId
        channel
        channelId
        status
        phoneNumber
        validUntil
        gatewayProcessor
        paymentMethod
        unbindDate
      }
    }
  }
`;

export const REPORT_PATH = 'gCashBindingReport';
