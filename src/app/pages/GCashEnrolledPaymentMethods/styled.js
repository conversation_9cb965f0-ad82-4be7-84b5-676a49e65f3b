import styled from 'styled-components';
import Dropdown from '../../components/Dropdown';
import {
  DropdownButton,
  DropdownContainer,
  DropdownMenu,
} from '../../components/Dropdown/Dropdown';
import Row from '../../components/Row';

export const ResponsiveRow = styled(Row)`
  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    width: 100%;
  }
`;

export const TransactionLogHighlight = styled.span`
  color: #3c7eda;
`;

export const ColumnVisibilityDropdown = styled(Dropdown)`
  ${DropdownContainer} {
    @media (max-width: ${props => props.theme.breakpoint.phone}) {
      margin-top: 10px;
      width: 100%;
    }
  }

  ${DropdownButton} {
    font-size: ${props => props.theme.fontSize.s};
    width: 200px;
    border: 0;
    border-bottom: 1px solid #979797;
    @media (max-width: ${props => props.theme.breakpoint.phone}) {
      width: 100%;
    }
  }

  ${DropdownMenu} {
    font-size: ${props => props.theme.fontSize.s};
    border: 1px solid #979797;
  }
`;
