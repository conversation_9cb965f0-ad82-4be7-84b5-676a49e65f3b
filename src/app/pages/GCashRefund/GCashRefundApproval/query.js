import { gql } from '@apollo/client';

export const GET_GCASHREFUNDAPPROVAL = gql`
  query getRefundApprovalModule(
    $filter: SearchGcashRefundInput!
    $pagination: PaginationReportInput!
  ) {
    refundApprovalModule(filter: $filter, pagination: $pagination) {
      lastKey
      filteredData {
        reference
        accountNumber
        channelName
        timestamp
        postedTimestamp
        amountValue
        status
        refundAmount
        refundReason
        refundApprovalStatus
        finalAmount
      }
    }
  }
`;

export const GET_GCASHREFUNDHISTORY = gql`
  query getRefundApprovalModuleHistory(
    $filter: SearchGcashRefundInput!
    $pagination: PaginationReportInput!
  ) {
    refundApprovalModuleHistory(filter: $filter, pagination: $pagination) {
      lastKey
      filteredData {
        reference
        accountNumber
        channelName
        timestamp
        postedTimestamp
        amountValue
        status
        refundAmount
        refundReason
        refundApprovalStatus
        refundRejectedTimestamp
      }
    }
  }
`;

export const GET_CHANNEL_OPTIONS = gql`
  query getChannelOptions {
    channelsLoose {
      id
      name
      channelId
    }
  }
`;

export const GET_REFUND_REASON = gql`
  query getRefundReasons {
    refundReason {
      reason
    }
  }
`;

export const REPORT_PATH = 'refundApprovalModule';
