import format from 'date-fns/format';
import addDays from 'date-fns/add_days';
import isWithinRage from 'date-fns/is_within_range';
import isBefore from 'date-fns/is_before';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import PropTypes from 'prop-types';
import * as Yup from 'yup';
import React, { useState, useEffect } from 'react';
import PrimaryButton from '../../../components/Button/PrimaryButton';
import SecondaryButton from '../../../components/Button/SecondaryButton';
import DataContainer from '../../../components/DataContainer';
import DataTable from '../../../components/DataTable';
import { FIELD_TYPES } from '../../../components/Form/constants';
import GlobalSearch from '../../../components/GlobalSearch';
import Header from '../../../components/Header';
import { AlertModal, FormModal } from '../../../components/Modal';
import { AlertModalIcon, RequestButtonContainer } from './../styled';
import Page from '../../../components/Page';
import { useMutation } from '@apollo/client';
import sanitize from '../../../utils/sanitize';
import { useQuery } from '@apollo/client';
import { REQUEST_REFUND } from './mutation';
import useQueryReportSeries from '../../../hooks/useQueryReportSeries';
import { ColumnVisibilityDropdown, ResponsiveRow } from '../../Reports/styled';
import {
  GET_GCASHREFUNDREQUEST,
  REPORT_PATH,
  GET_CHANNEL_OPTIONS,
  GET_REFUND_REASON,
} from './query';
import { numberWithCommas } from '../../../components/GlobalSearch/utils';

const GCashRefundRequest = ({ history }) => {
  const [state, setState] = useState({
    isRequestRefundModalOpen: false,
    isConfirmRequestRefundModalOpen: false,
    isSuccesRequestRefundOpen: false,
    isFailureRequestRefundOpen: false,
    isAboutToExcceed180: false,
    isExceed180: false,

    days: 0,

    referenceValue: null,
    amountPaid: 0,

    isLeavingPageWhileAdding: false,

    selectedRequestRefund: null,
    nextLocation: null,
    requestRefundError: null,
  });

  const {
    pagination,
    setNewPagination,
    setFilter,
    data,
    loading,
    page,
    setPage,
    isLastPage,
    refetch,
    loadData,
    loadDataOnError,
  } = useQueryReportSeries(GET_GCASHREFUNDREQUEST, REPORT_PATH, {
    pagination: {
      startKeys: '',
      limit: 10,
    },
  });

  const { data: channelData, loading: isLoadingChannels } = useQuery(
    GET_CHANNEL_OPTIONS,
    {
      fetchPolicy: 'network-only',
    }
  );

  const { data: refundReasonData, loading: refundReasonLoading } = useQuery(
    GET_REFUND_REASON,
    {
      fetchPolicy: 'network-only',
    }
  );

  useEffect(() => {
    if (isRequestingRefund) {
      loadData();
    }
  });

  const channelOptions =
    !isLoadingChannels && channelData && channelData.channelsLoose !== undefined
      ? channelData.channelsLoose.map(channel => ({
          value: channel.id,
          label: channel.name,
        }))
      : [];

  channelOptions.unshift({
    value: null,
    label: 'Any',
  });

  const refundReasonOptions =
    !refundReasonLoading &&
    refundReasonData &&
    refundReasonData.refundReason !== undefined
      ? refundReasonData.refundReason.map(reason => ({
          value: reason.reason,
          label: reason.reason,
        }))
      : [];

  refundReasonOptions.unshift({
    value: null,
    label: '-Select-',
  });

  refundReasonOptions.push({
    value: 'Others',
    label: 'Others',
  });

  useEffect(() => {
    const unblock = history.block(location => {
      if (state.isLeavingPageWhileAdding || !state.isRequestRefundModalOpen) {
        return true;
      }
      setState({
        ...state,
        nextLocation: location,
        isLeavingPageWhileAdding: true,
      });
      return false;
    });

    return () => {
      unblock();
    };
  }, [state.isLeavingPageWhileAdding, state.isRequestRefundModalOpen]);

  const [requestRefund, { loading: isRequestingRefund }] = useMutation(
    REQUEST_REFUND,
    {
      onCompleted: () => {
        setState({
          ...state,
          isConfirmRequestRefundModalOpen: false,
          isSuccesRequestRefundOpen: true,
          isRequestRefundModalOpen: false,
        });
        refetch();
      },
      onError: err => {
        setState({
          ...state,
          requestRefundError: err.networkError.result
            ? err.networkError.result.message
            : null,
          isConfirmRequestRefundModalOpen: false,
          isFailureRequestRefundOpen: true,
        });
        loadDataOnError();
      },
    }
  );

  const tableConfig = {
    reference: {
      headerLabel: 'Reference No.',
      sortable: true,
    },
    accountNumber: {
      headerLabel: 'Account No.',
      sortable: true,
    },
    channelName: {
      headerLabel: 'Channel',
      sortable: true,
    },
    timestamp: {
      headerLabel: 'Transaction Date',
      sortable: true,
      renderAs: data => format(data.timestamp, 'MM/DD/YYYY - hh:mm:ss A'),
    },
    postedTimestamp: {
      headerLabel: 'Date Posted/Authorised',
      sortable: true,
      renderAs: data => format(data.postedTimestamp, 'MM/DD/YYYY - hh:mm:ss A'),
    },
    amountValue: {
      headerLabel: 'Amount Paid',
      sortable: true,
      renderAs: data =>
        numberWithCommas(
          !data.finalAmount ? data.amountValue : data.finalAmount,
          2
        ),
    },
    status: {
      headerLabel: 'Payment Status',
      sortable: true,
    },
    refundStatus: {
      headerLabel: 'Refund Status',
      sortable: true,
    },
    refundAmount: {
      headerLabel: 'Refund Amount',
      sortable: true,
      renderAs: data =>
        data.refundAmount === null
          ? ''
          : numberWithCommas(data.refundAmount, 2),
    },
    refundReason: {
      headerLabel: 'Refund Reason',
      sortable: true,
    },
    refundType: {
      headerLabel: 'Refund Type',
      sortable: true,
    },
    refundApprovalStatus: {
      renderAs: data => (
        <RequestButtonContainer>
          <PrimaryButton
            onClick={() =>
              setState({
                referenceValue: data.reference,
                amountPaid: !data.finalAmount
                  ? data.amountValue
                  : data.finalAmount,
                isRequestRefundModalOpen: isBefore(
                  format(new Date()),
                  format(addDays(data.timestamp, 175))
                )
                  ? true
                  : false,
                isAboutToExcceed180: isWithinRage(
                  format(new Date()),
                  format(addDays(data.timestamp, 175)),
                  format(addDays(data.timestamp, 176))
                )
                  ? true
                  : false ||
                      isWithinRage(
                        format(new Date()),
                        format(addDays(data.timestamp, 176)),
                        format(addDays(data.timestamp, 177))
                      )
                    ? true
                    : false ||
                        isWithinRage(
                          format(new Date()),
                          format(addDays(data.timestamp, 177)),
                          format(addDays(data.timestamp, 178))
                        )
                      ? true
                      : false ||
                          isWithinRage(
                            format(new Date()),
                            format(addDays(data.timestamp, 178)),
                            format(addDays(data.timestamp, 179))
                          )
                        ? true
                        : false ||
                            isWithinRage(
                              format(new Date()),
                              format(addDays(data.timestamp, 179)),
                              format(addDays(data.timestamp, 180))
                            )
                          ? true
                          : false,
                days: isWithinRage(
                  format(new Date()),
                  format(addDays(data.timestamp, 175)),
                  format(addDays(data.timestamp, 176))
                )
                  ? 175
                  : 0 ||
                      isWithinRage(
                        format(new Date()),
                        format(addDays(data.timestamp, 176)),
                        format(addDays(data.timestamp, 177))
                      )
                    ? 176
                    : 0 ||
                        isWithinRage(
                          format(new Date()),
                          format(addDays(data.timestamp, 177)),
                          format(addDays(data.timestamp, 178))
                        )
                      ? 177
                      : 0 ||
                          isWithinRage(
                            format(new Date()),
                            format(addDays(data.timestamp, 178)),
                            format(addDays(data.timestamp, 179))
                          )
                        ? 178
                        : 0 ||
                            isWithinRage(
                              format(new Date()),
                              format(addDays(data.timestamp, 179)),
                              format(addDays(data.timestamp, 180))
                            )
                          ? 179
                          : 0,
              })
            }
            disabled={
              data.refundApprovalStatus === 'For Approval' ||
              data.refundApprovalStatus === 'Approved' ||
              !data.isRefundable
            }
          >
            {(data.refundApprovalStatus === 'For Approval' && 'For Approval') ||
              (data.refundApprovalStatus === 'Approved' && 'Approved') ||
              'Request Refund'}
          </PrimaryButton>
          {(!data.isRefundable && data.refundApprovalStatus === null) ||
          (!data.isRefundable && data.refundApprovalStatus === 'Rejected') ? (
            <SecondaryButton
              onClick={() => {
                setState({
                  ...state,
                  isExceed180: true,
                });
              }}
            >
              <FontAwesomeIcon icon={'exclamation-circle'} color={'#FF9933'} />
            </SecondaryButton>
          ) : (
            ''
          )}
        </RequestButtonContainer>
      ),
    },
  };

  const [visibleColumns, setVisibleColumns] = useState(
    Object.keys(tableConfig)
  );

  return (
    <>
      <Page>
        <Header
          withHome
          title="GCash Refund Request"
          path={['GCash Refund', 'GCash Refund Request']}
        />
        <DataContainer>
          <DataTable
            loading={loading}
            data={data}
            minCellWidth={200}
            headerOptions={
              <>
                <GlobalSearch
                  onSearch={filter => {
                    const newPagination = {
                      ...pagination,
                      startKeys: '',
                    };
                    setFilter(filter);
                    setNewPagination(newPagination);
                  }}
                  fields={[
                    {
                      label: 'Reference No.',
                      name: 'reference',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Channel',
                      name: 'channelId',
                      type: FIELD_TYPES.SELECT,
                      options: channelOptions,
                      isKey: true,
                    },
                    {
                      label: 'Payment Status',
                      name: 'status',
                      type: FIELD_TYPES.SELECT,
                      options: [
                        { value: null, label: 'Any' },
                        { value: 'POSTED', label: 'PAYMENT_POSTED' },
                        { value: 'POSTED_LUKE', label: 'PAYMENT_POSTED_LUKE' },
                        {
                          value: 'ADYEN_AUTHORISED',
                          label: 'PAYMENT_AUTHORIZED',
                        },
                        {
                          value: 'GCASH_AUTHORISED',
                          label: 'GCASH_AUTHORISED',
                        },
                      ],
                    },
                    {
                      label: 'Date Range',
                      name: 'createdAt',
                      type: FIELD_TYPES.DATE_RANGE,
                    },
                  ]}
                  placeholder="Search entries here..."
                />
                <ResponsiveRow>
                  <ColumnVisibilityDropdown
                    multi
                    showMulti={false}
                    placeholder="Visible Columns"
                    value={visibleColumns}
                    options={Object.keys(tableConfig).map(key => ({
                      value: key,
                      label: tableConfig[key].headerLabel,
                    }))}
                    onChange={setVisibleColumns}
                  />
                </ResponsiveRow>
              </>
            }
            config={Object.keys(tableConfig).reduce((config, key) => {
              if (visibleColumns.includes(key)) {
                config[key] = tableConfig[key];
              }
              return config;
            }, {})}
            pagination={{
              ...pagination,
              startKeys: pagination.startKeys,
              withStartKeys: true,
              count: 0,
              cursors: [],
              handleChange: setNewPagination,
            }}
            series={{ page, setPage, isLastPage }}
          />
        </DataContainer>
      </Page>
      {state.isRequestRefundModalOpen && (
        <FormModal
          isOpen={state.isRequestRefundModalOpen}
          width="450px"
          handleClose={() =>
            setState({
              ...state,
              isLeavingPageWhileAdding: true,
              isRequestRefundModalOpen: false,
            })
          }
          title="REFUND CONFIRMATION"
          instructions={
            <span>
              <center>
                <AlertModalIcon icon="exclamation-circle" variant="warn" />
              </center>
              <b style={{ fontSize: 20 }}>
                <center>ARE YOU SURE?</center>
              </b>
              <center style={{ fontSize: 17 }}>
                You are about to submit a transaction.
              </center>
              <b style={{ fontSize: 15 }}>
                <br />
                <center>
                  Paid Amount: {numberWithCommas(state.amountPaid, 2)}
                </center>
              </b>
            </span>
          }
          submitText="Yes"
          changeListener={(name, data, values, setValues) => {
            if (name === 'channelName') {
              let { reference, refundAmount, refundReason } = values;

              setValues({
                ...values,
                reference,
                refundAmount,
                refundReason,
              });
            }
          }}
          handleSubmit={values => {
            setState({
              ...state,
              selectedRequestRefund: {
                reference: values.reference,
                refundAmount: values.refundAmount,
                refundReason:
                  values.refundReason === 'Others'
                    ? values.others
                    : values.refundReason,
              },
              isConfirmRequestRefundModalOpen: true,
            });
          }}
          fields={{
            reference: {
              initialValue: state.referenceValue,
            },
            refundAmount: {
              type: FIELD_TYPES.TEXT,
              label: 'Refund Amount',
              placeholder: 'Refund Amount',
              validation: Yup.string()
                .matches(
                  /^\d*(\.\d+)?$/,
                  'Invalid input! Allowed characters are positive number or number with decimal placed only'
                )
                .required('Please enter a value')
                .test(
                  'AmountValidation',
                  'Value should not be less than 0 and not greater than the amount paid',
                  value => {
                    const amount = state.amountPaid;
                    if (value > Number(amount)) {
                      return false;
                    }
                    return true;
                  }
                ),
              required: true,
              perRow: 1.1,
            },
            refundReason: {
              type: FIELD_TYPES.SELECT,
              label: 'Refund Reason',
              placeholder: 'Refund Reason',
              options: refundReasonOptions,
              validation: Yup.string()
                .nullable()
                .required('Please select a value'),
              required: true,
              perRow: 1.1,
            },
            others: {
              typeWhen: data =>
                data.refundReason ===
                refundReasonOptions[refundReasonOptions.length - 1].value
                  ? FIELD_TYPES.TEXT
                  : '',
              labelWhen: data =>
                data.refundReason ===
                refundReasonOptions[refundReasonOptions.length - 1].value
                  ? 'Others'
                  : '',
              placeholder: '',
              disableWhen: data =>
                !(
                  data.refundReason ===
                  refundReasonOptions[refundReasonOptions.length - 1].value
                ),
              requiredWhen: data =>
                data.refundReason ===
                refundReasonOptions[refundReasonOptions.length - 1].value,
              validation: data =>
                data.refundReason ===
                refundReasonOptions[refundReasonOptions.length - 1].value
                  ? Yup.string()
                      .min(3, 'Minimum should be 3 characters')
                      .max(50, 'Must not exceed 50 characters')
                      .required('Please enter value')
                  : Yup.string().nullable(),
              perRow: 1.1,
              initialValue: '',
            },
          }}
        />
      )}

      <AlertModal
        isOpen={state.isConfirmRequestRefundModalOpen}
        title="Request Refund Alert"
        icon="exclamation-circle"
        variant="warn"
        header="ARE YOU SURE?"
        subHeader="You are about to Request a Refund."
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        confirmLoading={isRequestingRefund}
        confirmText="Yes"
        handleConfirm={() => {
          requestRefund({
            variables: {
              data: sanitize(state.selectedRequestRefund),
            },
          });
        }}
        handleClose={() => {
          setState({
            ...state,
            isConfirmRequestRefundModalOpen: false,
          });
        }}
      />

      <AlertModal
        isOpen={state.isSuccesRequestRefundOpen}
        title="Request Refund Success"
        handleClose={() => {
          setState({ ...state, isSuccesRequestRefundOpen: false });
        }}
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader="Refund has been requested successfully."
        description=""
        confirmText="Go to Refund Request Module"
        handleConfirm={() => {
          setState({ ...state, isSuccesRequestRefundOpen: false });
        }}
      />

      <AlertModal
        isOpen={state.isAboutToExcceed180}
        title="Gcash Notification"
        handleClose={() => {
          setState({ ...state, isAboutToExcceed180: false });
        }}
        icon="exclamation-circle"
        variant="warn"
        header="Gcash Warning!"
        subHeader={`You only have ${180 - state.days} day(s) to process the refund. Please process immediately .`}
        description=""
        confirmText="Okay"
        handleConfirm={() => {
          setState({
            ...state,
            isAboutToExcceed180: false,
            isRequestRefundModalOpen: true,
          });
        }}
      />

      <AlertModal
        isOpen={state.isExceed180}
        title="Gcash Notification"
        handleClose={() => {
          setState({ ...state, isExceed180: false });
        }}
        icon="exclamation-circle"
        variant="warn"
        header="Warning!"
        subHeader={
          'Unable to request refund. The transaction was beyond processing period.'
        }
        description=""
        confirmText="Go to Refund Request Module"
        handleConfirm={() => {
          setState({
            ...state,
            isExceed180: false,
          });
        }}
      />

      <AlertModal
        isOpen={state.isFailureRequestRefundOpen}
        title="Request Refund Alert"
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader={
          (state.requestRefundError === 'Request for refund already filed' &&
            'Request for refund already filed.') ||
          (state.requestRefundError === 'Request for refund already approved' &&
            'Request for refund already approved') ||
          (state.requestRefundError ===
            'Refund Amount is greater than Payment amount' &&
            'Refund Amount is greater than Payment amount') ||
          state.requestRefundError === ''
        }
        description={
          (state.requestRefundError === 'Request for refund already filed' &&
            'Request for refund already filed.') ||
          (state.requestRefundError === 'Request for refund already approved' &&
            'Request for refund already approved')
            ? 'Please refresh the page to see updated results.'
            : 'Please go back and try requesting it again.'
        }
        handleClose={() =>
          setState({ ...state, isFailureRequestRefundOpen: false })
        }
        confirmText="Go Back"
        handleConfirm={() => {
          setState({ ...state, isFailureRequestRefundOpen: false });
        }}
      />
    </>
  );
};

GCashRefundRequest.propTypes = {
  history: PropTypes.object,
};

export default GCashRefundRequest;
