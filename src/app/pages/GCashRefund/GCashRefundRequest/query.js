import { gql } from '@apollo/client';

export const GET_GCASHREFUNDREQUEST = gql`
  query getRefundRequestModule(
    $filter: SearchGcashRefundInput!
    $pagination: PaginationReportInput!
  ) {
    refundRequestModule(filter: $filter, pagination: $pagination) {
      lastKey
      filteredData {
        reference
        accountNumber
        channelName
        timestamp
        postedTimestamp
        amountValue
        status
        refundAmount
        refundReason
        refundApprovalStatus
        refundStatus
        refundType
        isRefundable
        finalAmount
      }
    }
  }
`;

export const GET_CHANNEL_OPTIONS = gql`
  query getChannelOptions {
    channelsLoose {
      id
      name
      channelId
    }
  }
`;

export const GET_REFUND_REASON = gql`
  query getRefundReasons {
    refundReason {
      reason
    }
  }
`;

export const REPORT_PATH = 'refundRequestModule';
