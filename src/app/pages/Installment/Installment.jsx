import React, { useState, useContext } from 'react';
import * as Yup from 'yup';
import sanitize from '../../utils/sanitize';
import { FIELD_TYPES } from '../../components/Form/constants';
import Page from '../../components/Page';
import Header from '../../components/Header';
import { Required } from '../../components/Form/FormField';
import AlertModal from '../../components/Modal/AlertModal';
import DataContainer from '../../components/DataContainer';
import DataTable from '../../components/DataTable';
import GlobalSearch from '../../components/GlobalSearch';
import ActionButtons from '../../components/ActionButtons';
import AuthContext from '../../context/AuthContext/AuthContext';
import { GET_INSTALLMENT, GET_BANKLIST } from './query';
import CreateButton from '../../components/Button/CreateButton';
import { FormModal } from '../../components/Modal';
import { useMutation } from '@apollo/client';
import { useQuery } from '@apollo/client';
import { json2csv } from 'json-2-csv';
import {
  ADD_INSTALLMENT,
  DELETE_INSTALLMENT,
  EXPORT_INSTALLMENT,
} from './mutation';
import { ExportButton } from '../../components/Button/ExportButton';

export const TERMS = [
  { value: null, label: 'Any' },
  { value: 3, label: '3' },
  { value: 6, label: '6' },
  { value: 9, label: '9' },
  { value: 12, label: '12' },
  { value: 18, label: '18' },
  { value: 24, label: '24' },
  { value: 36, label: '36' },
];

const Installment = () => {
  const { permissions } = useContext(AuthContext);

  const [state, setState] = useState({
    isAddInstallmentModalOpen: false,
    isConfirmAddInstallmentModalOpen: false,
    isSuccessAddInstallmentModalOpen: false,
    isFailureAddInstallmentModalOpen: false,

    isLeavingPageWhileAdding: false,

    isLeavingPageWhileEditing: false,

    isConfirmDeleteInstallmentModalOpen: false,
    isSuccessDeleteInstallmentModalOpen: false,
    isFailureDeleteInstallmentModalOpen: false,

    isConfirmDeleteInstallmentsModalOpen: false,
    isSuccessDeleteInstallmentsModalOpen: false,

    filter: {},
    pagination: {
      limit: 10,
      start: '',
    },

    selectedInstallment: null,
    selectedInstallmentInitial: null,

    addInstallmentError: null,
    editInstallmentError: null,
    deleteErrorMessage: null,
  });

  const { data, loading, refetch } = useQuery(GET_INSTALLMENT, {
    variables: {
      pagination: state.pagination,
      filter: state.filter,
    },
    fetchPolicy: 'network-only',
  });

  const { data: installmentData, refetch: refetchInstallment } = useQuery(
    GET_INSTALLMENT,
    {
      variables: {
        pagination: {
          limit: 1000,
          start: '',
        },
        filter: {},
      },
      fetchPolicy: 'network-only',
    }
  );

  const {
    data: bankList,
    loading: isLoadingBanks,
    refetch: refetchBank,
  } = useQuery(GET_BANKLIST, {
    fetchPolicy: 'network-only',
  });

  const bankOptionsFilter =
    !isLoadingBanks && bankList
      ? bankList.listInstallmentBank.map(bank => ({
          value: bank,
          label: bank,
        }))
      : [];

  bankOptionsFilter.unshift({
    value: null,
    label: 'Any',
  });

  const bankOptions =
    !isLoadingBanks && bankList
      ? bankList.listInstallmentBank.map(bank => ({
          value: bank,
          label: bank,
        }))
      : [];

  bankOptions.unshift({
    value: null,
    label: 'None',
  });

  bankOptions.push({
    value: 'Others',
    label: 'Others',
  });

  const [addInstallment, { loading: isAddingInstallment }] = useMutation(
    ADD_INSTALLMENT,
    {
      onCompleted: () => {
        setState({
          ...state,
          isConfirmAddInstallmentModalOpen: false,
          isSuccessAddInstallmentModalOpen: true,
          isAddInstallmentModalOpen: false,
          pagination: {
            ...state.pagination,
            start: '',
          },
        });
        refetch();
        refetchBank();
        refetchInstallment();
      },
      onError: err => {
        setState({
          ...state,
          addInstallmentError: err.networkError.result
            ? err.networkError.result.message
            : null,
          isConfirmAddInstallmentModalOpen: false,
          isFailureAddInstallmentModalOpen: true,
        });
      },
    }
  );

  const [deleteInstallment, { loading: isDeletingInstallment }] = useMutation(
    DELETE_INSTALLMENT,
    {
      onCompleted: () => {
        setState({
          ...state,
          isConfirmDeleteInstallmentModalOpen: false,
          isSuccessDeleteInstallmentModalOpen: true,
          selectedBank: null,
        });
        refetch();
        refetchBank();
        refetchInstallment();
      },
      onError: err => {
        setState({
          ...state,
          isConfirmDeleteInstallmentModalOpen: false,
          isFailureDeleteInstallmentModalOpen: true,
          deleteErrorMessage: err.message,
        });
      },
    }
  );

  const [exportInstallment] = useMutation(EXPORT_INSTALLMENT, {
    onCompleted: async data => {
      if (
        data &&
        data.downloadInstallmentMid &&
        data.downloadInstallmentMid.filteredData
      ) {
        const keys = Object.keys(
          data.downloadInstallmentMid.filteredData[0]
        ).filter(key => key !== '__typename');

        const csv = await json2csv(data.downloadInstallmentMid.filteredData, {
          keys,
        });

        const fileData = {
          mime: 'text/csv',
          filename: 'Installment-Mid.csv',
          contents: csv,
        };
        const blob = new Blob([fileData.contents], {
          type: fileData.mime,
        });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        document.body.appendChild(a);
        a.download = fileData.filename;
        a.href = url;
        a.click();
        document.body.removeChild(a);
      }
    },
  });

  const [isConfirmDownloadModalOpen, setIsConfirmDownloadModalOpen] =
    useState(false);

  return (
    <>
      <Page>
        <Header withHome title="Installment" path={['Installment']} />
        <DataContainer>
          <DataTable
            loading={loading}
            data={
              data && data.searchInstallmentMid
                ? data.searchInstallmentMid.filteredData
                : []
            }
            headerOptions={
              <>
                <GlobalSearch
                  onSearch={filter => {
                    setState({
                      ...state,
                      filter,
                      pagination: { ...state.pagination, start: '' },
                    });
                  }}
                  fields={[
                    {
                      label: 'Bank',
                      name: 'bank',
                      type: FIELD_TYPES.SELECT,
                      options: bankOptionsFilter,
                    },
                    {
                      label: 'Term',
                      name: 'term',
                      type: FIELD_TYPES.SELECT,
                      options: TERMS,
                    },
                    {
                      label: 'Payment Id',
                      name: 'paymentId',
                      type: FIELD_TYPES.TEXT,
                    },
                  ]}
                />

                <div style={{ display: 'flex' }}>
                  {permissions.InstallmentMid.export && (
                    <ExportButton
                      icon="file-csv"
                      iconPosition="left"
                      disabled={loading}
                      onClick={() =>
                        setIsConfirmDownloadModalOpen({
                          ...state,
                          isConfirmDownloadModalOpen: true,
                        })
                      }
                    >
                      CSV
                    </ExportButton>
                  )}

                  {permissions.InstallmentMid.create && (
                    <CreateButton
                      icon="plus"
                      onClick={() => {
                        setState({ ...state, isAddInstallmentModalOpen: true });
                      }}
                    >
                      Add Installment
                    </CreateButton>
                  )}
                </div>
              </>
            }
            config={{
              bank: {
                headerLabel: 'Bank',
                sortable: true,
              },
              term: {
                headerLabel: 'Term',
                sortable: true,
                renderAs: data => `${data.term} Months`,
              },
              paymentId: {
                headerLabel: 'Payment ID',
                sortable: true,
              },
              actions: {
                renderAs: data => (
                  <ActionButtons
                    disabled={{
                      delete: !permissions.InstallmentMid.delete,
                      edit: !permissions.InstallmentMid.update,
                    }}
                    handleDelete={() => {
                      setState({
                        ...state,
                        selectedInstallment: data,
                        isConfirmDeleteInstallmentModalOpen: true,
                      });
                    }}
                  />
                ),
              },
            }}
            pagination={{
              ...state.pagination,
              count:
                data && data.searchInstallmentMid
                  ? data.searchInstallmentMid.count
                  : 0,
              cursors:
                data && data.searchInstallmentMid
                  ? data.searchInstallmentMid.cursors
                  : [''],
              handleChange: pagination => {
                setState({ ...state, pagination });
              },
            }}
          />
        </DataContainer>
      </Page>

      {state.isAddInstallmentModalOpen && (
        <FormModal
          isOpen={state.isAddInstallmentModalOpen}
          width="600px"
          handleClose={() =>
            setState({ ...state, isLeavingPageWhileAdding: true })
          }
          title="Add New Installment"
          instructions={
            <span>
              To create new Installment, please fill out the required
              <Required>*</Required> fields.
            </span>
          }
          submitText="Create Installment"
          changeListener={(name, data, values, setValues) => {
            if (name === 'bank') {
              let { term } = values;

              if (data === bankOptions[1].value) {
                term = null;
              } else if (data === bankOptions[2]) {
                term = null;
              } else if (data === bankOptions[3]) {
                term = null;
              } else if (data === bankOptions[4]) {
                term = null;
              } else if (data === bankOptions[5]) {
                term = null;
              } else if (data === bankOptions[6]) {
                term = null;
              } else {
                term = null;
              }

              setValues({
                ...values,
                bank: data,
                term,
              });
            }
          }}
          handleSubmit={values => {
            setState({
              ...state,
              selectedInstallment: {
                bank: values.bank === 'Others' ? values.others : values.bank,
                term: values.term,
                paymentId: Number(values.paymentId),
              },
              isConfirmAddInstallmentModalOpen: true,
            });
          }}
          fields={{
            bank: {
              type: FIELD_TYPES.SELECT,
              label: 'Bank',
              placeholder: 'Bank',
              validation: dataValue =>
                Yup.string()
                  .nullable()
                  .required('Please select a value')
                  .test(
                    'Bank Validation',
                    `The bank already exist with the current term.`,
                    value => {
                      const exist =
                        installmentData &&
                        installmentData.searchInstallmentMid &&
                        installmentData.searchInstallmentMid.filteredData.find(
                          data =>
                            value === data.bank && data.term === dataValue.term
                        );

                      if (exist) {
                        return false;
                      }
                      return true;
                    }
                  ),
              options: bankOptions,
              required: true,
              initialValue: null,
            },
            others: {
              typeWhen: data =>
                data.bank === bankOptions[bankOptions.length - 1].value
                  ? FIELD_TYPES.TEXT
                  : '',
              labelWhen: data =>
                data.bank === bankOptions[bankOptions.length - 1].value
                  ? 'Others'
                  : '',
              placeholder: '',
              disableWhen: data =>
                !(data.bank === bankOptions[bankOptions.length - 1].value),
              requiredWhen: data =>
                data.bank === bankOptions[bankOptions.length - 1].value,
              validation: data =>
                data.bank === bankOptions[bankOptions.length - 1].value
                  ? Yup.string()
                      .matches(
                        /^[A-Z]+(?:_[A-Z]+)*$/,
                        'Must be capital letters with underscore only'
                      )
                      .min(2, 'Minimum should be 2 characters')
                      .max(20, 'Must not exceed 20 characters')
                      .required('Please enter value')
                  : Yup.string().nullable(),
              initialValue: '',
            },
            term: {
              type: FIELD_TYPES.SELECT,
              label: 'Term',
              placeholder: 'Term',
              disableWhen: dataValue =>
                !bankOptions.find(bankData => bankData.value === dataValue.bank)
                  .value,
              validation: dataValue =>
                bankOptions.find(bankData => bankData.value === dataValue.bank)
                  .value &&
                Yup.string()
                  .nullable()
                  .required('Please select a value')
                  .test(
                    'Term Validation',
                    'This term already exist on bank',
                    value => {
                      const exist =
                        installmentData &&
                        installmentData.searchInstallmentMid &&
                        installmentData.searchInstallmentMid.filteredData.find(
                          data =>
                            value === data.term && data.bank === dataValue.bank
                        );
                      if (exist) {
                        return false;
                      }
                      return true;
                    }
                  ),
              requireWhen: dataValue =>
                bankOptions.find(bankData => bankData.value === dataValue.bank)
                  .value,
              options: [
                { value: 3, label: '3' },
                { value: 6, label: '6' },
                { value: 9, label: '9' },
                { value: 12, label: '12' },
                { value: 18, label: '18' },
                { value: 24, label: '24' },
                { value: 36, label: '36' },
              ],
              required: true,
              initialValue: null,
            },
            paymentId: {
              type: FIELD_TYPES.TEXT,
              label: 'Payment Id',
              placeholder: 'Payment Id',
              validation: Yup.string()
                .required('Please enter value')
                .matches(/^[0-9]/, 'Must be digits only')
                .max(3, 'Must not exceed 3 digits')
                .min(1, 'Minimum should be 1 digit'),
              required: true,
              initialValue: '',
            },
          }}
        />
      )}

      <AlertModal
        isOpen={state.isLeavingPageWhileEditing}
        title="Edit Installment Alert"
        icon="question-circle"
        variant="warn"
        header="SAVE INSTALLMENT?"
        subHeader="You are about to leave without saving Modified Installment."
        description="Your entry will be lost if you don't save it"
        handleClose={() =>
          setState({ ...state, isLeavingPageWhileEditing: false })
        }
        cancelText="Discard Entry"
        confirmText="Go Back"
        handleCancel={() => {
          setState({
            ...state,
            isLeavingPageWhileEditing: false,
          });
        }}
        handleConfirm={() => {
          setState({ ...state, isLeavingPageWhileEditing: false });
        }}
      />

      <AlertModal
        isOpen={state.isLeavingPageWhileAdding}
        title="New Installment Alert"
        icon="question-circle"
        variant="warn"
        header="SAVE INSTALLMENT?"
        subHeader="You are about to leave without saving New Installment."
        description="Your entry will be lost if you don't save it"
        handleClose={() =>
          setState({ ...state, isLeavingPageWhileAdding: false })
        }
        cancelText="Discard Entry"
        confirmText="Go Back"
        handleCancel={() => {
          setState({
            ...state,
            isLeavingPageWhileAdding: false,
            isAddInstallmentModalOpen: false,
          });
        }}
        handleConfirm={() => {
          setState({ ...state, isLeavingPageWhileAdding: false });
        }}
      />

      <AlertModal
        isOpen={state.isConfirmAddInstallmentModalOpen}
        title="New Installment Alert"
        icon="exclamation-circle"
        variant="warn"
        header="ARE YOU SURE?"
        subHeader={'You are about to create a new Installment.'}
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        confirmLoading={isAddingInstallment}
        confirmText="Yes"
        handleConfirm={() => {
          addInstallment({
            variables: { data: sanitize(state.selectedInstallment) },
          });
        }}
        handleClose={() => {
          setState({
            ...state,
            isConfirmAddInstallmentModalOpen: false,
          });
        }}
      />

      <AlertModal
        isOpen={state.isSuccessAddInstallmentModalOpen}
        title="New Installment Alert"
        handleClose={() => {
          setState({ ...state, isSuccessAddInstallmentModalOpen: false });
        }}
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader="Installment has been created successfully."
        description="Editing of Installment is now enabled."
        confirmText="Go to All Installment"
        handleConfirm={() => {
          setState({ ...state, isSuccessAddInstallmentModalOpen: false });
        }}
      />

      <AlertModal
        isOpen={state.isFailureAddInstallmentModalOpen}
        title="New Installment Alert"
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader={
          state.addInstallmentError === 'INSTALLMENT_DETAILS_ALREADY_EXIST'
            ? 'Current Installment Details already exists.'
            : 'There was a problem on saving New Installment.'
        }
        description="Please go back and try saving it again."
        handleClose={() =>
          setState({ ...state, isFailureAddInstallmentModalOpen: false })
        }
        confirmText="Go Back"
        handleConfirm={() => {
          setState({ ...state, isFailureAddInstallmentModalOpen: false });
        }}
      />

      <AlertModal
        isOpen={state.isLeavingPageWhileEditing}
        title="Edit Installment Alert"
        icon="question-circle"
        variant="warn"
        header="SAVE INSTALLMENT?"
        subHeader="You are about to leave without saving Modified Installment."
        description="Your entry will be lost if you don't save it"
        handleClose={() =>
          setState({ ...state, isLeavingPageWhileEditing: false })
        }
        cancelText="Discard Entry"
        confirmText="Go Back"
        handleCancel={() => {
          setState({
            ...state,
            isLeavingPageWhileEditing: false,
          });
        }}
        handleConfirm={() => {
          setState({ ...state, isLeavingPageWhileEditing: false });
        }}
      />

      <AlertModal
        isOpen={state.isConfirmDeleteInstallmentModalOpen}
        title="Delete Installment Alert"
        icon="exclamation-circle"
        variant="warn"
        header="ARE YOU SURE?"
        subHeader="You are about to delete a Installment."
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() =>
          setState({ ...state, isConfirmDeleteInstallmentModalOpen: false })
        }
        selectLabel="Reason"
        options={['No longer in use', 'Others'].map(reason => ({
          value: reason,
          label: reason,
        }))}
        confirmLoading={isDeletingInstallment}
        confirmText="Yes"
        handleConfirm={value => {
          deleteInstallment({
            variables: {
              data: { reasonToDelete: value },
              where: {
                bank: state.selectedInstallment.bank,
                term: state.selectedInstallment.term,
              },
            },
          });
        }}
      />

      <AlertModal
        isOpen={state.isSuccessDeleteInstallmentModalOpen}
        title="Delete Installment Alert"
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader="Installment has been deleted successfully."
        description="Deleted Installment is now no longer in use."
        handleClose={() =>
          setState({ ...state, isSuccessDeleteInstallmentModalOpen: false })
        }
        confirmText="Go to All Installment"
        handleConfirm={() =>
          setState({ ...state, isSuccessDeleteInstallmentModalOpen: false })
        }
      />

      <AlertModal
        isOpen={state.isFailureDeleteInstallmentModalOpen}
        title="Delete Installment Alert"
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader={
          state.deleteErrorMessage
            ? `${state.selectedInstallment.bank} with term ${state.selectedInstallment.term} is inused in MID Management`
            : 'There was a problem on deleting Installment.'
        }
        description="Please go back and try deleting again."
        handleClose={() =>
          setState({ ...state, isFailureDeleteInstallmentModalOpen: false })
        }
        confirmText="Go Back"
        handleConfirm={() => {
          setState({ ...state, isFailureDeleteInstallmentModalOpen: false });
        }}
      />

      <AlertModal
        isOpen={isConfirmDownloadModalOpen}
        title="Export Installment Alert"
        header="ARE YOU SURE?"
        variant="warn"
        icon="exclamation-circle"
        subHeader={
          <span>
            You are about to export all filtered records as .CSV File.
          </span>
        }
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() => setIsConfirmDownloadModalOpen(false)}
        handleCancel={() => setIsConfirmDownloadModalOpen(false)}
        confirmText="Yes"
        handleConfirm={async () => {
          setIsConfirmDownloadModalOpen(false);
          exportInstallment({
            variables: {
              pagination: {
                limit: 1000,
                start: '',
              },
              filter: state.filter,
            },
          });
        }}
      />
    </>
  );
};

export default Installment;
