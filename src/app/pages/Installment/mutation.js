import { gql } from '@apollo/client';

export const ADD_INSTALLMENT = gql`
  mutation createInstallment($data: CreateInstallmentMIdInput!) {
    createInstallmentMid(data: $data) {
      bank
      term
      paymentId
    }
  }
`;

export const DELETE_INSTALLMENT = gql`
  mutation deleteInstallmentMid(
    $data: DeleteInstallmentMIdInput!
    $where: InstallmentPrimary!
  ) {
    deleteInstallmentMid(data: $data, where: $where) {
      bank
      term
    }
  }
`;

export const EXPORT_INSTALLMENT = gql`
  mutation downloadInstallmentMid(
    $filter: InstallmentMerchantIdFilter!
    $pagination: PaginationInput!
  ) {
    downloadInstallmentMid(filter: $filter, pagination: $pagination) {
      cursors
      count
      filteredData {
        bank
        term
        paymentId
      }
    }
  }
`;
