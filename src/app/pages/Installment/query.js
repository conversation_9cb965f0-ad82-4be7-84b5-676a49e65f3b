import { gql } from '@apollo/client';

export const GET_INSTALLMENT = gql`
  query getInstallmentsMid(
    $filter: InstallmentMerchantIdFilter!
    $pagination: PaginationInput!
  ) {
    searchInstallmentMid(filter: $filter, pagination: $pagination) {
      cursors
      count
      filteredData {
        bank
        term
        paymentId
      }
    }
  }
`;

export const GET_BANKLIST = gql`
  query getBankList {
    listInstallmentBank
  }
`;
