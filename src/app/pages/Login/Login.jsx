import PropTypes from 'prop-types';
import React, { useContext, useState } from 'react';
import { Redirect } from 'react-router-dom';
import globeLogo from '../../assets/globe-logo.png';
import PrimaryButton from '../../components/Button/PrimaryButton';
import { AlertModal } from '../../components/Modal';
import Row from '../../components/Row';
import AuthContext from '../../context/AuthContext/AuthContext';
import ResponsiveContext from '../../context/ResponsiveContext';
import {
  Footer,
  GlobeLogo,
  LoginBanner,
  LoginForm,
  LoginPageContainer,
  LoginPageFooter,
  LoginPageInstructions,
  LoginPageTitle,
} from './styled';
import { useOktaAuth } from '@okta/okta-react';

function Login() {
  const { authUser } = useContext(AuthContext);
  const { isMobile } = useContext(ResponsiveContext);
  const { oktaAuth } = useOktaAuth();

  const [isLoggingIn, setIsLoggingIn] = useState(false);
  const [loginError, setLoginError] = useState(null);

  const oktaLogin = async () => {
    try {
      setIsLoggingIn(true);
      await oktaAuth.signInWithRedirect();
    } catch (err) {
      console.error('Okta login error:', err);
      setIsLoggingIn(false);
      setLoginError(
        err?.error_description || err?.message || 'An error occurred during Okta login.'
      );
    }
  };
  const handleLogin = () => {
    setIsLoggingIn(true);
    oktaLogin();
  };

  if (authUser) return <Redirect to="/" />;

  const loginBanner = (
    <LoginBanner>
      <div>
        <h2>Login to Payment Service to access the following features:</h2>
        <ul>
          <li>Manage User Accounts and Roles</li>
          <li>Manage Channels and Reports</li>
          <li>Be updated via Dashboard</li>
        </ul>
      </div>
    </LoginBanner>
  );

  const errorModal = (
    <AlertModal
      isOpen={!!loginError}
      title="Login Alert"
      icon="times-circle"
      variant="error"
      header="OH, SNAP!"
      subHeader={
        loginError === 'DomainHostedNotAllowedError'
          ? 'Email is not yet registered.'
          : 'Something went wrong while logging in.'
      }
      description={
        loginError === 'DomainHostedNotAllowedError'
          ? 'Kindly contact your administrator.'
          : 'Kindly contact your payment service administrator.'
      }
      handleConfirm={() => {
        setLoginError(null);
      }}
      handleClose={() => {
        setLoginError(null);
      }}
      confirmText="Go Back"
    />
  );

  if (isMobile) {
    return (
      <>
        <LoginPageContainer>
          <GlobeLogo src={globeLogo} alt="Globe" />
          <LoginPageTitle>PAYMENT SERVICE</LoginPageTitle>
          <LoginPageInstructions>
            Log in with your registered email account.
          </LoginPageInstructions>
          {oktaLogin}
          {loginBanner}
          <LoginPageFooter>
            © GLOBE TELECOM - Payment Service 2019. All rights reserved.
          </LoginPageFooter>
        </LoginPageContainer>
        {errorModal}
      </>
    );
  }

  return (
    <>
      <LoginPageContainer>
        <div>
          <LoginForm>
            <Row>
              <GlobeLogo src={globeLogo} alt="Globe" />
              <LoginPageTitle>PAYMENT SERVICE</LoginPageTitle>
            </Row>

            <LoginPageInstructions>
              Log in with your registered email account.
            </LoginPageInstructions>
            <PrimaryButton
              loading={isLoggingIn}
              onClick={handleLogin}
              style={{
                margin: isMobile ? 0 : '10px 0 40px 0',
                minWidth: isMobile ? 'calc(100% - 20px)' : 0,
              }}
            >
              LOG IN via Okta
            </PrimaryButton>
          </LoginForm>
          <LoginPageFooter>
            © GLOBE TELECOM - Payment Service 2019. All rights reserved.
          </LoginPageFooter>
        </div>

        {loginBanner}
      </LoginPageContainer>
      <Footer />
      {errorModal}
    </>
  );
}

Login.propTypes = {
  history: PropTypes.object,
};

export default Login;
