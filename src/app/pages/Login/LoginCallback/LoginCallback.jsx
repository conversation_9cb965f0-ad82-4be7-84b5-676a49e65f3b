import React, { useEffect, useContext, useState } from 'react';
import { useOktaAuth } from '@okta/okta-react';
import { useHistory } from 'react-router-dom'
import AuthContext from '../../../context/AuthContext/AuthContext';
import globeLogo from '../../../assets/globe-logo.png';
import {
  GlobeLogo,
  LoginPageContainer,
  LoginPageTitle,
  LoginPageInstructions,
} from './../styled';


const LoginCallback = () => {
  const history = useHistory();
  const { oktaAuth } = useOktaAuth();
  const { login } = useContext(AuthContext);
  const [error, setError] = useState(null);

  useEffect(() => {
    const handleLogin = async () => {
      try {
        // Get auth result from the redirect
        await oktaAuth.handleLoginRedirect();
      } catch (err) {
        console.error('Login callback error:', err);
        setError('Authentication failed. Please try again.' + err);
        // Wait a bit to show the error, then redirect
        setTimeout(() => {
          const originalUri = oktaAuth.getOriginalUri();
          history.push(originalUri || '/');
        }, 3000); // Delay to show error message
      }
    };

    // Override default restore behavior to delay redirect
    oktaAuth.options.restoreOriginalUri = async (_oktaAuth, originalUri) => {
      const tokens = await oktaAuth.tokenManager.getTokens();
      await login(tokens); // call your backend
      history.push(originalUri || '/');
    };
    handleLogin();
  }, [oktaAuth]);

  return (
    <LoginPageContainer>
      <GlobeLogo src={globeLogo} alt="Globe" />
      <LoginPageTitle>Logging in...</LoginPageTitle>
      <LoginPageInstructions>
        Please wait while we process your authentication.
      </LoginPageInstructions>
      {error ? (
        <p style={{ color: 'red', marginTop: '20px' }}>{error}</p>
      ) : (
        <p style={{ color: 'red', marginTop: '20px' }}>{error}</p>
      )}
    </LoginPageContainer>
  );
};

export default LoginCallback;
