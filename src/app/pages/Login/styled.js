import styled from 'styled-components';
import backgroundImg from './background.jpg';

export const LoginForm = styled.div`
  display: flex;
  flex-direction: column;
  background-color: white;
  padding: 20px 40px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.5);
`;

export const LoginPageContainer = styled.div`
  height: calc(100vh - 5px);
  width: 100vw;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;

  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    flex-direction: column;
    justify-content: space-evenly;
    height: 100vh;
    padding: 20px;
  }
`;

export const GlobeLogo = styled.img`
  height: 40px;
  margin-right: 10px;

  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    height: 60px;
  }
`;

export const LoginPageTitle = styled.h3`
  position: relative;
  top: 2px;
  margin: 0;
  font-size: ${props => props.theme.fontSize.m};
  font-weight: lighter;
  color: #244958;
  @media (max-width: $phone-width) {
    font-weight: normal;
  }
`;

export const LoginPageInstructions = styled.p`
  font-weight: lighter;
  text-align: center;
  margin: 40px 0 10px 0;
  font-size: ${props => props.theme.fontSize.m};
`;

export const LoginPageFooter = styled.p`
  margin-top: 20px;
  color: #333333;
  font-size: ${props => props.theme.fontSize.xs};
  text-align: center;
`;

export const LoginBanner = styled.div`
  min-height: 80%;
  width: 400px;
  padding: 20px;
  background:
    linear-gradient(180deg, rgba(37, 56, 72, 0) 0%, rgba(13, 34, 55, 0.83) 80%),
    url(${backgroundImg}) no-repeat;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: row;
  align-items: center;
  color: white;
  font-weight: bold;

  background-size: cover;

  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    min-width: 100%;
    min-height: 50%;
    width: auto;
  }

  h2 {
    color: white;
    font-size: ${props => props.theme.fontSize.l};
    font-weight: lighter;
  }

  ul {
    list-style-type: circle;

    li {
      font-weight: lighter;
      margin-top: 30px;
      font-size: ${props => props.theme.fontSize.s};
    }
  }
`;

export const Footer = styled.div`
  background-color: #15a3c7;
  height: 5px;
  width: 100vw;
`;
