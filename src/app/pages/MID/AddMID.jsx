import PropTypes from 'prop-types';
import React, { useContext, useEffect, useMemo, useState } from 'react';
import * as Yup from 'yup';
import styled from 'styled-components';
import useForm from '../../hooks/useForm';
import sanitize from '../../utils/sanitize';
import { useQuery } from '@apollo/client';
import { useMutation } from '@apollo/client';
import Row from '../../components/Row';
import Page from '../../components/Page';
import Header from '../../components/Header';
import { AlertModal } from '../../components/Modal';
import DataHeader from '../../components/DataHeader';
import FormField from '../../components/Form/FormField';
import DataContainer from '../../components/DataContainer';
import { FIELD_TYPES } from '../../components/Form/constants';
import ResponsiveContext from '../../context/ResponsiveContext';
import PrimaryButton from '../../components/Button/PrimaryButton';
import SecondaryButton from '../../components/Button/SecondaryButton';
import {
  ButtonsContainer,
  PageSubsection,
  SubsectionTitle,
} from '../../components/InformationPage';
import {
  PAYMENT_TYPES_BILL,
  PAYMENT_TYPES_NONBILL,
  COMPANIES,
  BILL_TYPES,
} from './MID';
import { GET_BANKLIST } from '../Installment/query';
import { ADD_MID } from './mutation';
import { GET_CHANNEL_BILLTYPE } from '../MIDInformation/query';
import { GET_CHANNEL_OPTIONS } from '../Reports/TransactionLogs/query';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

const ActionFields = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;

  width: ${props => {
    const base = 100 / props.perRow;
    const margin = 20;
    return `calc(${base}% - ${margin}px)`;
  }};

  flex: 1;

  margin-left: ${props => (props.isMobile ? '0px' : '20px')};
  margin-bottom: 20px;
`;

const ActionIcon = styled(FontAwesomeIcon)`
  color: ${props => (props.disabled ? 'gray' : props.color)};
  font-size: 20px;
  cursor: pointer;

  margin-left: 10px;
  &:first-child {
    margin-left: 0;
  }
`;

const ActionButton = styled.button`
  background-color: ${props =>
    props.disabled ? 'gray' : props.backgroundColor};
  color: #fff;
  font-size: ${props => props.theme.fontSize.s};
  cursor: pointer;
  flex: 1;
  border-radius: 5px;
`;

const TermsOptions = [
  { value: null, label: 'None' },
  { value: '3', label: '3' },
  { value: '6', label: '6' },
  { value: '9', label: '9' },
  { value: '12', label: '12' },
  { value: '18', label: '18' },
  { value: '24', label: '24' },
  { value: '36', label: '36' },
];

const EnrollmentTypeOptions = [
  { value: null, label: 'None' },
  { value: 'Straight', label: 'Straight (via Payment)' },
  { value: 'PreAuth', label: 'Pre Auth (via Channel Module)' },
];

const MIDInformation = ({ location, history }) => {
  const { isMobile } = useContext(ResponsiveContext);

  const [state, setState] = useState({
    isEditing: true,
    isLeavingWhileEditing: false,

    isSuccessEditMIDModalOpen: false,
    isFailureEditMIDModalOpen: false,
    isConfirmEditMIDModalOpen: false,

    isAddMIDModalOpen: false,
    isConfirmAddMIDModalOpen: false,
    isSuccessAddMIDModalOpen: false,
    isFailureAddMIDModalOpen: false,

    selectedMid: null,
    nextLocation: null,
    addMidError: null,
  });

  const { data: allChannels, loading: allChannelsLoading } = useQuery(
    GET_CHANNEL_OPTIONS,
    {
      fetchPolicy: 'network-only',
    }
  );

  const { data: bankList, loading: isLoadingBanks } = useQuery(GET_BANKLIST, {
    fetchPolicy: 'network-only',
  });

  const bankOptions =
    !isLoadingBanks && bankList
      ? bankList.listInstallmentBank.map(bank => ({
          value: bank,
          label: bank,
        }))
      : [];

  bankOptions.unshift({
    value: null,
    label: 'None',
  });

  const [channelOptions, setChannelOptions] = useState([]);

  const [selectedInstallment, setselectedInstallment] = useState({
    bankTerm: '',
    bank: '',
    index: '',
  });

  const [newInstallments, setNewInstallments] = useState([
    {
      bank: null,
      bankTerm: null,
      bankMid: null,
    },
  ]);

  const [newInstallmentErrors, setnewInstallmentErrors] = useState([{}]);

  const [selectedAda, setSelectedAda] = useState({
    adaMid: '',
    enrollmentType: '',
    index: '',
  });

  const [newAda, setNewAda] = useState([
    {
      enrollmentType: null,
      adaMid: null,
    },
  ]);

  const [newAdaErrors, setNewAdaErrors] = useState([{}]);

  let addNewInstallments = newInstallments.filter(
    newValue => newValue.bank && newValue.bankTerm && newValue.bankMid
  );

  let addNewAda = newAda.filter(
    newValue => newValue.enrollmentType && newValue.adaMid
  );

  // initialize isEditing
  // this takes affect when user clicks on edit button from table pages
  useEffect(() => {
    if (location.state && location.state.isEditing !== undefined) {
      setState({ ...state, isEditing: location.state.isEditing });
    }
  }, []);

  useEffect(() => {
    if (selectedInstallment.bankTerm && selectedInstallment.bank) {
      onChange.installments(
        values.installments.filter((bank, index) => {
          return index !== selectedInstallment.index;
        })
      );

      setselectedInstallment({ bankTerm: '', bank: '' });
    }
  });

  useEffect(() => {
    if (selectedAda.enrollmentType && selectedAda.adaMid) {
      onChange.ada(
        values.ada.filter((enrollmentType, index) => {
          return index !== selectedAda.index;
        })
      );

      setSelectedAda({ enrollmentType: '', adaMid: '' });
    }
  });

  const [addMid, { loading: isAddingMid }] = useMutation(ADD_MID, {
    onCompleted: () => {
      setState({
        ...state,
        isConfirmAddMIDModalOpen: false,
        isSuccessAddMIDModalOpen: true,
        isAddMIDModalOpen: false,
        pagination: {
          ...state.pagination,
          start: '',
        },
      });
    },
    onError: err => {
      setState({
        ...state,
        addMidError: err.networkError.result
          ? err.networkError.result.message
          : null,
        isConfirmAddMIDModalOpen: false,
        isFailureAddMIDModalOpen: true,
      });
    },
  });

  // eslint-disable-next-line
  const { fields, initialValue } = useMemo(() => {
    const fields = {
      name: {
        validation: Yup.string()
          .max(50, 'Must not exceed 50 characters')
          .required('Please enter a value')
          .matches(/[^-\s]/, 'Must not be a whitespace')
          .matches(
            /^(?![=,@,+,-])(.+)$/,
            'Input must not begin with this special characters (=,@,+,-)'
          ),
      },
      billType: {
        validation: Yup.string().required('Please enter a value'),
      },
      company: {
        validation: value =>
          value && value.billType === BILL_TYPES[0].value
            ? Yup.string()
                .max(100, 'Must not exceed 100 characters')
                .required('Please enter a value')
            : Yup.string().nullable(),
      },
      channelId: {
        validation: value =>
          value && value.billType === BILL_TYPES[1].value
            ? Yup.string()
                .max(100, 'Must not exceed 100 characters')
                .required('Please enter a value')
            : Yup.string().nullable(),
      },
      depositoryBankName: {
        validation: Yup.string()
          .max(50, 'Must not exceed 50 characters')
          .required('Please enter a value')
          .matches(/[^-\s]/, 'Must not be a whitespace')
          .matches(
            /^(?![=,@,+,-])(.+)$/,
            'Input must not begin with this special characters (=,@,+,-)'
          ),
      },
      merchantId: {
        validation: Yup.string()
          .min(1, 'Minimum should be 1 character')
          .max(50, 'Must not exceed 50 characters')
          .matches(/[^-\s]/, 'Must not be a whitespace')
          .matches(
            /^(?![=,@,+,-])(.+)$/,
            'Input must not begin with this special characters (=,@,+,-)'
          )
          .matches(/^[0-9a-zA-Z-]*$/, 'Must be alphanumeric and dashes')
          .required('Please enter a value'),
      },
      previousMerchantId: {
        validation: Yup.string()
          .min(1, 'Minimum should be 1 character')
          .max(50, 'Must not exceed 50 characters')
          .matches(
            /^(?![=,@,+,-])(.+)$/,
            'Input must not begin with this special characters (=,@,+,-)'
          )
          .matches(/^[0-9a-zA-Z-]*$/, 'Must be alphanumeric and dashes')
          .nullable(),
      },
      depositoryBankAccount: {
        validation: Yup.string()
          .min(1, 'Minimum should be 1 character')
          .max(50, 'Must not exceed 50 characters')
          .matches(
            /^(?![=,@,+,-])(.+)$/,
            'Input must not begin with this special characters (=,@,+,-)'
          )
          .matches(/^[0-9-]+$/, 'Allowed characters are numbers and dashes')
          .required('Please enter a value'),
      },
      withholdingTax: {
        validation: Yup.string()
          .min(1, 'Minimum should be 1 character')
          .max(5, 'Must not exceed 5 characters')
          .matches(
            /^\d*(\.\d+)?$/,
            'Invalid input! Allowed characters are positive number or number with decimal placed only'
          )
          .required('Please enter a value'),
      },
      bankDiscount: {
        validation: Yup.string()
          .min(1, 'Minimum should be 1 character')
          .max(5, 'Must not exceed 5 characters')
          .matches(
            /^\d*(\.\d+)?$/,
            'Invalid input! Allowed characters are positive number or number with decimal placed only'
          )
          .required('Please enter a value'),
      },
      paymentType: {
        validation: Yup.string().required('Please enter a value'),
      },
      costCenter: {
        validation: Yup.string()
          .min(1, 'Minimum should be 1 character')
          .max(50, 'Must not exceed 50 characters')
          .required('Please enter a value')
          .matches(
            /^(?![=,@,+,-])(.+)$/,
            'Input must not begin with this special characters (=,@,+,-)'
          )
          .matches(/^[0-9a-zA-Z-]*$/, 'Must be alphanumeric or dashes'),
      },
      businessUnit: {
        validation: Yup.string()
          .min(1, 'Minimum should be 1 character')
          .max(50, 'Must not exceed 50 characters')
          .matches(/[^-\s]/, 'Must not be a whitespace')
          .required('Please enter a value')
          .matches(
            /^(?![=,@,+,-])(.+)$/,
            'Input must not begin with this special characters (=,@,+,-)'
          ),
      },
      installments: {},
      ada: {},
    };

    const initialValue = {};

    return { fields, initialValue };
  });

  const { values, onChange, onBlur, errors } = useForm(fields, values => {
    setState({
      ...state,
      isConfirmAddMIDModalOpen: true,
      selectedMid: values,
    });
  });

  // To get values of bill type when changing
  const { data: dataChannels, loading: dataChannelsLoading } = useQuery(
    GET_CHANNEL_BILLTYPE,
    {
      variables: {
        billType: values.billType,
      },
      skip: !values.billType,
      fetchPolicy: 'network-only',
    }
  );

  useEffect(() => {
    if (
      !dataChannelsLoading &&
      dataChannels &&
      !allChannelsLoading &&
      allChannels
    ) {
      let channelOptions =
        dataChannels &&
        dataChannels.channelsBillType
          .filter(channel => channel !== null)
          .map(channel => {
            return {
              label: channel.name,
              value: channel.id,
            };
          });

      if (values.billType === BILL_TYPES[1].value) {
        if (values.channelId) {
          let midChannel = allChannels.channelsLoose.find(
            channel => channel.id === values.channelId
          );

          if (midChannel) {
            channelOptions.push({
              label: midChannel.name,
              value: midChannel.id,
            });
          }
        }
      }

      setChannelOptions(channelOptions);
    }
  }, [dataChannels, allChannels]);

  const backButton = (
    <SecondaryButton
      onClick={() => {
        history.push('/mid');
      }}
    >
      Back to All MID
    </SecondaryButton>
  );

  const addButtonGroup = (
    <Row>
      <PrimaryButton
        icon="save"
        disabled={
          !state.isEditing ||
          newInstallmentErrors.find(
            newError =>
              newError.bank ||
              newError.bankTerm ||
              (newError.bankMid &&
                newError.bankMid !==
                  'Duplicate MID. Are you sure want to use it?')
          ) ||
          newInstallments.find(
            newInstallment =>
              newInstallment.bankMid &&
              newInstallment.bank === null &&
              newInstallment.bankTerm
          ) ||
          newAdaErrors.find(
            newError =>
              newError.enrollmentType ||
              (newError.adaMid &&
                newError.adaMid !==
                  'Duplicate MID. Are you sure want to use it?')
          ) ||
          newAda.find(
            newAdaData =>
              newAdaData.adaMid && newAdaData.enrollmentType === null
          )
        }
        onClick={() => {
          setState({
            ...state,
            isConfirmAddMIDModalOpen: true,
            selectedMid: values,
          });
        }}
      >
        Create MID
      </PrimaryButton>
    </Row>
  );

  const [paymentTypeOptions, setPaymentTypeOptions] = useState([]);

  useEffect(() => {
    const paymentTypeOptions =
      values.billType === 'Bill' ? PAYMENT_TYPES_BILL : PAYMENT_TYPES_NONBILL;
    if (
      !paymentTypeOptions.find(option => option.value === values.paymentType)
    ) {
      values.paymentType = '';
    }

    if (values.billType === BILL_TYPES[0].value) {
      values.channelId = '';
    } else if (values.billType === BILL_TYPES[1].value) {
      values.company = '';
    }

    setPaymentTypeOptions(paymentTypeOptions);
  }, [values.billType]);

  function handleNewBankTermValidation(event, bank, index) {
    const existsInNew = newInstallments.find(
      value => value.bankTerm === event && value.bank === bank
    );

    let newErrors = newInstallmentErrors;

    if (existsInNew) {
      setnewInstallmentErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              bankTerm: 'Duplicate term on existing bank',
            };
          }

          return val;
        })
      );

      return;
    }

    if (event === null) {
      setnewInstallmentErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              bankTerm: 'Please select a term',
            };
          }

          return val;
        })
      );

      return;
    }

    Yup.string()
      .nullable()
      .validate(event)
      .then(() => {
        if (newInstallmentErrors[index].bankTerm) {
          const installmentError = newInstallmentErrors.map((val, valIndex) => {
            if (valIndex === index) {
              delete val.bankTerm;
            }

            return val;
          });

          setnewInstallmentErrors(installmentError);
        }
      })
      .catch(err => {
        if (err.errors) {
          const installmentError = newInstallmentErrors.map((val, valIndex) => {
            if (valIndex === index) {
              return {
                ...val,
                bankTerm: err.errors[0],
              };
            }

            return val;
          });

          setnewInstallmentErrors(installmentError);
        }
      });
  }

  function handleNewBankMidValidation(event, index) {
    const { target } = event;
    Yup.string()
      .matches(/[^-\s]/, 'Must not be a whitespace')
      .matches(
        /^(?![=,@,+,-])(.+)$/,
        'Input must not begin with this special characters (=,@,+,-)'
      )
      .matches(/^[0-9a-zA-Z-]+$/, 'Must be alphanumeric and dashes')
      .required('Please enter value')
      .test(
        'MID Checker',
        'Duplicate MID. Are you sure want to use it?',
        value => {
          const exist = newInstallments.find(
            (data, valIndex) => value === data.bankMid && valIndex !== index
          );
          if (exist) {
            return false;
          }
          return true;
        }
      )
      .min(3, 'Must not less than 3 characters')
      .max(50, 'Must not exceed 50 characters')
      .validate(target.value)
      .then(() => {
        if (newInstallmentErrors[index].bankMid) {
          const installmentError = newInstallmentErrors.map((val, valIndex) => {
            if (valIndex === index) {
              delete val.bankMid;
            }

            return val;
          });

          setnewInstallmentErrors(installmentError);
        }
      })
      .catch(err => {
        if (err.errors.length) {
          const installmentError = newInstallmentErrors.map((val, valIndex) => {
            if (valIndex === index) {
              return {
                ...val,
                bankMid: err.errors[0],
              };
            }

            return val;
          });

          setnewInstallmentErrors(installmentError);
        }
      });
  }

  function handleNewEnrollmentTypeValidation(event, index) {
    const existsInNew = newAda.find(
      (value, valIndex) => value.enrollmentType === event && valIndex !== index
    );

    let newErrors = newAdaErrors;

    if (existsInNew) {
      setNewAdaErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              enrollmentType: 'Duplicate enrollment type',
            };
          }

          return val;
        })
      );

      return;
    }

    Yup.string()
      .nullable()
      .validate(event)
      .then(() => {
        if (newAdaErrors[index].enrollmentType) {
          const adaError = newAdaErrors.map((val, valIndex) => {
            if (valIndex === index) {
              delete val.enrollmentType;
            }

            return val;
          });

          setNewAdaErrors(adaError);
        }
      })
      .catch(err => {
        if (err.errors) {
          const adaError = newAdaErrors.map((val, valIndex) => {
            if (valIndex === index) {
              return {
                ...val,
                enrollmentType: err.errors[0],
              };
            }

            return val;
          });

          setNewAdaErrors(adaError);
        }
      });
  }

  function handleNewAdaMidValidation(event, index) {
    const { target } = event;
    Yup.string()
      .matches(/[^-\s]/, 'Must not be a whitespace')
      .matches(
        /^(?![=,@,+,-])(.+)$/,
        'Input must not begin with this special characters (=,@,+,-)'
      )
      .matches(/^[0-9a-zA-Z-]+$/, 'Must be alphanumeric and dashes')
      .required('Please enter value')
      .test(
        'ADA Checker',
        'Duplicate MID. Are you sure want to use it?',
        value => {
          const exist = newAda.find(
            (data, valIndex) => value === data.adaMid && valIndex !== index
          );
          if (exist) {
            return false;
          }
          return true;
        }
      )
      .min(3, 'Must not less than 3 characters')
      .max(50, 'Must not exceed 50 characters')
      .validate(target.value)
      .then(() => {
        if (newAdaErrors[index].adaMid) {
          const adaError = newAdaErrors.map((val, valIndex) => {
            if (valIndex === index) {
              delete val.adaMid;
            }

            return val;
          });

          setNewAdaErrors(adaError);
        }
      })
      .catch(err => {
        if (err.errors.length) {
          const adaError = newAdaErrors.map((val, valIndex) => {
            if (valIndex === index) {
              return {
                ...val,
                adaMid: err.errors[0],
              };
            }

            return val;
          });

          setNewAdaErrors(adaError);
        }
      });
  }

  return (
    <>
      <Page>
        <Header withHome title={'Add MID'} path={['Merchant', 'Add MID']} />
        <DataContainer>
          <>
            <DataHeader>
              <DataHeader.Title>ADD MID</DataHeader.Title>
            </DataHeader>
            <SubsectionTitle>GENERAL</SubsectionTitle>
            <PageSubsection>
              <FormField
                label="Project Name"
                name="name"
                type={FIELD_TYPES.TEXT}
                value={values.name}
                onChange={onChange.name}
                onBlur={onBlur.name}
                error={errors.name}
                readOnly={!state.isEditing}
                perRow={2}
                required
              />

              <FormField
                label="Bill Type"
                name="billType"
                type={FIELD_TYPES.SELECT}
                options={BILL_TYPES}
                value={values.billType}
                onChange={onChange.billType}
                onBlur={onBlur.billType}
                error={errors.billType}
                readOnly={!state.isEditing}
                perRow={2}
                required
              />

              <FormField
                label="Company"
                name="company"
                placeholder="Company"
                type={FIELD_TYPES.SELECT}
                options={COMPANIES}
                value={values.company}
                onChange={onChange.company}
                onBlur={onBlur.company}
                error={errors.company}
                readOnly={!state.isEditing}
                perRow={2}
                required={values.billType === BILL_TYPES[0].value}
              />

              <FormField
                label="Channel"
                name="channelId"
                placeholder="Channel"
                type={FIELD_TYPES.SELECT}
                options={channelOptions}
                value={values.channelId}
                onChange={onChange.channelId}
                onBlur={onBlur.channelId}
                error={errors.channelId}
                readOnly={!state.isEditing}
                perRow={2}
                required={values.billType === BILL_TYPES[1].value}
              />
              <FormField
                label="Payment Type"
                name="paymentType"
                type={FIELD_TYPES.SELECT}
                options={paymentTypeOptions}
                value={values.paymentType}
                onChange={onChange.paymentType}
                onBlur={onBlur.paymentType}
                error={errors.paymentType}
                readOnly={!state.isEditing}
                perRow={2}
                required
              />
              <FormField
                label="Cost Center"
                name="costCenter"
                type={FIELD_TYPES.TEXT}
                value={values.costCenter}
                onChange={onChange.costCenter}
                onBlur={onBlur.costCenter}
                error={errors.costCenter}
                readOnly={!state.isEditing}
                perRow={2}
                required
              />
              <FormField
                label="MID"
                name="merchantId"
                type={FIELD_TYPES.TEXT}
                value={values.merchantId}
                onChange={onChange.merchantId}
                onBlur={onBlur.merchantId}
                error={errors.merchantId}
                readOnly={values.paymentType === 'Straight' ? false : true}
                perRow={2}
                required={values.paymentType === 'Straight' ? true : false}
              />
              <FormField
                label="Previous MID"
                name="previousMerchantId"
                type={FIELD_TYPES.TEXT}
                value={values.previousMerchantId}
                onChange={onChange.previousMerchantId}
                onBlur={onBlur.previousMerchantId}
                error={errors.previousMerchantId}
                readOnly={!state.isEditing}
                perRow={2}
              />
              <FormField
                label="Depository Bank Name"
                name="depositoryBankName"
                type={FIELD_TYPES.TEXT}
                value={values.depositoryBankName}
                onChange={onChange.depositoryBankName}
                onBlur={onBlur.depositoryBankName}
                error={errors.depositoryBankName}
                readOnly={!state.isEditing}
                perRow={2}
                required
              />

              <FormField
                label="Depository Bank Account"
                name="depositoryBankAccount"
                type={FIELD_TYPES.TEXT}
                value={values.depositoryBankAccount}
                onChange={onChange.depositoryBankAccount}
                onBlur={onBlur.depositoryBankAccount}
                error={errors.depositoryBankAccount}
                readOnly={!state.isEditing}
                perRow={2}
                required
              />

              <FormField
                label="Withholding Tax"
                name="withholdingTax"
                type={FIELD_TYPES.TEXT}
                value={values.withholdingTax}
                onChange={onChange.withholdingTax}
                onBlur={onBlur.withholdingTax}
                error={errors.withholdingTax}
                readOnly={!state.isEditing}
                perRow={2}
                required
                isPercent
              />

              <FormField
                label="Merchant Discount Rate"
                name="bankDiscount"
                type={FIELD_TYPES.TEXT}
                value={values.bankDiscount}
                onChange={onChange.bankDiscount}
                onBlur={onBlur.bankDiscount}
                error={errors.bankDiscount}
                readOnly={!state.isEditing}
                perRow={2}
                required
                isPercent
              />

              <FormField
                label="Business Unit"
                name="businessUnit"
                type={FIELD_TYPES.TEXT}
                value={values.businessUnit}
                onChange={onChange.businessUnit}
                onBlur={onBlur.businessUnit}
                error={errors.businessUnit}
                readOnly={!state.isEditing}
                perRow={2}
                required
              />
            </PageSubsection>

            {values.paymentType === 'Installment' && (
              <SubsectionTitle>INSTALLMENT MID</SubsectionTitle>
            )}
            {state.isEditing &&
              values.paymentType === 'Installment' &&
              newInstallments.map((newInstallment, index) => {
                return (
                  <PageSubsection key={index}>
                    <FormField
                      placeholder="Bank"
                      label={index === 0 ? 'Bank' : ''}
                      name={`newInstallment-${index}`}
                      type={FIELD_TYPES.SELECT}
                      value={newInstallment.bank}
                      options={bankOptions}
                      onChange={event => {
                        let newValue = newInstallments.map(
                          (newValue, newIndex) => {
                            if (newIndex === index) {
                              return {
                                bank: event,
                                bankTerm: null,
                                bankMid: newInstallment.bankMid,
                              };
                            }
                            return newValue;
                          }
                        );

                        setNewInstallments(newValue);
                      }}
                      error={newInstallmentErrors[index].bank}
                      perRow={3.5}
                      readOnly={!state.isEditing}
                      required
                    />
                    <FormField
                      placeholder="Bank Term"
                      label={index === 0 ? 'Bank Term' : ''}
                      name={`newInstallment-${index}`}
                      type={FIELD_TYPES.SELECT}
                      value={newInstallment.bankTerm}
                      options={TermsOptions}
                      onChange={event => {
                        let newValue = newInstallments.map(
                          (newValue, newIndex) => {
                            if (newIndex === index) {
                              return {
                                bank: newInstallment.bank,
                                bankTerm: event,
                                bankMid: newInstallment.bankMid,
                              };
                            }
                            return newValue;
                          }
                        );

                        setNewInstallments(newValue);
                        handleNewBankTermValidation(
                          event,
                          newInstallment.bank,
                          index
                        );
                      }}
                      onBlur={event =>
                        handleNewBankTermValidation(event, index)
                      }
                      error={newInstallmentErrors[index].bankTerm}
                      perRow={3.5}
                      readOnly={
                        !state.isEditing || newInstallment.bank === null
                      }
                      required
                    />
                    <FormField
                      placeholder="Bank MID"
                      label={index === 0 ? 'Bank MID' : ''}
                      name={`installment-${index}`}
                      type={FIELD_TYPES.TEXT}
                      value={newInstallment.bankMid}
                      onChange={event => {
                        let newValue = newInstallments.map(
                          (newValue, newIndex) => {
                            if (newIndex === index) {
                              return {
                                bank: newInstallment.bank,
                                bankTerm: newInstallment.bankTerm,
                                bankMid: event.target.value,
                              };
                            }
                            return newValue;
                          }
                        );

                        setNewInstallments(newValue);
                        handleNewBankMidValidation(event, index);
                      }}
                      onBlur={event => handleNewBankMidValidation(event, index)}
                      error={newInstallmentErrors[index].bankMid}
                      perRow={3.5}
                      readOnly={!state.isEditing}
                      required
                    />
                    <ActionFields
                      isMobile={isMobile}
                      fieldsPerRow={2.5}
                      perRow={5}
                    >
                      {!(
                        newInstallmentErrors[index].bank ||
                        newInstallmentErrors[index].bankTerm ||
                        (newInstallmentErrors[index].bankMid &&
                          newInstallmentErrors[index].bankMid !==
                            'Duplicate MID. Are you sure want to use it?')
                      ) &&
                        newInstallment.bank &&
                        newInstallment.bankTerm &&
                        newInstallment.bankMid &&
                        index === newInstallments.length - 1 &&
                        (isMobile ? (
                          <ActionButton
                            backgroundColor="green"
                            onClick={() => {
                              setNewInstallments([
                                ...newInstallments,
                                {
                                  bank: null,
                                  bankTerm: null,
                                  bankMid: null,
                                },
                              ]);
                              setnewInstallmentErrors([
                                ...newInstallmentErrors,
                                {},
                              ]);
                            }}
                          >
                            Add
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon="plus-circle"
                            color="green"
                            onClick={() => {
                              setNewInstallments([
                                ...newInstallments,
                                {
                                  bank: null,
                                  bankTerm: null,
                                  bankMid: null,
                                },
                              ]);
                              setnewInstallmentErrors([
                                ...newInstallmentErrors,
                                {},
                              ]);
                            }}
                          />
                        ))}
                      {isMobile ? (
                        <ActionButton
                          backgroundColor="red"
                          onClick={() => {
                            let newValue = newInstallments.filter(
                              (installment, installmentIndex) => {
                                return installmentIndex !== index;
                              }
                            );

                            let newErrors = newInstallmentErrors.filter(
                              (installment, installmentIndex) => {
                                return installmentIndex !== index;
                              }
                            );

                            if (newValue.length === 0) {
                              newValue.push({
                                bank: null,
                                bankTerm: null,
                                bankMid: null,
                              });
                              newErrors.push({});
                            }

                            setNewInstallments(newValue);
                            setnewInstallmentErrors(newErrors);
                          }}
                        >
                          Delete
                        </ActionButton>
                      ) : (
                        <ActionIcon
                          icon="minus-circle"
                          color="red"
                          onClick={() => {
                            let newValue = newInstallments.filter(
                              (installment, installmentIndex) => {
                                return installmentIndex !== index;
                              }
                            );

                            let newErrors = newInstallmentErrors.filter(
                              (installment, installmentIndex) => {
                                return installmentIndex !== index;
                              }
                            );

                            if (newValue.length === 0) {
                              newValue.push({
                                bank: null,
                                bankTerm: null,
                                bankMid: null,
                              });
                              newErrors.push({});
                            }

                            setNewInstallments(newValue);
                            setnewInstallmentErrors(newErrors);
                          }}
                        />
                      )}
                    </ActionFields>
                  </PageSubsection>
                );
              })}
            {values.paymentType === 'AutoDebit' && (
              <SubsectionTitle>ADA MID</SubsectionTitle>
            )}
            {state.isEditing &&
              values.paymentType === 'AutoDebit' &&
              newAda.map((newAdaData, index) => {
                return (
                  <PageSubsection key={index}>
                    <FormField
                      placeholder="Enrollment Type"
                      label={index === 0 ? 'Enrollment Type' : ''}
                      name={`newAda-${index}`}
                      type={FIELD_TYPES.SELECT}
                      value={newAdaData.enrollmentType}
                      options={EnrollmentTypeOptions}
                      onChange={event => {
                        let newValue = newAda.map((newValue, newIndex) => {
                          if (newIndex === index) {
                            return {
                              enrollmentType: event,
                              adaMid: newAdaData.adaMid,
                            };
                          }
                          return newValue;
                        });

                        setNewAda(newValue);
                        handleNewEnrollmentTypeValidation(event, index);
                      }}
                      onBlur={event =>
                        handleNewEnrollmentTypeValidation(event, index)
                      }
                      error={newAdaErrors[index].enrollmentType}
                      perRow={2.5}
                      readOnly={!state.isEditing}
                      required
                    />
                    <FormField
                      placeholder="ADA MID"
                      label={index === 0 ? 'ADA MID' : ''}
                      name={`newAda-${index}`}
                      type={FIELD_TYPES.TEXT}
                      value={newAdaData.adaMid}
                      onChange={event => {
                        let newValue = newAda.map((newValue, newIndex) => {
                          if (newIndex === index) {
                            return {
                              enrollmentType: newAdaData.enrollmentType,
                              adaMid: event.target.value,
                            };
                          }
                          return newValue;
                        });

                        setNewAda(newValue);
                        handleNewAdaMidValidation(event, index);
                      }}
                      onBlur={event => handleNewAdaMidValidation(event, index)}
                      error={newAdaErrors[index].adaMid}
                      perRow={2.5}
                      readOnly={!state.isEditing}
                      required
                    />
                    <ActionFields
                      isMobile={isMobile}
                      fieldsPerRow={2.5}
                      perRow={5}
                    >
                      {!(
                        newAdaErrors[index].enrollmentType ||
                        (newAdaErrors[index].adaMid &&
                          newAdaErrors[index].adaMid !==
                            'Duplicate MID. Are you sure want to use it?')
                      ) &&
                        newAdaData.enrollmentType &&
                        newAdaData.adaMid &&
                        index === newAda.length - 1 &&
                        (isMobile ? (
                          <ActionButton
                            backgroundColor="green"
                            onClick={() => {
                              setNewAda([
                                ...newAda,
                                {
                                  enrollmentType: null,
                                  adaMid: null,
                                },
                              ]);
                              setNewAdaErrors([...newAdaErrors, {}]);
                            }}
                          >
                            Add
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon="plus-circle"
                            color="green"
                            onClick={() => {
                              setNewAda([
                                ...newAda,
                                {
                                  enrollmentType: null,
                                  adaMid: null,
                                },
                              ]);
                              setNewAdaErrors([...newAdaErrors, {}]);
                            }}
                          />
                        ))}
                      {isMobile ? (
                        <ActionButton
                          backgroundColor="red"
                          onClick={() => {
                            let newValue = newAda.filter((ada, adaIndex) => {
                              return adaIndex !== index;
                            });

                            let newErrors = newAdaErrors.filter(
                              (ada, adaIndex) => {
                                return adaIndex !== index;
                              }
                            );

                            if (newValue.length === 0) {
                              newValue.push({
                                enrollmentType: null,
                                adaMid: null,
                              });
                              newErrors.push({});
                            }

                            setNewAda(newValue);
                            setNewAdaErrors(newErrors);
                          }}
                        >
                          Delete
                        </ActionButton>
                      ) : (
                        <ActionIcon
                          icon="minus-circle"
                          color="red"
                          onClick={() => {
                            let newValue = newAda.filter((ada, adaIndex) => {
                              return adaIndex !== index;
                            });

                            let newErrors = newAdaErrors.filter(
                              (ada, adaIndex) => {
                                return adaIndex !== index;
                              }
                            );

                            if (newValue.length === 0) {
                              newValue.push({
                                enrollmentType: null,
                                adaMid: null,
                              });
                              newErrors.push({});
                            }

                            setNewAda(newValue);
                            setNewAdaErrors(newErrors);
                          }}
                        />
                      )}
                    </ActionFields>
                  </PageSubsection>
                );
              })}
            <PageSubsection>
              <ButtonsContainer>
                {isMobile ? (
                  <>
                    {addButtonGroup}
                    {backButton}
                  </>
                ) : (
                  <>
                    {backButton}
                    {addButtonGroup}
                  </>
                )}
              </ButtonsContainer>
            </PageSubsection>
          </>
        </DataContainer>
      </Page>

      <AlertModal
        isOpen={state.isConfirmAddMIDModalOpen}
        title="New MID Alert"
        icon="exclamation-circle"
        variant="warn"
        header="ARE YOU SURE?"
        subHeader="You are about to create a new MID."
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        confirmLoading={isAddingMid}
        confirmText="Yes"
        handleConfirm={() => {
          const value = sanitize(state.selectedMid);
          addMid({
            variables: {
              data: {
                ...value,
                installments: [...addNewInstallments],
                ada: [...addNewAda],
              },
            },
          });
        }}
        handleClose={() => {
          setState({
            ...state,
            isConfirmAddMIDModalOpen: false,
          });
        }}
      />
      <AlertModal
        isOpen={state.isSuccessAddMIDModalOpen}
        title="New MID Alert"
        handleClose={() => {
          setState({ ...state, isSuccessAddMIDModalOpen: false });
        }}
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader="MID has been created successfully."
        description="Editing of MID Account is now enabled."
        confirmText="Go to All MID"
        handleConfirm={() => {
          setState({ ...state, isSuccessAddMIDModalOpen: false });
          history.push('/mid');
        }}
      />
      <AlertModal
        isOpen={state.isSuccessEditMIDModalOpen}
        title="New MID Alert"
        variant="success"
        icon="check-circle"
        header="SUCCESS!"
        subHeader="Changes have been saved successfully."
        description="Changes are now reflected on the records."
        confirmText="Go to All MID"
        handleConfirm={() => {
          history.push('/mid');
        }}
        handleClose={() =>
          setState({ ...state, isSuccessEditMIDModalOpen: false })
        }
      />
      <AlertModal
        isOpen={state.isFailureAddMIDModalOpen}
        title="New MID Alert"
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader={
          state.addMidError === 'MERCHANT_ID_ALREADY_EXISTS'
            ? 'MID already exists.'
            : state.addMidError === 'COMPANY_PAYMENT_TYPE_ALREADY_EXIST' ||
                state.addMidError ===
                  'COMPANY_PAYMENT_TYPE_BILL_TYPE_ALREADY_EXIST'
              ? `A record with a Company of ${state.selectedMid.company}, Bill Type of ${state.selectedMid.billType} and Payment Type of ${state.selectedMid.paymentType} already exists`
              : 'There was a problem on saving New MID.'
        }
        description="Please go back and try saving it again."
        handleClose={() =>
          setState({ ...state, isFailureAddMIDModalOpen: false })
        }
        confirmText="Go Back"
        handleConfirm={() => {
          setState({ ...state, isFailureAddMIDModalOpen: false });
        }}
      />
    </>
  );
};

MIDInformation.propTypes = {
  location: PropTypes.object,
  history: PropTypes.object,
  match: PropTypes.object,
};

export default MIDInformation;
