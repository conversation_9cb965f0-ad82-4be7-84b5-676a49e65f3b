import React, { useEffect, useState, useContext } from 'react';
import PropTypes from 'prop-types';
import sanitize from '../../utils/sanitize';
import { useMutation } from '@apollo/client';
import Page from '../../components/Page';
import { useQuery } from '@apollo/client';
import DataTable from '../../components/DataTable';
import Header from '../../components/Header/Header';
import GlobalSearch from '../../components/GlobalSearch';
import ActionButtons from '../../components/ActionButtons';
import DataContainer from '../../components/DataContainer';
import { FIELD_TYPES } from '../../components/Form/constants';
import { AlertModal } from '../../components/Modal';
import CreateButton from '../../components/Button/CreateButton';
import { GET_MID_ACCOUNTS } from './query';
import { ADD_MID, DELETE_MID } from './mutation';
import AuthContext from '../../context/AuthContext/AuthContext';
import { ExportButton } from '../../components/Button/ExportButton';
import NotificationContext from '../../context/NotificationContext';
import { GET_CHANNEL_OPTIONS } from '../Reports/TransactionLogs/query';

export const PAYMENT_TYPES = [
  { value: 'Straight', label: 'Straight' },
  { value: 'AutoDebit', label: 'AutoDebit' },
  { value: 'Installment', label: 'Installment' },
  { value: 'AdvancePayment', label: 'Advance' },
];

export const PAYMENT_TYPES_NONBILL = [
  { value: 'Straight', label: 'Straight' },
  { value: 'Installment', label: 'Installment' },
];

export const PAYMENT_TYPES_BILL = [
  { value: 'Straight', label: 'Straight' },
  { value: 'AutoDebit', label: 'AutoDebit' },
];

export const BILL_TYPES = [
  { value: 'Bill', label: 'Bill' },
  { value: 'NonBill', label: 'NonBill' },
];

export const COMPANIES = [
  { value: 'Globe', label: 'Globe' },
  { value: 'Innove', label: 'Innove' },
  { value: 'Bayan', label: 'Bayan' },
];

const MID = ({ history }) => {
  const { permissions } = useContext(AuthContext);
  const [state, setState] = useState({
    isAddMIDModalOpen: false,
    isConfirmAddMIDModalOpen: false,
    isSuccessAddMIDModalOpen: false,
    isFailureAddMIDModalOpen: false,

    isLeavingPageWhileAdding: false,

    isConfirmDeleteMIDModalOpen: false,
    isSuccessDeleteMIDModalOpen: false,
    isFailureDeleteMIDModalOpen: false,

    isConfirmDeleteMIDsModalOpen: false,
    isSuccessDeleteMIDsModalOpen: false,
    isConfirmDownloadMIDsModalOpen: false,

    filter: {},
    pagination: {
      limit: 10,
      start: '',
    },
    selectedMid: null,
    nextLocation: null,
    addMidError: null,
  });

  const { data, loading, refetch } = useQuery(GET_MID_ACCOUNTS, {
    variables: {
      filter: state.filter,
      pagination: state.pagination,
    },
    fetchPolicy: 'network-only',
  });

  const { data: allChannels, loading: allChannelsLoading } = useQuery(
    GET_CHANNEL_OPTIONS,
    {
      fetchPolicy: 'network-only',
    }
  );

  useEffect(() => {
    const unblock = history.block(location => {
      if (state.isLeavingPageWhileAdding || !state.isAddMIDModalOpen) {
        return true;
      }
      setState({
        ...state,
        nextLocation: location,
        isLeavingPageWhileAdding: true,
      });
      return false;
    });

    return () => {
      unblock();
    };
  }, [state.isLeavingPageWhileAdding, state.isAddMIDModalOpen]);

  const [addMid, { loading: isAddingMid }] = useMutation(ADD_MID, {
    onCompleted: () => {
      setState({
        ...state,
        isConfirmAddMIDModalOpen: false,
        isSuccessAddMIDModalOpen: true,
        isAddMIDModalOpen: false,
        pagination: {
          ...state.pagination,
          start: '',
        },
      });
      refetch();
    },
    onError: err => {
      setState({
        ...state,
        addMidError: err.networkError.result
          ? err.networkError.result.message
          : null,
        isConfirmAddMIDModalOpen: false,
        isFailureAddMIDModalOpen: true,
      });
    },
  });

  const [deleteMid, { loading: isDeletingMid }] = useMutation(DELETE_MID, {
    onCompleted: () => {
      setState({
        ...state,
        isConfirmDeleteMIDModalOpen: false,
        isSuccessDeleteMIDModalOpen: true,
        selectedMid: null,
      });
      refetch();
    },
    onError: () => {
      setState({
        ...state,
        isConfirmDeleteMIDModalOpen: false,
        isFailureDeleteMIDModalOpen: true,
      });
    },
  });

  const { addNotif } = useContext(NotificationContext);

  return (
    <>
      <Page>
        <Header withHome title="Merchant ID" path={['Merchant ID']} />
        <DataContainer>
          <DataTable
            loading={loading}
            minCellWidth={250}
            data={data && data.mids && data.mids.filteredData}
            headerOptions={
              <>
                <GlobalSearch
                  onSearch={filter => {
                    setState({
                      ...state,
                      pagination: {
                        ...state.pagination,
                        start: '',
                      },
                      filter,
                    });
                  }}
                  fields={[
                    {
                      label: 'Project Name',
                      name: 'name',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Bill Type',
                      name: 'billType',
                      type: FIELD_TYPES.SELECT,
                      options: BILL_TYPES,
                    },
                    {
                      label: 'MID',
                      name: 'merchantId',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Previous MID',
                      name: 'previousMerchantId',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Depository Bank Name',
                      name: 'depositoryBankName',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Depository Bank Account',
                      name: 'depositoryBankAccount',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Payment Type',
                      name: 'paymentType',
                      type: FIELD_TYPES.SELECT,
                      options: PAYMENT_TYPES,
                    },
                    {
                      label: 'Company',
                      name: 'company',
                      type: FIELD_TYPES.SELECT,
                      options: COMPANIES,
                    },
                    {
                      label: 'Merchant Discount Rate',
                      name: 'bankDiscount',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Withholding Tax',
                      name: 'withholdingTax',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Cost Center',
                      name: 'costCenter',
                      type: FIELD_TYPES.TEXT,
                    },
                  ]}
                />
                <div style={{ display: 'flex' }}>
                  {permissions.Mid.export && (
                    <ExportButton
                      icon="file-csv"
                      iconPosition="left"
                      disabled={loading}
                      onClick={() =>
                        setState({
                          ...state,
                          isConfirmDownloadMIDsModalOpen: true,
                        })
                      }
                    >
                      CSV
                    </ExportButton>
                  )}
                  {permissions.Mid.create && (
                    <CreateButton
                      icon="plus"
                      onClick={() => {
                        history.push('/addmid/');
                      }}
                    >
                      Add MID
                    </CreateButton>
                  )}
                </div>
              </>
            }
            config={{
              name: {
                headerLabel: 'Project Name',
                sortable: true,
                onClick: data => {
                  history.push('/mid/' + data.id);
                },
              },
              billType: {
                headerLabel: 'Bill Type',
                sortable: true,
                renderAs: data => {
                  let billTypeVal = BILL_TYPES.find(
                    billType => billType.value === data.billType
                  );
                  if (billTypeVal) {
                    return billTypeVal.label;
                  }

                  return 'N/A';
                },
              },
              company: {
                headerLabel: 'Company',
                sortable: true,
                renderAs: data =>
                  data.company
                    ? COMPANIES.find(company => company.value === data.company)
                        .label
                    : '',
              },
              merchantId: {
                headerLabel: 'MID',
                sortable: true,
              },
              previousMerchantId: {
                headerLabel: 'Previous MID',
                sortable: true,
              },
              depositoryBankName: {
                headerLabel: 'Depository Bank Name',
                sortable: true,
              },
              depositoryBankAccount: {
                headerLabel: 'Depository Bank Account',
                sortable: true,
              },
              paymentType: {
                headerLabel: 'Payment Type',
                sortable: true,
                renderAs: data => {
                  let paymentTypeVal = PAYMENT_TYPES.find(
                    paymentType => paymentType.value === data.paymentType
                  );
                  if (paymentTypeVal) {
                    return paymentTypeVal.label;
                  }

                  return 'N/A';
                },
              },
              bankTerm: {
                headerLabel: 'Bank Term',
                sortable: true,
              },
              bank: {
                headerLabel: 'Bank',
                sortable: true,
              },
              channelId: {
                headerLabel: 'Channel',
                sortable: true,
                renderAs: data => {
                  if (
                    data.channelId &&
                    !allChannelsLoading &&
                    allChannels &&
                    allChannels.channelsLoose
                  ) {
                    const channelData = allChannels.channelsLoose.find(
                      channel => channel.id === data.channelId
                    );

                    if (channelData) {
                      return channelData.name;
                    }
                  }
                  return '';
                },
              },
              bankDiscount: {
                headerLabel: 'Merchant Discount Rate',
                sortable: true,
                renderAs: data => `${data.bankDiscount} %`,
              },
              withholdingTax: {
                headerLabel: 'Withholding Tax',
                sortable: true,
                renderAs: data => `${data.withholdingTax} %`,
              },
              costCenter: {
                headerLabel: 'Cost Center',
                sortable: true,
              },
              actions: {
                renderAs: data => (
                  <ActionButtons
                    handleView={() => {
                      history.push('/mid/' + data.id);
                    }}
                    handleEdit={
                      permissions.Mid.update
                        ? () => {
                            history.push('/mid/' + data.id, {
                              isEditing: true,
                            });
                          }
                        : undefined
                    }
                    handleDelete={
                      permissions.Mid.delete
                        ? () => {
                            setState({
                              ...state,
                              selectedMid: data,
                              isConfirmDeleteMIDModalOpen: true,
                            });
                          }
                        : undefined
                    }
                  />
                ),
              },
            }}
            pagination={{
              ...state.pagination,
              count: data && data.mids ? data.mids.count : 0,
              cursors: data && data.mids ? data.mids.cursors : [],
              handleChange: pagination => {
                setState({ ...state, pagination });
              },
            }}
          />
        </DataContainer>
      </Page>

      <AlertModal
        isOpen={state.isConfirmDownloadMIDsModalOpen}
        title="Export Reports Alert"
        header="ARE YOU SURE?"
        variant="warn"
        icon="exclamation-circle"
        subHeader={
          <span>You are about to export all MID records as .CSV File.</span>
        }
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() =>
          setState({ ...state, isConfirmDownloadMIDsModalOpen: false })
        }
        handleCancel={() =>
          setState({ ...state, isConfirmDownloadMIDsModalOpen: false })
        }
        confirmText="Yes"
        handleConfirm={async () => {
          const notifTime = new Date().getTime();
          setState({ ...state, isConfirmDownloadMIDsModalOpen: false });
          addNotif({
            id: 'MID-' + notifTime,
            notifTime,
            type: 'info',
            title: 'Downloading File',
            message: 'Downloading All MID Records',
            isProgress: true,
            progressData: {
              progress: 0,
              isProgressive: false,
            },
            isLocal: true,
            data: {},
            recordDLParams: {
              query: GET_MID_ACCOUNTS,
              path: 'mids',
              variables: {
                filter: state.filter,
                pagination: {
                  start: '',
                  limit: 1000,
                },
              },
              onDownload: () => {
                // logExtract({
                //   variables: {
                //     data: {
                //       type: 'mids',
                //     },
                //   },
                // });
              },
              tableConfig: {
                name: {
                  headerLabel: 'Project Name',
                  sortable: true,
                },
                merchantId: {
                  headerLabel: 'MID',
                  sortable: true,
                },
                previousMerchantId: {
                  headerLabel: 'Previous MID',
                  sortable: true,
                },
                depositoryBankName: {
                  headerLabel: 'Depository Bank Name',
                  sortable: true,
                },
                depositoryBankAccount: {
                  headerLabel: 'Depository Bank Account',
                  sortable: true,
                },
                paymentType: {
                  headerLabel: 'Payment Type',
                  sortable: true,
                },
                bankTerm: {
                  headerLabel: 'Bank Term',
                  sortable: true,
                },
                bank: {
                  headerLabel: 'Bank',
                  sortable: true,
                },
                channelId: {
                  headerLabel: 'Channel',
                  sortable: true,
                  renderAs: data => {
                    if (
                      data.channelId &&
                      !allChannelsLoading &&
                      allChannels &&
                      allChannels.channelsLoose
                    ) {
                      const channelData = allChannels.channelsLoose.find(
                        channel => channel.id === data.channelId
                      );

                      if (channelData) {
                        return channelData.name;
                      }
                    }
                    return '';
                  },
                },
                billType: {
                  headerLabel: 'Bill Type',
                  sortable: true,
                },
                company: {
                  headerLabel: 'Company',
                  sortable: true,
                },
                bankDiscount: {
                  headerLabel: 'Merchant Discount Rate',
                  sortable: true,
                },
                withholdingTax: {
                  headerLabel: 'Withholding Tax',
                  sortable: true,
                },
                costCenter: {
                  headerLabel: 'Cost Center',
                  sortable: true,
                },
              },
              fileName: 'MID Records.csv',
            },
          });
        }}
      />

      <AlertModal
        isOpen={state.isConfirmAddMIDModalOpen}
        title="New MID Alert"
        icon="exclamation-circle"
        variant="warn"
        header="ARE YOU SURE?"
        subHeader="You are about to create a new MID."
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        confirmLoading={isAddingMid}
        confirmText="Yes"
        handleConfirm={() => {
          addMid({ variables: { data: sanitize(state.selectedMid) } });
        }}
        handleClose={() => {
          setState({
            ...state,
            isConfirmAddMIDModalOpen: false,
          });
        }}
      />
      <AlertModal
        isOpen={state.isSuccessAddMIDModalOpen}
        title="New MID Alert"
        handleClose={() => {
          setState({ ...state, isSuccessAddMIDModalOpen: false });
        }}
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader="MID has been created successfully."
        description="Editing of MID Account is now enabled."
        confirmText="Go to All MID"
        handleConfirm={() => {
          setState({ ...state, isSuccessAddMIDModalOpen: false });
        }}
      />
      <AlertModal
        isOpen={state.isFailureAddMIDModalOpen}
        title="New MID Alert"
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader={
          state.addMidError === 'MERCHANT_ID_ALREADY_EXISTS'
            ? 'MID already exists.'
            : state.addMidError === 'COMPANY_PAYMENT_TYPE_ALREADY_EXIST' ||
                state.addMidError ===
                  'COMPANY_PAYMENT_TYPE_BILL_TYPE_ALREADY_EXIST'
              ? `A record with a Company of ${state.selectedMid.company}, Bill Type of ${state.selectedMid.billType} and Payment Type of ${state.selectedMid.paymentType} already exists`
              : 'There was a problem on saving New MID.'
        }
        description="Please go back and try saving it again."
        handleClose={() =>
          setState({ ...state, isFailureAddMIDModalOpen: false })
        }
        confirmText="Go Back"
        handleConfirm={() => {
          setState({ ...state, isFailureAddMIDModalOpen: false });
        }}
      />

      <AlertModal
        isOpen={state.isSuccessDeleteMIDsModalOpen}
        title="Delete MID Alert"
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader="MID Account(s) have been deleted successfully."
        description="Deleted MID Accounts will be reflected immediately."
        handleClose={() =>
          setState({ ...state, isSuccessDeleteMIDsModalOpen: false })
        }
        confirmText="Go to All MID"
        handleConfirm={() =>
          setState({ ...state, isSuccessDeleteMIDsModalOpen: false })
        }
      />

      <AlertModal
        isOpen={state.isConfirmDeleteMIDModalOpen}
        title="Delete MID Alert"
        icon="exclamation-circle"
        variant="warn"
        header="ARE YOU SURE?"
        subHeader="You are about to delete MID."
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() =>
          setState({ ...state, isConfirmDeleteMIDModalOpen: false })
        }
        selectLabel="Reason"
        options={['No longer in use', 'Others'].map(reason => ({
          value: reason,
          label: reason,
        }))}
        confirmLoading={isDeletingMid}
        confirmText="Yes"
        handleConfirm={value => {
          deleteMid({
            variables: {
              data: { reasonToDelete: value },
              where: state.selectedMid.id,
            },
          });
        }}
      />
      <AlertModal
        isOpen={state.isSuccessDeleteMIDModalOpen}
        title="Delete MID Alert"
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader="MID has been deleted successfully."
        description="Deleted MID is now no longer in use."
        handleClose={() =>
          setState({ ...state, isSuccessDeleteMIDModalOpen: false })
        }
        confirmText="Go to All MID"
        handleConfirm={() =>
          setState({ ...state, isSuccessDeleteMIDModalOpen: false })
        }
      />
      <AlertModal
        isOpen={state.isFailureDeleteMIDModalOpen}
        title="Delete MID Alert"
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader="There was a problem on deleting MID."
        description="Please go back and try deleting again."
        handleClose={() =>
          setState({ ...state, isFailureDeleteMIDModalOpen: false })
        }
        confirmText="Go Back"
        handleConfirm={() => {
          setState({ ...state, isFailureDeleteMIDModalOpen: false });
        }}
      />

      <AlertModal
        isOpen={state.isLeavingPageWhileAdding}
        title="New MID Alert"
        icon="question-circle"
        variant="warn"
        header="SAVE ACCOUNT?"
        subHeader="You are about to leave without saving New MID."
        description="Your entry will be lost if you don't save it"
        handleClose={() =>
          setState({ ...state, isLeavingPageWhileAdding: false })
        }
        cancelText="Discard Entry"
        confirmText="Go Back"
        handleCancel={() => {
          if (state.nextLocation) {
            history.push(state.nextLocation);
          } else {
            setState({
              ...state,
              isLeavingPageWhileAdding: false,
              isAddMIDModalOpen: false,
            });
          }
        }}
        handleConfirm={() => {
          setState({ ...state, isLeavingPageWhileAdding: false });
        }}
      />
    </>
  );
};

MID.propTypes = {
  history: PropTypes.object,
};

export default MID;
