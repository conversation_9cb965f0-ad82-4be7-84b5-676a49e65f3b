import { gql } from '@apollo/client';

export const ADD_MID = gql`
  mutation createMid($data: CreateMidInput!) {
    createMid(data: $data) {
      id
      name
      previousMerchantId
      depositoryBankName
      depositoryBankAccount
      paymentType
      billType
      company
      bankDiscount
      withholdingTax
      merchantId
      installments {
        bankTerm
        bankMid
        bank
      }
      ada {
        enrollmentType
        adaMid
      }
      createdAt
      updatedAt
      businessUnit
    }
  }
`;

export const DELETE_MID = gql`
  mutation deleteMid($data: DeleteMidInput!, $where: String!) {
    deleteMid(data: $data, where: $where) {
      id
    }
  }
`;
