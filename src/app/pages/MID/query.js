import { gql } from '@apollo/client';

export const GET_MID_ACCOUNTS = gql`
  query getMids(
    $filter: SearchMidsFilterInput!
    $pagination: PaginationInput!
  ) {
    mids(filter: $filter, pagination: $pagination) {
      cursors
      count
      filteredData {
        id
        name
        billType
        company
        merchantId
        previousMerchantId
        depositoryBankName
        depositoryBankAccount
        paymentType
        bankTerm
        bank
        channelId
        bankDiscount
        withholdingTax
        costCenter
        createdAt
        updatedAt
      }
    }
  }
`;
