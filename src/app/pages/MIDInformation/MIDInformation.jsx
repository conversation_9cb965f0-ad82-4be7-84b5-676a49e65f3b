import PropTypes from 'prop-types';
import React, { useContext, useEffect, useMemo, useState } from 'react';
import * as Yup from 'yup';
import styled from 'styled-components';
import useForm from '../../hooks/useForm';
import sanitize from '../../utils/sanitize';
import { useQuery } from '@apollo/client';
import { useMutation } from '@apollo/client';
import Row from '../../components/Row';
import Page from '../../components/Page';
import Loader from '../../components/Loader';
import Header from '../../components/Header';
import { AlertModal } from '../../components/Modal';
import DataHeader from '../../components/DataHeader';
import FormField from '../../components/Form/FormField';
import NotFound from '../../components/NotFound/NotFound';
import DataContainer from '../../components/DataContainer';
import { FIELD_TYPES } from '../../components/Form/constants';
import ResponsiveContext from '../../context/ResponsiveContext';
import PrimaryButton from '../../components/Button/PrimaryButton';
import SecondaryButton from '../../components/Button/SecondaryButton';
import {
  ButtonsContainer,
  PageSubsection,
  SubsectionTitle,
} from '../../components/InformationPage';
import { EDIT_MID } from './mutation';
import { GET_MID_INFORMATION, GET_CHANNEL_BILLTYPE } from './query';
import {
  PAYMENT_TYPES_BILL,
  PAYMENT_TYPES_NONBILL,
  COMPANIES,
  BILL_TYPES,
} from '../MID/MID';
import { GET_BANKLIST } from '../Installment/query';
import { GET_CHANNEL_OPTIONS } from '../Reports/TransactionLogs/query';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

const ActionFields = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;

  width: ${props => {
    const base = 100 / props.perRow;
    const margin = 20;
    return `calc(${base}% - ${margin}px)`;
  }};

  flex: 1;

  margin-left: ${props => (props.isMobile ? '0px' : '20px')};
  margin-bottom: 20px;
`;

const ActionIcon = styled(FontAwesomeIcon)`
  color: ${props => (props.disabled ? 'gray' : props.color)};
  font-size: 20px;
  cursor: pointer;

  margin-left: 10px;
  &:first-child {
    margin-left: 0;
  }
`;

const ActionButton = styled.button`
  background-color: ${props =>
    props.disabled ? 'gray' : props.backgroundColor};
  color: #fff;
  font-size: ${props => props.theme.fontSize.s};
  cursor: pointer;
  flex: 1;
  border-radius: 5px;
`;

const TermsOptions = [
  { value: null, label: 'None' },
  { value: '3', label: '3' },
  { value: '6', label: '6' },
  { value: '9', label: '9' },
  { value: '12', label: '12' },
  { value: '18', label: '18' },
  { value: '24', label: '24' },
  { value: '36', label: '36' },
];

const EnrollmentTypeOptions = [
  { value: null, label: 'None' },
  { value: 'Straight', label: 'Straight (via Payment)' },
  { value: 'PreAuth', label: 'Pre Auth (via Channel Module)' },
];

const MIDInformation = ({ location, history, match }) => {
  const { isMobile } = useContext(ResponsiveContext);

  const [state, setState] = useState({
    isEditing: false,
    isLeavingWhileEditing: false,

    isSuccessEditMIDModalOpen: false,
    isFailureEditMIDModalOpen: false,
    isConfirmEditMIDModalOpen: false,

    selectedMid: null,
    nextLocation: null,
    editMidError: null,

    id: match.params.id,
  });

  const { data, loading } = useQuery(GET_MID_INFORMATION, {
    variables: {
      where: state.id,
    },
    fetchPolicy: 'network-only',
  });

  const { data: allChannels, loading: allChannelsLoading } = useQuery(
    GET_CHANNEL_OPTIONS,
    {
      fetchPolicy: 'network-only',
    }
  );

  const { data: bankList, loading: isLoadingBanks } = useQuery(GET_BANKLIST, {
    fetchPolicy: 'network-only',
  });

  const bankOptions =
    !isLoadingBanks && bankList
      ? bankList.listInstallmentBank.map(bank => ({
          value: bank,
          label: bank,
        }))
      : [];

  bankOptions.unshift({
    value: null,
    label: 'None',
  });

  const [channelOptions, setChannelOptions] = useState([]);

  const [selectedInstallment, setselectedInstallment] = useState({
    bankTerm: '',
    bank: '',
    index: '',
  });

  const [newInstallments, setNewInstallments] = useState([
    {
      bank: null,
      bankTerm: null,
      bankMid: null,
    },
  ]);

  const [newInstallmentErrors, setnewInstallmentErrors] = useState([{}]);

  const [installmentErrors, setInstallmentErrors] = useState({
    bankMid: {},
  });

  const [selectedAda, setSelectedAda] = useState({
    adaMid: '',
    enrollmentType: '',
    index: '',
  });

  const [newAda, setNewAda] = useState([
    {
      enrollmentType: null,
      adaMid: null,
    },
  ]);

  const [newAdaErrors, setNewAdaErrors] = useState([{}]);

  const [adaErrors, setAdaErrors] = useState({
    adaMid: {},
  });

  let addNewInstallments = newInstallments.filter(
    newValue => newValue.bank && newValue.bankTerm && newValue.bankMid
  );

  let addNewAda = newAda.filter(
    newValue => newValue.enrollmentType && newValue.adaMid
  );

  // initialize isEditing
  // this takes affect when user clicks on edit button from table pages
  useEffect(() => {
    if (location.state && location.state.isEditing !== undefined) {
      setState({ ...state, isEditing: location.state.isEditing });
    }
  }, []);

  useEffect(() => {
    if (selectedInstallment.bankTerm && selectedInstallment.bank) {
      onChange.installments(
        values.installments.filter((bank, index) => {
          return index !== selectedInstallment.index;
        })
      );

      setselectedInstallment({ bankTerm: '', bank: '' });
    }
  });

  useEffect(() => {
    if (selectedAda.enrollmentType && selectedAda.adaMid) {
      onChange.ada(
        values.ada.filter((enrollmentType, index) => {
          return index !== selectedAda.index;
        })
      );

      setSelectedAda({ enrollmentType: '', adaMid: '' });
    }
  });

  useEffect(() => {
    const unblock = history.block(location => {
      if (state.isLeavingWhileEditing || !state.isEditing) return true;
      setState({
        ...state,
        nextLocation: location,
        isLeavingWhileEditing: true,
      });
      return false;
    });

    return () => {
      unblock();
    };
  }, [state.isLeavingWhileEditing, state.isEditing]);

  const [editMid, { loading: isEditingMid }] = useMutation(EDIT_MID, {
    onCompleted: () => {
      setNewInstallments([
        {
          bank: null,
          bankTerm: null,
          bankMid: null,
        },
      ]);
      setNewAda([
        {
          enrollmentType: null,
          adaMid: null,
        },
      ]);
      setNewAdaErrors([{}]);
      setnewInstallmentErrors([{}]);
    },
    onError: err => {
      setState({
        ...state,
        editMidError: err.networkError.result
          ? err.networkError.result.message
          : null,
        isConfirmEditMIDModalOpen: false,
        isFailureEditMIDModalOpen: true,
      });
    },
  });

  // eslint-disable-next-line
  const { fields, initialValue } = useMemo(() => {
    const fields = {
      name: {
        validation: Yup.string()
          .max(50, 'Must not exceed 50 characters')
          .required('Please enter a value')
          .matches(/[^-\s]/, 'Must not be a whitespace')
          .matches(
            /^(?![=,@,+,-])(.+)$/,
            'Input must not begin with this special characters (=,@,+,-)'
          ),
      },
      billType: {
        validation: Yup.string().required('Please enter a value'),
      },
      company: {
        validation: value =>
          value && value.billType === BILL_TYPES[0].value
            ? Yup.string()
                .max(100, 'Must not exceed 100 characters')
                .required('Please enter a value')
            : Yup.string().nullable(),
      },
      channelId: {
        validation: value =>
          value && value.billType === BILL_TYPES[1].value
            ? Yup.string()
                .max(100, 'Must not exceed 100 characters')
                .required('Please enter a value')
            : Yup.string().nullable(),
      },
      depositoryBankName: {
        validation: Yup.string()
          .max(50, 'Must not exceed 50 characters')
          .matches(/[^-\s]/, 'Must not be a whitespace')
          .matches(
            /^(?![=,@,+,-])(.+)$/,
            'Input must not begin with this special characters (=,@,+,-)'
          )
          .required('Please enter a value'),
      },
      merchantId: {
        validation: value =>
          value &&
          (value.paymentType === PAYMENT_TYPES_BILL[0].value ||
            value.paymentType === PAYMENT_TYPES_NONBILL[0].value)
            ? Yup.string()
                .min(1, 'Minimum should be 1 character')
                .max(50, 'Must not exceed 50 characters')
                .matches(/[^-\s]/, 'Must not be a whitespace')
                .matches(
                  /^(?![=,@,+,-])(.+)$/,
                  'Input must not begin with this special characters (=,@,+,-)'
                )
                .matches(/^[0-9a-zA-Z-]*$/, 'Must be alphanumeric and dashes')
                .required('Please enter a value')
                .nullable()
            : Yup.string().nullable(),
      },
      previousMerchantId: {
        validation: Yup.string()
          .min(1, 'Minimum should be 1 character')
          .max(50, 'Must not exceed 50 characters')
          .matches(
            /^(?![=,@,+,-])(.+)$/,
            'Input must not begin with this special characters (=,@,+,-)'
          )
          .matches(/^[0-9a-zA-Z-]*$/, 'Must be alphanumeric and dashes')
          .nullable(),
      },
      depositoryBankAccount: {
        validation: Yup.string()
          .min(1, 'Minimum should be 1 character')
          .max(50, 'Must not exceed 50 characters')
          .matches(
            /^(?![=,@,+,-])(.+)$/,
            'Input must not begin with this special characters (=,@,+,-)'
          )
          .matches(/^[0-9-]+$/, 'Allowed characters are numbers and dashes')
          .required('Please enter a value'),
      },
      withholdingTax: {
        validation: Yup.string()
          .min(1, 'Minimum should be 1 character')
          .max(5, 'Must not exceed 5 characters')
          .matches(
            /^\d*(\.\d+)?$/,
            'Invalid input! Allowed characters are positive number or number with decimal placed only'
          )
          .required('Please enter a value'),
      },
      bankDiscount: {
        validation: Yup.string()
          .min(1, 'Minimum should be 1 character')
          .max(5, 'Must not exceed 5 characters')
          .matches(
            /^\d*(\.\d+)?$/,
            'Invalid input! Allowed characters are positive number or number with decimal placed only'
          )
          .required('Please enter a value'),
      },
      paymentType: {
        validation: Yup.string().required('Please enter a value'),
      },
      costCenter: {
        validation: Yup.string()
          .min(1, 'Minimum should be 1 character')
          .max(50, 'Must not exceed 100 characters')
          .required('Please enter a value')
          .matches(
            /^(?![=,@,+,-])(.+)$/,
            'Input must not begin with this special characters (=,@,+,-)'
          )
          .matches(/^[0-9a-zA-Z-]*$/, 'Must be alphanumeric or dashes'),
      },
      businessUnit: {
        validation: Yup.string()
          .min(1, 'Minimum should be 1 character')
          .max(50, 'Must not exceed 50 characters')
          .required('Please enter a value')
          .matches(
            /^(?![=,@,+,-])(.+)$/,
            'Input must not begin with this special characters (=,@,+,-)'
          )
          .matches(/[^-\s]/, 'Must not be a whitespace'),
      },
      installments: {},
      ada: {},
    };

    const initialValue = {};
    if (!loading && data && data.mid) {
      for (const name of Object.keys(fields)) {
        if (Object.prototype.hasOwnProperty.call(data.mid, name)) {
          fields[name].initialValue = data.mid[name];
        }
        initialValue[name] = fields[name].initialValue;
      }
    }

    return { fields, initialValue };
  }, [data]);

  const { values, onChange, onBlur, errors, onSubmit } = useForm(
    fields,
    values => {
      setState({
        ...state,
        isConfirmEditMIDModalOpen: true,
        selectedMid: values,
      });
    }
  );

  // To get values of bill type when changing
  const { data: dataChannel, loading: dataChannelLoading } = useQuery(
    GET_CHANNEL_BILLTYPE,
    {
      variables: {
        billType: values.billType,
      },
      skip: !values.billType,
      fetchPolicy: 'network-only',
    }
  );

  useEffect(() => {
    if (
      !dataChannelLoading &&
      dataChannel &&
      !allChannelsLoading &&
      allChannels &&
      !loading &&
      data
    ) {
      let { mid } = data;
      let channelOptions =
        dataChannel &&
        dataChannel.channelsBillType
          .filter(channel => channel !== null)
          .map(channel => {
            return {
              label: channel.name,
              value: channel.id,
            };
          });

      if (mid.billType === BILL_TYPES[1].value) {
        if (mid.channelId) {
          let midChannel = allChannels.channelsLoose.find(
            channel => channel.id === mid.channelId
          );

          if (midChannel) {
            channelOptions.push({
              label: midChannel.name,
              value: midChannel.id,
            });
          }
        }
      }

      setChannelOptions(channelOptions);
    }
  }, [dataChannel, allChannels, data]);

  const backButton = (
    <SecondaryButton
      onClick={() => {
        history.push('/mid');
      }}
    >
      Back to All MID
    </SecondaryButton>
  );

  const editButtonGroup = (
    <Row>
      {!state.isEditing && (
        <PrimaryButton
          icon="pen"
          onClick={() => setState({ ...state, isEditing: true })}
        >
          Edit
        </PrimaryButton>
      )}
      <PrimaryButton
        icon="save"
        disabled={
          !state.isEditing ||
          Object.keys(installmentErrors.bankMid).length > 0 ||
          Object.keys(adaErrors.adaMid).length > 0 ||
          newInstallmentErrors.find(
            newError =>
              newError.bank ||
              newError.bankTerm ||
              (newError.bankMid &&
                newError.bankMid !==
                  'MID is already existing. Are you sure want to use it?' &&
                newError.bankMid !==
                  'Duplicate MID. Are you sure want to use it?')
          ) ||
          newInstallments.find(
            newInstallment =>
              newInstallment.bankMid &&
              newInstallment.bank === null &&
              newInstallment.bankTerm
          ) ||
          newAdaErrors.find(
            newError =>
              newError.enrollmentType ||
              (newError.adaMid &&
                newError.adaMid !==
                  'MID is already existing. Are you sure want to use it?')
          ) ||
          newAda.find(
            newAdaData =>
              newAdaData.adaMid && newAdaData.enrollmentType === null
          )
        }
        onClick={() => {
          onSubmit();
        }}
      >
        Save
      </PrimaryButton>
    </Row>
  );

  const [paymentTypeOptions, setPaymentTypeOptions] = useState([]);

  useEffect(() => {
    const paymentTypeOptions =
      values.billType === 'Bill' ? PAYMENT_TYPES_BILL : PAYMENT_TYPES_NONBILL;
    if (
      !paymentTypeOptions.find(option => option.value === values.paymentType)
    ) {
      values.paymentType = '';
    }

    setPaymentTypeOptions(paymentTypeOptions);
  }, [values.billType]);

  function handleBankMidValidation(event) {
    const { target } = event;
    Yup.string()
      .matches(/[^-\s]/, 'Must not be a whitespace')
      .matches(
        /^(?![=,@,+,-])(.+)$/,
        'Input must not begin with this special characters (=,@,+,-)'
      )
      .matches(/^[0-9a-zA-Z-]+$/, 'Must be alphanumeric and dashes')
      .required('Please enter value')
      .test(
        'MID Checker',
        'MID is already existing. Are you sure want to use it?',
        value => {
          const exist =
            values &&
            values.installments &&
            values.installments.find(data => value === data.bankMid);
          if (exist) {
            return false;
          }
          return true;
        }
      )
      .min(3, 'Must not less than 3 characters')
      .max(50, 'Must not exceed 50 characters')
      .validate(target.value)
      .then(() => {
        if (installmentErrors.bankMid[target.name]) {
          const errorsID = installmentErrors.bankMid;
          delete errorsID[target.name];
          setInstallmentErrors({
            ...installmentErrors,
            bankMid: errorsID,
          });
        }
      })
      .catch(err => {
        if (err.errors.length) {
          setInstallmentErrors({
            ...installmentErrors,
            bankMid: {
              ...installmentErrors.bankMid,
              [target.name]: err.errors[0],
            },
          });
        }
      });
  }

  function handleAdaMidValidation(event) {
    const { target } = event;
    Yup.string()
      .matches(/[^-\s]/, 'Must not be a whitespace')
      .matches(
        /^(?![=,@,+,-])(.+)$/,
        'Input must not begin with this special characters (=,@,+,-)'
      )
      .matches(/^[0-9a-zA-Z-]+$/, 'Must be alphanumeric and dashes')
      .required('Please enter value')
      .test(
        'Ada Checker',
        'MID is already existing. Are you sure want to use it?',
        value => {
          const exist =
            values &&
            values.ada &&
            values.ada.find(data => value === data.adaMid);
          if (exist) {
            return false;
          }
          return true;
        }
      )
      .min(3, 'Must not less than 3 characters')
      .max(50, 'Must not exceed 50 characters')
      .validate(target.value)
      .then(() => {
        if (adaErrors.adaMid[target.name]) {
          const errorsID = adaErrors.adaMid;
          delete errorsID[target.name];
          setAdaErrors({
            ...adaErrors,
            adaMid: errorsID,
          });
        }
      })
      .catch(err => {
        if (err.errors.length) {
          setAdaErrors({
            ...adaErrors,
            adaMid: {
              ...adaErrors.adaMid,
              [target.name]: err.errors[0],
            },
          });
        }
      });
  }

  function handleNewBankTermValidation(event, bank, index) {
    const existsInCurrent = values.installments.find(
      value => value.bankTerm === String(event) && value.bank === String(bank)
    );

    const existsInNew = newInstallments.find(
      value => value.bankTerm === event && value.bank === bank
    );

    let newErrors = newInstallmentErrors;

    if (existsInCurrent) {
      setnewInstallmentErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              bankTerm: 'Bank Term already exists',
            };
          }

          return val;
        })
      );

      return;
    }

    if (existsInNew) {
      setnewInstallmentErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              bankTerm: 'Duplicate term on existing bank',
            };
          }

          return val;
        })
      );

      return;
    }

    if (event === null) {
      setnewInstallmentErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              bankTerm: 'Please select a term',
            };
          }

          return val;
        })
      );

      return;
    }

    Yup.string()
      .nullable()
      .validate(event)
      .then(() => {
        if (newInstallmentErrors[index].bankTerm) {
          const installmentError = newInstallmentErrors.map((val, valIndex) => {
            if (valIndex === index) {
              delete val.bankTerm;
            }

            return val;
          });

          setnewInstallmentErrors(installmentError);
        }
      })
      .catch(err => {
        if (err.errors) {
          const installmentError = newInstallmentErrors.map((val, valIndex) => {
            if (valIndex === index) {
              return {
                ...val,
                bankTerm: err.errors[0],
              };
            }

            return val;
          });

          setnewInstallmentErrors(installmentError);
        }
      });
  }

  function handleNewBankMidValidation(event, index) {
    const { target } = event;
    Yup.string()
      .matches(/[^-\s]/, 'Must not be a whitespace')
      .matches(
        /^(?![=,@,+,-])(.+)$/,
        'Input must not begin with this special characters (=,@,+,-)'
      )
      .matches(/^[0-9a-zA-Z-]+$/, 'Must be alphanumeric and dashes')
      .required('Please enter value')
      .test(
        'MID Checker',
        'MID is already existing. Are you sure want to use it?',
        value => {
          const exist =
            values &&
            values.installments &&
            values.installments.find(data => value === data.bankMid);
          if (exist) {
            return false;
          }
          return true;
        }
      )
      .test(
        'MID Checker',
        'Duplicate MID. Are you sure want to use it?',
        value => {
          const exist = newInstallments.find(
            (data, valIndex) => value === data.bankMid && valIndex !== index
          );
          if (exist) {
            return false;
          }
          return true;
        }
      )
      .min(3, 'Must not less than 3 characters')
      .max(50, 'Must not exceed 50 characters')
      .validate(target.value)
      .then(() => {
        if (newInstallmentErrors[index].bankMid) {
          const installmentError = newInstallmentErrors.map((val, valIndex) => {
            if (valIndex === index) {
              delete val.bankMid;
            }

            return val;
          });

          setnewInstallmentErrors(installmentError);
        }
      })
      .catch(err => {
        if (err.errors.length) {
          const installmentError = newInstallmentErrors.map((val, valIndex) => {
            if (valIndex === index) {
              return {
                ...val,
                bankMid: err.errors[0],
              };
            }

            return val;
          });

          setnewInstallmentErrors(installmentError);
        }
      });
  }

  function handleNewAdaMidValidation(event, index) {
    const { target } = event;
    Yup.string()
      .matches(/[^-\s]/, 'Must not be a whitespace')
      .matches(
        /^(?![=,@,+,-])(.+)$/,
        'Input must not begin with this special characters (=,@,+,-)'
      )
      .matches(/^[0-9a-zA-Z-]+$/, 'Must be alphanumeric and dashes')
      .required('Please enter value')
      .test(
        'ADA Checker',
        'MID is already existing. Are you sure want to use it?',
        value => {
          const exist =
            values &&
            values.ada &&
            values.ada.find(data => value === data.adaMid);
          if (exist) {
            return false;
          }
          return true;
        }
      )
      .min(3, 'Must not less than 3 characters')
      .max(50, 'Must not exceed 50 characters')
      .validate(target.value)
      .then(() => {
        if (newAdaErrors[index].adaMid) {
          const adaError = newAdaErrors.map((val, valIndex) => {
            if (valIndex === index) {
              delete val.adaMid;
            }

            return val;
          });

          setNewAdaErrors(adaError);
        }
      })
      .catch(err => {
        if (err.errors.length) {
          const adaError = newAdaErrors.map((val, valIndex) => {
            if (valIndex === index) {
              return {
                ...val,
                adaMid: err.errors[0],
              };
            }

            return val;
          });

          setNewAdaErrors(adaError);
        }
      });
  }

  function handleNewEnrollmentTypeValidation(event, index) {
    const existsInCurrent = values.ada.find(
      value => value.enrollmentType === event
    );
    const existsInNew = newAda.find(
      (value, valIndex) => value.enrollmentType === event && valIndex !== index
    );

    let newErrors = newAdaErrors;

    if (existsInCurrent) {
      setNewAdaErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              enrollmentType: 'Enrollment Type already exists',
            };
          }

          return val;
        })
      );

      return;
    }

    if (existsInNew) {
      setNewAdaErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              enrollmentType: 'Duplicate enrollment type',
            };
          }

          return val;
        })
      );

      return;
    }

    Yup.string()
      .nullable()
      .validate(event)
      .then(() => {
        if (newAdaErrors[index].enrollmentType) {
          const adaError = newAdaErrors.map((val, valIndex) => {
            if (valIndex === index) {
              delete val.enrollmentType;
            }

            return val;
          });

          setNewAdaErrors(adaError);
        }
      })
      .catch(err => {
        if (err.errors) {
          const adaError = newAdaErrors.map((val, valIndex) => {
            if (valIndex === index) {
              return {
                ...val,
                enrollmentType: err.errors[0],
              };
            }

            return val;
          });

          setNewAdaErrors(adaError);
        }
      });
  }

  return (
    <>
      <Page>
        <Header
          withHome
          title={data && data.mid ? data.mid.name : ''}
          path={['Merchant', data && data.mid ? data.mid.id : '']}
        />
        <DataContainer loading={loading}>
          {loading && <Loader fullPage />}
          {!loading && !data.mid && <NotFound />}
          {!loading && data.mid && (
            <>
              <DataHeader>
                <DataHeader.Title>Merchant Information</DataHeader.Title>
              </DataHeader>
              <SubsectionTitle>GENERAL</SubsectionTitle>
              <PageSubsection>
                <FormField
                  label="Project Name"
                  name="name"
                  type={FIELD_TYPES.TEXT}
                  value={values.name}
                  onChange={onChange.name}
                  onBlur={onBlur.name}
                  error={errors.name}
                  readOnly={!state.isEditing}
                  perRow={2}
                  required
                />

                <FormField
                  label="Bill Type"
                  name="billType"
                  type={FIELD_TYPES.SELECT}
                  options={BILL_TYPES}
                  value={values.billType}
                  onChange={onChange.billType}
                  onBlur={onBlur.billType}
                  error={errors.billType}
                  readOnly={!state.isEditing}
                  perRow={2}
                  required
                />

                <FormField
                  label="Company"
                  name="company"
                  placeholder="Company"
                  type={FIELD_TYPES.SELECT}
                  options={COMPANIES}
                  value={values.company}
                  onChange={onChange.company}
                  onBlur={onBlur.company}
                  error={errors.company}
                  readOnly={!state.isEditing}
                  perRow={2}
                  required={values.billType === BILL_TYPES[0].value}
                />

                <FormField
                  label="Channel"
                  name="channelId"
                  placeholder="Channel"
                  type={FIELD_TYPES.SELECT}
                  options={channelOptions}
                  value={values.channelId}
                  onChange={onChange.channelId}
                  onBlur={onBlur.channelId}
                  error={errors.channelId}
                  readOnly={!state.isEditing}
                  perRow={2}
                  required={values.billType === BILL_TYPES[1].value}
                />
                <FormField
                  label="Payment Type"
                  name="paymentType"
                  type={FIELD_TYPES.SELECT}
                  options={paymentTypeOptions}
                  value={values.paymentType}
                  onChange={onChange.paymentType}
                  onBlur={onBlur.paymentType}
                  error={errors.paymentType}
                  readOnly={!state.isEditing}
                  perRow={2}
                  required
                />
                <FormField
                  label="Cost Center"
                  name="costCenter"
                  type={FIELD_TYPES.TEXT}
                  value={values.costCenter}
                  onChange={onChange.costCenter}
                  onBlur={onBlur.costCenter}
                  error={errors.costCenter}
                  readOnly={!state.isEditing}
                  perRow={2}
                  required
                />
                <FormField
                  label="MID"
                  name="merchantId"
                  type={FIELD_TYPES.TEXT}
                  value={values.merchantId}
                  onChange={onChange.merchantId}
                  onBlur={onBlur.merchantId}
                  error={errors.merchantId}
                  readOnly={
                    values.paymentType === 'Straight' && state.isEditing
                      ? false
                      : true
                  }
                  perRow={2}
                  required={
                    values.paymentType === 'Straight' && state.isEditing
                      ? true
                      : false
                  }
                />
                <FormField
                  label="Previous MID"
                  name="previousMerchantId"
                  type={FIELD_TYPES.TEXT}
                  value={values.previousMerchantId}
                  onChange={onChange.previousMerchantId}
                  onBlur={onBlur.previousMerchantId}
                  error={errors.previousMerchantId}
                  readOnly={!state.isEditing}
                  perRow={2}
                />
                <FormField
                  label="Depository Bank Name"
                  name="depositoryBankName"
                  type={FIELD_TYPES.TEXT}
                  value={values.depositoryBankName}
                  onChange={onChange.depositoryBankName}
                  onBlur={onBlur.depositoryBankName}
                  error={errors.depositoryBankName}
                  readOnly={!state.isEditing}
                  perRow={2}
                  required
                />

                <FormField
                  label="Depository Bank Account"
                  name="depositoryBankAccount"
                  type={FIELD_TYPES.TEXT}
                  value={values.depositoryBankAccount}
                  onChange={onChange.depositoryBankAccount}
                  onBlur={onBlur.depositoryBankAccount}
                  error={errors.depositoryBankAccount}
                  readOnly={!state.isEditing}
                  perRow={2}
                  required
                />

                <FormField
                  label="Withholding Tax"
                  name="withholdingTax"
                  type={FIELD_TYPES.TEXT}
                  value={values.withholdingTax}
                  onChange={onChange.withholdingTax}
                  onBlur={onBlur.withholdingTax}
                  error={errors.withholdingTax}
                  readOnly={!state.isEditing}
                  perRow={2}
                  required
                  isPercent
                />

                <FormField
                  label="Merchant Discount Rate"
                  name="bankDiscount"
                  type={FIELD_TYPES.TEXT}
                  value={values.bankDiscount}
                  onChange={onChange.bankDiscount}
                  onBlur={onBlur.bankDiscount}
                  error={errors.bankDiscount}
                  readOnly={!state.isEditing}
                  perRow={2}
                  required
                  isPercent
                />

                <FormField
                  label="Business Unit"
                  name="businessUnit"
                  type={FIELD_TYPES.TEXT}
                  value={values.businessUnit}
                  onChange={onChange.businessUnit}
                  onBlur={onBlur.businessUnit}
                  error={errors.businessUnit}
                  readOnly={!state.isEditing}
                  perRow={2}
                  required
                />
              </PageSubsection>

              {values.paymentType === 'Installment' && (
                <SubsectionTitle>INSTALLMENT MID</SubsectionTitle>
              )}
              {values.paymentType === 'Installment' &&
                !loading &&
                values &&
                values.installments &&
                values.installments.map((installment, index) => (
                  <PageSubsection key={installment.bank + '-' + index}>
                    <FormField
                      placeholder=""
                      label={index === 0 ? 'Bank' : ''}
                      name={`bank-${installment.bank}`}
                      type={FIELD_TYPES.TEXT}
                      value={installment.bank}
                      readOnly={!state.isEditing || state.isEditing}
                      perRow={3.5}
                      required
                    />
                    <FormField
                      placeholder=""
                      label={index === 0 ? 'Bank Term' : ''}
                      name={`bank-${installment.bankTerm}`}
                      type={FIELD_TYPES.TEXT}
                      value={installment.bankTerm}
                      readOnly={!state.isEditing || state.isEditing}
                      perRow={3.5}
                      required
                    />
                    <FormField
                      placeholder="Bank MID"
                      label={index === 0 ? 'Bank MID' : ''}
                      name={`bankMid-${installment.bankTerm}-${installment.bank}`}
                      type={FIELD_TYPES.TEXT}
                      value={installment.bankMid}
                      onChange={event => {
                        handleBankMidValidation(event);
                        onChange.installments(
                          values.installments.map(installmentIndex => {
                            if (
                              installmentIndex.bankTerm ===
                                installment.bankTerm &&
                              installmentIndex.bank === installment.bank
                            ) {
                              return {
                                bank: installment.bank,
                                bankTerm: installment.bankTerm,
                                bankMid: event.target.value,
                              };
                            }

                            return installmentIndex;
                          })
                        );
                      }}
                      error={
                        installmentErrors.bankMid[
                          `bankMid-${installment.bankTerm}-${installment.bank}`
                        ]
                      }
                      readOnly={!state.isEditing}
                      perRow={3.5}
                      required
                    />
                    {state.isEditing ? (
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      >
                        {isMobile ? (
                          <ActionButton
                            backgroundColor={'red'}
                            onClick={() => {
                              setselectedInstallment({
                                bank: installment.bank,
                                bankTerm: installment.bankTerm,
                                index: index,
                              });
                            }}
                          >
                            Delete
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon={'minus-circle'}
                            color={'red'}
                            onClick={() => {
                              setselectedInstallment({
                                bank: installment.bank,
                                bankTerm: installment.bankTerm,
                                index: index,
                              });
                            }}
                          />
                        )}
                      </ActionFields>
                    ) : (
                      ''
                    )}
                  </PageSubsection>
                ))}
              {state.isEditing &&
                values.paymentType === 'Installment' &&
                newInstallments.map((newInstallment, index) => {
                  return (
                    <PageSubsection key={index}>
                      <FormField
                        placeholder="Bank"
                        label={values.installments.length < 1 ? 'Bank' : ''}
                        name={`newInstallment-${index}`}
                        type={FIELD_TYPES.SELECT}
                        value={newInstallment.bank}
                        options={bankOptions}
                        onChange={event => {
                          let newValue = newInstallments.map(
                            (newValue, newIndex) => {
                              if (newIndex === index) {
                                return {
                                  bank: event,
                                  bankTerm: null,
                                  bankMid: newInstallment.bankMid,
                                };
                              }
                              return newValue;
                            }
                          );

                          setNewInstallments(newValue);
                        }}
                        error={newInstallmentErrors[index].bank}
                        perRow={3.5}
                        readOnly={!state.isEditing}
                        required
                      />
                      <FormField
                        placeholder="Bank Term"
                        label={
                          values.installments.length < 1 ? 'Bank Term' : ''
                        }
                        name={`newInstallment-${index}`}
                        type={FIELD_TYPES.SELECT}
                        value={newInstallment.bankTerm}
                        options={TermsOptions}
                        onChange={event => {
                          let newValue = newInstallments.map(
                            (newValue, newIndex) => {
                              if (newIndex === index) {
                                return {
                                  bank: newInstallment.bank,
                                  bankTerm: event,
                                  bankMid: newInstallment.bankMid,
                                };
                              }
                              return newValue;
                            }
                          );

                          setNewInstallments(newValue);
                          handleNewBankTermValidation(
                            event,
                            newInstallment.bank,
                            index
                          );
                        }}
                        onBlur={event =>
                          handleNewBankTermValidation(event, index)
                        }
                        error={newInstallmentErrors[index].bankTerm}
                        perRow={3.5}
                        readOnly={
                          !state.isEditing || newInstallment.bank === null
                        }
                        required
                      />
                      <FormField
                        placeholder="Bank MID"
                        label={values.installments.length < 1 ? 'Bank MID' : ''}
                        name={`installment-${index}`}
                        type={FIELD_TYPES.TEXT}
                        value={newInstallment.bankMid}
                        onChange={event => {
                          let newValue = newInstallments.map(
                            (newValue, newIndex) => {
                              if (newIndex === index) {
                                return {
                                  bank: newInstallment.bank,
                                  bankTerm: newInstallment.bankTerm,
                                  bankMid: event.target.value,
                                };
                              }
                              return newValue;
                            }
                          );

                          setNewInstallments(newValue);
                          handleNewBankMidValidation(event, index);
                        }}
                        onBlur={event =>
                          handleNewBankMidValidation(event, index)
                        }
                        error={newInstallmentErrors[index].bankMid}
                        perRow={3.5}
                        readOnly={!state.isEditing}
                        required
                      />
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      >
                        {!(
                          newInstallmentErrors[index].bank ||
                          newInstallmentErrors[index].bankTerm ||
                          (newInstallmentErrors[index].bankMid &&
                            newInstallmentErrors[index].bankMid !==
                              'MID is already existing. Are you sure want to use it?' &&
                            newInstallmentErrors[index].bankMid &&
                            newInstallmentErrors[index].bankMid !==
                              'Duplicate MID. Are you sure want to use it?')
                        ) &&
                          newInstallment.bank &&
                          newInstallment.bankTerm &&
                          newInstallment.bankMid &&
                          index === newInstallments.length - 1 &&
                          (isMobile ? (
                            <ActionButton
                              backgroundColor="green"
                              onClick={() => {
                                setNewInstallments([
                                  ...newInstallments,
                                  {
                                    bank: null,
                                    bankTerm: null,
                                    bankMid: null,
                                  },
                                ]);
                                setnewInstallmentErrors([
                                  ...newInstallmentErrors,
                                  {},
                                ]);
                              }}
                            >
                              Add
                            </ActionButton>
                          ) : (
                            <ActionIcon
                              icon="plus-circle"
                              color="green"
                              onClick={() => {
                                setNewInstallments([
                                  ...newInstallments,
                                  {
                                    bank: null,
                                    bankTerm: null,
                                    bankMid: null,
                                  },
                                ]);
                                setnewInstallmentErrors([
                                  ...newInstallmentErrors,
                                  {},
                                ]);
                              }}
                            />
                          ))}
                        {isMobile ? (
                          <ActionButton
                            backgroundColor="red"
                            onClick={() => {
                              let newValue = newInstallments.filter(
                                (installment, installmentIndex) => {
                                  return installmentIndex !== index;
                                }
                              );

                              let newErrors = newInstallmentErrors.filter(
                                (installment, installmentIndex) => {
                                  return installmentIndex !== index;
                                }
                              );

                              if (newValue.length === 0) {
                                newValue.push({
                                  bank: null,
                                  bankTerm: null,
                                  bankMid: null,
                                });
                                newErrors.push({});
                              }

                              setNewInstallments(newValue);
                              setnewInstallmentErrors(newErrors);
                            }}
                          >
                            Delete
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon="minus-circle"
                            color="red"
                            onClick={() => {
                              let newValue = newInstallments.filter(
                                (installment, installmentIndex) => {
                                  return installmentIndex !== index;
                                }
                              );

                              let newErrors = newInstallmentErrors.filter(
                                (installment, installmentIndex) => {
                                  return installmentIndex !== index;
                                }
                              );

                              if (newValue.length === 0) {
                                newValue.push({
                                  bank: null,
                                  bankTerm: null,
                                  bankMid: null,
                                });
                                newErrors.push({});
                              }

                              setNewInstallments(newValue);
                              setnewInstallmentErrors(newErrors);
                            }}
                          />
                        )}
                      </ActionFields>
                    </PageSubsection>
                  );
                })}
              {!state.isEditing &&
                values.paymentType === 'Installment' &&
                newInstallments.map((newInstallment, index) => {
                  return (
                    <PageSubsection key={index}>
                      <FormField
                        placeholder="Bank"
                        label={values.installments.length < 1 ? 'Bank' : ''}
                        name={`newInstallment-${index}`}
                        type={FIELD_TYPES.SELECT}
                        value={newInstallment.bank}
                        options={bankOptions}
                        onChange={event => {
                          let newValue = newInstallments.map(
                            (newValue, newIndex) => {
                              if (newIndex === index) {
                                return {
                                  bank: event,
                                  bankTerm: null,
                                  bankMid: newInstallment.bankMid,
                                };
                              }
                              return newValue;
                            }
                          );

                          setNewInstallments(newValue);
                        }}
                        error={newInstallmentErrors[index].bank}
                        perRow={3.5}
                        readOnly={!state.isEditing}
                        required
                      />
                      <FormField
                        placeholder="Bank Term"
                        label={
                          values.installments.length < 1 ? 'Bank Term' : ''
                        }
                        name={`newInstallment-${index}`}
                        type={FIELD_TYPES.SELECT}
                        value={newInstallment.bankTerm}
                        options={TermsOptions}
                        onChange={event => {
                          let newValue = newInstallments.map(
                            (newValue, newIndex) => {
                              if (newIndex === index) {
                                return {
                                  bank: newInstallment.bank,
                                  bankTerm: event,
                                  bankMid: newInstallment.bankMid,
                                };
                              }
                              return newValue;
                            }
                          );

                          let getBank = newValue.find(value => value.bank);

                          setNewInstallments(newValue);
                          handleNewBankTermValidation(
                            event,
                            getBank.bank,
                            index
                          );
                        }}
                        onBlur={event =>
                          handleNewBankTermValidation(event, index)
                        }
                        error={newInstallmentErrors[index].bankTerm}
                        perRow={3.5}
                        readOnly={!state.isEditing}
                        required
                      />
                      <FormField
                        placeholder="Bank MID"
                        label={values.installments.length < 1 ? 'Bank MID' : ''}
                        name={`installment-${index}`}
                        type={FIELD_TYPES.TEXT}
                        value={newInstallment.bankMid}
                        onChange={event => {
                          let newValue = newInstallments.map(
                            (newValue, newIndex) => {
                              if (newIndex === index) {
                                return {
                                  bank: newInstallment.bank,
                                  bankTerm: newInstallment.bankTerm,
                                  bankMid: event.target.value,
                                };
                              }
                              return newValue;
                            }
                          );

                          setNewInstallments(newValue);
                          handleNewBankMidValidation(event, index);
                        }}
                        onBlur={event =>
                          handleNewBankMidValidation(event, index)
                        }
                        error={newInstallmentErrors[index].bankMid}
                        perRow={3.5}
                        readOnly={!state.isEditing}
                        required
                      />
                    </PageSubsection>
                  );
                })}
              {values.paymentType === 'AutoDebit' && (
                <SubsectionTitle>ADA MID</SubsectionTitle>
              )}
              {values.paymentType === 'AutoDebit' &&
                !loading &&
                values &&
                values.ada &&
                values.ada.map((adaData, index) => (
                  <PageSubsection key={index}>
                    <FormField
                      placeholder=""
                      label={index === 0 ? 'Enrollment Type' : ''}
                      name={`enrollmentType-${adaData.enrollmentType}`}
                      type={FIELD_TYPES.TEXT}
                      value={adaData.enrollmentType}
                      readOnly={!state.isEditing || state.isEditing}
                      perRow={2.5}
                      required
                    />
                    <FormField
                      placeholder="Ada MID"
                      label={index === 0 ? 'ADA MID' : ''}
                      name={`adaMid-${adaData.enrollmentType}`}
                      type={FIELD_TYPES.TEXT}
                      value={adaData.adaMid}
                      onChange={event => {
                        handleAdaMidValidation(event);
                        onChange.ada(
                          values.ada.map(adaIndex => {
                            if (
                              adaIndex.enrollmentType === adaData.enrollmentType
                            ) {
                              return {
                                enrollmentType: adaData.enrollmentType,
                                adaMid: event.target.value,
                              };
                            }

                            return adaIndex;
                          })
                        );
                      }}
                      onBlur={handleAdaMidValidation}
                      error={
                        adaErrors.adaMid[`adaMid-${adaData.enrollmentType}`]
                      }
                      readOnly={!state.isEditing}
                      perRow={2.5}
                      required
                    />
                    {state.isEditing ? (
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      >
                        {isMobile ? (
                          <ActionButton
                            backgroundColor={'red'}
                            onClick={() => {
                              setSelectedAda({
                                enrollmentType: adaData.enrollmentType,
                                adaMid: adaData.adaMid,
                                index: index,
                              });
                            }}
                          >
                            Delete
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon={'minus-circle'}
                            color={'red'}
                            onClick={() => {
                              setSelectedAda({
                                enrollmentType: adaData.enrollmentType,
                                adaMid: adaData.adaMid,
                                index: index,
                              });
                            }}
                          />
                        )}
                      </ActionFields>
                    ) : (
                      ''
                    )}
                  </PageSubsection>
                ))}
              {!state.isEditing &&
                values.paymentType === 'AutoDebit' &&
                newAda.map((newAdaData, index) => {
                  return (
                    <PageSubsection key={index}>
                      <FormField
                        placeholder="Enrollment Type"
                        label={values.ada.length < 1 ? 'Enrollment Type' : ''}
                        name={`newAda-${index}`}
                        type={FIELD_TYPES.SELECT}
                        value={newAdaData.enrollmentType}
                        options={EnrollmentTypeOptions}
                        onChange={event => {
                          let newValue = newAda.map((newValue, newIndex) => {
                            if (newIndex === index) {
                              return {
                                enrollmentType: event,
                                adaMid: newAdaData.adaMid,
                              };
                            }
                            return newValue;
                          });

                          setNewAda(newValue);
                        }}
                        error={newAdaErrors[index].enrollmentType}
                        perRow={2.5}
                        readOnly={!state.isEditing}
                        required
                      />
                      <FormField
                        placeholder="ADA MID"
                        label={values.ada.length < 1 ? 'ADA MID' : ''}
                        name={`newAda-${index}`}
                        type={FIELD_TYPES.TEXT}
                        value={newAdaData.adaMid}
                        onChange={event => {
                          let newValue = newAda.map((newValue, newIndex) => {
                            if (newIndex === index) {
                              return {
                                enrollmentType: newAdaData.enrollmentType,
                                adaMid: event.target.value,
                              };
                            }
                            return newValue;
                          });

                          setNewAda(newValue);
                          handleNewAdaMidValidation(event, index);
                        }}
                        onBlur={event =>
                          handleNewAdaMidValidation(event, index)
                        }
                        error={newAdaErrors[index].adaMid}
                        perRow={2.5}
                        readOnly={!state.isEditing}
                        required
                      />
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      ></ActionFields>
                    </PageSubsection>
                  );
                })}
              {state.isEditing &&
                values.paymentType === 'AutoDebit' &&
                newAda.map((newAdaData, index) => {
                  return (
                    <PageSubsection key={index}>
                      <FormField
                        placeholder="Enrollment Type"
                        label={values.ada.length < 1 ? 'Enrollment Type' : ''}
                        name={`newAda-${index}`}
                        type={FIELD_TYPES.SELECT}
                        value={newAdaData.enrollmentType}
                        options={EnrollmentTypeOptions}
                        onChange={event => {
                          let newValue = newAda.map((newValue, newIndex) => {
                            if (newIndex === index) {
                              return {
                                enrollmentType: event,
                                adaMid: newAdaData.adaMid,
                              };
                            }
                            return newValue;
                          });

                          setNewAda(newValue);
                          handleNewEnrollmentTypeValidation(event, index);
                        }}
                        onBlur={event =>
                          handleNewEnrollmentTypeValidation(event, index)
                        }
                        error={newAdaErrors[index].enrollmentType}
                        perRow={2.5}
                        readOnly={!state.isEditing}
                        required
                      />
                      <FormField
                        placeholder="ADA MID"
                        label={values.ada.length < 1 ? 'ADA MID' : ''}
                        name={`newAda-${index}`}
                        type={FIELD_TYPES.TEXT}
                        value={newAdaData.adaMid}
                        onChange={event => {
                          let newValue = newAda.map((newValue, newIndex) => {
                            if (newIndex === index) {
                              return {
                                enrollmentType: newAdaData.enrollmentType,
                                adaMid: event.target.value,
                              };
                            }
                            return newValue;
                          });

                          setNewAda(newValue);
                          handleNewAdaMidValidation(event, index);
                        }}
                        onBlur={event =>
                          handleNewAdaMidValidation(event, index)
                        }
                        error={newAdaErrors[index].adaMid}
                        perRow={2.5}
                        readOnly={!state.isEditing}
                        required
                      />
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      >
                        {!(
                          newAdaErrors[index].enrollmentType ||
                          (newAdaErrors[index].adaMid &&
                            newAdaErrors[index].adaMid !==
                              'MID is already existing. Are you sure want to use it?')
                        ) &&
                          newAdaData.enrollmentType &&
                          newAdaData.adaMid &&
                          index === newAda.length - 1 &&
                          (isMobile ? (
                            <ActionButton
                              backgroundColor="green"
                              onClick={() => {
                                setNewAda([
                                  ...newAda,
                                  {
                                    enrollmentType: null,
                                    adaMid: null,
                                  },
                                ]);
                                setNewAdaErrors([...newAdaErrors, {}]);
                              }}
                            >
                              Add
                            </ActionButton>
                          ) : (
                            <ActionIcon
                              icon="plus-circle"
                              color="green"
                              onClick={() => {
                                setNewAda([
                                  ...newAda,
                                  {
                                    enrollmentType: null,
                                    adaMid: null,
                                  },
                                ]);
                                setNewAdaErrors([...newAdaErrors, {}]);
                              }}
                            />
                          ))}
                        {isMobile ? (
                          <ActionButton
                            backgroundColor="red"
                            onClick={() => {
                              let newValue = newAda.filter((ada, adaIndex) => {
                                return adaIndex !== index;
                              });

                              let newErrors = newAdaErrors.filter(
                                (ada, adaIndex) => {
                                  return adaIndex !== index;
                                }
                              );

                              if (newValue.length === 0) {
                                newValue.push({
                                  enrollmentType: null,
                                  adaMid: null,
                                });
                                newErrors.push({});
                              }

                              setNewAda(newValue);
                              setNewAdaErrors(newErrors);
                            }}
                          >
                            Delete
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon="minus-circle"
                            color="red"
                            onClick={() => {
                              let newValue = newAda.filter((ada, adaIndex) => {
                                return adaIndex !== index;
                              });

                              let newErrors = newAdaErrors.filter(
                                (ada, adaIndex) => {
                                  return adaIndex !== index;
                                }
                              );

                              if (newValue.length === 0) {
                                newValue.push({
                                  enrollmentType: null,
                                  adaMid: null,
                                });
                                newErrors.push({});
                              }

                              setNewAda(newValue);
                              setNewAdaErrors(newErrors);
                            }}
                          />
                        )}
                      </ActionFields>
                    </PageSubsection>
                  );
                })}
              <PageSubsection>
                <ButtonsContainer>
                  {isMobile ? (
                    <>
                      {editButtonGroup}
                      {backButton}
                    </>
                  ) : (
                    <>
                      {backButton}
                      {editButtonGroup}
                    </>
                  )}
                </ButtonsContainer>
              </PageSubsection>
            </>
          )}
        </DataContainer>
      </Page>

      <AlertModal
        isOpen={state.isConfirmEditMIDModalOpen}
        title="New MID Alert"
        icon="question-circle"
        variant="warn"
        header="ARE YOU SURE?"
        subHeader="You are about to save changes on MID."
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone',
        ]}
        confirmLoading={isEditingMid}
        confirmText="Yes"
        handleConfirm={async () => {
          // const value = getDiff(initialValue, sanitize(state.selectedMid));
          const updateValue = Object.assign({}, state.selectedMid, {
            id: undefined,
          });
          const value = sanitize(updateValue);

          const response = await editMid({
            variables: {
              data: {
                ...value,
                installments: [
                  ...values.installments.map(currentVal => {
                    return {
                      bank: currentVal.bank,
                      bankTerm: currentVal.bankTerm,
                      bankMid: currentVal.bankMid,
                    };
                  }),
                  ...addNewInstallments,
                ],
                ada: [
                  ...values.ada.map(currentVal => {
                    return {
                      enrollmentType: currentVal.enrollmentType,
                      adaMid: currentVal.adaMid,
                    };
                  }),
                  ...addNewAda,
                ],
              },
              where: data.mid.id,
            },
          });

          setState({
            ...state,
            id: response.data.updateMid[0].id,
            isConfirmEditMIDModalOpen: false,
            isSuccessEditMIDModalOpen: true,
            isEditing: false,
          });
        }}
        handleClose={() =>
          setState({ ...state, isConfirmEditMIDModalOpen: false })
        }
      />
      <AlertModal
        isOpen={state.isSuccessEditMIDModalOpen}
        title="New MID Alert"
        variant="success"
        icon="check-circle"
        header="SUCCESS!"
        subHeader="Changes have been saved successfully."
        description="Changes are now reflected on the records."
        confirmText="Go to All MID"
        handleConfirm={() => {
          setState({ ...state, isSuccessEditMIDModalOpen: false });
          history.push('/mid');
        }}
        handleClose={() => {
          setState({ ...state, isSuccessEditMIDModalOpen: false });
          history.push('/mid/' + state.id);
        }}
      />
      <AlertModal
        isOpen={state.isFailureEditMIDModalOpen}
        title="New MID Alert"
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader={
          state.editMidError === 'MERCHANT_ID_ALREADY_EXISTS'
            ? 'MID already exists.'
            : state.editMidError === 'COMPANY_PAYMENT_TYPE_ALREADY_EXIST'
              ? `A record with a Company of ${state.selectedMid.company}, Bill Type of ${state.selectedMid.billType} and Payment Type of ${state.selectedMid.paymentType} already exists`
              : 'There was a problem on saving changes on MID.'
        }
        description="Please go back and try saving it again."
        handleClose={() =>
          setState({ ...state, isFailureEditMIDModalOpen: false })
        }
        confirmText="Go Back"
        handleConfirm={() => {
          setState({ ...state, isFailureEditMIDModalOpen: false });
        }}
      />

      <AlertModal
        isOpen={state.isLeavingWhileEditing}
        title="New MID Alert"
        icon="question-circle"
        variant="warn"
        header="THERE ARE UNSAVED CHANGES."
        subHeader="Are you sure you want to leave this page without saving?"
        description="Your changes will be lost if you don't save them."
        handleClose={() => setState({ ...state, isLeavingWhileEditing: false })}
        cancelText="Discard Changes"
        confirmText="Go Back"
        handleCancel={() => {
          if (state.nextLocation) {
            history.push(state.nextLocation);
          }
        }}
        handleConfirm={() => {
          setState({ ...state, isLeavingWhileEditing: false });
        }}
      />
    </>
  );
};

MIDInformation.propTypes = {
  location: PropTypes.object,
  history: PropTypes.object,
  match: PropTypes.object,
};

export default MIDInformation;
