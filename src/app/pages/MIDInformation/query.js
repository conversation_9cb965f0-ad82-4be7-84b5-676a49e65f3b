import { gql } from '@apollo/client';

export const GET_MID_INFORMATION = gql`
  query getMid($where: String!) {
    mid(where: $where) {
      id
      name
      previousMerchantId
      depositoryBankName
      depositoryBankAccount
      company
      channelId
      bankDiscount
      withholdingTax
      merchantId
      costCenter
      paymentType
      billType
      createdAt
      updatedAt
      installments {
        bankTerm
        bankMid
        bank
      }
      ada {
        enrollmentType
        adaMid
      }
      businessUnit
    }
  }
`;

export const GET_CHANNEL_BILLTYPE = gql`
  query getChannelsBillType($billType: ChannelBillType!) {
    channelsBillType(billType: $billType) {
      id
      name
    }
  }
`;
