import format from 'date-fns/format';
import PropTypes from 'prop-types';
import React, { useContext, useState } from 'react';
import { useApolloClient, useQuery } from '@apollo/client';
import DataContainer from '../../components/DataContainer';
import DataTable from '../../components/DataTable';
import { FIELD_TYPES } from '../../components/Form/constants';
import GlobalSearch from '../../components/GlobalSearch';
import Header from '../../components/Header';
import { AlertModal } from '../../components/Modal';
import Page from '../../components/Page';
import AuthContext from '../../context/AuthContext/AuthContext';
import useQuerySeries from '../../hooks/useQuerySeries';
import {
  CHECK_USER_STATUS,
  GET_NOTIFICATIONS_INFO,
  GET_ROLES_INFO,
} from './query';

const Notifications = ({ history }) => {
  const [isUserUpdatedModalOpen, setIsUserUpdatedModalOpen] = useState(false);
  const [isUserDeletedModalOpen, setIsUserDeletedModalOpen] = useState(false);
  const { permissions } = useContext(AuthContext);

  const { data: rolesData, loading: isLoadingRolesInfo } =
    useQuery(GET_ROLES_INFO);

  const {
    pagination,
    setPagination,
    setFilter,
    data,
    loading,
    page,
    setPage,
    isLastPage,
  } = useQuerySeries(GET_NOTIFICATIONS_INFO, 'searchNotifications', {
    pagination: {
      start: { id: '', sortKey: '', createdAt: '' },
      limit: 10,
    },
  });

  const client = useApolloClient();

  const roleOptions =
    !isLoadingRolesInfo && rolesData
      ? rolesData.rolesLoose.map(role => ({
          value: role.id,
          label: role.name,
        }))
      : [];

  return (
    <>
      <Page>
        <Header
          withHome
          title="System Notifications"
          path={['System Notifications']}
          searchProps={{}}
        />
        <DataContainer>
          <DataTable
            loading={loading}
            headerOptions={
              <GlobalSearch
                onSearch={newFilter => {
                  setPagination({ ...pagination, start: '' });
                  setFilter(newFilter);
                }}
                fields={[
                  {
                    label: 'Performing User',
                    name: 'userEmail',
                    type: FIELD_TYPES.TEXT,
                  },
                  {
                    label: 'Full Name',
                    name: 'userName',
                    type: FIELD_TYPES.TEXT,
                  },
                  {
                    label: 'Role',
                    name: 'roleId',
                    type: FIELD_TYPES.MULTISELECT,
                    options: roleOptions,
                  },
                  {
                    label: 'Category',
                    name: 'category',
                    type: FIELD_TYPES.TEXT,
                  },
                  {
                    label: 'Record Date',
                    name: 'createdAt',
                    type: FIELD_TYPES.DATE_RANGE,
                  },
                ]}
                placeholder="Search entries here..."
              />
            }
            data={data}
            pagination={{
              ...pagination,
              count: 0,
              cursors: [],
              handleChange: pagination => {
                setPagination(pagination);
              },
            }}
            series={{
              page,
              setPage,
              isLastPage,
            }}
            config={{
              userEmail: {
                headerLabel: 'Performing User',
                sortable: true,
                onClick:
                  permissions.User.view &&
                  (async data => {
                    const { data: statusData } = await client.query({
                      query: CHECK_USER_STATUS,
                      variables: {
                        where: {
                          id: data.userId,
                        },
                      },
                      fetchPolicy: 'network-only',
                    });
                    if (statusData.user === null) {
                      // user doesn't exist
                      setIsUserDeletedModalOpen(true);
                    } else if (
                      statusData.user.email !== data.userEmail ||
                      statusData.user.name !== data.userName ||
                      statusData.user.role.id !== data.roleId
                    ) {
                      // user has been updated
                      setIsUserUpdatedModalOpen(true);
                    } else {
                      // user is still the same
                      history.push('/user-accounts/' + data.userId);
                    }
                  }),
              },
              userName: {
                headerLabel: 'Full Name',
                sortable: true,
              },
              roleName: {
                headerLabel: 'Role',
                sortable: true,
              },
              category: {
                headerLabel: 'Category',
                sortable: true,
              },
              createdAt: {
                headerLabel: 'Record Date',
                sortable: data =>
                  format(new Date(data.createdAt), 'MM/DD/YYYY - hh:mm:ss A'),
                renderAs: data =>
                  format(new Date(data.createdAt), 'MM/DD/YYYY - hh:mm:ss A'),
              },
            }}
          />
        </DataContainer>
      </Page>
      <AlertModal
        isOpen={isUserUpdatedModalOpen}
        title="View Account Alert"
        variant="error"
        icon="times-circle"
        header="OH, SNAP!"
        subHeader="Cannot view the Account."
        description="Account is already edited after the notification occured. Go to Users Accounts and search the User."
        handleConfirm={() => {
          history.push('/user-accounts');
        }}
        handleClose={() => {
          setIsUserUpdatedModalOpen(false);
        }}
        confirmText="Okay"
      />
      <AlertModal
        isOpen={isUserDeletedModalOpen}
        title="View Account Alert"
        variant="error"
        icon="times-circle"
        header="OH, SNAP!"
        subHeader="Cannot view the Account."
        description="Account is already deleted after the notification occured."
        handleConfirm={() => {
          setIsUserDeletedModalOpen(false);
        }}
        handleClose={() => {
          setIsUserDeletedModalOpen(false);
        }}
        confirmText="Okay"
      />
    </>
  );
};

Notifications.propTypes = {
  history: PropTypes.object,
};

export default Notifications;
