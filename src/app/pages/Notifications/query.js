import { gql } from '@apollo/client';

export const GET_ROLES_INFO = gql`
  query getRolesInfo {
    rolesLoose {
      id
      name
    }
  }
`;

export const GET_NOTIFICATIONS_INFO = gql`
  query getNotificationsInformation(
    $filter: SearchNotificationInput
    $pagination: AuditPaginationInput!
  ) {
    searchNotifications(filter: $filter, pagination: $pagination) {
      lastKey {
        id
        sortKey
        createdAt
      }
      count
      filteredData {
        id
        userId
        userName
        userEmail
        roleId
        roleName
        category
        createdAt
      }
    }
  }
`;

export const CHECK_USER_STATUS = gql`
  query checkUserStatus($where: UserPrimary!) {
    user(where: $where) {
      id
      name
      email
      role {
        id
      }
    }
  }
`;
