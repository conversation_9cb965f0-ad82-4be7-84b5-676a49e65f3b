import PropTypes from 'prop-types';
import React, { useContext, useState } from 'react';
import Row from '../../components/Row';
import Page from '../../components/Page';
import Header from '../../components/Header';
// import { AlertModal } from '../../components/Modal';
import DataHeader from '../../components/DataHeader';
import FormField from '../../components/Form/FormField';
import DataContainer from '../../components/DataContainer';
import { FIELD_TYPES } from '../../components/Form/constants';
import ResponsiveContext from '../../context/ResponsiveContext';
import PrimaryButton from '../../components/Button/PrimaryButton';
import SecondaryButton from '../../components/Button/SecondaryButton';
import {
  ButtonsContainer,
  PageSubsection,
} from '../../components/InformationPage';
import useForm from '../../hooks/useForm';
import { AlertModal } from '../../components/Modal';
import { usePaymentModeLookup } from './hooks';
import { useMutation } from '@apollo/client';
import { CREATE_POST_PAYMENT_CONFIG } from './mutation';
import { useLocation } from 'react-router-dom';
import * as Yup from 'yup';

const BackButton = ({ history }) => (
  <SecondaryButton
    onClick={() => {
      history.push('/payment-mode-management');
    }}
  >
    Back to Payment Mode Management
  </SecondaryButton>
);

BackButton.propTypes = {
  history: PropTypes.object,
};

const PaymentMode = ({ history }) => {
  const { isMobile } = useContext(ResponsiveContext);

  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const channelId = queryParams.get('channelId');
  const channelName = queryParams.get('channelName');

  if (!channelId && !channelName) {
    history.push('/payment-mode-management');
  }

  const [state, setState] = useState({
    isEditing: false,
    isConfirmModalOpen: false,
  });

  const { data: paymentModes } = usePaymentModeLookup(channelId);

  const fields = {
    channel: '',
    paymentMode: {
      validation: Yup.string().required('Please enter a value'),
    },
    billRealTime: {
      validation: Yup.string()
        .required('Please enter a value')
        .matches(/^[A-Za-z]{3}$/, 'Must be three alphabetic characters.'),
    },
    billFallout: {
      validation: Yup.string()
        .required('Please enter a value')
        .matches(/^(?!0)/, 'Input cannot start with a zero.')
        .matches(/^\d{2}$/, 'Must be two digits'),
    },
    nonBill: {
      validation: Yup.string()
        .required('Please enter a value')
        .matches(/^(?!0)/, 'Input cannot start with a zero.')
        .matches(/^\d{2}$/, 'Must be two digits'),
    },
  };

  const handleSubmit = () => {
    setState(prevState => ({
      ...prevState,
      isConfirmModalOpen: true,
    }));
  };

  const { values, onChange, onBlur, errors, onSubmit } = useForm(
    fields,
    handleSubmit
  );

  const mappedPaymentModes =
    paymentModes?.postPaymentConfigGatewayMethods.result?.xendit?.map(p => ({
      label: p.name,
      value: p.paymentMethod,
    })) || [];

  const [createPostPaymentConfig, { loading: isCreatingPostPaymentConfig }] =
    useMutation(CREATE_POST_PAYMENT_CONFIG, {
      onCompleted: () => {
        setState(prevState => ({
          ...prevState,
          isConfirmModalOpen: false,
          isSuccessModalOpen: true,
        }));
      },
      onError: err => {
        setState({
          ...state,
          createPostPaymentConfigError: err.networkError.result
            ? err.networkError.result.message
            : null,
          isConfirmModalOpen: false,
          isFailureModalOpen: true,
        });
      },
    });

  return (
    <>
      <Page>
        <Header
          withHome
          title={'Add Payment Mode'}
          path={['Payment Management', 'Add Payment Mode']}
        />
        <DataContainer>
          <>
            <DataHeader>
              <DataHeader.Title>
                ADD PAYMENT MODE CONFIGURATION
              </DataHeader.Title>
            </DataHeader>
            <PageSubsection>
              <FormField
                label="Channel"
                name="channel"
                type={FIELD_TYPES.TEXT}
                value={channelName}
                readOnly={true}
                perRow={2}
                required
              />
            </PageSubsection>
            <PageSubsection>
              <FormField
                label="Payment Mode"
                name="paymentMode"
                type={FIELD_TYPES.SELECT}
                options={mappedPaymentModes}
                value={values.paymentMode}
                onChange={onChange.paymentMode}
                onBlur={onBlur.paymentMode}
                error={errors.paymentMode}
                readOnly={false}
                perRow={2}
                required
                placeholder="Select a payment mode"
              />
            </PageSubsection>
            <PageSubsection>
              <FormField
                label="Bill - Realtime Code"
                name="name"
                type={FIELD_TYPES.TEXT}
                value={values.billRealTime}
                onChange={onChange.billRealTime}
                onBlur={onBlur.billRealTime}
                error={errors.billRealTime}
                readOnly={false}
                perRow={2}
                required
              />
            </PageSubsection>
            <PageSubsection>
              <FormField
                label="Bill - Failed Posting Code"
                name="name"
                type={FIELD_TYPES.NUMBER}
                value={values.billFallout}
                onChange={onChange.billFallout}
                onBlur={onBlur.billFallout}
                error={errors.billFallout}
                readOnly={false}
                perRow={2}
                required
              />
            </PageSubsection>
            <PageSubsection>
              <FormField
                label="Non-bill Code"
                name="name"
                type={FIELD_TYPES.NUMBER}
                value={values.nonBill}
                onChange={onChange.nonBill}
                onBlur={onBlur.nonBill}
                error={errors.nonBill}
                readOnly={false}
                perRow={2}
                required
              />
            </PageSubsection>
            <PageSubsection>
              <ButtonsContainer>
                {isMobile ? (
                  <>
                    <Row>
                      <PrimaryButton
                        icon="save"
                        disabled={isCreatingPostPaymentConfig}
                        onClick={onSubmit}
                      >
                        Create Payment Mode
                      </PrimaryButton>
                    </Row>
                    <BackButton history={history} />
                  </>
                ) : (
                  <>
                    <BackButton history={history} />
                    <Row>
                      <PrimaryButton
                        icon="save"
                        disabled={isCreatingPostPaymentConfig}
                        onClick={onSubmit}
                      >
                        Create Payment Mode
                      </PrimaryButton>
                    </Row>
                  </>
                )}
              </ButtonsContainer>
            </PageSubsection>
          </>
        </DataContainer>
      </Page>
      <AlertModal
        isOpen={state.isConfirmModalOpen}
        title="New Payment Mode Alert"
        icon="exclamation-circle"
        variant="warn"
        header="ARE YOU SURE?"
        subHeader="You are about to create a new payment mode."
        description={['This action requires your confirmation.']}
        confirmLoading={isCreatingPostPaymentConfig}
        confirmText="Yes"
        handleConfirm={() => {
          createPostPaymentConfig({
            variables: {
              data: {
                channelId: channelId,
                billRealTime: values.billRealTime,
                billFallout: parseInt(values.billFallout),
                nonBill: parseInt(values.nonBill),
                gatewayProcessor: 'xendit',
                paymentMethod: values.paymentMode,
              },
            },
          });

          setState(prevState => ({
            ...prevState,
            isConfirmModalOpen: false,
          }));
        }}
        handleClose={() => {
          console.log('closed!');
          setState(prevState => ({
            ...prevState,
            isConfirmModalOpen: false,
          }));
        }}
      />
      <AlertModal
        isOpen={state.isSuccessModalOpen}
        title="New Payment Mode Alert"
        handleClose={() => {
          setState(prevState => ({ ...prevState, isSuccessModalOpen: false }));
        }}
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader="New payment mode has been created successfully."
        description=""
        confirmText="Go to Payment Mode Management"
        handleConfirm={() => {
          setState(prevState => ({ ...prevState, isSuccessModalOpen: false }));
          history.push('/payment-mode-management');
        }}
      />
      <AlertModal
        isOpen={state.isFailureModalOpen}
        title="New Payment Mode Alert"
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader="A configuration for the selected payment mode already exists."
        description=""
        handleClose={() => setState({ ...state, isFailureModalOpen: false })}
        confirmText="Go Back"
        handleConfirm={() => {
          setState({ ...state, isFailureModalOpen: false });
        }}
      />
    </>
  );
};

PaymentMode.propTypes = {
  history: PropTypes.object,
};

export default PaymentMode;
