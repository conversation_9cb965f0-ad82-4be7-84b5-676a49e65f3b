import PropTypes from 'prop-types';
import React, { useContext, useEffect, useState } from 'react';
import CreateButton from '../../components/Button/CreateButton';
import DataContainer from '../../components/DataContainer';
import { FIELD_TYPES } from '../../components/Form/constants';
import Header from '../../components/Header/Header';
import Loader from '../../components/Loader';
import Page from '../../components/Page';
import AuthContext from '../../context/AuthContext/AuthContext';
import { useMutation } from '@apollo/client';
import { ChannelDropdown } from './styled';
import DataTable from '../../components/DataTable';
import { usePaymentModeQuery } from './hooks/usePaymentModeQuery';
import { usePostPaymentConfigChannelsQuery } from './hooks/usePostPaymentConfigChannelsQuery';
import { usePaymentModeLookup } from '../PaymentMode/hooks';
import ActionButtons from '../../components/ActionButtons';
import * as Yup from 'yup';
import { FormModal } from '../../components/Modal';
import {
  UPDATE_POST_PAYMENT_CONFIG,
  DELETE_POST_PAYMENT_CONFIG,
} from './mutation';
import AlertModal from '../../components/Modal/AlertModal';
import { GET_POST_PAYMENT_CONFIGS } from './query';
import PaymentModePagination from './components/PaymentModePagination';

const PaymentModeManagement = ({ history }) => {
  const { permissions, authUser } = useContext(AuthContext);

  const [state, setState] = useState({});

  const paginationDefaultValues = {
    startKey: null,
    limit: 10,
  };

  const [hasCompletedAllQueries, setHasCompletedAllQueries] = useState(false);
  const [channel, setChannel] = useState('');
  const [channelName, setChannelName] = useState('');
  const [compositeKey, setCompositeKey] = useState('');

  const { channelOptions, loading: channelsQueryLoading } =
    usePostPaymentConfigChannelsQuery(authUser.id);
  const { data: paymentModeLookup } = usePaymentModeLookup(channel);
  const {
    data,
    loading,
    refetch,
    handlePrevious,
    handleNext,
    disablePrevious,
    disableNext,
  } = usePaymentModeQuery(paymentModeLookup, channel, paginationDefaultValues);

  useEffect(() => {
    if (!loading && !channelsQueryLoading) {
      setChannel(channelOptions[0]?.value);
      setChannelName(channelOptions[0]?.label);
      setHasCompletedAllQueries(true);
    }
  }, [channelsQueryLoading]);

  const [editPaymentMode, { loading: isEditingPaymentMode }] = useMutation(
    UPDATE_POST_PAYMENT_CONFIG,
    {
      onCompleted: () => {
        setState(prevState => ({
          ...prevState,
          isConfirmEditModalOpen: false,
          isSuccessEditModalOpen: true,
          isEditModalOpen: false,
          selectedPaymentMode: null,
        }));
        refetch();
      },
      onError: err => {
        setState(prevState => ({
          ...prevState,
          editPaymentModeError: err.networkError.result
            ? err.networkError.result.message
            : null,
          isConfirmEditModalOpen: false,
          isFailureEditModalOpen: true,
        }));
      },
      refetchQueries: [
        {
          query: GET_POST_PAYMENT_CONFIGS,
          variables: {
            filter: {
              channelId: channel,
            },
            pagination: { ...paginationDefaultValues },
          },
        },
      ],
    }
  );

  const [deletePaymentMode, { loading: isDeletingPaymentMode }] = useMutation(
    DELETE_POST_PAYMENT_CONFIG,
    {
      onCompleted: () => {
        setState(prevState => ({
          ...prevState,
          isConfirmDeleteModalOpen: false,
          isSuccessDeleteModalOpen: true,
          isDeleteModalOpen: false,
          selectedPaymentMode: null,
        }));
        refetch();
      },
      onError: err => {
        setState(prevState => ({
          ...prevState,
          deletePaymentModeError: err.networkError.result
            ? err.networkError.result.message
            : null,
          isConfirmDeleteModalOpen: false,
          isFailureDeleteModalOpen: true,
        }));
      },
      refetchQueries: [
        {
          query: GET_POST_PAYMENT_CONFIGS,
          variables: {
            filter: {
              channelId: channel,
            },
            pagination: { ...paginationDefaultValues },
          },
        },
      ],
    }
  );

  const handleSetChannel = v => {
    setChannel(v);
    const channelName = channelOptions.find(c => c.value === v).label;
    setChannelName(channelName);
  };

  const mappedPaymentModes =
    paymentModeLookup?.postPaymentConfigGatewayMethods.result?.xendit?.map(
      p => ({
        label: p.name,
        value: p.paymentMethod,
      })
    ) || [];

  return (
    <>
      <Page>
        <Header
          title="Payment Mode Management"
          withHome
          path={['Payment Mode Management']}
        />
        {!hasCompletedAllQueries || loading ? (
          <Loader />
        ) : (
          <DataContainer>
            <div style={{ display: 'flex' }}>
              <label style={{ marginTop: '0.4rem' }}>Select a channel: </label>
              <ChannelDropdown
                options={channelOptions}
                placeholder="Select channel"
                onChange={handleSetChannel}
                value={channel}
              />
            </div>
            <div>
              <DataTable
                loading={loading}
                minCellWidth={0}
                data={data?.filteredResult}
                headerOptions={
                  <>
                    <div style={{ display: 'flex', marginLeft: 'auto' }}>
                      {permissions.PostPaymentConfig.create && (
                        <CreateButton
                          icon="plus"
                          onClick={() => {
                            const queryString = `?channelId=${encodeURIComponent(channel)}&channelName=${encodeURIComponent(channelName)}`;
                            history.push(
                              `/payment-mode-management/add${queryString}`
                            );
                          }}
                          disabled={!channel}
                        >
                          Add New Payment Mode
                        </CreateButton>
                      )}
                    </div>
                  </>
                }
                config={{
                  name: {
                    headerLabel: 'Name',
                  },
                  billRealTime: {
                    headerLabel: 'Bill - Real Time',
                  },
                  billFallout: {
                    headerLabel: 'Bill - Failed Posting',
                  },
                  nonBill: {
                    headerLabel: 'Non-Bill',
                  },
                  actions: {
                    renderAs: data => (
                      <ActionButtons
                        disabled={{
                          edit: !permissions.PostPaymentConfig.update,
                          delete: !permissions.PostPaymentConfig.delete,
                        }}
                        handleEdit={() => {
                          setState(prevState => ({
                            ...prevState,
                            selectedPaymentMode: data,
                            isEditModalOpen: true,
                          }));
                          setCompositeKey(data.compositeKey);
                        }}
                        handleDelete={() => {
                          setState(prevState => ({
                            ...prevState,
                            selectedPaymentMode: data,
                            isConfirmDeleteModalOpen: true,
                          }));
                          setCompositeKey(data.compositeKey);
                        }}
                      />
                    ),
                  },
                }}
              />
            </div>
            <PaymentModePagination
              handlePrevious={handlePrevious}
              handleNext={handleNext}
              disablePrevious={disablePrevious}
              disableNext={disableNext}
            />
          </DataContainer>
        )}
      </Page>
      {state.isEditModalOpen && (
        <FormModal
          isOpen={state.isEditModalOpen}
          width="600px"
          handleClose={() =>
            setState(prevState => ({ ...prevState, isEditModalOpen: false }))
          }
          title="Edit Payment Mode"
          instructions={<span>Fill out the form with new fields.</span>}
          submitText="Update Payment Mode"
          handleSubmit={values => {
            setState(prevState => ({
              ...prevState,
              selectedPaymentMode: values,
              isConfirmEditModalOpen: true,
            }));
          }}
          fields={{
            paymentMode: {
              type: FIELD_TYPES.SELECT,
              label: 'Payment Mode',
              placeholder: 'Payment Mode',
              required: true,
              options: mappedPaymentModes,
              initialValue: state.selectedPaymentMode.paymentMethod,
            },
            billRealTime: {
              type: FIELD_TYPES.TEXT,
              label: 'Bill - Realtime',
              placeholder: 'Bill - Realtime',
              required: true,
              initialValue: state.selectedPaymentMode.billRealTime,
              validation: Yup.string()
                .required('Please enter a value')
                .matches(
                  /^[A-Za-z]{3}$/,
                  'Must be three alphabetic characters.'
                ),
            },
            billFallout: {
              type: FIELD_TYPES.TEXT,
              label: 'Bill - Fallout',
              placeholder: 'Bill - Fallout',
              required: true,
              initialValue: state.selectedPaymentMode.billFallout,
              validation: Yup.string()
                .required('Please enter a value')
                .matches(/^\d{2}$/, 'Must be two digits'),
            },
            nonBill: {
              type: FIELD_TYPES.TEXT,
              label: 'Non-Bill',
              placeholder: 'Non-Bill',
              required: true,
              initialValue: state.selectedPaymentMode.nonBill,
              validation: Yup.string()
                .required('Please enter a value')
                .matches(/^\d{2}$/, 'Must be two digits'),
            },
          }}
        />
      )}

      {/* Edit modals */}

      <AlertModal
        isOpen={state.isConfirmEditModalOpen}
        title="Edit Payment Mode Alert"
        icon="exclamation-circle"
        variant="warn"
        header="ARE YOU SURE?"
        subHeader="You are about to update an existing payment mode."
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        confirmLoading={isEditingPaymentMode}
        confirmText="Yes"
        handleConfirm={() => {
          editPaymentMode({
            variables: {
              data: {
                billRealTime: state.selectedPaymentMode.billRealTime,
                billFallout: state.selectedPaymentMode.billFallout,
                nonBill: state.selectedPaymentMode.nonBill,
              },
              where: {
                channelId: channel,
                compositeKey: compositeKey,
              },
            },
          });
        }}
        handleClose={() => {
          setState({
            ...state,
            isConfirmEditModalOpen: false,
          });
        }}
      />
      <AlertModal
        isOpen={state.isSuccessEditModalOpen}
        title="Edit Payment Mode Alert"
        handleClose={() => {
          setState(prevState => ({
            ...prevState,
            isSuccessEditModalOpen: false,
          }));
        }}
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader="Payment mode has been updated successfully."
        description="Changes are now reflected on the record."
        confirmText="Go to Payment Modes"
        handleConfirm={() => {
          setState(prevState => ({
            ...prevState,
            isSuccessEditModalOpen: false,
          }));
        }}
      />

      {/* Delete modals */}

      <AlertModal
        isOpen={state.isConfirmDeleteModalOpen}
        title="Delete Payment Mode Alert"
        icon="exclamation-circle"
        variant="warn"
        header="ARE YOU SURE?"
        subHeader="You are about to delete an existing payment mode."
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        confirmLoading={isDeletingPaymentMode}
        confirmText="Yes"
        handleConfirm={() => {
          deletePaymentMode({
            variables: {
              where: {
                channelId: channel,
                compositeKey: compositeKey,
              },
            },
          });
        }}
        handleClose={() => {
          setState({
            ...state,
            isConfirmDeleteModalOpen: false,
          });
        }}
      />
      <AlertModal
        isOpen={state.isSuccessDeleteModalOpen}
        title="Delete Payment Mode Alert"
        handleClose={() => {
          setState(prevState => ({
            ...prevState,
            isSuccessDeleteModalOpen: false,
          }));
        }}
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader="Payment mode has been deleted successfully."
        description="Changes are now reflected on the record."
        confirmText="Go to Payment Modes"
        handleConfirm={() => {
          setState(prevState => ({
            ...prevState,
            isSuccessDeleteModalOpen: false,
          }));
        }}
      />
    </>
  );
};

PaymentModeManagement.propTypes = {
  history: PropTypes.object,
};

export default PaymentModeManagement;
