import React from 'react';
import PropTypes from 'prop-types';
import {
  PaginationContainer,
  PaginationStepContainer,
  PaginationStepButton,
  PaginationStepIcon,
} from '../../../components/DataTable/Pagination/Pagination';

const PaymentModePagination = ({
  handlePrevious,
  handleNext,
  disablePrevious,
  disableNext,
}) => (
  <PaginationContainer>
    <PaginationStepContainer>
      <PaginationStepButton disabled={disablePrevious} onClick={handlePrevious}>
        <PaginationStepIcon size="lg" icon="caret-down" rotation={90} />
      </PaginationStepButton>
      <PaginationStepButton disabled={disableNext} onClick={handleNext}>
        <PaginationStepIcon size="lg" icon="caret-up" rotation={90} />
      </PaginationStepButton>
    </PaginationStepContainer>
  </PaginationContainer>
);

PaymentModePagination.propTypes = {
  handlePrevious: PropTypes.func.isRequired,
  handleNext: PropTypes.func.isRequired,
  disablePrevious: PropTypes.bool,
  disableNext: PropTypes.bool,
};

export default PaymentModePagination;
