import { useState } from 'react';
import { useQuery } from '@apollo/client';
import { GET_POST_PAYMENT_CONFIGS } from '../query';

export const usePaymentModeQuery = (
  paymentModeLookup,
  channelId,
  paginationDefaultValues
) => {
  const { startKey, limit } = paginationDefaultValues;

  // Stack to keep track of previous start keys
  const [startKeys, setStartKeys] = useState([]);

  const { data, loading, refetch, fetchMore } = useQuery(
    GET_POST_PAYMENT_CONFIGS,
    {
      variables: {
        filter: {
          channelId: channelId,
        },
        pagination: {
          startKey: startKey,
          limit: limit,
        },
      },
      skip: !channelId,
      fetchPolicy: 'network-only',
    }
  );

  const handleNext = () => {
    const lastKey = data?.postPaymentConfig?.lastKey;

    if (lastKey) {
      fetchMore({
        variables: {
          pagination: {
            startKey: lastKey,
            limit: limit,
          },
        },
        updateQuery: (prev, { fetchMoreResult }) => {
          if (!fetchMoreResult) return prev;
          return fetchMoreResult;
        },
      });
      setStartKeys(keys => [...keys, lastKey]);
    }
  };

  const handlePrevious = () => {
    if (startKeys.length > 1) {
      const newKeys = startKeys.slice(0, -1);
      const previousKey = newKeys[newKeys.length - 1];

      fetchMore({
        variables: {
          pagination: {
            startKey: previousKey,
            limit: limit,
          },
        },
        updateQuery: (prev, { fetchMoreResult }) => {
          if (!fetchMoreResult) return prev;
          return fetchMoreResult;
        },
      });

      setStartKeys(newKeys);
    } else {
      // If there are no startKeys, it means the first page has been reached
      fetchMore({
        variables: {
          pagination: {
            startKey: null,
            limit: limit,
          },
        },
        updateQuery: (prev, { fetchMoreResult }) => {
          if (!fetchMoreResult) return prev;
          return fetchMoreResult;
        },
      });

      // Trigger a re-render with empty start keys
      setStartKeys([]);
    }
  };

  const postPaymentConfigs = data?.postPaymentConfig?.filteredResult;

  /**
   *  Map post payment configs to include a name column
   *  The name should come from the result of `postPaymentConfigGatewayMethods`
   *  Then filter out records with empty values
   */

  const postPaymentConfigsWithName = postPaymentConfigs
    ?.map(c => {
      const matchingPaymentMode =
        paymentModeLookup?.postPaymentConfigGatewayMethods.result?.xendit?.find(
          l => l.paymentMethod === c.paymentMethod
        );

      if (matchingPaymentMode) {
        return { ...c, name: matchingPaymentMode.name };
      }

      return c;
    })
    .filter(d => d.billRealTime || d.billFallout !== 0 || d.nonBill !== 0);

  /**
   *  Merge the post payment config records with name to the
   *   query result that contains other data e.g. lastKey for pagination
   */
  const queryDataWithConfigName =
    postPaymentConfigs?.length > 0
      ? {
          ...data.postPaymentConfig,
          filteredResult: [...postPaymentConfigsWithName],
        }
      : [];

  const disablePrevious = startKeys.length === 0;
  const disableNext =
    data?.postPaymentConfig?.lastKey === undefined ||
    data?.postPaymentConfig?.lastKey === null;

  return {
    data: queryDataWithConfigName,
    loading,
    refetch,
    handlePrevious,
    handleNext,
    disablePrevious,
    disableNext,
  };
};
