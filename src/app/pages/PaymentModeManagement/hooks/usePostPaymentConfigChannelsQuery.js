import { useQuery } from '@apollo/client';
import { GET_POST_PAYMENT_CONFIG_CHANNELS } from '../query';

export const usePostPaymentConfigChannelsQuery = userId => {
  const { data, loading } = useQuery(GET_POST_PAYMENT_CONFIG_CHANNELS, {
    variables: { where: { id: userId } },
    fetchPolicy: 'network-only',
  });

  const channelOptions =
    data?.user?.postPaymentConfigChannels.map(c => ({
      label: c.name,
      value: c.channelId,
    })) || [];

  return {
    channelOptions,
    loading,
  };
};
