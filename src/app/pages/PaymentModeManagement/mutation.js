import { gql } from '@apollo/client';

export const UPDATE_POST_PAYMENT_CONFIG = gql`
  mutation UpdatePostPaymentConfig(
    $data: UpdatePostPaymentConfigInput!
    $where: UpdatePostPaymentConfigWhere!
  ) {
    updatePostPaymentConfig(data: $data, where: $where) {
      channelId
      billRealTime
      billFallout
      nonBill
    }
  }
`;

export const DELETE_POST_PAYMENT_CONFIG = gql`
  mutation deletePostPaymentConfig($where: DeletePostPaymentConfigWhere!) {
    deletePostPaymentConfig(where: $where) {
      channelId
    }
  }
`;
