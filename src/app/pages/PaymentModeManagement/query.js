import { gql } from '@apollo/client';

export const GET_POST_PAYMENT_CONFIGS = gql`
  query GetPostPaymentConfigs(
    $filter: PostPaymentConfigInput!
    $pagination: PostPaymentPagination
  ) {
    postPaymentConfig(filter: $filter, pagination: $pagination) {
      filteredResult {
        channelId
        compositeKey
        gatewayProcessor
        paymentMethod
        billRealTime
        billFallout
        nonBill
      }
      count
      lastKey {
        channelId
        compositeKey
      }
    }
  }
`;

export const GET_POST_PAYMENT_CONFIG_CHANNELS = gql`
  query getPostPaymentConfigChannels($where: UserPrimary!) {
    user(where: $where) {
      id
      postPaymentConfigChannels {
        channelId
        name
      }
    }
  }
`;
