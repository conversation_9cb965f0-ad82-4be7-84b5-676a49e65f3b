import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import styled from 'styled-components';
import Button from '../../components/Button/Button';
import Checkbox from '../../components/Checkbox';
import Row from '../../components/Row';
import Dropdown from '../../components/Dropdown';
import { DropdownButton } from '../../components/Dropdown/Dropdown';

export const ChannelsContainer = styled.div`
  margin-top: 10px;
  background-color: white;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
`;

export const ChannelCard = styled.div`
  display: flex;
  flex-direction: column;
  height: 136px;
  width: 128px;
  border-radius: 8px;
  margin: 10px;
  border: ${props =>
    props.selected
      ? '2px solid #458DDE'
      : '1px solid rgba(165, 165, 165, 0.5)'};
  padding: 10px;
`;

export const AddChannelButton = styled(ChannelCard)`
  background-color: #d1e6f6;
  padding: 21px 25px;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-size: 18px;
  color: #002244;
  font-weight: 300;
  cursor: pointer;
  border: 0;
  padding: 0;

  &:active {
    border: 1px solid rgba(165, 165, 165, 0.5);
  }
`;

export const AddChannelButtonIcon = styled(FontAwesomeIcon)`
  margin-top: 10px;
  color: #002244;
  font-size: 26px;
`;

export const ChannelCardTitle = styled.div`
  font-weight: bold;
  align-self: center;
  margin-bottom: 2px;
  width: 100%;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  text-align: center;
  font-size: ${props => props.theme.fontSize.m};
`;

export const ChannelCardId = styled.div`
  font-weight: 300;
  font-size: ${props => props.theme.fontSize.xs};
  align-self: center;
  color: #333333;
  width: 100%;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  text-align: center;
`;

export const ChannelCardPicture = styled.img`
  border-radius: 50%;
  height: 45px;
  width: 45px;
  object-fit: cover;
  align-self: center;
  margin: 10px;
`;

export const ChannelCardSettingsRow = styled(Row)`
  justify-content: space-between;
`;

export const ChannelCardSettingsButtonContainer = styled.div`
  align-self: flex-end;
  cursor: pointer;
  border: none;
  margin: 0;
  padding: 0;
  position: relative;
`;

export const ChannelCardSettingsButton = styled(Button)`
  padding: 0;
`;

export const ChannelCardSettingsIcon = styled(FontAwesomeIcon)`
  color: #787878;
`;

export const ChannelCardSettingsCheckbox = styled(Checkbox)`
  margin-right: 7px;
`;

export const ChannelCardSettingsMenu = styled.div`
  position: absolute;

  top: 20px;
  left: 0;
  display: flex;
  flex-direction: column;

  background-color: white;
  font-size: 12px;
  border-radius: 2px;
  border: 1px solid #cfcfcf;
  box-shadow:
    inset 0 1px 3px 0 rgba(0, 0, 0, 0.5),
    1px 3px 2px 0 #dadada;
`;

export const ChannelCardSettingsMenuItem = styled.button`
  background-color: white;
  color: #787878;
  font-size: 12px;
  padding: 10px 10px;
  display: flex;
  border: 0;
  cursor: pointer;
  align-items: center;

  &:focus:not(:disabled) {
    background-color: #ddeef7;
    outline: none;
  }

  &:hover:not(:disabled) {
    background-color: #ddeef7;
  }

  &:disabled {
    cursor: initial;
    background-color: #d8d8d8;
    color: #5f7186;
  }
`;

export const ChannelCardSettingsMenuItemIcon = styled(FontAwesomeIcon)`
  margin-right: 10px;
`;

export const ChannelDropdown = styled(Dropdown)`
  border: 0;
  border-bottom: 1px solid #6a6a6a;
  padding: 0;
  margin: 0 8px;
  display: flex;
  width: 7rem;
  margin-bottom: 1rem;
  height: 30px;

  &:focus {
    border-color: #0090e1;
  }

  ${DropdownButton} {
    border: 0;
    flex: 1;
  }
`;

export const ChannelDropdownLabel = styled.div`
  display: flex;
  align-items: center;
  width: 20%;
  color: #4a4a4a;

  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    width: 30%;
  }
`;
