import format from 'date-fns/format';
import PropTypes from 'prop-types';
import React, { useState } from 'react';
import { useQuery } from '@apollo/client';
import DataContainer from '../../components/DataContainer';
import DataTable from '../../components/DataTable';
import { FIELD_TYPES } from '../../components/Form/constants';
import GlobalSearch from '../../components/GlobalSearch';
import Header from '../../components/Header';
import Page from '../../components/Page';
import { GET_PAYMENT_STATUS_INFO } from './query';

const PaymentStatus = ({ history }) => {
  const [pagination, setPagination] = useState({
    limit: 10,
    start: { channelId: '', paymentMethod: '' },
  });
  const [filter, setFilter] = useState({});

  const { data, loading } = useQuery(GET_PAYMENT_STATUS_INFO, {
    notifyOnNetworkStatusChange: true,
    fetchPolicy: 'network-only',
    variables: {
      filter,
      pagination: {
        ...pagination,
        start: {
          channelId: pagination.start.channelId,
          paymentMethod: pagination.start.paymentMethod,
        },
      },
    },
  });

  const channelOptions =
    !loading && data
      ? data.channelsLoose.map(channel => ({
          value: channel.id,
          label: channel.name,
        }))
      : [];

  return (
    <>
      <Page>
        <Header
          withHome
          title="Channel and Payment Gateway Status"
          path={['Channel and Payment Gateway Status']}
        />
        <DataContainer>
          <DataTable
            loading={loading}
            headerOptions={
              <GlobalSearch
                onSearch={filter => {
                  const newPagination = {
                    ...pagination,
                    start: {
                      channelId: '',
                      paymentMethod: '',
                    },
                  };
                  setFilter(filter);
                  setPagination(newPagination);
                }}
                fields={[
                  {
                    label: 'Channel Name',
                    name: 'channelId',
                    type: FIELD_TYPES.MULTISELECT,
                    options: channelOptions,
                  },
                  {
                    label: 'Payment Gateway',
                    name: 'paymentMethod',
                    type: FIELD_TYPES.TEXT,
                  },
                  {
                    label: 'Status',
                    name: 'status',
                    type: FIELD_TYPES.SELECT,
                    options: [
                      { value: null, label: 'Any' },
                      { value: true, label: 'Online' },
                      { value: false, label: 'Offline' },
                    ],
                  },
                  {
                    label: 'Record Date',
                    name: 'updatedAt',
                    type: FIELD_TYPES.DATE_RANGE,
                  },
                ]}
              />
            }
            data={
              data && data.channelAndGatewayStatus
                ? data.channelAndGatewayStatus.filteredData
                : []
            }
            config={{
              channel: {
                headerLabel: 'Channel Name',
                sortable: data => data.channel.name,
                renderAs: data => data.channel.name,
                onClick: data =>
                  history.push('/channels-management/' + data.channel.id),
              },
              paymentMethod: {
                headerLabel: 'Payment Gateway',
                sortable: true,
              },
              status: {
                headerLabel: 'Status',
                sortable: data => (data.status ? 'Online' : 'Offline'),
                renderAs: data => (data.status ? 'Online' : 'Offline'),
              },
              updatedAt: {
                headerLabel: 'Record Date',
                sortable: true,
                renderAs: data =>
                  format(new Date(data.updatedAt), 'MM/DD/YYYY - hh:mm:ss A'),
              },
            }}
            pagination={{
              ...pagination,
              count:
                data && data.channelAndGatewayStatus
                  ? data.channelAndGatewayStatus.count
                  : 0,
              cursors:
                data && data.channelAndGatewayStatus
                  ? data.channelAndGatewayStatus.cursors
                  : [''],
              handleChange: setPagination,
            }}
          />
        </DataContainer>
      </Page>
    </>
  );
};

PaymentStatus.propTypes = {
  history: PropTypes.object,
};

export default PaymentStatus;
