import { gql } from '@apollo/client';

export const GET_PAYMENT_STATUS_INFO = gql`
  query getPaymentStatusInfo(
    $filter: SearchPaymentGatewayInput
    $pagination: PaginationGatewayInput!
  ) {
    channelAndGatewayStatus(filter: $filter, pagination: $pagination) {
      cursors {
        channelId
        paymentMethod
      }
      count
      filteredData {
        channel {
          id
          name
        }
        paymentMethod
        status
        updatedAt
      }
    }

    channelsLoose {
      id
      name
    }
  }
`;
