import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import format from 'date-fns/format';
import React, { useContext, useState } from 'react';
import { useQuery } from '@apollo/client';
import {
  ActionButton,
  ActionButtonsContainer,
} from '../../components/ActionButtons/ActionButtons';
import DataContainer from '../../components/DataContainer';
import DataTable from '../../components/DataTable';
import { FIELD_TYPES } from '../../components/Form/constants';
import GlobalSearch from '../../components/GlobalSearch';
import Header from '../../components/Header';
import AlertModal from '../../components/Modal/AlertModal';
import Page from '../../components/Page';
import AuthContext from '../../context/AuthContext/AuthContext';
import { useMutation } from '@apollo/client';
import { UPDATE_PROVIDER } from './mutation';
import { GET_PROVIDER_MANAGEMENT_INFO } from './query';

function isAllProviderOn(data) {
  return (
    data.adyenEnabled === true &&
    data.gcashEnabled === true &&
    data.ipay88Enabled === true &&
    data.xenditEnabled === true &&
    data.bpiEnabled === true
  );
}

const STATUS = {
  online: {
    color: '#66B825',
    icon: 'toggle-on',
  },
  offline: {
    color: 'gray',
    icon: 'toggle-off',
  },
};

const ProviderManagement = () => {
  const { permissions } = useContext(AuthContext);

  const [state, setState] = useState({
    pagination: {
      limit: 10,
      start: '',
    },
    filter: {},
    selectedProvider: null,
    selectedProviderReason: null,
    isSuccessUpdateModalOpen: false,
    isFailureUpdateModalOpen: false,
  });

  const { data, loading, refetch } = useQuery(GET_PROVIDER_MANAGEMENT_INFO, {
    variables: { pagination: state.pagination, filter: state.filter },
    fetchPolicy: 'network-only',
  });

  const [editProvider, { loading: isEditingProvider }] = useMutation(
    UPDATE_PROVIDER,
    {
      onCompleted: () => {
        setState({ ...state, isSuccessUpdateModalOpen: true });
        refetch();
      },
      onError: () => {
        setState({ ...state, isFailureUpdateModalOpen: true });
      },
    }
  );

  return (
    <>
      <Page>
        <Header
          withHome
          title="Payment Gateway"
          path={['Provider Mgt.', 'Payment Gateway']}
        />
        <DataContainer>
          <DataTable
            loading={loading}
            data={data && data.providers ? data.providers.filteredData : []}
            headerOptions={
              <GlobalSearch
                onSearch={filter => {
                  setState({
                    ...state,
                    filter,
                    pagination: { ...state.pagination, start: '' },
                  });
                }}
                fields={[
                  {
                    label: 'Channel Name',
                    name: 'name',
                    type: FIELD_TYPES.TEXT,
                  },
                  {
                    label: 'Email Address',
                    name: 'email',
                    type: FIELD_TYPES.TEXT,
                  },
                  {
                    label: 'IP Address',
                    name: 'ipAddress',
                    type: FIELD_TYPES.TEXT,
                  },
                  {
                    label: 'Date Created',
                    name: 'createdAt',
                    type: FIELD_TYPES.DATE_RANGE,
                  },
                ]}
              />
            }
            config={{
              name: {
                headerLabel: 'Channel Name',
                sortable: true,
              },
              email: {
                headerLabel: 'Email Address',
                sortable: true,
              },
              ipAddress: {
                headerLabel: 'IP Address',
                sortable: true,
              },
              createdAt: {
                headerLabel: 'Date Created',
                sortable: true,
                renderAs: data =>
                  format(new Date(data.createdAt), 'MM/DD/YYYY - hh:mm:ss A'),
              },
              adyenEnabled: {
                headerLabel: 'Adyen Status',
                renderAs: data => (
                  <ActionButtonsContainer>
                    <ActionButton
                      disabled={!permissions.Provider.update}
                      onClick={() =>
                        setState({
                          ...state,
                          selectedProvider: {
                            ...data,
                            providerName: 'Adyen',
                            providerField: 'adyenEnabled',
                          },
                          selectedProviderReason: {
                            adyenEnabled: data.adyenEnabled,
                          },
                        })
                      }
                    >
                      <FontAwesomeIcon
                        color={
                          data.adyenEnabled === null ||
                          data.adyenEnabled === false
                            ? STATUS.offline.color
                            : STATUS.online.color
                        }
                        icon={
                          data.adyenEnabled === null ||
                          data.adyenEnabled === false
                            ? STATUS.offline.icon
                            : STATUS.online.icon
                        }
                      />
                    </ActionButton>
                  </ActionButtonsContainer>
                ),
              },
              gcashEnabled: {
                headerLabel: 'GCash Status',
                renderAs: data => (
                  <ActionButtonsContainer>
                    <ActionButton
                      disabled={!permissions.Provider.update}
                      onClick={() =>
                        setState({
                          ...state,
                          selectedProvider: {
                            ...data,
                            providerName: 'GCash',
                            providerField: 'gcashEnabled',
                          },
                          selectedProviderReason: {
                            gcashEnabled: data.gcashEnabled,
                          },
                        })
                      }
                    >
                      <FontAwesomeIcon
                        color={
                          data.gcashEnabled === null ||
                          data.gcashEnabled === false
                            ? STATUS.offline.color
                            : STATUS.online.color
                        }
                        icon={
                          data.gcashEnabled === null ||
                          data.gcashEnabled === false
                            ? STATUS.offline.icon
                            : STATUS.online.icon
                        }
                      />
                    </ActionButton>
                  </ActionButtonsContainer>
                ),
              },
              ipay88Enabled: {
                headerLabel: 'iPay88 Status',
                renderAs: data => (
                  <ActionButtonsContainer>
                    <ActionButton
                      disabled={!permissions.Provider.update}
                      onClick={() =>
                        setState({
                          ...state,
                          selectedProvider: {
                            ...data,
                            providerName: 'iPay88',
                            providerField: 'ipay88Enabled',
                          },
                          selectedProviderReason: {
                            ipay88Enabled: data.ipay88Enabled,
                          },
                        })
                      }
                    >
                      <FontAwesomeIcon
                        color={
                          data.ipay88Enabled === null ||
                          data.ipay88Enabled === false
                            ? STATUS.offline.color
                            : STATUS.online.color
                        }
                        icon={
                          data.ipay88Enabled === null ||
                          data.ipay88Enabled === false
                            ? STATUS.offline.icon
                            : STATUS.online.icon
                        }
                      />
                    </ActionButton>
                  </ActionButtonsContainer>
                ),
              },
              bpiEnabled: {
                headerLabel: 'BPI Status',
                renderAs: data => (
                  <ActionButtonsContainer>
                    <ActionButton
                      disabled={!permissions.Provider.update}
                      onClick={() =>
                        setState({
                          ...state,
                          selectedProvider: {
                            ...data,
                            providerName: 'BPI',
                            providerField: 'bpiEnabled',
                          },
                          selectedProviderReason: {
                            bpiEnabled: data.bpiEnabled,
                          },
                        })
                      }
                    >
                      <FontAwesomeIcon
                        color={
                          data.bpiEnabled === null || data.bpiEnabled === false
                            ? STATUS.offline.color
                            : STATUS.online.color
                        }
                        icon={
                          data.bpiEnabled === null || data.bpiEnabled === false
                            ? STATUS.offline.icon
                            : STATUS.online.icon
                        }
                      />
                    </ActionButton>
                  </ActionButtonsContainer>
                ),
              },
              xenditEnabled: {
                headerLabel: 'Xendit Status',
                renderAs: data => (
                  <ActionButtonsContainer>
                    <ActionButton
                      disabled={!permissions.Provider.update}
                      onClick={() =>
                        setState({
                          ...state,
                          selectedProvider: {
                            ...data,
                            providerName: 'Xendit',
                            providerField: 'xenditEnabled',
                          },
                          selectedProviderReason: {
                            xenditEnabled: data.xenditEnabled,
                          },
                        })
                      }
                    >
                      <FontAwesomeIcon
                        color={
                          data.xenditEnabled === null ||
                          data.xenditEnabled === false
                            ? STATUS.offline.color
                            : STATUS.online.color
                        }
                        icon={
                          data.xenditEnabled === null ||
                          data.xenditEnabled === false
                            ? STATUS.offline.icon
                            : STATUS.online.icon
                        }
                      />
                    </ActionButton>
                  </ActionButtonsContainer>
                ),
              },
              allProvider: {
                headerLabel: 'All Provider',
                renderAs: data => (
                  <ActionButtonsContainer>
                    <ActionButton
                      disabled={!permissions.Provider.update}
                      onClick={() =>
                        setState({
                          ...state,
                          selectedProvider: {
                            ...data,
                            providerName: 'All Provider',
                            providerField: 'all',
                          },
                          selectedProviderReason: {
                            adyenEnabled: data.adyenEnabled,
                            gcashEnabled: data.gcashEnabled,
                            ipay88Enabled: data.ipay88Enabled,
                            bpiEnabled: data.bpiEnabled,
                            xenditEnabled: data.xenditEnabled,
                          },
                        })
                      }
                    >
                      <FontAwesomeIcon
                        color={
                          isAllProviderOn(data)
                            ? STATUS.online.color
                            : STATUS.offline.color
                        }
                        icon={
                          isAllProviderOn(data)
                            ? STATUS.online.icon
                            : STATUS.offline.icon
                        }
                      />
                    </ActionButton>
                  </ActionButtonsContainer>
                ),
              },
            }}
            pagination={{
              ...state.pagination,
              count: data && data.providers ? data.providers.count : 0,
              cursors: data && data.providers ? data.providers.cursors : [''],
              handleChange: pagination => {
                setState({ ...state, pagination });
              },
            }}
          />
        </DataContainer>
      </Page>
      {!!state.selectedProvider && (
        <AlertModal
          isOpen={!!state.selectedProvider}
          title="Payment Gateway Alert"
          variant="warn"
          icon="exclamation-circle"
          header="ARE YOU SURE?"
          subHeader={
            <span>
              You are about to{' '}
              {(
                state.selectedProvider.providerField === 'all'
                  ? isAllProviderOn(state.selectedProviderReason)
                  : state.selectedProviderReason.adyenEnabled === true ||
                    state.selectedProviderReason.gcashEnabled === true ||
                    state.selectedProviderReason.ipay88Enabled === true ||
                    state.selectedProviderReason.bpiEnabled === true ||
                    state.selectedProviderReason.xenditEnabled === true
              )
                ? 'disable'
                : 'enable'}{' '}
              {state.selectedProvider.providerName}
              {' for '}
              <span style={{ color: '#0090E1' }}>
                {state.selectedProvider.name}
              </span>
              ?
            </span>
          }
          selectLabel="Reason"
          options={[
            (
              state.selectedProvider.providerField === 'all'
                ? isAllProviderOn(state.selectedProviderReason)
                : state.selectedProviderReason.adyenEnabled === true ||
                  state.selectedProviderReason.gcashEnabled === true ||
                  state.selectedProviderReason.ipay88Enabled === true ||
                  state.selectedProviderReason.bpiEnabled === true ||
                  state.selectedProviderReason.xenditEnabled === true
            )
              ? 'No longer in use'
              : 'Approved for use',
            'Others',
          ].map(reason => ({ value: reason, label: reason }))}
          description={[
            'This action requires your confirmation.',
            'Action cannot be undone.',
          ]}
          confirmLoading={isEditingProvider}
          handleConfirm={async reason => {
            if (state.selectedProvider.providerField === 'all') {
              let allProviderValue = isAllProviderOn(state.selectedProvider);
              editProvider({
                variables: {
                  where: { id: state.selectedProvider.id },
                  data: {
                    adyenEnabled: !allProviderValue,
                    gcashEnabled: !allProviderValue,
                    ipay88Enabled: !allProviderValue,
                    bpiEnabled: !allProviderValue,
                    xenditEnabled: !allProviderValue,
                    reasonToUpdate: reason,
                  },
                },
              });
            } else {
              let providerState =
                state.selectedProvider[state.selectedProvider.providerField];
              editProvider({
                variables: {
                  where: { id: state.selectedProvider.id },
                  data: {
                    [state.selectedProvider.providerField]: !providerState,
                    reasonToUpdate: reason,
                  },
                },
              });
            }
          }}
          confirmText="Yes"
          handleClose={() => {
            setState({ ...state, selectedProvider: null });
          }}
        />
      )}
      {!!state.selectedProvider && (
        <AlertModal
          isOpen={state.isSuccessUpdateModalOpen}
          title="Payment Gateway Alert"
          variant="success"
          icon="check-circle"
          header="SUCCESS!"
          subHeader={
            <span>
              <span style={{ color: '#0090E1' }}>
                {state.selectedProvider.name}
              </span>{' '}
              has been successfully{' '}
              {(
                state.selectedProvider.providerField === 'all'
                  ? isAllProviderOn(state.selectedProviderReason)
                  : state.selectedProviderReason.adyenEnabled === true ||
                    state.selectedProviderReason.gcashEnabled === true ||
                    state.selectedProviderReason.ipay88Enabled === true ||
                    state.selectedProviderReason.bpiEnabled === true ||
                    state.selectedProviderReason.xenditEnabled === true
              )
                ? 'disabled'
                : 'enabled'}
              .
            </span>
          }
          description={['Related Channels will be notified about this action']}
          handleConfirm={() => {
            setState({
              ...state,
              isSuccessUpdateModalOpen: false,
              selectedProvider: null,
            });
          }}
          confirmText="Go to All Payment Gateway"
          handleClose={() => {
            setState({
              ...state,
              isSuccessUpdateModalOpen: false,
              selectedProvider: null,
            });
          }}
        />
      )}
      <AlertModal
        isOpen={state.isFailureUpdateModalOpen}
        title="Payment Gateway Alert"
        variant="error"
        icon="check-circle"
        header="OH, SNAP!"
        subHeader={'There was problem updating the provider.'}
        description={['Please go back and try saving it again.']}
        handleConfirm={() => {
          setState({
            ...state,
            isFailureUpdateModalOpen: false,
            selectedProvider: null,
          });
        }}
        confirmText="Go to All Payment Gateway"
        handleClose={() => {
          setState({
            ...state,
            isFailureUpdateModalOpen: false,
            selectedProvider: null,
          });
        }}
      />
    </>
  );
};

ProviderManagement.propTypes = {};

export default ProviderManagement;
