import { gql } from '@apollo/client';

export const GET_PROVIDER_MANAGEMENT_INFO = gql`
  query getProviderManagementInfo(
    $filter: SearchProviderFilterInput!
    $pagination: PaginationInput!
  ) {
    providers(filter: $filter, pagination: $pagination) {
      cursors
      count
      filteredData {
        id
        name
        email
        ipAddress
        isEnabled
        createdAt
        adyenEnabled
        gcashEnabled
        ipay88Enabled
        bpiEnabled
        xenditEnabled
        isEnabled
      }
    }
  }
`;
