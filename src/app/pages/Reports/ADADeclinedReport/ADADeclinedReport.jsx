import format from 'date-fns/format';
import React, { useContext, useState } from 'react';
import { ExportButton } from '../../../components/Button/ExportButton';
import { useQuery } from '@apollo/client';
import DataContainer from '../../../components/DataContainer';
import DataTable from '../../../components/DataTable';
import { FIELD_TYPES } from '../../../components/Form/constants';
import GlobalSearch from '../../../components/GlobalSearch';
import Header from '../../../components/Header';
import { AlertModal } from '../../../components/Modal';
import Page from '../../../components/Page';
import AuthContext from '../../../context/AuthContext/AuthContext';
import { useMutation } from '@apollo/client';
import useQueryReportSeries from '../../../hooks/useQueryReportSeries';
import { EXPORT_REPORTS } from '../mutation';
import {
  ColumnVisibilityDropdown,
  ResponsiveRow,
  TransactionLogHighlight,
} from '../styled';
import {
  GET_ADADECLINED_REPORT,
  GET_CHANNEL_OPTIONS,
  REPORT_PATH,
} from './query';
import NotificationContext from '../../../context/NotificationContext';
import { numberWithCommas } from '../../../components/GlobalSearch/utils';

const ADADeclinedReport = () => {
  const { reportPermissions } = useContext(AuthContext);
  const {
    pagination,
    setNewPagination,
    filter,
    setFilter,
    data,
    loading,
    page,
    setPage,
    isLastPage,
  } = useQueryReportSeries(GET_ADADECLINED_REPORT, REPORT_PATH, {
    pagination: {
      startKeys: '',
      limit: 10,
    },
  });

  const { addNotif } = useContext(NotificationContext);

  const [isConfirmDownloadModalOpen, setIsConfirmDownloadModalOpen] =
    useState(false);
  const [isSuccessDownloadModalOpen, setIsSuccessDownloadModalOpen] =
    useState(false);

  const [logExtraction] = useMutation(EXPORT_REPORTS);

  const { data: channelData, loading: isLoadingChannels } =
    useQuery(GET_CHANNEL_OPTIONS);

  const channelOptions =
    !isLoadingChannels && channelData
      ? channelData.channelsLoose.map(channel => ({
          value: channel.id,
          label: channel.name,
        }))
      : [];

  channelOptions.unshift({
    value: null,
    label: 'Any',
  });

  const tableConfig = {
    accountNumber: {
      headerLabel: 'Account Number',
      sortable: true,
    },
    isActive: {
      headerLabel: 'Account Status',
      sortable: true,
      renderAs: data => data.isActive && 'Active',
    },
    channelName: {
      headerLabel: 'Channel',
      sortable: true,
    },
    transId: {
      headerLabel: 'Transaction ID',
      sortable: true,
    },
    timestamp: {
      headerLabel: 'Date',
      sortable: true,
      renderAs: data => format(data.timestamp, 'MM/DD/YYYY - hh:mm:ss A'),
    },
    enrollmentType: {
      headerLabel: 'Direct Debit Enrollment',
      sortable: true,
    },
    ccNumber: {
      headerLabel: 'Credit Card Number',
      sortable: true,
    },
    ccType: {
      headerLabel: 'Card Type',
      sortable: true,
    },
    bankName: {
      headerLabel: 'Bank Name',
      sortable: true,
    },
    tokenId: {
      headerLabel: 'Token Number',
      sortable: true,
    },
    amount: {
      headerLabel: 'Amount',
      sortable: true,
      renderAs: data => numberWithCommas(data.amount, 2),
    },
    status: {
      headerLabel: 'Payment Status',
      sortable: true,
    },
    reason: {
      headerLabel: 'Payment Reason',
      sortable: true,
    },
    entity: {
      headerLabel: 'Entity',
      sortable: true,
    },
    currency: {
      headerLabel: 'Account Currency',
      sortable: true,
    },
    userContact: {
      headerLabel: 'MSISDN',
      sortable: true,
    },
    userEmail: {
      headerLabel: 'Email Addess',
      sortable: true,
    },
  };

  const [visibleColumns, setVisibleColumns] = useState(
    Object.keys(tableConfig)
  );

  return (
    <>
      <Page>
        <Header
          withHome
          title="ADA Declined Rate Detailed Report"
          path={['Reports', 'ADA Declined Rate Detailed Report']}
        />
        <DataContainer>
          <DataTable
            minCellWidth={200}
            headerOptions={
              <>
                <GlobalSearch
                  onSearch={filter => {
                    const newPagination = {
                      ...pagination,
                      startKeys: '',
                    };
                    setFilter(filter);
                    setNewPagination(newPagination);
                  }}
                  fields={[
                    {
                      label: 'Account No.',
                      name: 'accountNumber',
                      type: FIELD_TYPES.TEXT,
                      isKey: true,
                    },
                    {
                      label: 'Channel',
                      name: 'channelId',
                      type: FIELD_TYPES.SELECT,
                      options: channelOptions,
                      isKey: true,
                    },
                    {
                      label: 'Entity',
                      name: 'entity',
                      type: FIELD_TYPES.SELECT,
                      options: [
                        { value: null, label: 'Any' },
                        { value: 'G', label: 'Globe-msisdn' },
                        { value: 'B', label: 'Bayan' },
                        { value: 'I', label: 'Innove-wireline' },
                        {
                          value: 'N',
                          label: 'Innove-iccbs',
                        },
                      ],
                    },
                    {
                      label: 'Payment Status',
                      name: 'status',
                      type: FIELD_TYPES.SELECT,
                      options: [
                        { value: null, label: 'Any' },
                        {
                          value: 'Declined',
                          label: 'Declined',
                        },
                        { value: 'Approved', label: 'Approved' },
                      ],
                    },
                    {
                      label: 'Date Range',
                      name: 'timestamp',
                      type: FIELD_TYPES.DATE_RANGE,
                    },
                  ]}
                />
                <ResponsiveRow>
                  {reportPermissions.ADADeclinedReport.export && (
                    <ExportButton
                      icon="file-csv"
                      iconPosition="left"
                      disabled={loading}
                      onClick={() => setIsConfirmDownloadModalOpen(true)}
                    >
                      CSV
                    </ExportButton>
                  )}
                  <ColumnVisibilityDropdown
                    multi
                    showMulti={false}
                    placeholder="Visible Columns"
                    value={visibleColumns}
                    options={Object.keys(tableConfig).map(key => ({
                      value: key,
                      label: tableConfig[key].headerLabel,
                    }))}
                    onChange={setVisibleColumns}
                  />
                </ResponsiveRow>
              </>
            }
            loading={loading}
            data={data}
            config={Object.keys(tableConfig).reduce((config, key) => {
              if (visibleColumns.includes(key)) {
                config[key] = tableConfig[key];
              }
              return config;
            }, {})}
            pagination={{
              ...pagination,
              start: pagination.startKeys,
              withStartKeys: true,
              count: 0,
              cursors: [],
              handleChange: setNewPagination,
            }}
            series={{
              page,
              setPage,
              isLastPage,
            }}
          />
        </DataContainer>
      </Page>
      <AlertModal
        isOpen={isConfirmDownloadModalOpen}
        title="Export Reports Alert"
        header="ARE YOU SURE?"
        variant="warn"
        icon="exclamation-circle"
        subHeader={
          <span>
            You are about to export all filtered records as .CSV File.
          </span>
        }
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() => setIsConfirmDownloadModalOpen(false)}
        handleCancel={() => setIsConfirmDownloadModalOpen(false)}
        confirmText="Yes"
        handleConfirm={() => {
          const notifTime = new Date().getTime();
          setIsConfirmDownloadModalOpen(false);
          addNotif({
            id: 'GOTS-' + notifTime,
            notifTime,
            type: 'info',
            title: 'Downloading Report File',
            message: 'Downloading ADA Declined Rate Detailed Report',
            isProgress: true,
            progressData: {
              progress: 0,
              isProgressive: false,
            },
            isLocal: true,
            data: {},
            reportDLParams: {
              query: GET_ADADECLINED_REPORT,
              path: REPORT_PATH,
              variables: {
                filter,
                pagination: {
                  startKeys: '',
                  limit: 1000,
                },
              },
              onDownload: () => {
                logExtraction({
                  variables: {
                    data: {
                      type: 'ada',
                    },
                  },
                });
              },
              tableConfig,
              fileName: `ADA Declined Rate Detailed Report ${format(new Date(), 'MMDDYYYY')}.csv`,
            },
          });
        }}
      />
      <AlertModal
        isOpen={isSuccessDownloadModalOpen}
        title="Export Reports Alert"
        header="SUCCESS!"
        variant="success"
        icon="check-circle"
        subHeader={
          <span>
            You exported{' '}
            <TransactionLogHighlight>
              {data.length} row(s){' '}
            </TransactionLogHighlight>{' '}
            as .CSV File succesfully.
          </span>
        }
        description="Kindly check the downloaded file."
        confirmText="Back to All Reports"
        handleClose={() => setIsSuccessDownloadModalOpen(false)}
        handleConfirm={() => {
          setIsSuccessDownloadModalOpen(false);
        }}
      />
    </>
  );
};

ADADeclinedReport.propTypes = {};

export default ADADeclinedReport;
