import { gql } from '@apollo/client';

export const GET_CHANNEL_OPTIONS = gql`
  query getChannelOptions {
    channelsLoose {
      id
      name
      channelId
    }
  }
`;

export const GET_ADADECLINED_REPORT = gql`
  query getadaDeclinedDetailedReport(
    $filter: adaDeclinedDetailedInput!
    $pagination: PaginationReportInput!
  ) {
    adaDeclinedDetailedReport(filter: $filter, pagination: $pagination) {
      lastKey
      filteredData {
        accountNumber
        isActive
        channelName
        transId
        timestamp
        enrollmentType
        ccNumber
        ccType
        bankName
        tokenId
        amount
        status
        reason
        entity
        currency
        userContact
        userEmail
      }
    }
  }
`;

export const REPORT_PATH = 'adaDeclinedDetailedReport';
