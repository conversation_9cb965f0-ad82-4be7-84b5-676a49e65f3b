import React, { useContext, useState } from 'react';
import { ExportButton } from '../../../components/Button/ExportButton';
import DataContainer from '../../../components/DataContainer';
import DataTable from '../../../components/DataTable';
import { FIELD_TYPES } from '../../../components/Form/constants';
import GlobalSearch from '../../../components/GlobalSearch';
import Header from '../../../components/Header';
import { AlertModal } from '../../../components/Modal';
import Page from '../../../components/Page';
import AuthContext from '../../../context/AuthContext/AuthContext';
import { useMutation } from '@apollo/client';
import useQueryFilterSeries from '../../../hooks/useQueryFilterSeries';
import { EXPORT_REPORTS } from '../mutation';
import {
  ColumnVisibilityDropdown,
  ResponsiveRow,
  TransactionLogHighlight,
} from '../styled';
import { GET_ADASUMMARY_REPORT, REPORT_PATH } from './query';
import NotificationContext from '../../../context/NotificationContext';
import {
  numberWithCommas,
  MONTHS,
} from '../../../components/GlobalSearch/utils';

const ADASummaryReport = () => {
  const { reportPermissions } = useContext(AuthContext);

  const [state, setState] = useState({
    filter: {
      month: {
        start: MONTHS[0].value,
        end: MONTHS[11].value,
      },
      year: {
        start: new Date().getFullYear(),
        end: new Date().getFullYear(),
      },
    },
  });

  const { data, loading, page, setPage, isLastPage } = useQueryFilterSeries(
    GET_ADASUMMARY_REPORT,
    REPORT_PATH,
    {
      filter: state.filter,
    },
    null,
    false
  );

  const { addNotif } = useContext(NotificationContext);

  const [isConfirmDownloadModalOpen, setIsConfirmDownloadModalOpen] =
    useState(false);
  const [isSuccessDownloadModalOpen, setIsSuccessDownloadModalOpen] =
    useState(false);

  const [logExtraction] = useMutation(EXPORT_REPORTS);

  const tableConfig = {
    month: {
      headerLabel: 'Month',
      sortable: true,
    },
    bank: {
      headerLabel: 'Bank',
      sortable: true,
    },
    numberOfBilledAccount: {
      headerLabel: 'No. of Accounts Billed',
      sortable: true,
      renderAs: data => numberWithCommas(data.numberOfBilledAccount, 0),
    },
    totalAmountAR: {
      headerLabel: 'Total Amount A/R',
      sortable: true,
      renderAs: data => numberWithCommas(data.totalAmountAR, 2),
    },
    approvedTransCount: {
      headerLabel: '# Approved Accounts',
      sortable: true,
      renderAs: data => numberWithCommas(data.approvedTransCount, 0),
    },
    approvedTransAmount: {
      headerLabel: 'Approved Amount',
      sortable: true,
      renderAs: data => numberWithCommas(data.approvedTransAmount, 2),
    },
    declinedTransCount: {
      headerLabel: '# Declined Accounts',
      sortable: true,
      renderAs: data => numberWithCommas(data.declinedTransCount, 0),
    },
    declinedTransAmount: {
      headerLabel: 'Declined Amount',
      sortable: true,
      renderAs: data => numberWithCommas(data.declinedTransAmount, 2),
    },
    declinedRate: {
      headerLabel: 'Decline Rate',
      sortable: true,
      renderAs: data => `${data.declinedRate} %`,
    },
  };

  const [visibleColumns, setVisibleColumns] = useState(
    Object.keys(tableConfig)
  );

  return (
    <>
      <Page>
        <Header
          withHome
          title="ADA Declined Rate Summary Report"
          path={['Reports', 'ADA Declined Rate Summary Report']}
        />
        <DataContainer>
          <DataTable
            minCellWidth={200}
            headerOptions={
              <>
                <GlobalSearch
                  onSearch={filter => {
                    setState({ ...state, filter });
                  }}
                  fields={[
                    {
                      label: 'Bank',
                      name: 'bank',
                      type: FIELD_TYPES.TEXT,
                      isKey: true,
                    },
                    {
                      label: 'Month',
                      name: 'month',
                      type: FIELD_TYPES.MONTH_RANGE,
                    },
                    {
                      label: 'Year',
                      name: 'year',
                      type: FIELD_TYPES.YEAR_RANGE,
                    },
                  ]}
                />
                <ResponsiveRow>
                  {reportPermissions.ADASummaryReport.export && (
                    <ExportButton
                      icon="file-csv"
                      iconPosition="left"
                      disabled={loading}
                      onClick={() => setIsConfirmDownloadModalOpen(true)}
                    >
                      CSV
                    </ExportButton>
                  )}
                  <ColumnVisibilityDropdown
                    multi
                    showMulti={false}
                    placeholder="Visible Columns"
                    value={visibleColumns}
                    options={Object.keys(tableConfig).map(key => ({
                      value: key,
                      label: tableConfig[key].headerLabel,
                    }))}
                    onChange={setVisibleColumns}
                  />
                </ResponsiveRow>
              </>
            }
            loading={loading}
            data={data}
            config={Object.keys(tableConfig).reduce((config, key) => {
              if (visibleColumns.includes(key)) {
                config[key] = tableConfig[key];
              }
              return config;
            }, {})}
            series={{
              page,
              setPage,
              isLastPage,
            }}
          />
        </DataContainer>
      </Page>
      <AlertModal
        isOpen={isConfirmDownloadModalOpen}
        title="Export Reports Alert"
        header="ARE YOU SURE?"
        variant="warn"
        icon="exclamation-circle"
        subHeader={
          <span>
            You are about to export all filtered records as .CSV File.
          </span>
        }
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() => setIsConfirmDownloadModalOpen(false)}
        handleCancel={() => setIsConfirmDownloadModalOpen(false)}
        confirmText="Yes"
        handleConfirm={() => {
          const notifTime = new Date().getTime();
          setIsConfirmDownloadModalOpen(false);
          addNotif({
            id: 'TRANS-' + notifTime,
            notifTime,
            type: 'info',
            title: 'Downloading Report File',
            message: 'Downloading Channel Transactions Report',
            isProgress: true,
            progressData: {
              progress: 0,
              isProgressive: false,
            },
            isLocal: true,
            data: {},
            reportDLParams: {
              query: GET_ADASUMMARY_REPORT,
              path: REPORT_PATH,
              variables: {
                filter: state.filter,
              },
              hasLastKey: false,
              onDownload: () => {
                logExtraction({
                  variables: {
                    data: {
                      type: 'adasummaryreport',
                    },
                  },
                });
              },
              tableConfig,
              fileName: 'ADA Declined Rate Summary Report.csv',
            },
          });
        }}
      />
      <AlertModal
        isOpen={isSuccessDownloadModalOpen}
        title="Export Reports Alert"
        header="SUCCESS!"
        variant="success"
        icon="check-circle"
        subHeader={
          <span>
            You exported{' '}
            <TransactionLogHighlight>
              {data.length} row(s){' '}
            </TransactionLogHighlight>{' '}
            as .CSV File succesfully.
          </span>
        }
        description="Kindly check the downloaded file."
        confirmText="Back to All Reports"
        handleClose={() => setIsSuccessDownloadModalOpen(false)}
        handleConfirm={() => {
          setIsSuccessDownloadModalOpen(false);
        }}
      />
    </>
  );
};

ADASummaryReport.propTypes = {};

export default ADASummaryReport;
