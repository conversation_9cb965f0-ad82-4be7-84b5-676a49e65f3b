import { gql } from '@apollo/client';

export const GET_ADASUMMARY_REPORT = gql`
  query getadaSummaryReport($filter: adaSummaryInput!) {
    adaSummaryReport(filter: $filter) {
      filteredData {
        month
        bank
        numberOfBilledAccount
        totalAmountAR
        approvedTransCount
        approvedTransAmount
        declinedTransCount
        declinedTransAmount
        declinedRate
      }
    }
  }
`;

export const REPORT_PATH = 'adaSummaryReport';
