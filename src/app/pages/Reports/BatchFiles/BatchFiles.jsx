import format from 'date-fns/format';
import React, { useState } from 'react';
import useQueryReportSeries from '../../../hooks/useQueryReportSeries';
import { useMutation } from '@apollo/client';
import { GET_BATCH_FILES, REPORT_PATH } from './query';
import { EXPORT_S3_BATCH_FILES } from './mutation';
import Page from '../../../components/Page';
import Header from '../../../components/Header';
import DataTable from '../../../components/DataTable';
import DataContainer from '../../../components/DataContainer';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import GlobalSearch from '../../../components/GlobalSearch';
import { FIELD_TYPES } from '../../../components/Form/constants';

const BatchFiles = () => {
  const {
    pagination,
    setNewPagination,
    setFilter,
    data,
    loading,
    page,
    setPage,
    isLastPage,
  } = useQueryReportSeries(GET_BATCH_FILES, REPORT_PATH, {
    pagination: {
      startKey: '',
      limit: 10,
    },
  });

  const [state, setState] = useState({
    isFailureDownloadFile: false,
  });

  const [csvExtraction] = useMutation(EXPORT_S3_BATCH_FILES, {
    onCompleted: async data => {
      window.open(data.downloadS3LukeBatchFiles.urlLink, '_blank');
    },
    onError: async () => {
      setState({
        ...state,
        isFailureDownloadFile: true,
      });
    },
  });

  const tableConfig = {
    createdAt: {
      headerLabel: 'Generated Date',
      sortable: true,
      renderAs: data => format(data.createdAt, 'MM/DD/YYYY - hh:mm:ss A'),
    },
    filename: {
      headerLabel: 'File Name',
      sortable: true,
    },
    actions: {
      headerLabel: 'Actions',
      renderAs: data => (
        <FontAwesomeIcon
          style={{ fontSize: '20px', cursor: 'pointer' }}
          icon="file-download"
          color="#787878"
          onClick={() => {
            csvExtraction({
              variables: {
                data: { filename: data.filename },
              },
            });
          }}
        />
      ),
    },
  };

  return (
    <>
      <Page>
        <Header
          withHome
          title="Batch Files"
          path={['Reports', 'Batch Files']}
        />
        <DataContainer>
          <DataTable
            loading={loading}
            data={data}
            minCellWidth={200}
            headerOptions={
              <>
                <GlobalSearch
                  onSearch={filter => {
                    const newPagination = {
                      ...pagination,
                      startKey: '',
                    };
                    setFilter(filter);
                    setNewPagination(newPagination);
                  }}
                  fields={[
                    {
                      label: 'File Name',
                      name: 'filename',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Generated Date',
                      name: 'createdAt',
                      type: FIELD_TYPES.DATE_RANGE,
                    },
                  ]}
                />
              </>
            }
            config={tableConfig}
            pagination={{
              ...pagination,
              start: pagination.startKey,
              withStartKeys: true,
              count: 0,
              cursors: [],
              handleChange: setNewPagination,
            }}
            series={{ page, setPage, isLastPage }}
          />
        </DataContainer>
      </Page>
    </>
  );
};

export default BatchFiles;
