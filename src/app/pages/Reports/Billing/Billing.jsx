import format from 'date-fns/format';
import React, { useContext, useState } from 'react';
import { useQuery } from '@apollo/client';
import { ExportButton } from '../../../components/Button/ExportButton';
import DataContainer from '../../../components/DataContainer';
import DataTable from '../../../components/DataTable';
import { FIELD_TYPES } from '../../../components/Form/constants';
import GlobalSearch from '../../../components/GlobalSearch';
import Header from '../../../components/Header';
import { AlertModal } from '../../../components/Modal';
import Page from '../../../components/Page';
import AuthContext from '../../../context/AuthContext/AuthContext';
import { useMutation } from '@apollo/client';
import useQueryReportSeries from '../../../hooks/useQueryReportSeries-v2';
import formatCurrency from '../../../utils/formatCurrency';
import { EXPORT_REPORTS } from '../mutation';
import {
  ColumnVisibilityDropdown,
  ResponsiveRow,
  TransactionLogHighlight,
} from '../styled';
import { GET_CHANNEL_OPTIONS, GET_BILLING_REPORTS, REPORT_PATH } from './query';
import NotificationContext from '../../../context/NotificationContext';

const Billing = () => {
  const { reportPermissions } = useContext(AuthContext);
  const {
    pagination,
    filter,
    setNewPagination,
    setFilter,
    data,
    loading,
    page,
    setPage,
    isLastPage,
  } = useQueryReportSeries(GET_BILLING_REPORTS, REPORT_PATH, {
    pagination: {
      startKey: '',
      limit: 10,
    },
  });

  const { addNotif } = useContext(NotificationContext);

  const [isConfirmDownloadModalOpen, setIsConfirmDownloadModalOpen] =
    useState(false);
  const [isSuccessDownloadModalOpen, setIsSuccessDownloadModalOpen] =
    useState(false);

  const { data: channelData, loading: isLoadingChannels } =
    useQuery(GET_CHANNEL_OPTIONS);

  const [logExtraction] = useMutation(EXPORT_REPORTS);

  const channelOptions =
    !isLoadingChannels && channelData
      ? channelData.channelsLoose.map(channel => ({
          value: channel.id,
          label: channel.name,
        }))
      : [];

  channelOptions.unshift({
    value: null,
    label: 'Any',
  });

  const tableConfig = {
    createDateTime: {
      headerLabel: 'Record Date Time',
      sortable: data =>
        format(new Date(data.createDateTime), 'MM/DD/YYYY - hh:mm:ss A'),
      renderAs: data =>
        format(new Date(data.createDateTime), 'MM/DD/YYYY - hh:mm:ss A'),
    },
    paymentId: {
      headerLabel: 'Payment ID',
      sortable: true,
    },
    channelName: {
      headerLabel: 'Channel Name',
      sortable: true,
    },
    gatewayProcessor: {
      headerLabel: 'Gateway Processor',
      sortable: true,
    },
    paymentMethod: {
      headerLabel: 'Payment Method',
      sortable: true,
    },
    totalAmount: {
      headerLabel: 'Total Amount',
      sortable: true,
      renderAs: data => formatCurrency(+data.totalAmount, true),
      textAlign: 'right',
    },
    status: {
      headerLabel: 'Payment Status',
      sortable: true,
    },
    updateDateTime: {
      headerLabel: 'Last Updated',
      sortable: data =>
        format(new Date(data.updateDateTime), 'MM/DD/YYYY - hh:mm:ss A'),
      renderAs: data =>
        format(new Date(data.updateDateTime), 'MM/DD/YYYY - hh:mm:ss A'),
    },
    settlementAccountNumber: {
      headerLabel: 'Settlement Account',
      sortable: true,
    },
    settlementAccountType: {
      headerLabel: 'Settlement Account Type',
      sortable: true,
    },
    settlementAmountValue: {
      headerLabel: 'Settlement Amount',
      sortable: true,
      renderAs: data => formatCurrency(data.settlementAmountValue, true),
    },
    settlementBrand: {
      headerLabel: 'Settlement Brand',
      sortable: true,
    },
    settlementEmailAddress: {
      headerLabel: 'Settlement Email',
      sortable: true,
    },
    settlementMobileNumber: {
      headerLabel: 'Settlement Mobile',
      sortable: true,
    },
    settlementTransactionType: {
      headerLabel: 'Settlement Txn Type',
      sortable: true,
    },
  };

  const [visibleColumns, setVisibleColumns] = useState(
    Object.keys(tableConfig)
  );

  return (
    <>
      <Page>
        <Header
          withHome
          title="Billing Reports"
          path={['Reports', 'Billing Reports']}
        />
        <DataContainer>
          <DataTable
            loading={loading}
            data={data}
            minCellWidth={200}
            headerOptions={
              <>
                <GlobalSearch
                  onSearch={filter => {
                    const newPagination = {
                      ...pagination,
                      startKey: '',
                    };
                    setFilter(filter);
                    setNewPagination(newPagination);
                  }}
                  fields={[
                    {
                      label: 'Payment ID',
                      name: 'paymentId',
                      type: FIELD_TYPES.TEXT,
                      isKey: true,
                    },
                    {
                      label: 'Channel Name',
                      name: 'channelId',
                      type: FIELD_TYPES.SELECT,
                      options: channelOptions,
                      isKey: true,
                    },
                    {
                      label: 'Status',
                      name: 'status',
                      type: FIELD_TYPES.SELECT,
                      options: [
                        { value: null, label: 'Any' },
                        { value: 'POSTED', label: 'PAYMENT_POSTED' },
                        {
                          value: 'POSTING_FAILED',
                          label: 'PAYMENT_POSTED_FAILED',
                        },
                        {
                          value: 'POSTED_LUKE',
                          label: 'PAYMENT_POSTED_LUKE',
                        },
                      ],
                    },
                    {
                      label: 'Record Date',
                      name: 'createdAt',
                      type: FIELD_TYPES.DATE_RANGE,
                    },
                  ]}
                  placeholder="Search entries here..."
                />
                <ResponsiveRow>
                  {reportPermissions.Billing.export && (
                    <ExportButton
                      icon="file-csv"
                      iconPosition="left"
                      disabled={loading}
                      onClick={() => setIsConfirmDownloadModalOpen(true)}
                    >
                      CSV
                    </ExportButton>
                  )}
                  <ColumnVisibilityDropdown
                    multi
                    showMulti={false}
                    placeholder="Visible Columns"
                    value={visibleColumns}
                    options={Object.keys(tableConfig).map(key => ({
                      value: key,
                      label: tableConfig[key].headerLabel,
                    }))}
                    onChange={setVisibleColumns}
                  />
                </ResponsiveRow>
              </>
            }
            config={Object.keys(tableConfig).reduce((config, key) => {
              if (visibleColumns.includes(key)) {
                config[key] = tableConfig[key];
              }
              return config;
            }, {})}
            pagination={{
              ...pagination,
              start: pagination.startKey,
              withStartKeys: true,
              count: 0,
              cursors: [],
              handleChange: setNewPagination,
            }}
            series={{ page, setPage, isLastPage }}
          />
        </DataContainer>
      </Page>
      <AlertModal
        isOpen={isConfirmDownloadModalOpen}
        title="Export Reports Alert"
        header="ARE YOU SURE?"
        variant="warn"
        icon="exclamation-circle"
        subHeader={
          <span>
            You are about to export all filtered records as .CSV File.
          </span>
        }
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() => setIsConfirmDownloadModalOpen(false)}
        handleCancel={() => setIsConfirmDownloadModalOpen(false)}
        confirmText="Yes"
        handleConfirm={async () => {
          const notifTime = new Date().getTime();
          setIsConfirmDownloadModalOpen(false);
          addNotif({
            id: 'BILL-' + notifTime,
            notifTime,
            type: 'info',
            title: 'Downloading Report File',
            message: 'Downloading Billing Report',
            isProgress: true,
            progressData: {
              progress: 0,
              isProgressive: false,
            },
            isLocal: true,
            data: {},
            reportDLParams: {
              query: GET_BILLING_REPORTS,
              path: REPORT_PATH,
              variables: {
                filter,
                pagination: {
                  startKey: '',
                  limit: 1000,
                },
              },
              onDownload: () => {
                logExtraction({
                  variables: {
                    data: {
                      type: 'billing',
                    },
                  },
                });
              },
              tableConfig,
              fileName: `Globe-Bills ${format(new Date(), 'MMDDYYYY')}.csv`,
            },
          });
        }}
      />
      <AlertModal
        isOpen={isSuccessDownloadModalOpen}
        title="Export Reports Alert"
        header="SUCCESS!"
        variant="success"
        icon="check-circle"
        subHeader={
          <span>
            You exported{' '}
            <TransactionLogHighlight>
              {data.length} row(s){' '}
            </TransactionLogHighlight>{' '}
            as .CSV File succesfully.
          </span>
        }
        description="Kindly check the downloaded file."
        confirmText="Back to All Reports"
        handleClose={() => setIsSuccessDownloadModalOpen(false)}
        handleConfirm={() => {
          setIsSuccessDownloadModalOpen(false);
        }}
      />
    </>
  );
};

Billing.propTypes = {};

export default Billing;
