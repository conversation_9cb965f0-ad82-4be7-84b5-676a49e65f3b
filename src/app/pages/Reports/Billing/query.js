import { gql } from '@apollo/client';

export const GET_CHANNEL_OPTIONS = gql`
  query getChannelOptions {
    channelsLoose {
      id
      name
      channelId
    }
  }
`;

export const GET_BILLING_REPORTS = gql`
  query getBillingReports(
    $filter: SearchTransactionLogsFilter!
    $pagination: PaginationInput!
  ) {
    billingReports(filter: $filter, pagination: $pagination) {
      lastKey
      count
      filteredData {
        paymentId
        totalAmount
        status
        gatewayProcessor
        channelId
        channelName
        settlementBreakdown {
          accountNumber
          accountType
          amountValue
          brand
          emailAddress
          mobileNumber
          transactionType
        }
        paymentMethod
        createDateTime
        updateDateTime
      }
    }
  }
`;

export const REPORT_PATH = 'billingReports';
