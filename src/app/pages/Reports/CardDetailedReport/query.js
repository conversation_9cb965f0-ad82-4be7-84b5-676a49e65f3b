import { gql } from '@apollo/client';

export const GET_CARDDETAILED_REPORT = gql`
  query getCardRefundDetailedReport(
    $filter: SearchCardRefundDetailedReport!
    $pagination: PaginationReportInput!
  ) {
    cardRefundDetailedReport(filter: $filter, pagination: $pagination) {
      lastKey
      filteredData {
        reference
        accountNumber
        mobileNumber
        billType
        channelName
        postedTimestamp
        refundDate
        refundReason
        refundAmount
        refundId
      }
    }
  }
`;

export const GET_CHANNEL_OPTIONS = gql`
  query getChannelOptions {
    channelsLoose {
      id
      name
      channelId
    }
  }
`;

export const REPORT_PATH = 'cardRefundDetailedReport';
