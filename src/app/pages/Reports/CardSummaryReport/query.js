import { gql } from '@apollo/client';

export const GET_CARD_SUMMARY_REPORTS = gql`
  query getCreditCardRefundSummaryReport(
    $filter: SearchCardRefundSummaryReport!
  ) {
    cardRefundSummaryReport(filter: $filter) {
      Bill {
        channelName
        totalApprovedRefundAmount
        totalApprovedRefundCount
        totalForApprovalAmount
        totalForApprovalCount
        totalAutoRefundAmount
        totalAutoRefundCount
      }
      NonBill {
        channelName
        totalApprovedRefundAmount
        totalApprovedRefundCount
        totalForApprovalAmount
        totalForApprovalCount
        totalAutoRefundAmount
        totalAutoRefundCount
      }
    }
  }
`;

export const GET_CHANNEL_OPTIONS = gql`
  query getChannelOptions {
    channelsLoose {
      id
      name
      channelId
    }
  }
`;

export const REPORT_PATH = 'cardRefundSummaryReport';
