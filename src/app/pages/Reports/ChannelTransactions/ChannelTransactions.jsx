import React, { useContext, useState, useEffect } from 'react';
import { ExportButton } from '../../../components/Button/ExportButton';
import DataContainer from '../../../components/DataContainer';
import DataTable from '../../../components/DataTable';
import { FIELD_TYPES } from '../../../components/Form/constants';
import GlobalSearch from '../../../components/GlobalSearch';
import Header from '../../../components/Header';
import { AlertModal } from '../../../components/Modal';
import Page from '../../../components/Page';
import AuthContext from '../../../context/AuthContext/AuthContext';
import { useMutation } from '@apollo/client';
import useQueryFilterSeries from '../../../hooks/useQueryFilterSeries';
import { EXPORT_REPORTS } from '../mutation';
import {
  ColumnVisibilityDropdown,
  ResponsiveRow,
  TransactionLogHighlight,
} from '../styled';
import {
  GET_CHANNEL_TRANSACTIONS,
  GET_CHANNEL_OPTIONS,
  REPORT_PATH,
} from './query';
import NotificationContext from '../../../context/NotificationContext';
import { useQuery } from '@apollo/client';
import {
  numberWithCommas,
  MONTHS,
} from '../../../components/GlobalSearch/utils';

const ChannelTransactions = () => {
  const { reportPermissions } = useContext(AuthContext);

  const [state, setState] = useState({
    filter: {
      month: {
        start: MONTHS[0].value,
        end: MONTHS[11].value,
      },
      year: {
        start: new Date().getFullYear(),
        end: new Date().getFullYear(),
      },
    },
  });

  const { data, loading, page, setPage, isLastPage } = useQueryFilterSeries(
    GET_CHANNEL_TRANSACTIONS,
    REPORT_PATH,
    {
      filter: state.filter,
    },
    null,
    false
  );

  const [footer, setFooter] = useState({});

  const { data: channelData, loading: isLoadingChannels } =
    useQuery(GET_CHANNEL_OPTIONS);

  const channelOptions =
    !isLoadingChannels && channelData
      ? channelData.channelsLoose.map(channel => ({
          value: channel.id,
          label: channel.name,
        }))
      : [];

  channelOptions.unshift({
    value: null,
    label: 'Any',
  });

  const { addNotif } = useContext(NotificationContext);

  const [isConfirmDownloadModalOpen, setIsConfirmDownloadModalOpen] =
    useState(false);
  const [isSuccessDownloadModalOpen, setIsSuccessDownloadModalOpen] =
    useState(false);

  const [logExtraction] = useMutation(EXPORT_REPORTS);

  const tableConfig = {
    billType: {
      headerLabel: 'Bill Type',
      sortable: true,
    },
    channelName: {
      headerLabel: 'Channel Name',
      sortable: true,
    },
    businessUnit: {
      headerLabel: 'Business Unit',
      sortable: true,
    },
    gateway: {
      headerLabel: 'Payment Gateway',
      sortable: true,
    },
    fundingSource: {
      headerLabel: 'Card Type (Funding Source)',
      sortable: true,
    },
    paymentType: {
      headerLabel: 'Payment Type',
      sortable: true,
    },
    transCount: {
      headerLabel: 'Total Count',
      sortable: true,
      renderAs: data => numberWithCommas(data.transCount, 0),
    },
    transAmount: {
      headerLabel: 'Total Amount',
      sortable: true,
      renderAs: data => numberWithCommas(data.transAmount, 2),
    },
  };

  const [visibleColumns, setVisibleColumns] = useState(
    Object.keys(tableConfig)
  );

  useEffect(() => {
    if (data) {
      var footer = data.reduce(
        (finalVal, currentVal) => {
          return {
            amount: finalVal.amount + parseFloat(currentVal.transAmount),
            count: finalVal.count + parseFloat(currentVal.transCount),
          };
        },
        { amount: 0, count: 0 }
      );

      setFooter({
        billType: 'Total',
        transAmount: numberWithCommas(footer.amount, 2),
        transCount: numberWithCommas(footer.count),
      });
    }
  }, [JSON.stringify(data)]);

  return (
    <>
      <Page>
        <Header
          withHome
          title="Channel Transactions"
          path={['Reports', 'Channel Transactions']}
        />
        <DataContainer>
          <DataTable
            minCellWidth={200}
            headerOptions={
              <>
                <GlobalSearch
                  onSearch={filter => {
                    setState({ ...state, filter });
                  }}
                  fields={[
                    {
                      label: 'Bill Type',
                      name: 'billType',
                      type: FIELD_TYPES.SELECT,
                      options: [
                        { label: 'Any', value: null },
                        { label: 'Bill', value: 'Bill' },
                        { label: 'NonBill', value: 'NonBill' },
                      ],
                      isKey: true,
                    },
                    {
                      label: 'Channel Name',
                      name: 'channelIds',
                      type: FIELD_TYPES.SELECT,
                      options: channelOptions,
                      isKey: true,
                    },
                    {
                      label: 'Month',
                      name: 'month',
                      type: FIELD_TYPES.MONTH_RANGE,
                    },
                    {
                      label: 'Year',
                      name: 'year',
                      type: FIELD_TYPES.YEAR_RANGE,
                    },
                  ]}
                />
                <ResponsiveRow>
                  {reportPermissions.Transaction.export && (
                    <ExportButton
                      icon="file-csv"
                      iconPosition="left"
                      disabled={loading}
                      onClick={() => setIsConfirmDownloadModalOpen(true)}
                    >
                      CSV
                    </ExportButton>
                  )}
                  <ColumnVisibilityDropdown
                    multi
                    showMulti={false}
                    placeholder="Visible Columns"
                    value={visibleColumns}
                    options={Object.keys(tableConfig).map(key => ({
                      value: key,
                      label: tableConfig[key].headerLabel,
                    }))}
                    onChange={setVisibleColumns}
                  />
                </ResponsiveRow>
              </>
            }
            loading={loading}
            data={data}
            config={Object.keys(tableConfig).reduce((config, key) => {
              if (visibleColumns.includes(key)) {
                config[key] = tableConfig[key];
              }
              return config;
            }, {})}
            footer={footer}
            series={{
              page,
              setPage,
              isLastPage,
            }}
          />
        </DataContainer>
      </Page>
      <AlertModal
        isOpen={isConfirmDownloadModalOpen}
        title="Export Reports Alert"
        header="ARE YOU SURE?"
        variant="warn"
        icon="exclamation-circle"
        subHeader={
          <span>
            You are about to export all filtered records as .CSV File.
          </span>
        }
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() => setIsConfirmDownloadModalOpen(false)}
        handleCancel={() => setIsConfirmDownloadModalOpen(false)}
        confirmText="Yes"
        handleConfirm={() => {
          const notifTime = new Date().getTime();
          setIsConfirmDownloadModalOpen(false);
          addNotif({
            id: 'TRANS-' + notifTime,
            notifTime,
            type: 'info',
            title: 'Downloading Report File',
            message: 'Downloading Channel Transactions Report',
            isProgress: true,
            progressData: {
              progress: 0,
              isProgressive: false,
            },
            isLocal: true,
            data: {},
            reportDLParams: {
              query: GET_CHANNEL_TRANSACTIONS,
              path: REPORT_PATH,
              variables: {
                filter: state.filter,
                isPolling: false,
              },
              hasLastKey: false,
              onDownload: () => {
                logExtraction({
                  variables: {
                    data: {
                      type: 'channelreport',
                    },
                  },
                });
              },
              tableConfig,
              fileName: 'Channel Reports.csv',
            },
          });
        }}
      />
      <AlertModal
        isOpen={isSuccessDownloadModalOpen}
        title="Export Reports Alert"
        header="SUCCESS!"
        variant="success"
        icon="check-circle"
        subHeader={
          <span>
            You exported{' '}
            <TransactionLogHighlight>
              {data.length} row(s){' '}
            </TransactionLogHighlight>{' '}
            as .CSV File succesfully.
          </span>
        }
        description="Kindly check the downloaded file."
        confirmText="Back to All Reports"
        handleClose={() => setIsSuccessDownloadModalOpen(false)}
        handleConfirm={() => {
          setIsSuccessDownloadModalOpen(false);
        }}
      />
    </>
  );
};

ChannelTransactions.propTypes = {};

export default ChannelTransactions;
