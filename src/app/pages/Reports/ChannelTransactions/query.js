import { gql } from '@apollo/client';

export const GET_CHANNEL_TRANSACTIONS = gql`
  query getChannelTransactions($filter: channelReportFilterInput!) {
    channelReports(filter: $filter) {
      filteredData {
        billType
        channelName
        businessUnit
        gateway
        fundingSource
        paymentType
        transCount
        transAmount
        channelId
      }
    }
  }
`;

export const GET_CHANNEL_OPTIONS = gql`
  query getChannelOptions {
    channelsLoose {
      id
      name
      channelId
    }
  }
`;

export const REPORT_PATH = 'channelReports';
