import format from 'date-fns/format';
import React, { useContext, useState } from 'react';
import { ExportButton } from '../../../components/Button/ExportButton';
import DataContainer from '../../../components/DataContainer';
import DataTable from '../../../components/DataTable';
import { FIELD_TYPES } from '../../../components/Form/constants';
import GlobalSearch from '../../../components/GlobalSearch';
import Header from '../../../components/Header';
import { AlertModal } from '../../../components/Modal';
import Page from '../../../components/Page';
import AuthContext from '../../../context/AuthContext/AuthContext';
import { useMutation } from '@apollo/client';
import { EXPORT_REPORTS } from '../mutation';
import useQueryReportSeries from '../../../hooks/useQueryReportSeries';
import {
  ColumnVisibilityDropdown,
  ResponsiveRow,
  TransactionLogHighlight,
} from '../styled';
import { GET_CONTENTGCASH_REPORTS, REPORT_PATH } from './query';
import { numberWithCommas } from '../../../components/GlobalSearch/utils';

import NotificationContext from '../../../context/NotificationContext';

const ContentGcashReports = () => {
  const { reportPermissions } = useContext(AuthContext);
  const {
    pagination,
    filter,
    setNewPagination,
    setFilter,
    data,
    loading,
    page,
    setPage,
    isLastPage,
  } = useQueryReportSeries(GET_CONTENTGCASH_REPORTS, REPORT_PATH, {
    pagination: {
      startKeys: '',
      limit: 10,
    },
  });

  const { addNotif } = useContext(NotificationContext);

  const [isConfirmDownloadModalOpen, setIsConfirmDownloadModalOpen] =
    useState(false);
  const [isSuccessDownloadModalOpen, setIsSuccessDownloadModalOpen] =
    useState(false);

  const [logExtraction] = useMutation(EXPORT_REPORTS);

  const tableConfig = {
    psReferenceNo: {
      headerLabel: 'PS Reference No.',
      sortable: true,
    },
    gcashReferenceNo: {
      headerLabel: 'GCash Reference No.',
      sortable: true,
    },
    accountNo: {
      headerLabel: 'Account No.',
      sortable: true,
    },
    srn: {
      headerLabel: 'SRN',
      sortable: true,
    },
    channelName: {
      headerLabel: 'Channel Name',
      sortable: true,
    },
    paymentMethod: {
      headerLabel: 'Payment Method',
      sortable: true,
      renderAs: data => (data.paymentMethod === 'gcash' ? 'GCash' : ''),
    },
    paymentStatus: {
      headerLabel: 'Payment Status',
      sortable: true,
    },
    amount: {
      headerLabel: 'Amount',
      sortable: true,
      renderAs: data => numberWithCommas(data.amount, 2),
    },
    currency: {
      headerLabel: 'Currency',
      sortable: true,
    },
    date: {
      headerLabel: 'Date',
      sortable: true,
      renderAs: data => format(data.date, 'MM/DD/YYYY - hh:mm:ss A'),
    },
    msisdn: {
      headerLabel: 'MSISDN',
      sortable: true,
    },
    productDescription: {
      headerLabel: 'Product Description',
      sortable: true,
    },
    emailAddress: {
      headerLabel: 'Email Address',
      sortable: true,
    },
    paymentGateway: {
      headerLabel: 'Payment Gateway',
      sortable: true,
    },
    customerSegment: {
      headerLabel: 'Customer Segment',
      sortable: true,
    },
    customerSubType: {
      headerLabel: 'Customer Sub Type',
      sortable: true,
    },
    brand: {
      headerLabel: 'Brand',
      sortable: true,
    },
    entity: {
      headerLabel: 'Entity',
      sortable: true,
    },
    sku: {
      headerLabel: 'SKU',
      sortable: true,
    },
    modeOfPayment: {
      headerLabel: 'Mode of Payment',
      sortable: true,
    },
    subscriberType: {
      headerLabel: 'Sub Service / Subscriber Type',
      sortable: true,
    },
    contentPartnerShortName: {
      headerLabel: 'Content Partner Short Name',
      sortable: true,
    },
    voucherDispenseStatus: {
      headerLabel: 'Voucher Dispense Status',
      sortable: true,
    },
    refundStatus: {
      headerLabel: 'Refund Status',
      sortable: true,
    },
    refundAmount: {
      headerLabel: 'Refund Amount',
      sortable: true,
      renderAs: data => numberWithCommas(data.refundAmount, 2),
    },
  };

  const [visibleColumns, setVisibleColumns] = useState(
    Object.keys(tableConfig)
  );

  return (
    <>
      <Page>
        <Header
          withHome
          title="Content-GCash Reports"
          path={['Reports', 'ContentGCashReports']}
        />
        <DataContainer>
          <DataTable
            loading={loading}
            data={data}
            minCellWidth={200}
            headerOptions={
              <>
                <GlobalSearch
                  onSearch={filter => {
                    const newPagination = {
                      ...pagination,
                      startKeys: '',
                    };
                    setFilter(filter);
                    setNewPagination(newPagination);
                  }}
                  fields={[
                    {
                      label: 'PS Reference Number',
                      name: 'psReferenceNo',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'GCash Reference Number',
                      name: 'gcashReferenceNo',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Account Number',
                      name: 'accountNo',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Mobile Number',
                      name: 'msisdn',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Brand',
                      name: 'brand',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Payment Status',
                      name: 'paymentStatus',
                      type: FIELD_TYPES.SELECT,
                      options: [
                        { value: null, label: 'Any' },
                        {
                          value: 'GCASH_AUTHORISED',
                          label: 'GCASH_AUTHORISED',
                        },
                        {
                          value: 'GCASH_REFUSED',
                          label: 'GCASH_REFUSED',
                        },
                      ],
                    },
                    {
                      label: 'Date',
                      name: 'date',
                      type: FIELD_TYPES.DATE_RANGE,
                    },
                  ]}
                  placeholder="Search entries here..."
                />
                <ResponsiveRow>
                  {reportPermissions.ContentGcashReport.export && (
                    <ExportButton
                      icon="file-csv"
                      iconPosition="left"
                      disabled={loading}
                      onClick={() => setIsConfirmDownloadModalOpen(true)}
                    >
                      CSV
                    </ExportButton>
                  )}
                  <ColumnVisibilityDropdown
                    multi
                    showMulti={false}
                    placeholder="Visible Columns"
                    value={visibleColumns}
                    options={Object.keys(tableConfig).map(key => ({
                      value: key,
                      label: tableConfig[key].headerLabel,
                    }))}
                    onChange={setVisibleColumns}
                  />
                </ResponsiveRow>
              </>
            }
            config={Object.keys(tableConfig).reduce((config, key) => {
              if (visibleColumns.includes(key)) {
                config[key] = tableConfig[key];
              }
              return config;
            }, {})}
            pagination={{
              ...pagination,
              start: pagination.startKeys,
              withStartKeys: true,
              paginationLabel: true,
              count: data.length,
              cursors: [],
              handleChange: setNewPagination,
            }}
            series={{ page, setPage, isLastPage }}
          />
        </DataContainer>
      </Page>
      <AlertModal
        isOpen={isConfirmDownloadModalOpen}
        title="Export Reports Alert"
        header="ARE YOU SURE?"
        variant="warn"
        icon="exclamation-circle"
        subHeader={
          <span>
            You are about to export all filtered records as .CSV File.
          </span>
        }
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() => setIsConfirmDownloadModalOpen(false)}
        handleCancel={() => setIsConfirmDownloadModalOpen(false)}
        confirmText="Yes"
        handleConfirm={async () => {
          const notifTime = new Date().getTime();
          setIsConfirmDownloadModalOpen(false);
          addNotif({
            id: 'ContentGcashReport-' + notifTime,
            notifTime,
            type: 'info',
            title: 'Downloading Report File',
            message: 'Downloading ContentGcash Report',
            isProgress: true,
            progressData: {
              progress: 0,
              isProgressive: false,
            },
            isLocal: true,
            data: {},
            reportDLParams: {
              query: GET_CONTENTGCASH_REPORTS,
              path: REPORT_PATH,
              variables: {
                filter,
                pagination: {
                  startKeys: '',
                  limit: 1000,
                },
              },
              onDownload: () => {
                logExtraction({
                  variables: {
                    data: {
                      type: 'contentgcashreport',
                    },
                  },
                });
              },
              tableConfig,
              fileName: `Content-Gcash-Report ${format(new Date(), 'MMDDYYYY')}.csv`,
            },
          });
        }}
      />
      <AlertModal
        isOpen={isSuccessDownloadModalOpen}
        title="Export Reports Alert"
        header="SUCCESS!"
        variant="success"
        icon="check-circle"
        subHeader={
          <span>
            You exported{' '}
            <TransactionLogHighlight>
              {data.length} row(s){' '}
            </TransactionLogHighlight>{' '}
            as .CSV File succesfully.
          </span>
        }
        description="Kindly check the downloaded file."
        confirmText="Back to All Reports"
        handleClose={() => setIsSuccessDownloadModalOpen(false)}
        handleConfirm={() => {
          setIsSuccessDownloadModalOpen(false);
        }}
      />
    </>
  );
};

ContentGcashReports.propTypes = {};

export default ContentGcashReports;
