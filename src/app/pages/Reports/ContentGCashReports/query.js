import { gql } from '@apollo/client';

export const GET_CONTENTGCASH_REPORTS = gql`
  query getContentGCashReports(
    $filter: contentGcashReportFilterInput!
    $pagination: PaginationReportInput!
  ) {
    contentGcashReport(filter: $filter, pagination: $pagination) {
      lastKey
      filteredData {
        psReferenceNo
        gcashReferenceNo
        accountNo
        srn
        channelName
        channelId
        paymentMethod
        paymentStatus
        amount
        currency
        date
        msisdn
        productDescription
        emailAddress
        paymentGateway
        customerSegment
        customerSubType
        brand
        entity
        sku
        modeOfPayment
        subscriberType
        contentPartnerShortName
        voucherDispenseStatus
        refundStatus
        refundAmount
      }
    }
  }
`;

export const REPORT_PATH = 'contentGcashReport';
