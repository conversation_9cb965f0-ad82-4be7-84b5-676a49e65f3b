import format from 'date-fns/format';
import React, { useContext, useState } from 'react';
import { ExportButton } from '../../../components/Button/ExportButton';
import DataContainer from '../../../components/DataContainer';
import DataTable from '../../../components/DataTable';
import { FIELD_TYPES } from '../../../components/Form/constants';
import GlobalSearch from '../../../components/GlobalSearch';
import Header from '../../../components/Header';
import { AlertModal } from '../../../components/Modal';
import Page from '../../../components/Page';
import AuthContext from '../../../context/AuthContext/AuthContext';
import { useMutation } from '@apollo/client';
import { EXPORT_REPORTS } from '../mutation';
import useQueryReportSeries from '../../../hooks/useQueryReportSeries';
import {
  ColumnVisibilityDropdown,
  ResponsiveRow,
  TransactionLogHighlight,
} from '../styled';
import { GET_ENDGAMETRANSACTION_REPORTS, REPORT_PATH } from './query';
import { numberWithCommas } from '../../../components/GlobalSearch/utils';

import NotificationContext from '../../../context/NotificationContext';

const EndGameTransactionLogs = () => {
  const { reportPermissions } = useContext(AuthContext);
  const {
    pagination,
    filter,
    setNewPagination,
    setFilter,
    data,
    loading,
    page,
    setPage,
    isLastPage,
  } = useQueryReportSeries(GET_ENDGAMETRANSACTION_REPORTS, REPORT_PATH, {
    pagination: {
      startKeys: '',
      limit: 10,
    },
  });

  const { addNotif } = useContext(NotificationContext);

  const [isConfirmDownloadModalOpen, setIsConfirmDownloadModalOpen] =
    useState(false);
  const [isSuccessDownloadModalOpen, setIsSuccessDownloadModalOpen] =
    useState(false);

  const [logExtraction] = useMutation(EXPORT_REPORTS);

  const tableConfig = {
    pspReferenceNumber: {
      headerLabel: 'PS Reference No.',
      sortable: true,
    },
    accountNumber: {
      headerLabel: 'Account No.',
      sortable: true,
    },
    isActive: {
      headerLabel: 'Account Status',
      sortable: true,
      renderAs: data => (data.isActive ? 'Active' : 'Inactive'),
    },
    channelName: {
      headerLabel: 'Channel',
      sortable: true,
    },
    transId: {
      headerLabel: 'Transaction ID',
      sortable: true,
    },
    timestamp: {
      headerLabel: 'Transaction Date',
      sortable: true,
      renderAs: data => format(data.timestamp, 'MM/DD/YYYY - hh:mm:ss A'),
    },
    ccNumber: {
      headerLabel: 'Card Number',
      sortable: true,
    },
    ccType: {
      headerLabel: 'Card Type',
      sortable: true,
    },
    bankName: {
      headerLabel: 'Bank Name',
      sortable: true,
    },
    tokenId: {
      headerLabel: 'Token ID',
      sortable: true,
    },
    amount: {
      headerLabel: 'Amount Debited',
      sortable: true,
      renderAs: data => numberWithCommas(data.amount, 2),
    },
    status: {
      headerLabel: 'Payment Status',
      sortable: true,
    },
    reason: {
      headerLabel: 'Payment Reason',
      sortable: true,
    },
    entity: {
      headerLabel: 'Entity',
      sortable: true,
    },
    currency: {
      headerLabel: 'Amount Currency',
      sortable: true,
    },
    productDescription: {
      headerLabel: 'Promo / Product Name / Description',
      sortable: true,
    },
    price: {
      headerLabel: 'Price',
      sortable: true,
      renderAs: data => numberWithCommas(data.price, 2),
    },
    userContact: {
      headerLabel: 'MSISDN',
      sortable: true,
    },
  };

  const [visibleColumns, setVisibleColumns] = useState(
    Object.keys(tableConfig)
  );

  return (
    <>
      <Page>
        <Header
          withHome
          title="EndGame Transaction Logs"
          path={['Reports', 'EndGame Transaction Logs']}
        />
        <DataContainer>
          <DataTable
            loading={loading}
            data={data}
            minCellWidth={200}
            headerOptions={
              <>
                <GlobalSearch
                  onSearch={filter => {
                    const newPagination = {
                      ...pagination,
                      startKeys: '',
                    };
                    setFilter(filter);
                    setNewPagination(newPagination);
                  }}
                  fields={[
                    {
                      label: 'PS Reference No.',
                      name: 'id',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Account Number',
                      name: 'accountNumber',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Mobile Number',
                      name: 'userContact',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Payment Status',
                      name: 'status',
                      type: FIELD_TYPES.SELECT,
                      options: [
                        { value: null, label: 'Any' },
                        {
                          value: 'For Processing',
                          label: 'For_Processing',
                        },
                        { value: 'Approved', label: 'Approved' },
                        { value: 'Declined', label: 'Declined' },
                      ],
                    },
                    {
                      label: 'Date',
                      name: 'timestamp',
                      type: FIELD_TYPES.DATE_RANGE,
                    },
                  ]}
                  placeholder="Search entries here..."
                />
                <ResponsiveRow>
                  {reportPermissions.EndGameReport.export && (
                    <ExportButton
                      icon="file-csv"
                      iconPosition="left"
                      disabled={loading}
                      onClick={() => setIsConfirmDownloadModalOpen(true)}
                    >
                      CSV
                    </ExportButton>
                  )}
                  <ColumnVisibilityDropdown
                    multi
                    showMulti={false}
                    placeholder="Visible Columns"
                    value={visibleColumns}
                    options={Object.keys(tableConfig).map(key => ({
                      value: key,
                      label: tableConfig[key].headerLabel,
                    }))}
                    onChange={setVisibleColumns}
                  />
                </ResponsiveRow>
              </>
            }
            config={Object.keys(tableConfig).reduce((config, key) => {
              if (visibleColumns.includes(key)) {
                config[key] = tableConfig[key];
              }
              return config;
            }, {})}
            pagination={{
              ...pagination,
              start: pagination.startKeys,
              withStartKeys: true,
              count: 0,
              cursors: [],
              handleChange: setNewPagination,
            }}
            series={{ page, setPage, isLastPage }}
          />
        </DataContainer>
      </Page>
      <AlertModal
        isOpen={isConfirmDownloadModalOpen}
        title="Export Reports Alert"
        header="ARE YOU SURE?"
        variant="warn"
        icon="exclamation-circle"
        subHeader={
          <span>
            You are about to export all filtered records as .CSV File.
          </span>
        }
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() => setIsConfirmDownloadModalOpen(false)}
        handleCancel={() => setIsConfirmDownloadModalOpen(false)}
        confirmText="Yes"
        handleConfirm={async () => {
          const notifTime = new Date().getTime();
          setIsConfirmDownloadModalOpen(false);
          addNotif({
            id: 'EndGame Transaction Logs-' + notifTime,
            notifTime,
            type: 'info',
            title: 'Downloading Report File',
            message: 'Downloading EndGame Transaction Report Report',
            isProgress: true,
            progressData: {
              progress: 0,
              isProgressive: false,
            },
            isLocal: true,
            data: {},
            reportDLParams: {
              query: GET_ENDGAMETRANSACTION_REPORTS,
              path: REPORT_PATH,
              variables: {
                filter,
                pagination: {
                  startKeys: '',
                  limit: 1000,
                },
              },
              onDownload: () => {
                logExtraction({
                  variables: {
                    data: {
                      type: 'endgametrasnactionreport',
                    },
                  },
                });
              },
              tableConfig,
              fileName: `EndGame Transaction Logs ${format(new Date(), 'MMDDYYYY')}.csv`,
            },
          });
        }}
      />
      <AlertModal
        isOpen={isSuccessDownloadModalOpen}
        title="Export Reports Alert"
        header="SUCCESS!"
        variant="success"
        icon="check-circle"
        subHeader={
          <span>
            You exported{' '}
            <TransactionLogHighlight>
              {data.length} row(s){' '}
            </TransactionLogHighlight>{' '}
            as .CSV File succesfully.
          </span>
        }
        description="Kindly check the downloaded file."
        confirmText="Back to All Reports"
        handleClose={() => setIsSuccessDownloadModalOpen(false)}
        handleConfirm={() => {
          setIsSuccessDownloadModalOpen(false);
        }}
      />
    </>
  );
};

EndGameTransactionLogs.propTypes = {};

export default EndGameTransactionLogs;
