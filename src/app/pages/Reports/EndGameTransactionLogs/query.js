import { gql } from '@apollo/client';

export const GET_ENDGAMETRANSACTION_REPORTS = gql`
  query getEndGameTransactionReport(
    $filter: endGameReportFilterInput!
    $pagination: PaginationReportInput!
  ) {
    endGameTransactionReport(filter: $filter, pagination: $pagination) {
      lastKey
      filteredData {
        pspReferenceNumber
        accountNumber
        isActive
        channelName
        transId
        timestamp
        ccNumber
        ccType
        bankName
        tokenId
        amount
        status
        reason
        entity
        currency
        productDescription
        price
        userContact
      }
    }
  }
`;

export const REPORT_PATH = 'endGameTransactionReport';
