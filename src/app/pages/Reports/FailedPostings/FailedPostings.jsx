import format from 'date-fns/format';
import React, { useContext, useState } from 'react';
import { useQuery } from '@apollo/client';
import { ExportButton } from '../../../components/Button/ExportButton';
import DataContainer from '../../../components/DataContainer';
import DataTable from '../../../components/DataTable';
import { FIELD_TYPES } from '../../../components/Form/constants';
import GlobalSearch from '../../../components/GlobalSearch';
import Header from '../../../components/Header';
import { AlertModal } from '../../../components/Modal';
import Page from '../../../components/Page';
import AuthContext from '../../../context/AuthContext/AuthContext';
import { useMutation } from '@apollo/client';
import useQueryReportSeries from '../../../hooks/useQueryReportSeries';
import formatCurrency from '../../../utils/formatCurrency';
import { EXPORT_REPORTS } from '../mutation';
import {
  ColumnVisibilityDropdown,
  ResponsiveRow,
  TransactionLogHighlight,
} from '../styled';
import { POST_TRANSACTION } from './mutation';
import {
  GET_CHANNEL_OPTIONS,
  GET_FAILED_POSTINGS_INFO,
  REPORT_PATH,
} from './query';
import NotificationContext from '../../../context/NotificationContext';

const FailedPostings = () => {
  const { reportPermissions } = useContext(AuthContext);
  const [isConfirmDownloadModalOpen, setIsConfirmDownloadModalOpen] =
    useState(false);
  const [isSuccessDownloadModalOpen, setIsSuccessDownloadModalOpen] =
    useState(false);

  const [isConfirmPostModalOpen, setIsConfirmPostModalOpen] = useState(false);
  const [isSuccessPostModalOpen, setIsSuccessPostModalOpen] = useState(false);
  const [isFailedPostModalOpen, setIsFailedPostModalOpen] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState(null);

  const {
    pagination,
    filter,
    setNewPagination,
    setFilter,
    data,
    loading,
    page,
    setPage,
    isLastPage,
  } = useQueryReportSeries(GET_FAILED_POSTINGS_INFO, REPORT_PATH, {
    pagination: {
      startKeys: '',
      limit: 10,
    },
  });

  const { addNotif } = useContext(NotificationContext);

  const [logExtraction] = useMutation(EXPORT_REPORTS);

  const { data: channelData, loading: isLoadingChannels } =
    useQuery(GET_CHANNEL_OPTIONS);

  const channelOptions =
    !isLoadingChannels && channelData
      ? channelData.channelsLoose.map(channel => ({
          value: channel.id,
          label: channel.name,
        }))
      : [];

  channelOptions.unshift({
    value: null,
    label: 'Any',
  });

  const [postTransaction, { loading: isPostingTransaction }] = useMutation(
    POST_TRANSACTION,
    {
      onCompleted: () => {
        setIsConfirmPostModalOpen(false);
        setIsSuccessPostModalOpen(true);
        setSelectedTransaction(null);
      },
      onError: () => {
        setIsConfirmPostModalOpen(false);
        setIsFailedPostModalOpen(true);
        setSelectedTransaction(null);
      },
    }
  );

  const tableConfig = {
    paymentId: {
      headerLabel: 'Reference No.',
      sortable: true,
    },
    accountId: {
      headerLabel: 'Account No.',
      sortable: true,
    },
    channelName: {
      headerLabel: 'Channel Name',
      sortable: true,
    },
    paymentMethod: {
      headerLabel: 'Payment Method',
      sortable: true,
    },
    mobileNumber: {
      headerLabel: 'MSISDN',
      sortable: true,
    },
    amountCurrency: {
      headerLabel: 'Currency',
      sortable: true,
    },
    amount: {
      headerLabel: 'Amount',
      sortable: true,
      renderAs: data => formatCurrency(data.amount, true),
      textAlign: 'right',
    },
    createDateTime: {
      headerLabel: 'Date',
      sortable: true,
      renderAs: data => format(data.createDateTime, 'MM/DD/YYYY - hh:mm:ss A'),
    },
  };

  const [visibleColumns, setVisibleColumns] = useState(
    Object.keys(tableConfig)
  );

  return (
    <>
      <Page>
        <Header
          withHome
          title="Payment Service Report"
          path={['Reports', 'Failed Postings', 'Payment Service Report']}
        />
        <DataContainer>
          <DataTable
            minCellWidth={200}
            headerOptions={
              <>
                <GlobalSearch
                  onSearch={filter => {
                    const newPagination = {
                      ...pagination,
                      startKeys: '',
                    };
                    setFilter(filter);
                    setNewPagination(newPagination);
                  }}
                  fields={[
                    {
                      label: 'Reference No.',
                      name: 'reference',
                      type: FIELD_TYPES.TEXT,
                      isKey: true,
                    },
                    {
                      label: 'Account No.',
                      name: 'accountNumber',
                      type: FIELD_TYPES.TEXT,
                      isKey: true,
                    },
                    {
                      label: 'Channel Name',
                      name: 'channelId',
                      type: FIELD_TYPES.SELECT,
                      options: channelOptions,
                      isKey: true,
                    },
                    {
                      label: 'Date',
                      name: 'createdAt',
                      type: FIELD_TYPES.DATE_RANGE,
                    },
                  ]}
                />
                <ResponsiveRow>
                  {reportPermissions.Collection.export && (
                    <ExportButton
                      icon="file-csv"
                      iconPosition="left"
                      disabled={loading}
                      onClick={() => setIsConfirmDownloadModalOpen(true)}
                    >
                      CSV
                    </ExportButton>
                  )}
                  <ColumnVisibilityDropdown
                    multi
                    showMulti={false}
                    placeholder="Visible Columns"
                    value={visibleColumns}
                    options={Object.keys(tableConfig).map(key => ({
                      value: key,
                      label: tableConfig[key].headerLabel,
                    }))}
                    onChange={setVisibleColumns}
                  />
                </ResponsiveRow>
              </>
            }
            loading={loading}
            data={data}
            config={Object.keys(tableConfig).reduce((config, key) => {
              if (visibleColumns.includes(key)) {
                config[key] = tableConfig[key];
              }
              return config;
            }, {})}
            pagination={{
              ...pagination,
              start: pagination.startKeys,
              withStartKeys: true,
              count: 0,
              cursors: [],
              handleChange: setNewPagination,
            }}
            series={{
              page,
              setPage,
              isLastPage,
            }}
          />
        </DataContainer>
      </Page>
      <AlertModal
        isOpen={isConfirmDownloadModalOpen}
        title="Export Reports Alert"
        header="ARE YOU SURE?"
        variant="warn"
        icon="exclamation-circle"
        subHeader={
          <span>
            You are about to export all filtered records as .CSV File.
          </span>
        }
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() => setIsConfirmDownloadModalOpen(false)}
        handleCancel={() => setIsConfirmDownloadModalOpen(false)}
        confirmText="Yes"
        handleConfirm={async () => {
          const notifTime = new Date().getTime();
          setIsConfirmDownloadModalOpen(false);
          addNotif({
            id: 'FPOST-' + notifTime,
            notifTime,
            type: 'info',
            title: 'Downloading Report File',
            message: 'Downloading Failed Postings Report',
            isProgress: true,
            progressData: {
              progress: 0,
              isProgressive: false,
            },
            isLocal: true,
            data: {},
            reportDLParams: {
              query: GET_FAILED_POSTINGS_INFO,
              path: REPORT_PATH,
              variables: {
                filter,
                pagination: {
                  startKeys: '',
                  limit: 1000,
                },
              },
              onDownload: () => {
                logExtraction({
                  variables: {
                    data: {
                      type: 'failed',
                    },
                  },
                });
              },
              tableConfig,
              fileName: 'failed-postings.csv',
            },
          });
        }}
      />
      <AlertModal
        isOpen={isSuccessDownloadModalOpen}
        title="Export Reports Alert"
        header="SUCCESS!"
        variant="success"
        icon="check-circle"
        subHeader={
          <span>
            You exported{' '}
            <TransactionLogHighlight>
              {data.length} row(s){' '}
            </TransactionLogHighlight>{' '}
            as .CSV File succesfully.
          </span>
        }
        description="Kindly check the downloaded file."
        confirmText="Back to All Reports"
        handleClose={() => setIsSuccessDownloadModalOpen(false)}
        handleConfirm={() => {
          setIsSuccessDownloadModalOpen(false);
        }}
      />
      <AlertModal
        isOpen={isConfirmPostModalOpen}
        title="Transaction Alert"
        header="ARE YOU SURE?"
        variant="warn"
        icon="exclamation-circle"
        subHeader={'You are about to post this Transaction.'}
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        confirmLoading={isPostingTransaction}
        confirmText="Yes"
        handleClose={() => {
          setIsConfirmPostModalOpen(false);
        }}
        handleConfirm={() => {
          postTransaction({
            variables: {
              data: {
                id: selectedTransaction.reference,
                accountNumber: selectedTransaction.accountNumber,
              },
            },
          });
        }}
      />
      <AlertModal
        isOpen={isSuccessPostModalOpen}
        title="Transaction Alert"
        header="SUCCESS!"
        variant="success"
        icon="check-circle"
        subHeader={'Transaction now posted.'}
        description="Please go to Transaction Logs to see the changes."
        confirmText="Back to Transaction Logs"
        handleClose={() => setIsSuccessPostModalOpen(false)}
        handleConfirm={() => {
          setIsSuccessPostModalOpen(false);
        }}
      />
      <AlertModal
        isOpen={isFailedPostModalOpen}
        title="Transaction Alert"
        header="OH SNAP!"
        variant="error"
        icon="times-circle"
        subHeader="There was a problem in posting the Transaction."
        description="Please try again."
        confirmText="Go Back"
        handleClose={() => setIsFailedPostModalOpen(false)}
        handleConfirm={() => {
          setIsFailedPostModalOpen(false);
        }}
      />
    </>
  );
};

FailedPostings.propTypes = {};

export default FailedPostings;
