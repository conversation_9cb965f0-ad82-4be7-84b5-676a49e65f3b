import { gql } from '@apollo/client';

export const GET_CHANNEL_OPTIONS = gql`
  query getChannelOptions {
    channelsLoose {
      id
      name
      channelId
    }
  }
`;

export const GET_FAILED_POSTINGS_INFO = gql`
  query getFailedPostingsInfo(
    $filter: SearchFailedLogsFilter!
    $pagination: PaginationInput!
  ) {
    failedReports(filter: $filter, pagination: $pagination) {
      lastKey
      filteredData {
        paymentId
        accountId
        reference
        accountNumber
        paymentMethod
        amount
        createDateTime
        mobileNumber
        amountCurrency
        channelName
      }
    }
  }
`;

export const REPORT_PATH = 'failedReports';
