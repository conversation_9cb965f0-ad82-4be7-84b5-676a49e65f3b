import { gql } from '@apollo/client';

export const GET_ECPAY_REPORTS = gql`
  query getecpayReports(
    $filter: ecpayReportFilterInput!
    $pagination: PaginationReportInput!
  ) {
    ecpayReports(filter: $filter, pagination: $pagination) {
      lastKey
      filteredData {
        reference
        billerName
        subMerchantId
        amountValue
        status
      }
    }
  }
`;

export const REPORT_PATH = 'ecpayReports';
