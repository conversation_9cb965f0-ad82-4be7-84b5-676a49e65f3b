import format from 'date-fns/format';
import React, { useContext, useState } from 'react';
import { useQuery } from '@apollo/client';
import { ExportButton } from '../../../components/Button/ExportButton';
import DataContainer from '../../../components/DataContainer';
import DataTable from '../../../components/DataTable';
import { FIELD_TYPES } from '../../../components/Form/constants';
import GlobalSearch from '../../../components/GlobalSearch';
import { numberWithCommas } from '../../../components/GlobalSearch/utils';
import Header from '../../../components/Header';
import { AlertModal } from '../../../components/Modal';
import Page from '../../../components/Page';
import AuthContext from '../../../context/AuthContext/AuthContext';
import NotificationContext from '../../../context/NotificationContext';
import { useMutation } from '@apollo/client';
import useQueryReportSeries from '../../../hooks/useQueryReportSeries';
import { EXPORT_REPORTS } from '../mutation';
import {
  ColumnVisibilityDropdown,
  ResponsiveRow,
  TransactionLogHighlight,
} from '../styled';
import {
  GET_CHANNEL_OPTIONS,
  GET_GCASHREFUND_REPORTS_DETAILED,
  REPORT_PATH,
} from './query';

const GCashRefundReportDetailed = () => {
  const { reportPermissions } = useContext(AuthContext);
  const {
    pagination,
    filter,
    setNewPagination,
    setFilter,
    data,
    loading,
    page,
    setPage,
    isLastPage,
  } = useQueryReportSeries(GET_GCASHREFUND_REPORTS_DETAILED, REPORT_PATH, {
    pagination: {
      startKeys: '',
      limit: 10,
    },
  });

  const { data: channelData, loading: isLoadingChannels } = useQuery(
    GET_CHANNEL_OPTIONS,
    {
      fetchPolicy: 'network-only',
    }
  );

  const { addNotif } = useContext(NotificationContext);

  const [isConfirmDownloadModalOpen, setIsConfirmDownloadModalOpen] =
    useState(false);
  const [isSuccessDownloadModalOpen, setIsSuccessDownloadModalOpen] =
    useState(false);

  const [logExtraction] = useMutation(EXPORT_REPORTS);

  const channelOptions =
    !isLoadingChannels && channelData
      ? channelData.channelsLoose.map(channel => ({
          value: channel.id,
          label: channel.name,
        }))
      : [];

  channelOptions.unshift({
    value: null,
    label: 'Any',
  });

  const tableConfig = {
    refundId: {
      headerLabel: 'Refund ID',
      sortable: true,
    },
    reference: {
      headerLabel: 'PS Reference No.',
      sortable: true,
    },
    gcashTransId: {
      headerLabel: 'GCash Transaction ID',
      sortable: true,
    },
    accountNumber: {
      headerLabel: 'Account No.',
      sortable: true,
    },
    mobileNumber: {
      headerLabel: 'MSISDN',
      sortable: true,
    },
    channelName: {
      headerLabel: 'Channel',
      sortable: true,
    },
    postedTimestamp: {
      headerLabel: 'Date Posted/Authorised',
      sortable: true,
      renderAs: data => format(data.postedTimestamp, 'MM/DD/YYYY - hh:mm:ss A'),
    },
    refundDate: {
      headerLabel: 'Refund Date',
      sortable: true,
      renderAs: data => format(data.refundDate, 'MM/DD/YYYY - hh:mm:ss A'),
    },
    refundReason: {
      headerLabel: 'Refund Reason',
      sortable: true,
    },
    refundAmount: {
      headerLabel: 'Refund Amount',
      sortable: true,
      renderAs: data => numberWithCommas(data.refundAmount, 2),
    },
    billType: {
      headerLabel: 'Bill Type',
      sortable: true,
    },
    convenienceFee: {
      headerLabel: 'Convenience Fee',
      sortable: true,
      renderAs: data =>
        data.convenienceFee &&
        !Number.isNaN(data.convenienceFee) &&
        Number(data.convenienceFee) !== 0
          ? numberWithCommas(data.convenienceFee, 2)
          : '',
    },
  };

  const [visibleColumns, setVisibleColumns] = useState(
    Object.keys(tableConfig)
  );

  const BILL_TYPES = [
    { value: 'Bill', label: 'Bill' },
    { value: 'NonBill', label: 'NonBill' },
  ];

  return (
    <>
      <Page>
        <Header
          withHome
          title="GCash Refund Detailed Report"
          path={['Reports', 'GCash Refund Detailed Report']}
        />
        <DataContainer>
          <DataTable
            loading={loading}
            data={data}
            minCellWidth={200}
            headerOptions={
              <>
                <GlobalSearch
                  onSearch={filter => {
                    const newPagination = {
                      ...pagination,
                      startKeys: '',
                    };
                    setFilter(filter);
                    setNewPagination(newPagination);
                  }}
                  fields={[
                    {
                      label: 'Refund ID',
                      name: 'refundId',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Reference No.',
                      name: 'reference',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Channel',
                      name: 'channelId',
                      type: FIELD_TYPES.SELECT,
                      options: channelOptions,
                      isKey: true,
                    },
                    {
                      label: 'Bill Type',
                      name: 'billType',
                      type: FIELD_TYPES.SELECT,
                      options: [...BILL_TYPES],
                      isKey: true,
                    },
                    {
                      label: 'Has Convenience Fee',
                      name: 'hasConvenienceFee',
                      type: FIELD_TYPES.SELECT,
                      options: [
                        { value: true, label: 'Yes' },
                        { value: false, label: 'No' },
                      ],
                    },
                    {
                      label: 'Date Range',
                      name: 'createdAt',
                      type: FIELD_TYPES.DATE_RANGE,
                    },
                  ]}
                  placeholder="Search entries here..."
                />
                <ResponsiveRow>
                  {reportPermissions.GcashRefundDetailedReport.export && (
                    <ExportButton
                      icon="file-csv"
                      iconPosition="left"
                      disabled={loading}
                      onClick={() => setIsConfirmDownloadModalOpen(true)}
                    >
                      CSV
                    </ExportButton>
                  )}
                  <ColumnVisibilityDropdown
                    multi
                    showMulti={false}
                    placeholder="Visible Columns"
                    value={visibleColumns}
                    options={Object.keys(tableConfig).map(key => ({
                      value: key,
                      label: tableConfig[key].headerLabel,
                    }))}
                    onChange={setVisibleColumns}
                  />
                </ResponsiveRow>
              </>
            }
            config={Object.keys(tableConfig).reduce((config, key) => {
              if (visibleColumns.includes(key)) {
                config[key] = tableConfig[key];
              }
              return config;
            }, {})}
            pagination={{
              ...pagination,
              start: pagination.startKeys,
              withStartKeys: true,
              count: 0,
              cursors: [],
              handleChange: setNewPagination,
            }}
            series={{ page, setPage, isLastPage }}
          />
        </DataContainer>
      </Page>
      <AlertModal
        isOpen={isConfirmDownloadModalOpen}
        title="Export Reports Alert"
        header="ARE YOU SURE?"
        variant="warn"
        icon="exclamation-circle"
        subHeader={
          <span>
            You are about to export all filtered records as .CSV File.
          </span>
        }
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() => setIsConfirmDownloadModalOpen(false)}
        handleCancel={() => setIsConfirmDownloadModalOpen(false)}
        confirmText="Yes"
        handleConfirm={async () => {
          const notifTime = new Date().getTime();
          setIsConfirmDownloadModalOpen(false);
          addNotif({
            id: 'GcashRefundDetailed-' + notifTime,
            notifTime,
            type: 'info',
            title: 'Downloading Report File',
            message: 'Downloading GCash Refund Detailed Report',
            isProgress: true,
            progressData: {
              progress: 0,
              isProgressive: false,
            },
            isLocal: true,
            data: {},
            reportDLParams: {
              query: GET_GCASHREFUND_REPORTS_DETAILED,
              path: REPORT_PATH,
              variables: {
                filter,
                pagination: {
                  startKeys: '',
                  limit: 1000,
                },
              },
              onDownload: () => {
                logExtraction({
                  variables: {
                    data: {
                      type: 'gcashrefunddetailedreport',
                    },
                  },
                });
              },
              tableConfig,
              fileName: `GCash Refund_Detailed ${format(new Date(), 'MMDDYYYY')}.csv`,
            },
          });
        }}
      />
      <AlertModal
        isOpen={isSuccessDownloadModalOpen}
        title="Export Reports Alert"
        header="SUCCESS!"
        variant="success"
        icon="check-circle"
        subHeader={
          <span>
            You exported{' '}
            <TransactionLogHighlight>
              {data.length} row(s){' '}
            </TransactionLogHighlight>{' '}
            as .CSV File succesfully.
          </span>
        }
        description="Kindly check the downloaded file."
        confirmText="Back to All Reports"
        handleClose={() => setIsSuccessDownloadModalOpen(false)}
        handleConfirm={() => {
          setIsSuccessDownloadModalOpen(false);
        }}
      />
    </>
  );
};

GCashRefundReportDetailed.propTypes = {};

export default GCashRefundReportDetailed;
