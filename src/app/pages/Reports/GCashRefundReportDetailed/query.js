import { gql } from '@apollo/client';

export const GET_GCASHREFUND_REPORTS_DETAILED = gql`
  query getgcashRefundDetailedReport(
    $filter: SearchGcashRefundDetailedReport!
    $pagination: PaginationReportInput!
  ) {
    gcashRefundDetailedReport(filter: $filter, pagination: $pagination) {
      lastKey
      filteredData {
        refundId
        reference
        gcashTransId
        accountNumber
        mobileNumber
        channelName
        postedTimestamp
        status
        refundDate
        refundReason
        refundAmount
        refundApprovalStatus
        billType
        convenienceFee
        hasConvenienceFee
      }
    }
  }
`;

export const GET_CHANNEL_OPTIONS = gql`
  query getChannelOptions {
    channelsLoose {
      id
      name
      channelId
    }
  }
`;

export const REPORT_PATH = 'gcashRefundDetailedReport';
