import format from 'date-fns/format';
import React, { useContext, useEffect, useState } from 'react';
import { useQuery } from '@apollo/client';
import { ExportButton } from '../../../components/Button/ExportButton';
import DataContainer from '../../../components/DataContainer';
import DataTable from '../../../components/DataTable';
import { FIELD_TYPES } from '../../../components/Form/constants';
import GlobalSearch from '../../../components/GlobalSearch';
import {
  json2CSVYTD,
  numberWithCommas,
} from '../../../components/GlobalSearch/utils';
import Header from '../../../components/Header';
import { AlertModal } from '../../../components/Modal';
import Page from '../../../components/Page';
import AuthContext from '../../../context/AuthContext/AuthContext';
import {
  ColumnVisibilityDropdown,
  ResponsiveRow,
  TransactionLogHighlight,
} from '../styled';
import { GET_CHANNEL_OPTIONS, GET_GCASHREFUND_SUMMARY_REPORTS } from './query';

const GCashRefundReportSummarized = () => {
  const [state, setState] = useState({
    filter: {},
  });
  const { reportPermissions } = useContext(AuthContext);

  const { data: channelData, loading: isLoadingChannels } = useQuery(
    GET_CHANNEL_OPTIONS,
    {
      fetchPolicy: 'network-only',
    }
  );

  const { data, loading } = useQuery(GET_GCASHREFUND_SUMMARY_REPORTS, {
    variables: {
      filter: state.filter,
    },
    fetchPolicy: 'network-only',
  });

  const [footer, setFooter] = useState({});

  const [isConfirmDownloadModalOpen, setIsConfirmDownloadModalOpen] =
    useState(false);
  const [isSuccessDownloadModalOpen, setIsSuccessDownloadModalOpen] =
    useState(false);

  const channelOptions =
    !isLoadingChannels && channelData
      ? channelData.channelsLoose.map(channel => ({
          value: channel.id,
          label: channel.name,
        }))
      : [];

  channelOptions.unshift({
    value: null,
    label: 'Any',
  });

  const tableConfig = {
    channelName: {
      headerLabel: 'Channel',
      sortable: true,
    },
    totalApprovedRefundAmount: {
      headerLabel: 'Total Approved Refund Amount (Webtool)',
      sortable: true,
      renderAs: data => numberWithCommas(data.totalApprovedRefundAmount, 2),
    },
    totalApprovedRefundCount: {
      headerLabel: 'Total Approved Refund Count (Webtool)',
      sortable: true,
      renderAs: data => numberWithCommas(data.totalApprovedRefundCount),
    },
    totalForApprovalAmount: {
      headerLabel: 'Total For Approval Amount',
      sortable: true,
      renderAs: data => numberWithCommas(data.totalForApprovalAmount, 2),
    },
    totalForApprovalCount: {
      headerLabel: 'Total For Approval Refund Count',
      sortable: true,
      renderAs: data => numberWithCommas(data.totalForApprovalCount),
    },
    totalAutoRefundAmount: {
      headerLabel: 'Total Approved Refund Amount (Auto)',
      sortable: true,
      renderAs: data => numberWithCommas(data.totalAutoRefundAmount, 2),
    },
    totalAutoRefundCount: {
      headerLabel: 'Total Approved Refund Count (Auto)',
      sortable: true,
      renderAs: data => numberWithCommas(data.totalAutoRefundCount),
    },
  };

  const [visibleColumns, setVisibleColumns] = useState(
    Object.keys(tableConfig)
  );

  useEffect(() => {
    if (data?.gcashRefundSummaryReport) {
      var footer = data.gcashRefundSummaryReport.reduce(
        (finalVal, currentVal) => {
          return {
            amountApproved:
              finalVal.amountApproved +
              parseFloat(currentVal.totalApprovedRefundAmount),
            countApproved:
              finalVal.countApproved +
              parseFloat(currentVal.totalApprovedRefundCount),
            amountApproval:
              finalVal.amountApproval +
              parseFloat(currentVal.totalForApprovalAmount),
            countApproval:
              finalVal.countApproval +
              parseFloat(currentVal.totalForApprovalCount),
            amountAutoApproved:
              finalVal.amountAutoApproved +
              parseFloat(currentVal.totalAutoRefundAmount),
            countAutoApproved:
              finalVal.countAutoApproved +
              parseFloat(currentVal.totalAutoRefundCount),
          };
        },
        {
          amountApproved: 0,
          countApproved: 0,
          amountApproval: 0,
          countApproval: 0,
          amountAutoApproved: 0,
          countAutoApproved: 0,
        }
      );

      setFooter({
        channelName: 'Grand Total',
        totalApprovedRefundAmount: numberWithCommas(footer.amountApproved, 2),
        totalApprovedRefundCount: numberWithCommas(footer.countApproved),
        totalForApprovalAmount: numberWithCommas(footer.amountApproval, 2),
        totalForApprovalCount: numberWithCommas(footer.countApproval),
        totalAutoRefundAmount: numberWithCommas(footer.amountAutoApproved, 2),
        totalAutoRefundCount: numberWithCommas(footer.countAutoApproved),
      });
    }
  }, [JSON.stringify(data?.gcashRefundSummaryReport)]);

  return (
    <>
      <Page>
        <Header
          withHome
          title="GCash Refund Summarized Report"
          path={['Reports', 'GCash Refund Summarized Report']}
        />
        <DataContainer>
          <DataTable
            loading={loading}
            data={data?.gcashRefundSummaryReport}
            minCellWidth={200}
            headerOptions={
              <>
                <GlobalSearch
                  onSearch={filter => {
                    setState({ ...state, filter });
                  }}
                  fields={[
                    {
                      label: 'Channel',
                      name: 'channelId',
                      type: FIELD_TYPES.SELECT,
                      options: channelOptions,
                    },
                    {
                      label: 'Date Range',
                      name: 'refundRange',
                      type: FIELD_TYPES.DATE_RANGE,
                    },
                  ]}
                  placeholder="Search entries here..."
                />
                <ResponsiveRow>
                  {reportPermissions.GcashRefundSummaryReport.export && (
                    <ExportButton
                      icon="file-csv"
                      iconPosition="left"
                      disabled={loading}
                      onClick={() => setIsConfirmDownloadModalOpen(true)}
                    >
                      CSV
                    </ExportButton>
                  )}
                  <ColumnVisibilityDropdown
                    multi
                    showMulti={false}
                    placeholder="Visible Columns"
                    value={visibleColumns}
                    options={Object.keys(tableConfig).map(key => ({
                      value: key,
                      label: tableConfig[key].headerLabel,
                    }))}
                    onChange={setVisibleColumns}
                  />
                </ResponsiveRow>
              </>
            }
            config={Object.keys(tableConfig).reduce((config, key) => {
              if (visibleColumns.includes(key)) {
                config[key] = tableConfig[key];
              }
              return config;
            }, {})}
            footer={footer}
          />
        </DataContainer>
      </Page>
      <AlertModal
        isOpen={isConfirmDownloadModalOpen}
        title="Export Reports Alert"
        header="ARE YOU SURE?"
        variant="warn"
        icon="exclamation-circle"
        subHeader={
          <span>
            You are about to export all filtered records as .CSV File.
          </span>
        }
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() => setIsConfirmDownloadModalOpen(false)}
        handleCancel={() => setIsConfirmDownloadModalOpen(false)}
        confirmText="Yes"
        handleConfirm={async () => {
          setIsConfirmDownloadModalOpen(false);
          const gcashRefundSummary = await json2CSVYTD(
            data.gcashRefundSummaryReport,
            tableConfig
          );
          let csvData = `${gcashRefundSummary}\n,\n`;
          const fileData = {
            mime: 'text/csv',
            filename: `GCash Refund_Summarized ${format(new Date(), 'MMDDYYYY')}.csv`,
            contents: csvData,
          };
          const blob = new Blob([fileData.contents], {
            type: fileData.mime,
          });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          document.body.appendChild(a);
          a.download = fileData.filename;
          a.href = url;
          a.click();
          document.body.removeChild(a);
        }}
      />
      <AlertModal
        isOpen={isSuccessDownloadModalOpen}
        title="Export Reports Alert"
        header="SUCCESS!"
        variant="success"
        icon="check-circle"
        subHeader={
          <span>
            You exported{' '}
            <TransactionLogHighlight>
              {data?.length} row(s){' '}
            </TransactionLogHighlight>{' '}
            as .CSV File succesfully.
          </span>
        }
        description="Kindly check the downloaded file."
        confirmText="Back to All Reports"
        handleClose={() => setIsSuccessDownloadModalOpen(false)}
        handleConfirm={() => {
          setIsSuccessDownloadModalOpen(false);
        }}
      />
    </>
  );
};

GCashRefundReportSummarized.propTypes = {};

export default GCashRefundReportSummarized;
