import { gql } from '@apollo/client';

export const GET_GCASHREFUND_SUMMARY_REPORTS = gql`
  query getGCashRefundSummaryReport($filter: SearchGcashRefundSummaryReport!) {
    gcashRefundSummaryReport(filter: $filter) {
      channelName
      totalApprovedRefundAmount
      totalApprovedRefundCount
      totalForApprovalAmount
      totalForApprovalCount
      totalAutoRefundAmount
      totalAutoRefundCount
    }
  }
`;

export const GET_CHANNEL_OPTIONS = gql`
  query getChannelOptions {
    channelsLoose {
      id
      name
      channelId
    }
  }
`;

export const REPORT_PATH = 'gcashRefundSummaryReport';
