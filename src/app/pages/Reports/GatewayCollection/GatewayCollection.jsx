import { json2csv } from 'json-2-csv';
import { json2CSVYTD } from '../../../components/GlobalSearch/utils';
import React, { useContext, useEffect, useMemo, useState } from 'react';
import { useQuery } from '@apollo/client';
import styled from 'styled-components';
import formatCurrency from '../../../utils/formatCurrency';
import { ExportButton } from '../../../components/Button/ExportButton';
import DataContainer from '../../../components/DataContainer';
import DataHeader from '../../../components/DataHeader';
import DataTable from '../../../components/DataTable';
import Dropdown from '../../../components/Dropdown';
import {
  DropdownButton,
  DropdownMenu,
} from '../../../components/Dropdown/Dropdown';
import Header from '../../../components/Header';
import Loader from '../../../components/Loader';
import { AlertModal } from '../../../components/Modal';
import Page from '../../../components/Page';
import AuthContext from '../../../context/AuthContext/AuthContext';
import { useMutation } from '@apollo/client';
import { EXPORT_REPORTS } from '../mutation';
import { ResponsiveRow } from '../styled';
import { GET_GATEWAY_COLLECTION_REPORTS } from './query';

const TableViewDropdown = styled(Dropdown)`
  margin: 0 10px;
  ${DropdownButton} {
    border: 0;
    border-bottom: 1px solid #979797;
  }

  ${DropdownMenu} {
    border: 1px solid #979797;
  }
`;

const months = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
];

const GatewayCollection = () => {
  const { reportPermissions } = useContext(AuthContext);
  const [tableView, setTableView] = useState('company');
  const [isConfirmDownloadModalOpen, setIsConfirmDownloadModalOpen] =
    useState(false);
  const [isSuccessDownloadModalOpen, setIsSuccessDownloadModalOpen] =
    useState(false);

  const currentMonthIndex = new Date().getMonth();

  const [initialLoad, setInitialLoad] = useState(true);
  const [month1, setMonth1] = useState([{}]);
  const [month2, setMonth2] = useState([{}]);
  const [month3, setMonth3] = useState([{}]);
  const { data, loading } = useQuery(GET_GATEWAY_COLLECTION_REPORTS, {
    fetchPolicy: initialLoad ? 'network-only' : 'cache-first',
    variables: {
      where: {
        startMonth:
          months[
            currentMonthIndex - 2 < 0
              ? 12 + (currentMonthIndex - 2)
              : currentMonthIndex - 2
          ],
        endMonth: months[currentMonthIndex],
      },
      type: tableView,
    },
  });

  useEffect(() => {
    if (data && data.gatewayCollectionSummary) {
      setInitialLoad(false);
    }
  }, [data]);

  useEffect(() => {
    if (data?.gatewayCollectionSummary) {
      for (const collection of data.gatewayCollectionSummary) {
        if (collection.month === months[currentMonthIndex]) {
          setMonth1(collection.payload);
        }
      }
    }
  }, [data]);

  useEffect(() => {
    if (data?.gatewayCollectionSummary) {
      for (const collection of data.gatewayCollectionSummary) {
        if (collection.month === months[currentMonthIndex - 1]) {
          setMonth2(collection.payload);
        }
      }
    }
  }, [data]);

  useEffect(() => {
    if (data?.gatewayCollectionSummary) {
      for (const collection of data.gatewayCollectionSummary) {
        if (collection.month === months[currentMonthIndex - 2]) {
          setMonth3(collection.payload);
        }
      }
    }
  }, [data]);

  const [logExtraction] = useMutation(EXPORT_REPORTS);

  const { totals, totalPerMonth } = useMemo(() => {
    const totals = {};
    const totalPerMonth = {
      'YTD Total': { transCount: 0, transAmount: 0, bankCharge: 0 },
    };
    if (data?.gatewayCollectionSummary) {
      for (const collection of data.gatewayCollectionSummary) {
        totalPerMonth[collection.month] = {
          transCount: 0,
          transAmount: 0,
          bankCharge: 0,
        };
        for (const payload of collection.payload) {
          if (!totals[payload.name]) {
            totals[payload.name] = {
              name: payload.name,
              costCenter: payload.costCenter,
              transCount: 0,
              transAmount: 0,
              bankCharge: 0,
              merchantCompany: payload.merchantCompany,
              depositoryBankAccount: payload.depositoryBankAccount,
            };
          }
          totals[payload.name].transCount += payload.transCount;
          totals[payload.name].transAmount += payload.transAmount;
          totals[payload.name].bankCharge += payload.bankCharge;
          totalPerMonth['YTD Total'].transCount += payload.transCount;
          totalPerMonth['YTD Total'].transAmount += payload.transAmount;
          totalPerMonth['YTD Total'].bankCharge += payload.bankCharge;
          totalPerMonth[collection.month].transCount += payload.transCount;
          totalPerMonth[collection.month].transAmount += payload.transAmount;
          totalPerMonth[collection.month].bankCharge += payload.bankCharge;
        }
      }
    }

    return {
      totalPerMonth,
      totals: Object.keys(totals).reduce((acc, curr) => {
        acc.push({ [tableView]: curr, ...totals[curr] });
        return acc;
      }, []),
    };
  }, [data, tableView]);

  let configCompany = {
    name: {
      headerLabel: 'Company',
      sortable: true,
    },
    costCenter: {
      headerLabel: 'Cost Center',
      sortable: true,
    },
    depositoryBankAccount: {
      headerLabel: 'Bank Depository Account No',
      sortable: true,
    },
    transCount: {
      headerLabel: 'Trans Count',
      sortable: true,
    },
    transAmount: {
      headerLabel: 'Trans Amount',
      sortable: true,
      renderAs: data => formatCurrency(data.transAmount, true),
      textAlign: 'right',
    },
    bankCharge: {
      headerLabel: 'Bank Charge',
      sortable: true,
      renderAs: data => formatCurrency(data.bankCharge, true),
      textAlign: 'right',
    },
  };

  let configMid = {
    name: {
      headerLabel: 'MID',
      sortable: true,
    },
    costCenter: {
      headerLabel: 'Cost Center',
      sortable: true,
    },
    merchantCompany: {
      headerLabel: 'Company',
      sortable: true,
    },
    depositoryBankAccount: {
      headerLabel: 'Bank Depository Account No',
      sortable: true,
    },
    transCount: {
      headerLabel: 'Trans Count',
      sortable: true,
    },
    transAmount: {
      headerLabel: 'Trans Amount',
      sortable: true,
      renderAs: data => formatCurrency(data.transAmount, true),
      textAlign: 'right',
    },
    bankCharge: {
      headerLabel: 'Bank Charge',
      sortable: true,
      renderAs: data => formatCurrency(data.bankCharge, true),
      textAlign: 'right',
    },
  };

  return (
    <>
      <Page>
        <Header
          withHome
          title="Credit Card Collection Transactions"
          path={['Reports', 'Credit Card Collection Summary']}
        />
        <DataContainer>
          <DataHeader>
            {/* <GlobalSearch
              onSearch={filter => {
                setFilter(filter);
              }}
              fields={[
                {
                  label: 'Record Date',
                  name: 'createdAt',
                  type: FIELD_TYPES.DATE_RANGE,
                },
                {
                  label: 'Reference ID',
                  name: 'referenceId',
                  type: FIELD_TYPES.TEXT,
                },
              ]}
              placeholder="Search entries here..."
            /> */}
            <div />
            <ResponsiveRow>
              <div>Table View:</div>
              <TableViewDropdown
                value={tableView}
                onChange={setTableView}
                options={[
                  { value: 'company', label: 'Company' },
                  { value: 'mid', label: 'MID' },
                ]}
              />
              {reportPermissions.Report.view && (
                <ExportButton
                  icon="file-csv"
                  iconPosition="left"
                  disabled={loading}
                  onClick={() => setIsConfirmDownloadModalOpen(true)}
                >
                  CSV
                </ExportButton>
              )}
            </ResponsiveRow>
          </DataHeader>
          {!data?.gatewayCollectionSummary && <Loader />}
          {data?.gatewayCollectionSummary &&
            [
              { month: 'YTD Total', payload: totals },
              ...[...data.gatewayCollectionSummary].reverse(),
            ].map((collection, index) => {
              const footer = {
                name: 'Total',
                transCount: totalPerMonth[collection.month].transCount,
                transAmount: formatCurrency(
                  totalPerMonth[collection.month].transAmount,
                  true
                ),
                bankCharge: formatCurrency(
                  totalPerMonth[collection.month].bankCharge,
                  true
                ),
              };
              let config = {
                name: {
                  headerLabel: tableView === 'company' ? 'Company' : 'MID',
                  sortable: true,
                },
                costCenter: {
                  headerLabel: 'Cost Center',
                  sortable: true,
                  hideRow: collection.month === 'YTD Total' ? true : false,
                },
                merchantCompany: {
                  headerLabel: 'Company',
                  sortable: true,
                  hideRow: tableView === 'company' ? true : false,
                },
                depositoryBankAccount: {
                  headerLabel: 'Bank Depository Account No',
                  sortable: true,
                },
                transCount: {
                  headerLabel: 'Trans Count',
                  sortable: true,
                },
                transAmount: {
                  headerLabel: 'Trans Amount',
                  sortable: true,
                  renderAs: data => formatCurrency(data.transAmount, true),
                  textAlign: 'right',
                },
                bankCharge: {
                  headerLabel: 'Bank Charge',
                  sortable: true,
                  renderAs: data => formatCurrency(data.bankCharge, true),
                  textAlign: 'right',
                },
              };

              return (
                <div style={{ marginBottom: 30 }} key={index}>
                  <DataTable
                    loading={loading}
                    data={collection.payload}
                    headerOptions={
                      <DataHeader.Title>{collection.month}</DataHeader.Title>
                    }
                    minCellWidth={250}
                    footer={footer}
                    config={config}
                    pagination={null}
                  />
                </div>
              );
            })}
        </DataContainer>
      </Page>
      <AlertModal
        isOpen={isConfirmDownloadModalOpen}
        title="Export Reports Alert"
        header="ARE YOU SURE?"
        variant="warn"
        icon="exclamation-circle"
        subHeader={
          <span>You are about to export gateway collection as .CSV File.</span>
        }
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() => setIsConfirmDownloadModalOpen(false)}
        handleCancel={() => setIsConfirmDownloadModalOpen(false)}
        confirmText="Yes"
        handleConfirm={async () => {
          const headerLabel = tableView === 'company' ? 'Company' : 'MID';
          const keys = [headerLabel];
          keys.push(`Bank Depository Account No.`);
          keys.push(`Total Trans Count`);
          keys.push(`Total Trans Amount`);
          keys.push(`Total Bank Charge`);
          const keysMid = [headerLabel];
          keysMid.push('Company');
          keysMid.push(`Bank Depository Account No.`);
          keysMid.push(`Total Trans Count`);
          keysMid.push(`Total Trans Amount`);
          keysMid.push(`Total Bank Charge`);
          const csvData = [];
          for (const total of totals) {
            const entity = {
              [headerLabel]: total.name,
              'Bank Depository Account No.': total.depositoryBankAccount,
              'Total Trans Count': total.transCount,
              'Total Trans Amount': total.transAmount,
              'Total Bank Charge': total.bankCharge,
            };
            const entityMid = {
              [headerLabel]: total.name,
              Company: total.merchantCompany,
              'Bank Depository Account No.': total.depositoryBankAccount,
              'Total Trans Count': total.transCount,
              'Total Trans Amount': total.transAmount,
              'Total Bank Charge': total.bankCharge,
            };
            csvData.push(tableView === 'company' ? entity : entityMid);
          }
          logExtraction({
            variables: {
              data: {
                type: 'collection',
              },
            },
          });

          const csv = await json2csv(
            csvData,
            tableView === 'company' ? { keys } : { keysMid }
          );
          const csv1 = await json2CSVYTD(
            month1,
            tableView === 'company' ? configCompany : configMid
          );
          const csv2 = await json2CSVYTD(
            month2,
            tableView === 'company' ? configCompany : configMid
          );
          const csv3 = await json2CSVYTD(
            month3,
            tableView === 'company' ? configCompany : configMid
          );

          let csvDataAll = `,\nYTD Total Reports\n,\n`;
          csvDataAll += `${csv}\n,\n`;
          csvDataAll += `,\n${months[currentMonthIndex]} Reports\n,\n`;
          csvDataAll += `${csv1}\n,\n`;
          csvDataAll += `,\n${months[currentMonthIndex - 1]} Reports\n,\n`;
          csvDataAll += `${csv2}\n,\n`;
          csvDataAll += `,\n${months[currentMonthIndex - 2]} Reports\n,\n`;
          csvDataAll += `${csv3}\n`;

          const fileData = {
            mime: 'text/csv',
            filename: 'gateway-collection.csv',
            contents: csvDataAll,
          };
          const blob = new Blob([fileData.contents], {
            type: fileData.mime,
          });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          document.body.appendChild(a);
          a.download = fileData.filename;
          a.href = url;
          a.click();
          document.body.removeChild(a);
          setIsConfirmDownloadModalOpen(false);
          setIsSuccessDownloadModalOpen(true);
        }}
      />
      <AlertModal
        isOpen={isSuccessDownloadModalOpen}
        title="Export Reports Alert"
        header="SUCCESS!"
        variant="success"
        icon="check-circle"
        subHeader={
          <span>
            You exported the gateway collection as .CSV File successfully.
          </span>
        }
        description="Kindly check the downloaded file."
        confirmText="Back to All Reports"
        handleClose={() => setIsSuccessDownloadModalOpen(false)}
        handleConfirm={() => {
          setIsSuccessDownloadModalOpen(false);
        }}
      />
    </>
  );
};

GatewayCollection.propTypes = {};

export default GatewayCollection;
