import { gql } from '@apollo/client';

export const GET_GATEWAY_COLLECTION_REPORTS = gql`
  query getGatewayCollectionReports(
    $where: monthInput!
    $type: CollectionType!
  ) {
    gatewayCollectionSummary(where: $where, type: $type) {
      month
      year
      payload {
        name
        transCount
        transAmount
        bankCharge
        merchantCompany
        depositoryBankAccount
        costCenter
      }
    }
  }
`;
