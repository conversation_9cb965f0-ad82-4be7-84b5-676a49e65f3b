import format from 'date-fns/format';
import React, { useContext, useState } from 'react';
import { ExportButton } from '../../../components/Button/ExportButton';
import DataContainer from '../../../components/DataContainer';
import DataTable from '../../../components/DataTable';
import { FIELD_TYPES } from '../../../components/Form/constants';
import GlobalSearch from '../../../components/GlobalSearch';
import Header from '../../../components/Header';
import { AlertModal } from '../../../components/Modal';
import Page from '../../../components/Page';
import AuthContext from '../../../context/AuthContext/AuthContext';
import { useMutation } from '@apollo/client';
import useQueryReportSeries from '../../../hooks/useQueryReportSeries';
import formatCurrency from '../../../utils/formatCurrency';
import { EXPORT_REPORTS } from '../mutation';
import {
  ColumnVisibilityDropdown,
  ResponsiveRow,
  TransactionLogHighlight,
} from '../styled';
import { GET_GATEWAY_CC_REPORTS, REPORT_PATH } from './query';
import NotificationContext from '../../../context/NotificationContext';
import { PAYMENT_GATEWAYS } from '../../BankCodeManagement/BankCodeManagement';

const GatewayCreditCard = () => {
  const { reportPermissions } = useContext(AuthContext);
  const {
    pagination,
    filter,
    setNewPagination,
    setFilter,
    data,
    loading,
    page,
    setPage,
    isLastPage,
  } = useQueryReportSeries(GET_GATEWAY_CC_REPORTS, REPORT_PATH, {
    pagination: {
      startKeys: '',
      limit: 10,
    },
  });

  const { addNotif } = useContext(NotificationContext);

  const [isConfirmDownloadModalOpen, setIsConfirmDownloadModalOpen] =
    useState(false);
  const [isSuccessDownloadModalOpen, setIsSuccessDownloadModalOpen] =
    useState(false);
  const [logExtraction] = useMutation(EXPORT_REPORTS);

  const tableConfig = {
    createdAt: {
      headerLabel: 'Record Date Time',
      sortable: data =>
        format(new Date(data.createdAt), 'MM/DD/YYYY - hh:mm:ss A'),
      renderAs: data =>
        format(new Date(data.createdAt), 'MM/DD/YYYY - hh:mm:ss A'),
    },
    reference: {
      headerLabel: 'Reference No.',
      sortable: true,
    },
    accountNumber: {
      headerLabel: 'Account No.',
      sortable: true,
    },
    channelName: {
      headerLabel: 'Channel Name',
      sortable: true,
    },
    depositoryBank: {
      headerLabel: 'Depository Bank',
      sortable: true,
    },
    depositoryBankAccountNo: {
      headerLabel: 'Depository Bank Account No.',
      sortable: true,
    },
    merchantCompany: {
      headerLabel: 'Merchant Company',
      sortable: true,
    },
    mid: {
      headerLabel: 'MID',
      sortable: true,
    },
    transId: {
      headerLabel: 'Transaction ID',
      sortable: true,
    },
    authCode: {
      headerLabel: 'Auth Code',
      sortable: true,
    },
    creditCardNumber: {
      headerLabel: 'Credit Card No.',
      sortable: true,
    },
    creditCardHolderName: {
      headerLabel: 'CC Holder Name',
      sortable: true,
    },
    creditCardBank: {
      headerLabel: 'CC Bank',
      sortable: true,
    },
    creditCardCountry: {
      headerLabel: 'CC Country',
      sortable: true,
    },
    creditCardType: {
      headerLabel: 'CC Type',
      sortable: true,
    },
    paymentMethod: {
      headerLabel: 'Payment Method',
      sortable: true,
    },
    mobileNumber: {
      headerLabel: 'MSISDN',
      sortable: true,
    },
    amountCurrency: {
      headerLabel: 'Currency',
      sortable: true,
    },
    grossAmount: {
      headerLabel: 'Gross Amount',
      sortable: true,
      renderAs: data => formatCurrency(+data.grossAmount, true),
      textAlign: 'right',
    },
    bankDiscount: {
      headerLabel: 'Bank Discount',
      sortable: true,
      renderAs: data => formatCurrency(+data.bankDiscount, true),
      textAlign: 'right',
    },
    withholdingtax: {
      headerLabel: 'Withholding Tax',
      sortable: true,
      renderAs: data => formatCurrency(+data.withholdingtax, true),
      textAlign: 'right',
    },
    netAmount: {
      headerLabel: 'Net Amount',
      sortable: true,
      renderAs: data => formatCurrency(+data.netAmount, true),
      textAlign: 'right',
    },
    status: {
      headerLabel: 'Payment Status',
      sortable: true,
    },
    threeDFlag: {
      headerLabel: '3D Secure Flag',
      sortable: true,
    },
    paymentGateway: {
      headerLabel: 'Payment Gateway',
      sortable: true,
    },
    costCenter: {
      headerLabel: 'Cost Center',
      sortable: true,
    },
  };

  const [visibleColumns, setVisibleColumns] = useState(
    Object.keys(tableConfig)
  );

  return (
    <>
      <Page>
        <Header
          withHome
          title="Gateway for Online Payments"
          path={['Reports', 'Gateway for Online Payments']}
        />
        <DataContainer>
          <DataTable
            loading={loading}
            data={data}
            minCellWidth={250}
            headerOptions={
              <>
                <GlobalSearch
                  onSearch={filter => {
                    const newPagination = {
                      ...pagination,
                      startKeys: '',
                    };
                    setFilter(filter);
                    setNewPagination(newPagination);
                  }}
                  fields={[
                    {
                      label: 'Reference No.',
                      name: 'reference',
                      type: FIELD_TYPES.TEXT,
                      isKey: true,
                    },
                    {
                      label: 'Account No.',
                      name: 'accountNumber',
                      type: FIELD_TYPES.TEXT,
                      isKey: true,
                    },
                    {
                      label: 'MID',
                      name: 'mid',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Merchant Company',
                      name: 'merchantCompany',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'MSISDN',
                      name: 'mobileNumber',
                      type: FIELD_TYPES.TEXT,
                      disabled: ({ status }) => !!status,
                    },
                    {
                      label: 'Status',
                      name: 'status',
                      type: FIELD_TYPES.SELECT,
                      disabled: ({ mobileNumber }) => !!mobileNumber,
                      options: [
                        { value: null, label: 'Any' },

                        {
                          value: 'POSTED',
                          label: 'PAYMENT_POSTED',
                        },
                        {
                          value: 'POSTING_FAILED',
                          label: 'PAYMENT_POSTED_FAILED',
                        },
                        {
                          value: 'POSTED_LUKE',
                          label: 'PAYMENT_POSTED_LUKE',
                        },
                      ],
                    },
                    {
                      label: 'Payment Method',
                      name: 'paymentMethod',
                      type: FIELD_TYPES.SELECT,
                      options: [
                        { value: null, label: 'Any' },
                        { value: 'GCT', label: 'GCT' },
                        { value: 'OCC', label: 'Online Credit Card' },
                        { value: 'wechatpaySdk', label: 'WeChat Pay' },
                        {
                          value: 'dragonpay_otc_philippines',
                          label: 'Dragon Pay',
                        },
                        { value: 'alipay', label: 'Alipay' },
                        { value: 'grabpay_PH', label: 'GrabPay' },
                        { value: 'bankPayment', label: 'Bank Payment' },
                      ],
                    },
                    {
                      label: 'Payment Gateway',
                      name: 'paymentGateway',
                      type: FIELD_TYPES.SELECT,
                      options: PAYMENT_GATEWAYS,
                    },
                    {
                      label: 'Record Date',
                      name: 'createdAt',
                      type: FIELD_TYPES.DATE_RANGE,
                    },
                  ]}
                  placeholder="Search entries here..."
                />
                <ResponsiveRow>
                  {reportPermissions.Gateway.export && (
                    <ExportButton
                      icon="file-csv"
                      iconPosition="left"
                      disabled={loading}
                      onClick={() => setIsConfirmDownloadModalOpen(true)}
                    >
                      CSV
                    </ExportButton>
                  )}
                  <ColumnVisibilityDropdown
                    multi
                    showMulti={false}
                    placeholder="Visible Columns"
                    value={visibleColumns}
                    options={Object.keys(tableConfig).map(key => ({
                      value: key,
                      label: tableConfig[key].headerLabel,
                    }))}
                    onChange={setVisibleColumns}
                  />
                </ResponsiveRow>
              </>
            }
            config={Object.keys(tableConfig).reduce((config, key) => {
              if (visibleColumns.includes(key)) {
                config[key] = tableConfig[key];
              }
              return config;
            }, {})}
            pagination={{
              ...pagination,
              start: pagination.startKeys,
              withStartKeys: true,
              count: 0,
              cursors: [],
              handleChange: setNewPagination,
            }}
            series={{
              page,
              setPage,
              isLastPage,
            }}
          />
        </DataContainer>
      </Page>
      <AlertModal
        isOpen={isConfirmDownloadModalOpen}
        title="Export Reports Alert"
        header="ARE YOU SURE?"
        variant="warn"
        icon="exclamation-circle"
        subHeader={
          <span>
            You are about to export all filtered records as .CSV File.
          </span>
        }
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() => setIsConfirmDownloadModalOpen(false)}
        handleCancel={() => setIsConfirmDownloadModalOpen(false)}
        confirmText="Yes"
        handleConfirm={async () => {
          const notifTime = new Date().getTime();
          setIsConfirmDownloadModalOpen(false);
          addNotif({
            id: 'GWCC-' + notifTime,
            notifTime,
            type: 'info',
            title: 'Downloading Report File',
            message: 'Downloading Gateway for Online Payments Report',
            isProgress: true,
            progressData: {
              progress: 0,
              isProgressive: false,
            },
            isLocal: true,
            data: {},
            reportDLParams: {
              query: GET_GATEWAY_CC_REPORTS,
              path: REPORT_PATH,
              variables: {
                filter,
                pagination: {
                  startKeys: '',
                  limit: 1000,
                },
              },
              onDownload: () => {
                logExtraction({
                  variables: {
                    data: {
                      type: 'creditcard',
                    },
                  },
                });
              },
              tableConfig,
              fileName: 'gateway-cc.csv',
            },
          });
        }}
      />
      <AlertModal
        isOpen={isSuccessDownloadModalOpen}
        title="Export Reports Alert"
        header="SUCCESS!"
        variant="success"
        icon="check-circle"
        subHeader={
          <span>
            You exported{' '}
            <TransactionLogHighlight>
              {data.length} row(s){' '}
            </TransactionLogHighlight>
            as .CSV File succesfully.
          </span>
        }
        description="Kindly check the downloaded file."
        confirmText="Back to All Reports"
        handleClose={() => setIsSuccessDownloadModalOpen(false)}
        handleConfirm={() => {
          setIsSuccessDownloadModalOpen(false);
        }}
      />
    </>
  );
};

GatewayCreditCard.propTypes = {};

export default GatewayCreditCard;
