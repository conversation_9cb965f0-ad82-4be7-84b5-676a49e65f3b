import { gql } from '@apollo/client';

export const GET_GATEWAY_CC_REPORTS = gql`
  query getGatewayCCReports(
    $filter: SearchGatewayTransactionReportsFilterInput!
    $pagination: PaginationReportInput!
  ) {
    gatewayTransactionReports(filter: $filter, pagination: $pagination) {
      lastKey
      filteredData {
        createdAt
        channelName
        depositoryBank
        depositoryBankAccountNo
        mid
        reference
        transId
        authCode
        creditCardNumber
        creditCardHolderName
        accountNumber
        creditCardBank
        creditCardCountry
        creditCardType
        paymentMethod
        amountCurrency
        grossAmount
        bankDiscount
        withholdingtax
        netAmount
        status
        threeDFlag
        mobileNumber
        merchantCompany
        paymentGateway
        costCenter
      }
    }
  }
`;

export const REPORT_PATH = 'gatewayTransactionReports';
