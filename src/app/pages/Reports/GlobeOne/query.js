import { gql } from '@apollo/client';

export const GET_GLOBEONE_REPORTS = gql`
  query getGlobeOneReports(
    $filter: globeOneReportFilterInput!
    $pagination: PaginationReportInput!
  ) {
    globeOneReport(filter: $filter, pagination: $pagination) {
      lastKey
      filteredData {
        createdAt
        reference
        amountValue
        paymentMethod
        status
        refusalReasonRaw
        mobileNumber
      }
    }
  }
`;

export const REPORT_PATH = 'globeOneReport';
