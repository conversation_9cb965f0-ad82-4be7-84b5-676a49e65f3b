import format from 'date-fns/format';
import React, { useContext, useState } from 'react';
import { ExportButton } from '../../../components/Button/ExportButton';
import DataContainer from '../../../components/DataContainer';
import DataTable from '../../../components/DataTable';
import { FIELD_TYPES } from '../../../components/Form/constants';
import GlobalSearch from '../../../components/GlobalSearch';
import Header from '../../../components/Header';
import { AlertModal, FormModal } from '../../../components/Modal';
import Page from '../../../components/Page';
import AuthContext from '../../../context/AuthContext/AuthContext';
import { useMutation } from '@apollo/client';
import useQueryReportSeries from '../../../hooks/useQueryReportSeries';
import { EXPORT_REPORTS } from '../mutation';
import {
  ColumnVisibilityDropdown,
  ResponsiveRow,
  TransactionLogHighlight,
} from '../styled';
import { GET_GOTS_REPORT, REPORT_PATH } from './query';
import NotificationContext from '../../../context/NotificationContext';
import { numberWithCommas } from '../../../components/GlobalSearch/utils';
import { PAYMENT_GATEWAYS } from '../../BankCodeManagement/BankCodeManagement';

const GotsReport = () => {
  const { reportPermissions } = useContext(AuthContext);
  const {
    pagination,
    setNewPagination,
    filter,
    setFilter,
    data,
    loading,
    page,
    setPage,
    isLastPage,
  } = useQueryReportSeries(GET_GOTS_REPORT, REPORT_PATH, {
    pagination: {
      startKeys: '',
      limit: 10,
    },
  });

  const { addNotif } = useContext(NotificationContext);

  const [isConfirmDownloadModalOpen, setIsConfirmDownloadModalOpen] =
    useState(false);
  const [isSuccessDownloadModalOpen, setIsSuccessDownloadModalOpen] =
    useState(false);

  const [recordShowing, setRecordShowing] = useState(null);

  const [logExtraction] = useMutation(EXPORT_REPORTS);

  const tableConfig = {
    reference: {
      headerLabel: 'Reference No.',
      sortable: true,
      onClick: data => {
        const gotsField = Object.keys(tableConfig).reduce(
          (recordFields, field) => {
            return {
              ...recordFields,
              [field]: {
                type: FIELD_TYPES.TEXT,
                label: tableConfig[field].headerLabel || 'LABEL',
                placeholder: 'No value',
                initialValue: data[field],
                readOnly: true,
              },
            };
          },
          {}
        );
        setRecordShowing(gotsField);
      },
    },
    channelReference: {
      headerLabel: 'Order Reference',
      sortable: true,
    },
    createdAt: {
      headerLabel: 'Record Date Time',
      sortable: true,
      renderAs: data => format(data.createdAt, 'MM/DD/YYYY - hh:mm:ss A'),
    },
    amountCurrency: {
      headerLabel: 'Currency',
      sortable: true,
    },
    amountValue: {
      headerLabel: 'Amount',
      sortable: true,
      renderAs: data => numberWithCommas(data.amountValue, 2),
    },
    status: {
      headerLabel: 'Payment Status',
      sortable: true,
    },
    paymentMethod: {
      headerLabel: 'Payment Method',
      sortable: true,
    },
    emailAddress: {
      headerLabel: 'Email Address',
      sortable: true,
    },
    creditCardNumber: {
      headerLabel: 'Credit Card No.',
      sortable: true,
    },
    creditCardBank: {
      headerLabel: 'Credit Card Bank',
      sortable: true,
    },
    creditCardCountry: {
      headerLabel: 'Credit Card Country',
      sortable: true,
    },
    creditCardType: {
      headerLabel: 'Credit Card Type',
      sortable: true,
    },
    transId: {
      headerLabel: 'Transaction ID',
      sortable: true,
    },
    threeDFlag: {
      headerLabel: '3D Secure Flag',
      sortable: true,
    },
    mobileNumber: {
      headerLabel: 'Mobile No.',
      sortable: true,
    },
    creditCardHolderName: {
      headerLabel: 'Card Holder Name',
      sortable: true,
    },
  };

  const [visibleColumns, setVisibleColumns] = useState(
    Object.keys(tableConfig)
  );

  return (
    <>
      <Page>
        <Header
          withHome
          title="GOT Payment Report"
          path={['Reports', 'GOTS Report']}
        />
        <DataContainer>
          <DataTable
            minCellWidth={200}
            headerOptions={
              <>
                <GlobalSearch
                  onSearch={filter => {
                    const newPagination = {
                      ...pagination,
                      startKeys: '',
                    };
                    setFilter(filter);
                    setNewPagination(newPagination);
                  }}
                  fields={[
                    {
                      label: 'Reference No.',
                      name: 'reference',
                      type: FIELD_TYPES.TEXT,
                      isKey: true,
                    },
                    {
                      label: 'Order Reference.',
                      name: 'channelReference',
                      type: FIELD_TYPES.TEXT,
                      isKey: true,
                    },
                    {
                      label: 'Payment Method',
                      name: 'paymentMethod',
                      type: FIELD_TYPES.SELECT,
                      options: [
                        { value: null, label: 'Any' },
                        { value: 'GCT', label: 'GCT' },
                        { value: 'OCC', label: 'Online Credit Card' },
                        { value: 'wechatpaySdk', label: 'WeChat Pay' },
                        {
                          value: 'dragonpay_otc_philippines',
                          label: 'Dragon Pay',
                        },
                        { value: 'alipay', label: 'Alipay' },
                        { value: 'grabpay_PH', label: 'GrabPay' },
                        { value: 'bankPayment', label: 'Bank Payment' },
                      ],
                    },
                    {
                      label: 'Payment Status',
                      name: 'status',
                      type: FIELD_TYPES.SELECT,
                      options: [
                        { value: null, label: 'Any' },
                        {
                          value: 'ADYEN_AUTHORISED',
                          label: 'PAYMENT_AUTHORIZED',
                        },
                        { value: 'ADYEN_REFUSED', label: 'PAYMENT_REFUSED' },
                        { value: 'POSTED', label: 'PAYMENT_POSTED' },
                        {
                          value: 'POSTING_FAILED',
                          label: 'PAYMENT_POSTED_FAILED',
                        },
                        {
                          value: 'POSTED_LUKE',
                          label: 'PAYMENT_POSTED_LUKE',
                        },
                        {
                          value: 'GCASH_AUTHORISED',
                          label: 'GCASH_AUTHORISED',
                        },
                        {
                          value: 'GCASH_REFUSED',
                          label: 'GCASH_REFUSED',
                        },
                      ],
                    },
                    {
                      label: 'Mobile No.',
                      name: 'mobileNumber',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Payment Gateway',
                      name: 'paymentGateway',
                      type: FIELD_TYPES.SELECT,
                      options: PAYMENT_GATEWAYS,
                    },
                    {
                      label: 'Date Range',
                      name: 'createdAt',
                      type: FIELD_TYPES.DATE_RANGE,
                    },
                  ]}
                />
                <ResponsiveRow>
                  {reportPermissions.Transaction.export && (
                    <ExportButton
                      icon="file-csv"
                      iconPosition="left"
                      disabled={loading}
                      onClick={() => setIsConfirmDownloadModalOpen(true)}
                    >
                      CSV
                    </ExportButton>
                  )}
                  <ColumnVisibilityDropdown
                    multi
                    showMulti={false}
                    placeholder="Visible Columns"
                    value={visibleColumns}
                    options={Object.keys(tableConfig).map(key => ({
                      value: key,
                      label: tableConfig[key].headerLabel,
                    }))}
                    onChange={setVisibleColumns}
                  />
                </ResponsiveRow>
              </>
            }
            loading={loading}
            data={data}
            config={Object.keys(tableConfig).reduce((config, key) => {
              if (visibleColumns.includes(key)) {
                config[key] = tableConfig[key];
              }
              return config;
            }, {})}
            pagination={{
              ...pagination,
              start: pagination.startKeys,
              withStartKeys: true,
              count: 0,
              cursors: [],
              handleChange: setNewPagination,
            }}
            series={{
              page,
              setPage,
              isLastPage,
            }}
          />
        </DataContainer>
      </Page>
      {!!recordShowing && (
        <FormModal
          isOpen={!!recordShowing}
          width="600px"
          handleClose={() => setRecordShowing(null)}
          title="GOTS Record"
          submitText="Close"
          fields={recordShowing}
          handleSubmit={() => setRecordShowing(null)}
        />
      )}
      <AlertModal
        isOpen={isConfirmDownloadModalOpen}
        title="Export Reports Alert"
        header="ARE YOU SURE?"
        variant="warn"
        icon="exclamation-circle"
        subHeader={
          <span>
            You are about to export all filtered records as .CSV File.
          </span>
        }
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() => setIsConfirmDownloadModalOpen(false)}
        handleCancel={() => setIsConfirmDownloadModalOpen(false)}
        confirmText="Yes"
        handleConfirm={() => {
          const notifTime = new Date().getTime();
          setIsConfirmDownloadModalOpen(false);
          addNotif({
            id: 'GOTS-' + notifTime,
            notifTime,
            type: 'info',
            title: 'Downloading Report File',
            message: 'Downloading GOTS Report',
            isProgress: true,
            progressData: {
              progress: 0,
              isProgressive: false,
            },
            isLocal: true,
            data: {},
            reportDLParams: {
              query: GET_GOTS_REPORT,
              path: REPORT_PATH,
              variables: {
                filter,
                pagination: {
                  startKeys: '',
                  limit: 1000,
                },
              },
              onDownload: () => {
                logExtraction({
                  variables: {
                    data: {
                      type: 'gots',
                    },
                  },
                });
              },
              tableConfig,
              fileName: 'GOTS Report.csv',
            },
          });
        }}
      />
      <AlertModal
        isOpen={isSuccessDownloadModalOpen}
        title="Export Reports Alert"
        header="SUCCESS!"
        variant="success"
        icon="check-circle"
        subHeader={
          <span>
            You exported{' '}
            <TransactionLogHighlight>
              {data.length} row(s){' '}
            </TransactionLogHighlight>{' '}
            as .CSV File succesfully.
          </span>
        }
        description="Kindly check the downloaded file."
        confirmText="Back to All Reports"
        handleClose={() => setIsSuccessDownloadModalOpen(false)}
        handleConfirm={() => {
          setIsSuccessDownloadModalOpen(false);
        }}
      />
    </>
  );
};

GotsReport.propTypes = {};

export default GotsReport;
