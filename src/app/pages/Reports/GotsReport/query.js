import { gql } from '@apollo/client';

export const GET_GOTS_REPORT = gql`
  query getGotsReports(
    $filter: SearchTransactionLogsFilterInput!
    $pagination: PaginationReportInput!
  ) {
    gotsReports(filter: $filter, pagination: $pagination) {
      lastKey
      filteredData {
        channelReference
        reference
        createdAt
        amountCurrency
        amountValue
        status
        paymentMethod
        emailAddress
        creditCardNumber
        creditCardBank
        creditCardCountry
        creditCardType
        transId
        threeDFlag
        mobileNumber
        creditCardHolderName
      }
    }
  }
`;

export const REPORT_PATH = 'gotsReports';
