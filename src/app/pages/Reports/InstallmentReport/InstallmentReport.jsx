import format from 'date-fns/format';
import React, { useContext, useState } from 'react';
import { ExportButton } from '../../../components/Button/ExportButton';
import DataContainer from '../../../components/DataContainer';
import DataTable from '../../../components/DataTable';
import { FIELD_TYPES } from '../../../components/Form/constants';
import GlobalSearch from '../../../components/GlobalSearch';
import Header from '../../../components/Header';
import { AlertModal } from '../../../components/Modal';
import Page from '../../../components/Page';
import AuthContext from '../../../context/AuthContext/AuthContext';
import { useMutation } from '@apollo/client';
import { EXPORT_REPORTS } from '../mutation';
import useQueryReportSeries from '../../../hooks/useQueryReportSeries-v2';
import {
  ColumnVisibilityDropdown,
  ResponsiveRow,
  TransactionLogHighlight,
} from '../styled';
import { GET_INSTALLMENT_REPORT, REPORT_PATH } from './query';
import NotificationContext from '../../../context/NotificationContext';
import { numberWithCommas } from '../../../components/GlobalSearch/utils';

const InstallmentReport = () => {
  const { reportPermissions } = useContext(AuthContext);
  const {
    pagination,
    filter,
    setNewPagination,
    setFilter,
    data,
    loading,
    page,
    setPage,
    isLastPage,
  } = useQueryReportSeries(GET_INSTALLMENT_REPORT, REPORT_PATH, {
    pagination: {
      startKey: '',
      limit: 10,
    },
  });

  const { addNotif } = useContext(NotificationContext);

  const [isConfirmDownloadModalOpen, setIsConfirmDownloadModalOpen] =
    useState(false);
  const [isSuccessDownloadModalOpen, setIsSuccessDownloadModalOpen] =
    useState(false);

  const [logExtraction] = useMutation(EXPORT_REPORTS);

  const tableConfig = {
    orderReference: {
      headerLabel: 'Order Reference',
      sortable: true,
    },
    paymentId: {
      headerLabel: 'Reference No.',
      sortable: true,
    },
    createDateTime: {
      headerLabel: 'Record Date Time',
      sortable: true,
      renderAs: data => format(data.createDateTime, 'MM/DD/YYYY - hh:mm:ss A'),
    },
    itemPurchased: {
      headerLabel: 'Item(s) Purchased',
      sortable: true,
    },
    amountCurrency: {
      headerLabel: 'Currency',
      sortable: true,
    },
    amountValue: {
      headerLabel: 'Amount',
      sortable: true,
      renderAs: data => numberWithCommas(data.totalAmount, 2),
    },
    status: {
      headerLabel: 'Payment Status',
      sortable: true,
    },
    installmentPaymentId: {
      headerLabel: 'Payment Id',
      sortable: true,
    },
    creditCardBank: {
      headerLabel: 'Card Bank',
      sortable: true,
    },
    installmentTerm: {
      headerLabel: 'Installment Term',
      sortable: true,
      renderAs: data => {
        if (data.installment && data.installment.count) {
          const { count, interval } = data.installment;
          const unit =
            interval === 'months'
              ? 'Months'
              : interval === 'years'
                ? 'Years'
                : interval || 'Months';

          return `${count} ${unit}`;
        }
        // Fallback for old data or missing installment info
        return data.installmentTerm ? `${data.installmentTerm} Months` : '-';
      },
    },
    creditCardNumber: {
      headerLabel: 'Card No.',
      sortable: true,
    },
    creditCardCountry: {
      headerLabel: 'Card Country',
      sortable: true,
    },
    creditCardType: {
      headerLabel: 'Card Type',
      sortable: true,
    },
    creditCardHolderName: {
      headerLabel: 'Card Holder Name',
      sortable: true,
    },
    isThreeDFlag: {
      headerLabel: '3D Secure Flag',
      sortable: true,
      renderAs: data => (data.isThreeDFlag ? 'True' : 'False'),
    },
    customerContactNumber: {
      headerLabel: 'MSISDN',
      sortable: true,
    },
    emailAddress: {
      headerLabel: 'Email Address',
      sortable: true,
    },
  };

  const [visibleColumns, setVisibleColumns] = useState(
    Object.keys(tableConfig)
  );

  return (
    <>
      <Page>
        <Header
          withHome
          title="Installment Report"
          path={['Reports', 'Installment Report']}
        />
        <DataContainer>
          <DataTable
            loading={loading}
            data={data}
            minCellWidth={200}
            headerOptions={
              <>
                <GlobalSearch
                  onSearch={filter => {
                    const newPagination = {
                      ...pagination,
                      startKey: '',
                    };
                    setFilter(filter);
                    setNewPagination(newPagination);
                  }}
                  fields={[
                    {
                      label: 'Order Reference',
                      name: 'orderReference',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Reference No.',
                      name: 'paymentId',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Installment Term',
                      name: 'installmentTerm',
                      type: FIELD_TYPES.SELECT,
                      options: [
                        { value: null, label: 'None' },
                        { value: 3, label: '3' },
                        { value: 6, label: '6' },
                        { value: 9, label: '9' },
                        { value: 12, label: '12' },
                        { value: 18, label: '18' },
                        { value: 24, label: '24' },
                        { value: 36, label: '36' },
                      ],
                    },
                    {
                      label: 'Payment Status',
                      name: 'status',
                      type: FIELD_TYPES.SELECT,
                      options: [
                        { value: null, label: 'Any' },
                        {
                          value: 'PAYMENT_AUTHORIZED',
                          label: 'PAYMENT_AUTHORIZED',
                        },
                        { value: 'PAYMENT_REFUSED', label: 'PAYMENT_REFUSED' },
                      ],
                    },
                    {
                      label: 'Date Range',
                      name: 'createDateTime',
                      type: FIELD_TYPES.DATE_RANGE,
                    },
                  ]}
                  placeholder="Search entries here..."
                />
                <ResponsiveRow>
                  {reportPermissions.InstallmentReport.export && (
                    <ExportButton
                      icon="file-csv"
                      iconPosition="left"
                      disabled={loading}
                      onClick={() => setIsConfirmDownloadModalOpen(true)}
                    >
                      CSV
                    </ExportButton>
                  )}
                  <ColumnVisibilityDropdown
                    multi
                    showMulti={false}
                    placeholder="Visible Columns"
                    value={visibleColumns}
                    options={Object.keys(tableConfig).map(key => ({
                      value: key,
                      label: tableConfig[key].headerLabel,
                    }))}
                    onChange={setVisibleColumns}
                  />
                </ResponsiveRow>
              </>
            }
            config={Object.keys(tableConfig).reduce((config, key) => {
              if (visibleColumns.includes(key)) {
                config[key] = tableConfig[key];
              }
              return config;
            }, {})}
            pagination={{
              ...pagination,
              start: pagination.startKey,
              withStartKeys: true,
              count: 0,
              cursors: [],
              handleChange: setNewPagination,
            }}
            series={{ page, setPage, isLastPage }}
          />
        </DataContainer>
      </Page>
      <AlertModal
        isOpen={isConfirmDownloadModalOpen}
        title="Export Reports Alert"
        header="ARE YOU SURE?"
        variant="warn"
        icon="exclamation-circle"
        subHeader={
          <span>
            You are about to export all filtered records as .CSV File.
          </span>
        }
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() => setIsConfirmDownloadModalOpen(false)}
        handleCancel={() => setIsConfirmDownloadModalOpen(false)}
        confirmText="Yes"
        handleConfirm={async () => {
          const notifTime = new Date().getTime();
          setIsConfirmDownloadModalOpen(false);
          addNotif({
            id: 'installment-' + notifTime,
            notifTime,
            type: 'info',
            title: 'Downloading Report File',
            message: 'Downloading Installment Report',
            isProgress: true,
            progressData: {
              progress: 0,
              isProgressive: false,
            },
            isLocal: true,
            data: {},
            reportDLParams: {
              query: GET_INSTALLMENT_REPORT,
              path: REPORT_PATH,
              variables: {
                filter,
                pagination: {
                  startKey: '',
                  limit: 1000,
                },
              },
              onDownload: () => {
                logExtraction({
                  variables: {
                    data: {
                      type: 'installmentreport',
                    },
                  },
                });
              },
              tableConfig,
              fileName: `Installment Report${format(new Date(), 'MMDDYYYY')}.csv`,
            },
          });
        }}
      />
      <AlertModal
        isOpen={isSuccessDownloadModalOpen}
        title="Export Reports Alert"
        header="SUCCESS!"
        variant="success"
        icon="check-circle"
        subHeader={
          <span>
            You exported{' '}
            <TransactionLogHighlight>
              {data.length} row(s){' '}
            </TransactionLogHighlight>{' '}
            as .CSV File succesfully.
          </span>
        }
        description="Kindly check the downloaded file."
        confirmText="Back to All Reports"
        handleClose={() => setIsSuccessDownloadModalOpen(false)}
        handleConfirm={() => {
          setIsSuccessDownloadModalOpen(false);
        }}
      />
    </>
  );
};

InstallmentReport.propTypes = {};

export default InstallmentReport;
