import { gql } from '@apollo/client';

export const GET_INSTALLMENT_REPORT = gql`
  query getInstallmentReport(
    $filter: SearchInstallmentReportFilter!
    $pagination: PaginationInput!
  ) {
    installmentReport(filter: $filter, pagination: $pagination) {
      lastKey
      filteredData {
        orderReference
        paymentId
        createDateTime
        itemPurchased
        amountCurrency
        totalAmount
        status
        installmentPaymentId
        creditCardBank
        installment {
          count
          interval
        }
        creditCardNumber
        creditCardCountry
        creditCardType
        creditCardHolderName
        isThreeDFlag
        customerContactNumber
        emailAddress
      }
    }
  }
`;

export const REPORT_PATH = 'installmentReport';
