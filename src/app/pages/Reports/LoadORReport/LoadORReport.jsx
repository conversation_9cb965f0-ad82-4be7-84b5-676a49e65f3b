import format from 'date-fns/format';
import React, { useContext, useState } from 'react';
import { ExportButton } from '../../../components/Button/ExportButton';
import DataContainer from '../../../components/DataContainer';
import DataTable from '../../../components/DataTable';
import { FIELD_TYPES } from '../../../components/Form/constants';
import GlobalSearch from '../../../components/GlobalSearch';
import Header from '../../../components/Header';
import { AlertModal } from '../../../components/Modal';
import Page from '../../../components/Page';
import AuthContext from '../../../context/AuthContext/AuthContext';
import { useMutation } from '@apollo/client';
import { EXPORT_REPORTS } from '../mutation';
import useQueryReportSeries from '../../../hooks/useQueryReportSeries';
import { useQuery } from '@apollo/client';
import {
  ColumnVisibilityDropdown,
  ResponsiveRow,
  TransactionLogHighlight,
} from '../styled';
import {
  GET_OR_REPORTS,
  GET_CHANNEL_OPTIONS,
  GET_PSORPAYMENTTYPE_OPTIONS,
  REPORT_PATH,
} from './query';
import { numberWithCommas } from '../../../components/GlobalSearch/utils';

import NotificationContext from '../../../context/NotificationContext';

const ORReport = () => {
  const { reportPermissions } = useContext(AuthContext);
  const {
    pagination,
    filter,
    setNewPagination,
    setFilter,
    data,
    loading,
    page,
    setPage,
    isLastPage,
  } = useQueryReportSeries(GET_OR_REPORTS, REPORT_PATH, {
    pagination: {
      startKeys: '',
      limit: 10,
    },
  });

  const { data: channelData, loading: isLoadingChannels } = useQuery(
    GET_CHANNEL_OPTIONS,
    {
      fetchPolicy: 'network-only',
    }
  );

  const channelOptions =
    !isLoadingChannels && channelData
      ? channelData.channelsLoose.map(channel => ({
          value: channel.id,
          label: channel.name,
        }))
      : [];

  channelOptions.unshift({
    value: null,
    label: 'Any',
  });

  const { data: psORData, loading: isLoadingPSOR } = useQuery(
    GET_PSORPAYMENTTYPE_OPTIONS,
    {
      fetchPolicy: 'network-only',
    }
  );

  const psOROptions =
    !isLoadingPSOR && psORData
      ? psORData.listPSORPaymentType.map(psOR => ({
          value: psOR.or,
          label: psOR.or,
        }))
      : [];

  psOROptions.unshift(
    {
      value: null,
      label: 'Any',
    },
    {
      value: 'RETAILER',
      label: 'RETAILER',
    },
    {
      value: 'CONSUMER',
      label: 'CONSUMER',
    }
  );

  const { addNotif } = useContext(NotificationContext);

  const [isConfirmDownloadModalOpen, setIsConfirmDownloadModalOpen] =
    useState(false);
  const [isSuccessDownloadModalOpen, setIsSuccessDownloadModalOpen] =
    useState(false);

  const [logExtraction] = useMutation(EXPORT_REPORTS);

  const tableConfig = {
    createdAt: {
      headerLabel: 'Transaction Date/Time',
      sortable: true,
      renderAs: data => format(data.createdAt, 'MM/DD/YYYY - hh:mm:ss A'),
    },
    channelName: {
      headerLabel: 'Channel Name',
      sortable: true,
    },
    reference: {
      headerLabel: 'Reference No.',
      sortable: true,
    },
    amountValue: {
      headerLabel: 'Amount',
      sortable: true,
      renderAs: data => numberWithCommas(data.amountValue, 2),
    },
    isOrTransaction: {
      headerLabel: 'OR Transaction',
      sortable: true,
      renderAs: data => (data.isOrTransaction === true ? 'Yes' : 'No'),
    },
    paymentGateway: {
      headerLabel: 'Payment Gateway',
      sortable: true,
    },
    status: {
      headerLabel: 'Payment Status',
      sortable: true,
    },
    orStatus: {
      headerLabel: 'OR Status',
      sortable: true,
    },
    lukeOrTimestamp: {
      headerLabel: 'Luke OR Timestamp',
      sortable: true,
      renderAs: data => format(data.lukeOrTimestamp, 'MM/DD/YYYY - hh:mm:ss A'),
    },
    mobileNumber: {
      headerLabel: 'Mobile Number',
      sortable: true,
    },
    loadType: {
      headerLabel: 'Payment Type',
      sortable: true,
    },
  };

  const [visibleColumns, setVisibleColumns] = useState(
    Object.keys(tableConfig)
  );

  return (
    <>
      <Page>
        <Header withHome title="OR Report" path={['Reports', 'ORReport']} />
        <DataContainer>
          <DataTable
            loading={loading}
            data={data}
            minCellWidth={200}
            headerOptions={
              <>
                <GlobalSearch
                  onSearch={filter => {
                    const newPagination = {
                      ...pagination,
                      startKeys: '',
                    };
                    setFilter(filter);
                    setNewPagination(newPagination);
                  }}
                  fields={[
                    {
                      label: 'Reference No.',
                      name: 'reference',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'MobileNumber',
                      name: 'mobileNumber',
                      type: FIELD_TYPES.TEXT,
                      disabled: ({ loadType }) => !!loadType,
                    },
                    {
                      label: 'Channel Name',
                      name: 'channelId',
                      type: FIELD_TYPES.SELECT,
                      options: channelOptions,
                    },
                    {
                      label: 'Payment Type',
                      name: 'loadType',
                      type: FIELD_TYPES.SELECT,
                      options: psOROptions,
                      disabled: ({ mobileNumber }) => !!mobileNumber,
                    },
                    {
                      label: 'Payment Gateway',
                      name: 'paymentGateway',
                      type: FIELD_TYPES.SELECT,
                      options: [
                        { value: null, label: 'Any' },
                        {
                          value: 'adyen',
                          label: 'Adyen',
                        },
                        { value: 'gcash', label: 'Gcash' },
                        { value: 'xendit', label: 'Xendit' },
                      ],
                    },
                    {
                      label: 'Payment Status',
                      name: 'status',
                      type: FIELD_TYPES.SELECT,
                      options: [
                        { value: null, label: 'Any' },
                        {
                          value: 'GCASH_AUTHORISED',
                          label: 'GCASH_AUTHORISED',
                        },
                        {
                          value: 'ADYEN_AUTHORISED',
                          label: 'ADYEN_AUTHORISED',
                        },
                      ],
                    },
                    {
                      label: 'Transaction Date/Time',
                      name: 'createdAt',
                      type: FIELD_TYPES.DATE_RANGE,
                    },
                  ]}
                  placeholder="Search entries here..."
                />
                <ResponsiveRow>
                  {reportPermissions.LoadORReport.export && (
                    <ExportButton
                      icon="file-csv"
                      iconPosition="left"
                      disabled={loading}
                      onClick={() => setIsConfirmDownloadModalOpen(true)}
                    >
                      CSV
                    </ExportButton>
                  )}
                  <ColumnVisibilityDropdown
                    multi
                    showMulti={false}
                    placeholder="Visible Columns"
                    value={visibleColumns}
                    options={Object.keys(tableConfig).map(key => ({
                      value: key,
                      label: tableConfig[key].headerLabel,
                    }))}
                    onChange={setVisibleColumns}
                  />
                </ResponsiveRow>
              </>
            }
            config={Object.keys(tableConfig).reduce((config, key) => {
              if (visibleColumns.includes(key)) {
                config[key] = tableConfig[key];
              }
              return config;
            }, {})}
            pagination={{
              ...pagination,
              start: pagination.startKeys,
              withStartKeys: true,
              count: 0,
              cursors: [],
              handleChange: setNewPagination,
            }}
            series={{ page, setPage, isLastPage }}
          />
        </DataContainer>
      </Page>
      <AlertModal
        isOpen={isConfirmDownloadModalOpen}
        title="Export Reports Alert"
        header="ARE YOU SURE?"
        variant="warn"
        icon="exclamation-circle"
        subHeader={
          <span>
            You are about to export all filtered records as .CSV File.
          </span>
        }
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() => setIsConfirmDownloadModalOpen(false)}
        handleCancel={() => setIsConfirmDownloadModalOpen(false)}
        confirmText="Yes"
        handleConfirm={async () => {
          const notifTime = new Date().getTime();
          setIsConfirmDownloadModalOpen(false);
          addNotif({
            id: 'ORReport-' + notifTime,
            notifTime,
            type: 'info',
            title: 'Downloading Report File',
            message: 'Downloading ORReport Report',
            isProgress: true,
            progressData: {
              progress: 0,
              isProgressive: false,
            },
            isLocal: true,
            data: {},
            reportDLParams: {
              query: GET_OR_REPORTS,
              path: REPORT_PATH,
              variables: {
                filter,
                pagination: {
                  startKeys: '',
                  limit: 1000,
                },
              },
              onDownload: () => {
                logExtraction({
                  variables: {
                    data: {
                      type: 'loadorreport',
                    },
                  },
                });
              },
              tableConfig,
              fileName: `LoadORReport ${format(new Date(), 'MMDDYYYY')}.csv`,
            },
          });
        }}
      />
      <AlertModal
        isOpen={isSuccessDownloadModalOpen}
        title="Export Reports Alert"
        header="SUCCESS!"
        variant="success"
        icon="check-circle"
        subHeader={
          <span>
            You exported{' '}
            <TransactionLogHighlight>
              {data.length} row(s){' '}
            </TransactionLogHighlight>{' '}
            as .CSV File succesfully.
          </span>
        }
        description="Kindly check the downloaded file."
        confirmText="Back to All Reports"
        handleClose={() => setIsSuccessDownloadModalOpen(false)}
        handleConfirm={() => {
          setIsSuccessDownloadModalOpen(false);
        }}
      />
    </>
  );
};

ORReport.propTypes = {};

export default ORReport;
