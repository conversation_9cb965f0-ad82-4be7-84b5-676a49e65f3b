import { gql } from '@apollo/client';

export const GET_OR_REPORTS = gql`
  query getLoadORReport(
    $filter: loadORReportFilterInput!
    $pagination: PaginationReportInput!
  ) {
    loadORReport(filter: $filter, pagination: $pagination) {
      lastKey
      filteredData {
        createdAt
        channelName
        reference
        amountValue
        isOrTransaction
        paymentGateway
        status
        orStatus
        lukeOrTimestamp
        mobileNumber
        loadType
      }
    }
  }
`;

export const GET_CHANNEL_OPTIONS = gql`
  query getChannelOptions {
    channelsLoose {
      id
      name
      channelId
    }
  }
`;

export const GET_PSORPAYMENTTYPE_OPTIONS = gql`
  query getListPSORPaymentType {
    listPSORPaymentType {
      description
      name
      or
      orVat
    }
  }
`;

export const REPORT_PATH = 'loadORReport';
