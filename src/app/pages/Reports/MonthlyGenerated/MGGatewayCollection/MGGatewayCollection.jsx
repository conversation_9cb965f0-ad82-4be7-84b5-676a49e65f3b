import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import PropTypes from 'prop-types';
import React from 'react';
import styled from 'styled-components';
import SecondaryButton from '../../../../components/Button/SecondaryButton';
import DataContainer from '../../../../components/DataContainer';
import GlobalSearch from '../../../../components/GlobalSearch';
import Header from '../../../../components/Header';
import Page from '../../../../components/Page';
import Row from '../../../../components/Row';

const MGGatewayCollectionContainer = styled.div`
  border: 1px solid rgba(165, 165, 165, 0.5);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  margin-right: 20px;
  margin-top: 20px;
  min-width: 260px;

  ${SecondaryButton} {
    margin-top: 10px;
    padding: 0px;
    align-self: flex-end;

    &:hover {
      background: white;
    }

    &:active {
      outline: none;
    }
  }
`;

const MGGatewayCollectionCol = styled.div`
  display: inline-flex;
  align-items: center;
`;

const MGGatewayCollectionTitle = styled.h1`
  margin: 0;
  margin-left: 10px;
  margin-top: 10px;
  font-size: ${props => props.theme.fontSize.m};
`;

const MGGatewayCollectionItem = ({ rows, history }) => {
  return (
    <MGGatewayCollectionContainer>
      <MGGatewayCollectionCol>
        <FontAwesomeIcon icon="file-alt" color="#009bdd" size="2x" />
        <MGGatewayCollectionTitle>{rows.title}</MGGatewayCollectionTitle>
      </MGGatewayCollectionCol>
      <MGGatewayCollectionCol>
        <SecondaryButton
          onClick={() => {
            history.push(rows.history);
          }}
        >
          View
        </SecondaryButton>
      </MGGatewayCollectionCol>
    </MGGatewayCollectionContainer>
  );
};

MGGatewayCollectionItem.propTypes = {
  title: PropTypes.string,
  rows: PropTypes.object,
  history: PropTypes.object,
};

const MGGatewayCollection = ({ history }) => {
  const rows = [
    {
      title: 'Company',
      history: '/monthly-generated/gateway-collection/company',
    },
    {
      title: 'MID',
      history: '/monthly-generated/gateway-collection/mid',
    },
  ];

  return (
    <Page>
      <Header
        withHome
        title="CC Summary Reports"
        path={[
          'Reports',
          { label: 'Monthly Generated', to: '/monthly-generated' },
          'CC Summary Report',
        ]}
      />
      <DataContainer>
        <GlobalSearch onSearch={() => {}} fields={[]} />
        <Row>
          {rows.map((row, i) => (
            <MGGatewayCollectionItem key={i} rows={row} history={history} />
          ))}
        </Row>
      </DataContainer>
    </Page>
  );
};

MGGatewayCollection.propTypes = {
  history: PropTypes.object,
};

export default MGGatewayCollection;
