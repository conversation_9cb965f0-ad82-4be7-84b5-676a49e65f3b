import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import PropTypes from 'prop-types';
import React from 'react';
import styled from 'styled-components';
import SecondaryButton from '../../../../components/Button/SecondaryButton';
import DataContainer from '../../../../components/DataContainer';
import GlobalSearch from '../../../../components/GlobalSearch';
import Header from '../../../../components/Header';
import Page from '../../../../components/Page';
import Row from '../../../../components/Row';

const MGRevenueWirelessContainer = styled.div`
  border: 1px solid rgba(165, 165, 165, 0.5);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  margin-right: 20px;
  margin-top: 20px;
  min-width: 260px;

  ${SecondaryButton} {
    margin-top: 10px;
    padding: 0px;
    align-self: flex-end;

    &:hover {
      background: white;
    }

    &:active {
      outline: none;
    }
  }
`;

const MGRevenueWirelessCol = styled.div`
  display: inline-flex;
  align-items: center;
`;

const MGRevenueWirelessTitle = styled.h1`
  margin: 0;
  margin-left: 10px;
  margin-top: 10px;
  font-size: ${props => props.theme.fontSize.m};
`;

const MGRevenueWirelessItem = ({ rows, history }) => {
  return (
    <MGRevenueWirelessContainer>
      <MGRevenueWirelessCol>
        <FontAwesomeIcon icon="file-alt" color="#009bdd" size="2x" />
        <MGRevenueWirelessTitle>{rows.title}</MGRevenueWirelessTitle>
      </MGRevenueWirelessCol>
      <MGRevenueWirelessCol>
        <SecondaryButton
          onClick={() => {
            history.push(rows.history);
          }}
        >
          View
        </SecondaryButton>
      </MGRevenueWirelessCol>
    </MGRevenueWirelessContainer>
  );
};

MGRevenueWirelessItem.propTypes = {
  title: PropTypes.string,
  rows: PropTypes.object,
  history: PropTypes.object,
};

const MGRevenueWireless = ({ history }) => {
  const rows = [
    {
      title: 'Pay Mode',
      history: '/monthly-generated/revenue-wireless/mode',
    },
    {
      title: 'Pay Type',
      history: '/monthly-generated/revenue-wireless/type',
    },
  ];

  return (
    <Page>
      <Header
        withHome
        title="RA Wireless Reports"
        path={[
          'Reports',
          { label: 'Monthly Generated', to: '/monthly-generated' },
          'RA Wireless Report',
        ]}
      />
      <DataContainer>
        <GlobalSearch onSearch={() => {}} fields={[]} />
        <Row>
          {rows.map((row, i) => (
            <MGRevenueWirelessItem key={i} rows={row} history={history} />
          ))}
        </Row>
      </DataContainer>
    </Page>
  );
};

MGRevenueWireless.propTypes = {
  history: PropTypes.object,
};

export default MGRevenueWireless;
