import format from 'date-fns/format';
import React, { useState, useContext } from 'react';
import { useMutation } from '@apollo/client';
import useQueryReportSeries from '../../../../../hooks/useQueryReportSeries';
import DataContainer from '../../../../../components/DataContainer';
import DataTable from '../../../../../components/DataTable';
import GlobalSearch from '../../../../../components/GlobalSearch';
import Header from '../../../../../components/Header/Header';
import Page from '../../../../../components/Page';
import { AlertModal } from '../../../../../components/Modal';
import { GET_MONTHLY_GENERATED } from '../../query';
import { EXPORT_S3_REPORTS } from '../../../mutation';
import { FIELD_TYPES } from '../../../../../components/Form/constants';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import AuthContext from '../../../../../context/AuthContext/AuthContext';

const WirelessMode = () => {
  const { reportPermissions } = useContext(AuthContext);
  const [state, setState] = useState({
    isFailureDownloadFile: false,
  });

  const {
    pagination,
    setNewPagination,
    setFilter,
    data,
    loading,
    page,
    setPage,
    isLastPage,
  } = useQueryReportSeries(GET_MONTHLY_GENERATED, 'monthlyReports', {
    pagination: {
      startKeys: '',
      limit: 10,
    },
    filter: { type: 'revenuewirelesspaymode' },
  });

  const [csvExtraction, { loading: isDownloadingCsv }] = useMutation(
    EXPORT_S3_REPORTS,
    {
      onCompleted: async data => {
        setState({ ...state });
        window.open(data.downloadS3Reports.urlLink, '_blank');
      },
      onError: async () => {
        setState({
          ...state,
          isFailureDownloadFile: true,
        });
      },
    }
  );

  const tableConfig = {
    createdAt: {
      headerLabel: 'Generated Date',
      sortable: true,
      renderAs: data => format(data.createdAt, 'MM/DD/YYYY - hh:mm:ss A'),
    },
    fileName: {
      headerLabel: 'File Name',
      sortable: true,
    },
    month: {
      headerLabel: 'Month',
      sortable: true,
    },
    year: {
      headerLabel: 'Year',
      sortable: true,
    },
    actions: reportPermissions.MonthlyGenerated.export && {
      headerLabel: 'Actions',
      renderAs: data => (
        <FontAwesomeIcon
          style={{ fontSize: '20px', cursor: 'pointer' }}
          icon="file-download"
          color="#787878"
          onClick={() => {
            csvExtraction({
              variables: {
                data: { id: data.id, type: 'revenuewirelesspaymode' },
              },
            });
          }}
        />
      ),
    },
  };

  return (
    <>
      <Page>
        <Header
          withHome
          title="Company Reports"
          path={[
            'Reports',
            { label: 'Monthly Generated', to: '/monthly-generated' },
            {
              label: 'RA Wireless Report',
              to: '/monthly-generated/revenue-wireless',
            },
            'Pay Mode',
          ]}
        />
        <DataContainer>
          <DataTable
            loading={loading || isDownloadingCsv}
            minCellWidth={200}
            data={data}
            headerOptions={
              <>
                <GlobalSearch
                  onSearch={filter => {
                    const newPagination = {
                      ...pagination,
                      startKeys: '',
                    };
                    setFilter(filter);
                    setNewPagination(newPagination);
                  }}
                  fields={[
                    {
                      label: 'File Name',
                      name: 'fileName',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Month',
                      name: 'month',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Year',
                      name: 'year',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Generated Date',
                      name: 'createdAt',
                      type: FIELD_TYPES.DATE_RANGE,
                    },
                  ]}
                />
              </>
            }
            config={tableConfig}
            pagination={{
              ...pagination,
              start: pagination.startKeys,
              withStartKeys: true,
              count: 0,
              cursors: [],
              handleChange: setNewPagination,
            }}
            series={{ page, setPage, isLastPage }}
          />
        </DataContainer>
      </Page>
      <AlertModal
        isOpen={state.isFailureDownloadFile}
        title="Download Transaction Alert"
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader="There was a problem on downloading the transaction."
        description="File not found."
        handleClose={() => setState({ ...state, isFailureDownloadFile: false })}
        confirmText="Go Back"
        handleConfirm={() => {
          setState({ ...state, isFailureDownloadFile: false });
        }}
      />
    </>
  );
};

WirelessMode.propTypes = {};

export default WirelessMode;
