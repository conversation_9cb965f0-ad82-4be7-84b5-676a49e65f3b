import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import PropTypes from 'prop-types';
import React from 'react';
import styled from 'styled-components';
import SecondaryButton from '../../../components/Button/SecondaryButton';
import DataContainer from '../../../components/DataContainer';
import Header from '../../../components/Header';
import Page from '../../../components/Page';
import Row from '../../../components/Row';

const MonthlyGeneratedContainer = styled.div`
  border: 1px solid rgba(165, 165, 165, 0.5);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  margin-right: 20px;
  margin-top: 20px;
  min-width: 260px;

  ${SecondaryButton} {
    margin-top: 10px;
    padding: 0px;
    align-self: flex-end;

    &:hover {
      background: white;
    }

    &:active {
      outline: none;
    }
  }
`;

const MonthlyGeneratedCol = styled.div`
  display: inline-flex;
  align-items: center;
`;

const MonthlyGeneratedTitle = styled.h1`
  margin: 0;
  margin-left: 10px;
  margin-top: 10px;
  font-size: ${props => props.theme.fontSize.m};
`;

const MonthlyGeneratedItem = ({ rows, history }) => {
  return (
    <MonthlyGeneratedContainer>
      <MonthlyGeneratedCol>
        <FontAwesomeIcon icon="file-alt" color="#009bdd" size="2x" />
        <MonthlyGeneratedTitle>{rows.title}</MonthlyGeneratedTitle>
      </MonthlyGeneratedCol>
      <MonthlyGeneratedCol>
        <SecondaryButton
          onClick={() => {
            history.push(rows.history);
          }}
        >
          View
        </SecondaryButton>
      </MonthlyGeneratedCol>
    </MonthlyGeneratedContainer>
  );
};

MonthlyGeneratedItem.propTypes = {
  title: PropTypes.string,
  rows: PropTypes.object,
  history: PropTypes.object,
};

const MonthlyGenerated = ({ history }) => {
  const rows = [
    {
      title: 'CC Detailed Report',
      history: '/monthly-generated/gateway-credit-card',
    },
    {
      title: 'CC Summary Report',
      history: '/monthly-generated/gateway-collection',
    },
    {
      title: 'RA Wireless Report',
      history: '/monthly-generated/revenue-wireless',
    },
  ];

  return (
    <Page>
      <Header
        withHome
        title="Monthly Generated Reports"
        path={['Reports', 'Monthly Generated']}
      />
      <DataContainer>
        <Row>
          {rows.map((row, i) => (
            <MonthlyGeneratedItem key={i} rows={row} history={history} />
          ))}
        </Row>
      </DataContainer>
    </Page>
  );
};

MonthlyGenerated.propTypes = {
  history: PropTypes.object,
};

export default MonthlyGenerated;
