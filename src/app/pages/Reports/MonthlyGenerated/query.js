import { gql } from '@apollo/client';

export const GET_MONTHLY_GENERATED = gql`
  query getMonthlyGeneratedCC(
    $filter: MontlyReportInput!
    $pagination: PaginationMonthlyReportInput!
  ) {
    monthlyReports(filter: $filter, pagination: $pagination) {
      lastKey
      count
      filteredData {
        id
        year
        fileName
        type
        createdAt
        month
      }
    }
  }
`;
