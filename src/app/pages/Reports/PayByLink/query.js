import { gql } from '@apollo/client';

export const GET_PAYBYLINK_REPORTS = gql`
  query getPayByLinkReport(
    $filter: payByLinkFilterInput!
    $pagination: PaginationInput!
  ) {
    payByLink(filter: $filter, pagination: $pagination) {
      lastKey
      count
      filteredData {
        reference
        paymentLinkId
        createdAt
        description
        amountValue
        status
      }
    }
  }
`;

export const REPORT_PATH = 'payByLink';
