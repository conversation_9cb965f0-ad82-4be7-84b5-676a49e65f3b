import format from 'date-fns/format';
import React, { useContext, useState } from 'react';
import DataContainer from '../../../components/DataContainer';
import DataTable from '../../../components/DataTable';
import { FIELD_TYPES } from '../../../components/Form/constants';
import GlobalSearch from '../../../components/GlobalSearch';
import Header from '../../../components/Header/Header';
import Page from '../../../components/Page';
import useQueryReportSeries from '../../../hooks/useQueryReportSeries';
import { GET_REVENUE_INFO, REPORT_PATH } from './query';
import formatCurrency from '../../../utils/formatCurrency';
import AuthContext from '../../../context/AuthContext/AuthContext';
import { ExportButton } from '../../../components/Button/ExportButton';
import { AlertModal } from '../../../components/Modal';
import { ResponsiveRow, TransactionLogHighlight } from '../styled';
import { EXPORT_REPORTS } from '../mutation';
import { useMutation } from '@apollo/client';
import NotificationContext from '../../../context/NotificationContext';

const RevenueAccountingReport = () => {
  const { reportPermissions } = useContext(AuthContext);
  const {
    pagination,
    filter,
    setNewPagination,
    setFilter,
    data,
    loading,
    page,
    setPage,
    isLastPage,
  } = useQueryReportSeries(GET_REVENUE_INFO, REPORT_PATH, {
    pagination: {
      startKeys: '',
      limit: 10,
    },
  });

  const [isConfirmDownloadModalOpen, setIsConfirmDownloadModalOpen] =
    useState(false);
  const [isSuccessDownloadModalOpen, setIsSuccessDownloadModalOpen] =
    useState(false);
  const { addNotif } = useContext(NotificationContext);

  const [logExtraction] = useMutation(EXPORT_REPORTS);

  const tableConfig = {
    reference: {
      headerLabel: 'Reference No.',
      sortable: true,
    },
    accountNumber: {
      headerLabel: 'Account No.',
      sortable: true,
    },
    postedTimestamp: {
      headerLabel: 'Posting Date',
      sortable: true,
      renderAs: data => format(data.postedTimestamp, 'MM/DD/YYYY - hh:mm:ss A'),
    },
    prodDesc: {
      headerLabel: 'Product Description',
      sortable: true,
    },
    status: {
      headerLabel: 'Payment Status',
      sortable: true,
    },
    refundId: {
      headerLabel: 'Refund ID',
      sortable: true,
    },
    refundStatus: {
      headerLabel: 'Refund Status',
      sortable: true,
    },
    refundAmount: {
      headerLabel: 'Refund Amount',
      sortable: true,
    },
    createdAt: {
      headerLabel: 'Transaction Date',
      sortable: true,
      renderAs: data => format(data.createdAt, 'MM/DD/YYYY - hh:mm:ss A'),
    },
    paymentMethod: {
      headerLabel: 'Payment Method',
      sortable: true,
    },
    amountValue: {
      headerLabel: 'Amount',
      sortable: true,
      renderAs: data => formatCurrency(data.amountValue),
    },
  };

  return (
    <>
      <Page>
        <Header
          withHome
          title="Revenue Accounting Report"
          path={['Reports', 'Revenue Accounting Report']}
        />
        <DataContainer>
          <DataTable
            loading={loading}
            minCellWidth={200}
            data={data}
            headerOptions={
              <>
                <GlobalSearch
                  onSearch={filter => {
                    const newPagination = {
                      ...pagination,
                      startKeys: '',
                    };
                    setFilter(filter);
                    setNewPagination(newPagination);
                  }}
                  fields={[
                    {
                      label: 'Reference No.',
                      name: 'reference',
                      type: FIELD_TYPES.TEXT,
                      isKey: true,
                    },
                    {
                      label: 'Account No.',
                      name: 'accountNumber',
                      type: FIELD_TYPES.TEXT,
                      isKey: true,
                    },
                    {
                      label: 'Product Description',
                      name: 'prodDesc',
                      type: FIELD_TYPES.SELECT,
                      disabled: ({ status, paymentMethod }) =>
                        !!(status || paymentMethod),
                      options: [
                        { value: null, label: 'Any' },
                        { value: 'G', label: 'Globe-msisdn' },
                        { value: 'I', label: 'Innove-wireline' },
                        { value: 'B', label: 'Bayan' },
                        { value: 'N', label: 'Innove-iccbs' },
                      ],
                    },
                    {
                      label: 'Payment Status',
                      name: 'status',
                      type: FIELD_TYPES.SELECT,
                      disabled: ({ prodDesc, paymentMethod }) =>
                        !!(prodDesc || paymentMethod),
                      options: [
                        { value: null, label: 'Any' },
                        {
                          value: 'POSTED',
                          label: 'PAYMENT_POSTED',
                        },
                        {
                          value: 'POSTING_FAILED',
                          label: 'PAYMENT_POSTED_FAILED',
                        },
                        {
                          value: 'POSTED_LUKE',
                          label: 'PAYMENT_POSTED_LUKE',
                        },
                      ],
                    },
                    {
                      label: 'Payment Method',
                      name: 'paymentMethod',
                      type: FIELD_TYPES.SELECT,
                      disabled: ({ prodDesc, status }) =>
                        !!(prodDesc || status),
                      options: [
                        { value: null, label: 'Any' },
                        { value: 'GCT', label: 'GCT' },
                        { value: 'OCC', label: 'Online Credit Card' },
                        { value: 'wechatpaySdk', label: 'WeChat Pay' },
                        {
                          value: 'dragonpay_otc_philippines',
                          label: 'Dragon Pay',
                        },
                        { value: 'alipay', label: 'Alipay' },
                        { value: 'grabpay_PH', label: 'GrabPay' },
                        { value: 'bankPayment', label: 'Bank Payment' },
                      ],
                    },
                    {
                      label: 'Transaction Date',
                      name: 'createdAt',
                      type: FIELD_TYPES.DATE_RANGE,
                    },
                  ]}
                />
                <ResponsiveRow>
                  {reportPermissions.Wireline.export && (
                    <ExportButton
                      icon="file-csv"
                      iconPosition="left"
                      disabled={loading}
                      onClick={() => setIsConfirmDownloadModalOpen(true)}
                    >
                      CSV
                    </ExportButton>
                  )}
                </ResponsiveRow>
              </>
            }
            config={tableConfig}
            pagination={{
              ...pagination,
              start: pagination.startKeys,
              withStartKeys: true,
              count: 0,
              cursors: [],
              handleChange: setNewPagination,
            }}
            series={{ page, setPage, isLastPage }}
          />
        </DataContainer>
      </Page>
      <AlertModal
        isOpen={isConfirmDownloadModalOpen}
        title="Export Reports Alert"
        header="ARE YOU SURE?"
        variant="warn"
        icon="exclamation-circle"
        subHeader={
          <span>
            You are about to export all filtered records as .CSV File.
          </span>
        }
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() => setIsConfirmDownloadModalOpen(false)}
        handleCancel={() => setIsConfirmDownloadModalOpen(false)}
        confirmText="Yes"
        handleConfirm={async () => {
          const notifTime = new Date().getTime();
          setIsConfirmDownloadModalOpen(false);
          addNotif({
            id: 'RA-' + notifTime,
            notifTime,
            type: 'info',
            title: 'Downloading Report File',
            message: 'Downloading Revenue Accounting Report',
            isProgress: true,
            progressData: {
              progress: 0,
              isProgressive: false,
            },
            isLocal: true,
            data: {},
            reportDLParams: {
              query: GET_REVENUE_INFO,
              path: REPORT_PATH,
              variables: {
                filter,
                pagination: {
                  startKeys: '',
                  limit: 1000,
                },
              },
              onDownload: () => {
                logExtraction({
                  variables: {
                    data: {
                      type: 'raccounting',
                    },
                  },
                });
              },
              tableConfig,
              fileName: `raaccounting_report_${format(new Date(), 'YYYY_MM_DD')}.csv`,
            },
          });
        }}
      />
      <AlertModal
        isOpen={isSuccessDownloadModalOpen}
        title="Export Reports Alert"
        header="SUCCESS!"
        variant="success"
        icon="check-circle"
        subHeader={
          <span>
            You exported{' '}
            <TransactionLogHighlight>
              {data.length} row(s){' '}
            </TransactionLogHighlight>{' '}
            as .CSV File succesfully.
          </span>
        }
        description="Kindly check the downloaded file."
        confirmText="Back to All Reports"
        handleClose={() => setIsSuccessDownloadModalOpen(false)}
        handleConfirm={() => {
          setIsSuccessDownloadModalOpen(false);
        }}
      />
    </>
  );
};

RevenueAccountingReport.propTypes = {};

export default RevenueAccountingReport;
