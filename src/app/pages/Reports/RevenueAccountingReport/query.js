import { gql } from '@apollo/client';

export const GET_REVENUE_INFO = gql`
  query getWirelineRevenueInfo(
    $filter: SearchRevenueWirelineInput!
    $pagination: PaginationReportInput!
  ) {
    revenueWireline(filter: $filter, pagination: $pagination) {
      lastKey
      filteredData {
        reference
        accountNumber
        prodDesc
        status
        amountValue
        paymentMethod
        createdAt
        postedTimestamp
        refundId
        refundStatus
        refundAmount
      }
    }
  }
`;

export const REPORT_PATH = 'revenueWireline';
