import format from 'date-fns/format';
import React, { useContext } from 'react';
import DataContainer from '../../../components/DataContainer';
import DataTable from '../../../components/DataTable';
import { FIELD_TYPES } from '../../../components/Form/constants';
import GlobalSearch from '../../../components/GlobalSearch';
import Header from '../../../components/Header/Header';
import Page from '../../../components/Page';
import useQueryReportSeries from '../../../hooks/useQueryReportSeries';
import { GET_WIRELESS_REVENUE_INFO, REPORT_PATH } from './query';
import formatCurrency from '../../../utils/formatCurrency';
import AuthContext from '../../../context/AuthContext/AuthContext';
import { ExportButton } from '../../../components/Button/ExportButton';
import { EXPORT_REPORTS } from '../mutation';
import { useMutation } from '@apollo/client';
import NotificationContext from '../../../context/NotificationContext';

const RevenueAccountingWireless = () => {
  const { reportPermissions } = useContext(AuthContext);
  const {
    pagination,
    filter,
    setNewPagination,
    setFilter,
    data,
    loading,
    page,
    setPage,
    isLastPage,
  } = useQueryReportSeries(GET_WIRELESS_REVENUE_INFO, REPORT_PATH, {
    pagination: {
      startKeys: '',
      limit: 10,
    },
  });

  const { addNotif } = useContext(NotificationContext);

  const [logExtraction] = useMutation(EXPORT_REPORTS);

  const tableConfig = {
    reference: {
      headerLabel: 'Payment Reference',
      sortable: true,
    },
    createdAt: {
      headerLabel: 'Payment Date',
      sortable: true,
      renderAs: data => format(data.createdAt, 'MM/DD/YYYY - hh:mm:ss A'),
    },
    accountNumber: {
      headerLabel: 'Account No.',
      sortable: true,
    },
    payModeCode: {
      headerLabel: 'Payment Mode Code',
      sortable: true,
    },
    payModeDesc: {
      headerLabel: 'Payment Mode Description',
      sortable: true,
    },
    creditCardBank: {
      headerLabel: 'Credit Card Company',
      sortable: true,
    },
    paymentTypeCode: {
      headerLabel: 'Payment Type Code',
      sortable: true,
    },
    amountValue: {
      headerLabel: 'Amount',
      sortable: true,
      renderAs: data => formatCurrency(data.amountValue),
    },
  };

  return (
    <>
      <Page>
        <Header
          withHome
          title="Revenue Accounting Wireless Reports"
          path={['Reports', 'Revenue Accounting Wireless Reports']}
        />
        <DataContainer>
          <DataTable
            loading={loading}
            minCellWidth={250}
            data={data}
            headerOptions={
              <>
                <GlobalSearch
                  onSearch={filter => {
                    const newPagination = {
                      ...pagination,
                      startKeys: '',
                    };
                    setFilter(filter);
                    setNewPagination(newPagination);
                  }}
                  fields={[
                    {
                      label: 'Payment Reference',
                      name: 'reference',
                      type: FIELD_TYPES.TEXT,
                      isKey: true,
                    },
                    {
                      label: 'Account No.',
                      name: 'accountNumber',
                      type: FIELD_TYPES.TEXT,
                      isKey: true,
                    },
                    {
                      label: 'Credit Card Company',
                      name: 'creditCardBank',
                      type: FIELD_TYPES.TEXT,
                      disabled: ({ payModeCode }) => !!payModeCode,
                    },
                    {
                      label: 'Payment Mode Code',
                      name: 'payModeCode',
                      type: FIELD_TYPES.SELECT,
                      disabled: ({ creditCardBank }) => !!creditCardBank,
                      options: [
                        { value: null, label: 'Any' },
                        { value: 'GCT', label: 'GCT' },
                        { value: 'OCC', label: 'Online Credit Card' },
                      ],
                    },
                    {
                      label: 'Payment Date',
                      name: 'createdAt',
                      type: FIELD_TYPES.DATE_RANGE,
                    },
                  ]}
                />
                <div style={{ display: 'flex' }}>
                  {reportPermissions.Wireless.export && (
                    <ExportButton
                      style={{ marginRight: '0px' }}
                      icon="file-csv"
                      iconPosition="left"
                      disabled={loading}
                      onClick={async () => {
                        const notifTime = new Date().getTime();
                        addNotif({
                          id: 'RALINE-' + notifTime,
                          notifTime,
                          type: 'info',
                          title: 'Downloading Report File',
                          message:
                            'Downloading Revenue Accounting Wireless Report',
                          isProgress: true,
                          progressData: {
                            progress: 0,
                            isProgressive: false,
                          },
                          isLocal: true,
                          data: {},
                          reportDLParams: {
                            query: GET_WIRELESS_REVENUE_INFO,
                            path: REPORT_PATH,
                            variables: {
                              filter,
                              pagination: {
                                startKeys: '',
                                limit: 1000,
                              },
                            },
                            onDownload: () => {
                              logExtraction({
                                variables: {
                                  data: {
                                    type: 'rawireless',
                                  },
                                },
                              });
                            },
                            tableConfig,
                            fileName: `rawireless_report_${format(new Date(), 'YYYY_MM_DD')}.csv`,
                          },
                        });
                      }}
                    >
                      CSV
                    </ExportButton>
                  )}
                </div>
              </>
            }
            config={tableConfig}
            pagination={{
              ...pagination,
              start: pagination.startKeys,
              withStartKeys: true,
              count: 0,
              cursors: [],
              handleChange: setNewPagination,
            }}
            series={{ page, setPage, isLastPage }}
          />
        </DataContainer>
      </Page>
    </>
  );
};

RevenueAccountingWireless.propTypes = {};

export default RevenueAccountingWireless;
