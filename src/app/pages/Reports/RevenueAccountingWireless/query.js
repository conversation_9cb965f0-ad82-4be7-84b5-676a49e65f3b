import { gql } from '@apollo/client';

export const GET_WIRELESS_REVENUE_INFO = gql`
  query getWirelessRevenueInfo(
    $filter: SearchRevenueWirelessInput!
    $pagination: PaginationReportInput!
  ) {
    revenueWireless(filter: $filter, pagination: $pagination) {
      lastKey
      filteredData {
        reference
        accountNumber
        amountValue
        payModeCode
        payModeDesc
        createdAt
        creditCardBank
        paymentTypeCode
      }
    }
  }
`;

export const REPORT_PATH = 'revenueWireless';
