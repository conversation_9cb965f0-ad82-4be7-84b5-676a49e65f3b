import { gql } from '@apollo/client';

export const GET_SWIPETRANSACTION_REPORTS = gql`
  query getContentFraudReports(
    $filter: contentFraudReportFilterInput!
    $pagination: PaginationReportInput!
  ) {
    contentFraudReport(filter: $filter, pagination: $pagination) {
      lastKey
      filteredData {
        psReferenceNo
        gcashReferenceNo
        accountNo
        srn
        channelName
        channelId
        paymentMethod
        paymentStatus
        amount
        currency
        date
        msisdn
        productDescription
        emailAddress
        paymentGateway
        customerSegment
        customerSubType
        brand
        entity
        sku
        modeOfPayment
        subscriberType
        contentPartnerShortName
        voucherDispenseStatus
        refundStatus
        refundAmount
      }
    }
  }
`;

export const REPORT_PATH = 'contentFraudReport';
