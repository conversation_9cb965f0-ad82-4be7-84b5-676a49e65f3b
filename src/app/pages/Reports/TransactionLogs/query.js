import { gql } from '@apollo/client';

export const GET_CHANNEL_OPTIONS = gql`
  query getChannelOptions {
    channelsLoose {
      id
      name
      channelId
    }
  }
`;

export const GET_TRANSACTION_LOGS_INFO = gql`
  query getTransactionLogsInfo(
    $filter: SearchTransactionLogsFilter!
    $pagination: PaginationInput!
  ) {
    transactionLogs(filter: $filter, pagination: $pagination) {
      lastKey
      filteredData {
        paymentId
        createDateTime
        channelId
        customerId
        customerName
        gatewayProcessor
        paymentMethod
        sessionId
        status
        totalAmount
        updateDateTime
        convenienceFeeAmount
        oona
        budgetProtect
        settlementBreakdown {
          amount
          amountValue
          transactionType
        }
        settlement {
          accountType
          accountId
          amount
          amountValue
          brand
          email
          mobileNumber
          msisdn
          transactionType
          status
        }
        refundId
        refundApprovalStatus
        refundReason
        refundAmount
      }
    }
  }
`;

export const BULKUPLOAD = gql`
  query importBulkSearch($file: Upload!) {
    uploadTransactions(file: $file) {
      lastKey
      filteredData {
        reference
        accountNumber
        channelName
        emailAddress
        prodDesc
        paymentMethod
        mobileNumber
        status
        paymentType
        finalAmount
        convenienceFee
        refundId
        refundStatus
        refundAmount
        amountCurrency
        amountValue
        createdAt
        paymentGateway
        splitPayment
        billType
        fromBatchFile
        costCenter
        refusalReasonRaw
        postPaymentReason
        postPaymentEsbMessageId
      }
    }
  }
`;

export const REPORT_PATH = 'transactionLogs';
