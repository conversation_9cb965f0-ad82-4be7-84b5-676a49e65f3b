import React, { useContext, useState, useEffect } from 'react';
import DataContainer from '../../../../components/DataContainer';
import GlobalSearch from '../../../../components/GlobalSearch';
import Header from '../../../../components/Header';
import Page from '../../../../components/Page';
import { REPORT_TYPE, REPORT_ENTITIES } from '../TreasuryBill';
import {
  TreasuryHeader,
  TableViewDropdown,
  TreasuryTable,
  TreasuryReportTitle,
  REPORT_TYPE_LABEL,
} from '../YTD/YTD';
import { FIELD_TYPES } from '../../../../components/Form/constants';
import AuthContext from '../../../../context/AuthContext/AuthContext';
import { ExportButton } from '../../../../components/Button/ExportButton';
import {
  GET_TREASURY_BILL_MONTHLY_REPORTS,
  REPORT_PATH_MONTHLY,
} from '../query';
import useQueryFilterSeries from '../../../../hooks/useQueryFilterSeries';
import {
  numberWithCommas,
  json2CSVYTD,
} from '../../../../components/GlobalSearch/utils';
import { AlertModal } from '../../../../components/Modal';
import { EXPORT_REPORTS } from '../../mutation';
import { useMutation } from '@apollo/client';

const TABLE_CONFIG = {
  name: {
    headerLabel: 'Name',
    sortable: false,
  },
  accountType: {
    headerLabel: 'Account Type',
    sortable: false,
  },
  from: {
    headerLabel: 'Month, Year\n(From)',
    sortable: false,
    renderAs: data => `${data.startMonth}, ${data.startYear}`,
  },
  to: {
    headerLabel: 'Month, Year\n(To)',
    sortable: false,
    renderAs: data => `${data.endMonth}, ${data.endYear}`,
  },
  count: {
    headerLabel: 'Count',
    sortable: true,
    renderAs: data => numberWithCommas(data.count),
  },
  amount: {
    headerLabel: 'Amount',
    sortable: true,
    renderAs: data => numberWithCommas(data.amount, 2),
  },
};

const CARD_BRAND_TABLE_CONFIG = {
  name: {
    headerLabel: 'Name',
    sortable: false,
  },
  paymentGateway: {
    headerLabel: 'Payment Gateway',
    sortable: false,
  },
  accountType: {
    headerLabel: 'Account Type',
    sortable: false,
  },
  from: {
    headerLabel: 'Month, Year\n(From)',
    sortable: false,
    renderAs: data => `${data.startMonth}, ${data.startYear}`,
  },
  to: {
    headerLabel: 'Month, Year\n(To)',
    sortable: false,
    renderAs: data => `${data.endMonth}, ${data.endYear}`,
  },
  count: {
    headerLabel: 'Count',
    sortable: true,
    renderAs: data => numberWithCommas(data.count),
  },
  amount: {
    headerLabel: 'Amount',
    sortable: true,
    renderAs: data => numberWithCommas(data.amount, 2),
  },
};

const EWALLET_TABLE_CONFIG = {
  name: {
    headerLabel: 'Name',
    sortable: false,
  },
  paymentMethod: {
    headerLabel: 'Payment Method',
    sortable: false,
  },
  from: {
    headerLabel: 'Month, Year\n(From)',
    sortable: false,
    renderAs: data => `${data.startMonth}, ${data.startYear}`,
  },
  to: {
    headerLabel: 'Month, Year\n(To)',
    sortable: false,
    renderAs: data => `${data.endMonth}, ${data.endYear}`,
  },
  count: {
    headerLabel: 'Count',
    sortable: true,
    renderAs: data => numberWithCommas(data.count),
  },
  amount: {
    headerLabel: 'Amount',
    sortable: true,
    renderAs: data => numberWithCommas(data.amount, 2),
  },
};

const DIRECT_DEBIT_TABLE_CONFIG = {
  name: {
    headerLabel: 'Name',
    sortable: false,
  },
  paymentMethod: {
    headerLabel: 'Bank Account',
    sortable: false,
  },
  from: {
    headerLabel: 'Month, Year\n(From)',
    sortable: false,
    renderAs: data => `${data.startMonth}, ${data.startYear}`,
  },
  to: {
    headerLabel: 'Month, Year\n(To)',
    sortable: false,
    renderAs: data => `${data.endMonth}, ${data.endYear}`,
  },
  count: {
    headerLabel: 'Count',
    sortable: true,
    renderAs: data => numberWithCommas(data.count),
  },
  amount: {
    headerLabel: 'Amount',
    sortable: true,
    renderAs: data => numberWithCommas(data.amount, 2),
  },
};

const CARD_BRAND_CONFIG = {
  headerLabel: 'Card Brand',
  sortable: false,
};

const Monthly = () => {
  const { reportPermissions } = useContext(AuthContext);
  const [state, setState] = useState({
    filter: {
      month: {
        start: 'January',
        end: 'December',
      },
      year: {
        start: new Date().getFullYear(),
        end: new Date().getFullYear(),
      },
    },
    isConfirmDownloadModalOpen: false,
    isAlertMessageOpen: false,
    alertMsg: null,
  });

  const [logExtraction] = useMutation(EXPORT_REPORTS);

  const [tableView, setTableView] = useState(null);

  const [footers, setFooters] = useState({});
  const [cbFilteredData, setcbFilteredData] = useState([]);

  const { data: dcData, loading: dcLoading } = useQueryFilterSeries(
    GET_TREASURY_BILL_MONTHLY_REPORTS,
    REPORT_PATH_MONTHLY,
    {
      filter: {
        ...state.filter,
        accountType: tableView ? tableView : undefined,
      },
      reportType: REPORT_TYPE.CREDIT_DEBIT,
    }
  );

  const { data: cbData, loading: cbLoading } = useQueryFilterSeries(
    GET_TREASURY_BILL_MONTHLY_REPORTS,
    REPORT_PATH_MONTHLY,
    {
      filter: {
        ...state.filter,
        accountType: tableView ? tableView : undefined,
      },
      reportType: REPORT_TYPE.CARD_BRAND,
    }
  );

  const { data: gcData, loading: gcLoading } = useQueryFilterSeries(
    GET_TREASURY_BILL_MONTHLY_REPORTS,
    REPORT_PATH_MONTHLY,
    {
      filter: {
        ...state.filter,
        accountType: tableView ? tableView : undefined,
      },
      reportType: REPORT_TYPE.GCASH,
    }
  );

  const { data: eWalletData, loading: eWalletLoading } = useQueryFilterSeries(
    GET_TREASURY_BILL_MONTHLY_REPORTS,
    REPORT_PATH_MONTHLY,
    {
      filter: {
        ...state.filter,
        accountType: tableView ? tableView : undefined,
      },
      reportType: REPORT_TYPE.EWALLET,
    }
  );

  const { data: dDebitData, loading: dDebitLoading } = useQueryFilterSeries(
    GET_TREASURY_BILL_MONTHLY_REPORTS,
    REPORT_PATH_MONTHLY,
    {
      filter: {
        ...state.filter,
        accountType: tableView ? tableView : undefined,
      },
      reportType: REPORT_TYPE.BANKTRANSFER,
    }
  );

  useEffect(() => {
    if (dcData) {
      let footer = { name: 'Total' };
      let total = dcData.reduce(
        (total, currentData) => {
          return {
            amount: currentData.amount + total.amount,
            count: currentData.count + total.count,
          };
        },
        { amount: 0, count: 0 }
      );

      footer.amount = numberWithCommas(total.amount, 2);
      footer.count = numberWithCommas(total.count);

      setFooters({ ...footers, [REPORT_TYPE.CREDIT_DEBIT]: footer });
    }
  }, [JSON.stringify(dcData), dcLoading]);

  useEffect(() => {
    if (cbData) {
      let footer = { name: 'Total' };

      // REMOVE MC IN THE CARD BRAND DATA and UPDATE the null paymentGateway to Adyen
      let cbFiltered = cbData.filter(cb => {
        if (cb.paymentGateway === null) {
          cb.paymentGateway = 'adyen';
        }
        return cb.cardBrand !== 'MC';
      });
      setcbFilteredData(cbFiltered);

      let total = cbFiltered.reduce(
        (total, currentData) => {
          return {
            amount: currentData.amount + total.amount,
            count: currentData.count + total.count,
          };
        },
        { amount: 0, count: 0 }
      );

      footer.amount = numberWithCommas(total.amount, 2);
      footer.count = numberWithCommas(total.count);

      setFooters({ ...footers, [REPORT_TYPE.CARD_BRAND]: footer });
    }
  }, [JSON.stringify(cbData), cbLoading]);

  useEffect(() => {
    if (gcData) {
      let footer = { name: 'Total' };
      let total = gcData.reduce(
        (total, currentData) => {
          return {
            amount: currentData.amount + total.amount,
            count: currentData.count + total.count,
          };
        },
        { amount: 0, count: 0 }
      );

      footer.amount = numberWithCommas(total.amount, 2);
      footer.count = numberWithCommas(total.count);

      setFooters({ ...footers, [REPORT_TYPE.GCASH]: footer });
    }
  }, [JSON.stringify(gcData), gcLoading]);

  useEffect(() => {
    if (eWalletData) {
      let footer = { name: 'Total' };
      let total = eWalletData.reduce(
        (total, currentData) => {
          return {
            amount: currentData.amount + total.amount,
            count: currentData.count + total.count,
          };
        },
        { amount: 0, count: 0 }
      );

      footer.amount = numberWithCommas(total.amount, 2);
      footer.count = numberWithCommas(total.count);
      setFooters({ ...footers, [REPORT_TYPE.EWALLET]: footer });
    }
  }, [JSON.stringify(eWalletData), eWalletLoading]);

  useEffect(() => {
    if (dDebitData) {
      let footer = { name: 'Total' };
      let total = dDebitData.reduce(
        (total, currentData) => {
          return {
            amount: currentData.amount + total.amount,
            count: currentData.count + total.count,
          };
        },
        { amount: 0, count: 0 }
      );

      footer.amount = numberWithCommas(total.amount, 2);
      footer.count = numberWithCommas(total.count);
      setFooters({ ...footers, [REPORT_TYPE.BANKTRANSFER]: footer });
    }
  }, [JSON.stringify(dDebitData), dDebitLoading]);

  return (
    <>
      <Page>
        <Header
          withHome
          title="Monthly Reports"
          path={[
            'Reports',
            { label: 'Treasury Bills Reports', to: '/treasury-bill' },
            'Monthly Reports',
          ]}
        />
        <DataContainer>
          <TreasuryHeader>
            <GlobalSearch
              onSearch={filter => {
                setState({ ...state, filter });
              }}
              fields={[
                {
                  label: 'Month',
                  name: 'month',
                  type: FIELD_TYPES.MONTH_RANGE,
                },
                {
                  label: 'Year',
                  name: 'year',
                  type: FIELD_TYPES.YEAR_RANGE,
                },
              ]}
            />
            <div style={{ display: 'flex', alignItems: 'flex-end' }}>
              {reportPermissions.Treasury.export && (
                <ExportButton
                  style={{ marginRight: '0px' }}
                  icon="file-csv"
                  iconPosition="left"
                  disabled={false}
                  onClick={() =>
                    setState({ ...state, isConfirmDownloadModalOpen: true })
                  }
                >
                  CSV
                </ExportButton>
              )}
              <TableViewDropdown
                value={tableView}
                onChange={setTableView}
                options={REPORT_ENTITIES}
              />
            </div>
          </TreasuryHeader>

          <TreasuryReportTitle>{`${REPORT_TYPE_LABEL[REPORT_TYPE.CREDIT_DEBIT].label} Reports`}</TreasuryReportTitle>
          <TreasuryTable
            loading={dcLoading}
            minCellWidth={200}
            data={dcLoading ? [undefined] : dcData}
            config={TABLE_CONFIG}
            footer={dcLoading ? undefined : footers[REPORT_TYPE.CREDIT_DEBIT]}
          />

          <TreasuryReportTitle>{`${REPORT_TYPE_LABEL[REPORT_TYPE.CARD_BRAND].label} Reports`}</TreasuryReportTitle>
          <TreasuryTable
            loading={cbLoading}
            minCellWidth={200}
            data={cbLoading ? [undefined] : cbFilteredData}
            config={{
              ...CARD_BRAND_TABLE_CONFIG,
              cardBrand: CARD_BRAND_CONFIG,
            }}
            footer={cbLoading ? undefined : footers[REPORT_TYPE.CARD_BRAND]}
          />

          <TreasuryReportTitle>{`${REPORT_TYPE_LABEL[REPORT_TYPE.GCASH].label} Reports`}</TreasuryReportTitle>
          <TreasuryTable
            loading={gcLoading}
            minCellWidth={200}
            data={gcLoading ? [undefined] : gcData}
            config={TABLE_CONFIG}
            footer={gcLoading ? undefined : footers[REPORT_TYPE.GCASH]}
          />

          <TreasuryReportTitle>{`${REPORT_TYPE_LABEL[REPORT_TYPE.EWALLET].label} Reports`}</TreasuryReportTitle>
          <TreasuryTable
            loading={eWalletLoading}
            minCellWidth={200}
            data={eWalletLoading ? [undefined] : eWalletData}
            config={EWALLET_TABLE_CONFIG}
            footer={eWalletLoading ? undefined : footers[REPORT_TYPE.EWALLET]}
          />

          <TreasuryReportTitle>{`${REPORT_TYPE_LABEL[REPORT_TYPE.BANKTRANSFER].label} Reports`}</TreasuryReportTitle>
          <TreasuryTable
            loading={dDebitLoading}
            minCellWidth={200}
            data={dDebitLoading ? [undefined] : dDebitData}
            config={DIRECT_DEBIT_TABLE_CONFIG}
            footer={
              dDebitLoading ? undefined : footers[REPORT_TYPE.BANKTRANSFER]
            }
          />
        </DataContainer>
      </Page>

      <AlertModal
        isOpen={state.isConfirmDownloadModalOpen}
        title="Export Reports Alert"
        header="ARE YOU SURE?"
        variant="warn"
        icon="exclamation-circle"
        subHeader={
          <span>You are about to export treasury billing as .CSV File.</span>
        }
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() =>
          setState({ ...state, isConfirmDownloadModalOpen: false })
        }
        handleCancel={() =>
          setState({ ...state, isConfirmDownloadModalOpen: false })
        }
        confirmText="Yes"
        handleConfirm={async () => {
          const reportEntity =
            tableView === null
              ? 'Consolidated'
              : REPORT_ENTITIES.find(entity => entity.value === tableView)
                  .label;
          logExtraction({
            variables: {
              data: {
                type: 'treasury',
              },
            },
          });

          const dcCSV = await json2CSVYTD(
            [...dcData, footers[REPORT_TYPE.CREDIT_DEBIT]],
            TABLE_CONFIG
          );
          const cbCSV = await json2CSVYTD(
            [...cbFilteredData, footers[REPORT_TYPE.CARD_BRAND]],
            {
              ...CARD_BRAND_TABLE_CONFIG,
              cardBrand: CARD_BRAND_CONFIG,
            }
          );
          const gcCSV = await json2CSVYTD(
            [...gcData, footers[REPORT_TYPE.GCASH]],
            TABLE_CONFIG
          );
          const ewCSV = await json2CSVYTD(
            [...eWalletData, footers[REPORT_TYPE.EWALLET]],
            EWALLET_TABLE_CONFIG
          );
          const btCSV = await json2CSVYTD(
            [...dDebitData, footers[REPORT_TYPE.BANKTRANSFER]],
            DIRECT_DEBIT_TABLE_CONFIG
          );

          let csvData = `,\n${REPORT_TYPE_LABEL[REPORT_TYPE.CREDIT_DEBIT].label} Reports\n,\n`;
          csvData += `${dcCSV}\n,\n`;
          csvData += `,\n${REPORT_TYPE_LABEL[REPORT_TYPE.CARD_BRAND].label} Reports\n,\n`;
          csvData += `${cbCSV}\n,\n`;
          csvData += `,\n${REPORT_TYPE_LABEL[REPORT_TYPE.GCASH].label} Reports\n,\n`;
          csvData += `${gcCSV}\n`;
          csvData += `,\n${REPORT_TYPE_LABEL[REPORT_TYPE.EWALLET].label} Reports\n,\n`;
          csvData += `${ewCSV}\n`;
          csvData += `,\n${REPORT_TYPE_LABEL[REPORT_TYPE.BANKTRANSFER].label} Reports\n,\n`;
          csvData += `${btCSV}\n`;

          const fileData = {
            mime: 'text/csv',
            filename: `Treasury Bill Monthly ${reportEntity}.csv`,
            contents: csvData,
          };
          const blob = new Blob([fileData.contents], {
            type: fileData.mime,
          });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          document.body.appendChild(a);
          a.download = fileData.filename;
          a.href = url;
          a.click();
          document.body.removeChild(a);
          setState({
            ...state,
            isConfirmDownloadModalOpen: false,
            isAlertMessageOpen: true,
            alertMsg: {
              header: 'SUCCESS',
              variant: 'success',
              icon: 'check-circle',
              subHeader:
                'You exported the treasury billing Monthly report as .CSV File successfully.',
              description: 'Kindly check the downloaded file.',
              confirmText: 'Back to Monthly Report.',
            },
          });
        }}
      />
      <AlertModal
        isOpen={state.isAlertMessageOpen}
        title="Export Reports Alert"
        header={state.alertMsg && state.alertMsg.header}
        variant={state.alertMsg && state.alertMsg.variant}
        icon={state.alertMsg && state.alertMsg.icon}
        subHeader={<span>{state.alertMsg && state.alertMsg.subHeader}</span>}
        description={state.alertMsg ? state.alertMsg.description : ''}
        confirmText={state.alertMsg ? state.alertMsg.confirmText : ''}
        handleClose={() =>
          setState({
            ...state,
            isAlertMessageOpen: false,
            alertMessage: null,
          })
        }
        handleConfirm={() =>
          setState({
            ...state,
            isAlertMessageOpen: false,
            alertMessage: null,
          })
        }
      />
    </>
  );
};

export default Monthly;
