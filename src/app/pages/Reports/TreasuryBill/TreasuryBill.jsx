import styled from 'styled-components';
import React from 'react';
import DataContainer from '../../../components/DataContainer';
import Header from '../../../components/Header/Header';
import Page from '../../../components/Page';
import Row from '../../../components/Row';
import SecondaryButton from '../../../components/Button/SecondaryButton';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import PropTypes from 'prop-types';

const TreasuryBillContainer = styled.div`
  border: 1px solid rgba(165, 165, 165, 0.5);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  margin-right: 20px;
  margin-top: 20px;
  min-width: 260px;

  ${SecondaryButton} {
    margin-top: 10px;
    padding: 0px;
    align-self: flex-end;

    &:hover {
      background: white;
    }

    &:active {
      outline: none;
    }
  }
`;

const TreasuryBillCol = styled.div`
  display: inline-flex;
  align-items: center;
`;

const TreasuryBillTitle = styled.h1`
  margin: 0;
  margin-left: 10px;
  margin-top: 10px;
  font-size: ${props => props.theme.fontSize.m};
`;

export const REPORT_TYPE = {
  CREDIT_DEBIT: 'payloadCreditDebit',
  CARD_BRAND: 'payloadCardBrand',
  GCASH: 'payloadGcash',
  EWALLET: 'payloadXenditEwallet',
  BANKTRANSFER: 'payloadXenditDirectDebit',
};

export const REPORT_ENTITIES = [
  { value: null, label: 'All Entities' },
  { value: 'G', label: 'Globe' },
  { value: 'I', label: 'Innove' },
  { value: 'B', label: 'Bayan' },
];

export const TreasuryBillItem = ({ rows, history }) => {
  return (
    <TreasuryBillContainer>
      <TreasuryBillCol>
        <FontAwesomeIcon icon="file-alt" color="#009bdd" size="2x" />
        <TreasuryBillTitle>{rows.title}</TreasuryBillTitle>
      </TreasuryBillCol>
      <TreasuryBillCol>
        <SecondaryButton
          onClick={() => {
            history.push(rows.history);
          }}
        >
          View
        </SecondaryButton>
      </TreasuryBillCol>
    </TreasuryBillContainer>
  );
};

TreasuryBillItem.propTypes = {
  title: PropTypes.string,
  rows: PropTypes.object,
  history: PropTypes.object,
};

const TreasuryBill = ({ history }) => {
  const rows = [
    {
      title: 'YTD',
      history: '/treasury-bill/ytd',
    },
    {
      title: 'Monthly',
      history: '/treasury-bill/monthly',
    },
  ];

  return (
    <>
      <Page>
        <Header
          withHome
          title="Treasury Bills Reports"
          path={['Reports', 'Treasury Bills Reports']}
        />
        <DataContainer>
          <Row>
            {rows.map((row, i) => (
              <TreasuryBillItem key={i} rows={row} history={history} />
            ))}
          </Row>
        </DataContainer>
      </Page>
    </>
  );
};

TreasuryBill.propTypes = {
  history: PropTypes.object,
};

export default TreasuryBill;
