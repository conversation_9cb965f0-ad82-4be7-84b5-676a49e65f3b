import React, { useContext, useEffect, useState } from 'react';
import styled from 'styled-components';
import { ExportButton } from '../../../../components/Button/ExportButton';
import DataContainer from '../../../../components/DataContainer';
import DataHeader from '../../../../components/DataHeader';
import DataTable from '../../../../components/DataTable';
import Dropdown from '../../../../components/Dropdown';
import {
  DropdownButton,
  DropdownMenu,
} from '../../../../components/Dropdown/Dropdown';
import { FIELD_TYPES } from '../../../../components/Form/constants';
import GlobalSearch from '../../../../components/GlobalSearch';
import {
  getMonthNumber,
  json2CSVYTD,
  MONTHS,
  numberWithCommas,
} from '../../../../components/GlobalSearch/utils';
import Header from '../../../../components/Header';
import { AlertModal } from '../../../../components/Modal';
import Page from '../../../../components/Page';
import AuthContext from '../../../../context/AuthContext/AuthContext';
import { useMutation } from '@apollo/client';
import useQueryFilterSeries from '../../../../hooks/useQueryFilterSeries';
import { EXPORT_REPORTS } from '../../mutation';
import {
  EXT_DATA,
  GET_TREASURY_BILL_YTD_REPORTS,
  REPORT_PATH_YTD,
} from '../query';
import { REPORT_ENTITIES, REPORT_TYPE } from '../TreasuryBill';
import { isCreditOrDebit, isValidFromKeys } from '../../../../utils/helpers';

export const TableViewDropdown = styled(Dropdown)`
  width: 200px;
  margin: 0 10px;
  ${DropdownButton} {
    border: 0;
    border-bottom: 1px solid #979797;
  }

  ${DropdownMenu} {
    border: 1px solid #979797;
  }
`;

export const TreasuryHeader = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30px;
  display: flex;
  flex-wrap: wrap;
  width: 100%;
`;

export const TreasuryReportTitle = styled(DataHeader.Title)`
  margin-bottom: 30px;
`;

export const TreasuryTable = styled(DataTable)`
  margin-left: 20px;
  margin-bottom: 20px;
`;

const REPORT_NAME = [
  { value: 'count', label: 'Transaction Count' },
  { value: 'amount', label: 'Transaction Amount' },
];

export const REPORT_TYPE_LABEL = {
  [REPORT_TYPE.CREDIT_DEBIT]: {
    DEBIT: 'EPS/Debit Card',
    CREDIT: 'Credit Card - Straight',
    Others: 'Others',
    label: 'Debit and Credit Card',
  },
  [REPORT_TYPE.CARD_BRAND]: {
    amex: {},
    jcb: {},
    mc: {},
    visa: {},
    cc: {},
    others: {},
    //Last three values are xendit transactions
    cc_dc: {},
    VISA: {},
    MC: {},
    MASTERCARD: {},
    label: 'Card Brand',
  },
  [REPORT_TYPE.GCASH]: {
    BILL_PAYMENT: 'Bill Payment Transactions',
    label: 'GCash',
  },
  [REPORT_TYPE.EWALLET]: {
    label: 'E-Wallet',
    'Shopee Pay': {},
    Grabpay: {},
    Maya: {},
    Others: {},
    BILL_PAYMENT: 'Bill Payment Transactions',
  },
  [REPORT_TYPE.BANKTRANSFER]: {
    label: 'Direct Debit',
    BPI: {},
    Unionbank: {},
    RCBC: {},
    Others: {},
  },
};

function getFilteredTableConfig(tableConfig) {
  return Object.keys(tableConfig).reduce((config, current) => {
    if (current !== 'name') {
      return {
        ...config,
        [current]: tableConfig[current],
      };
    }
    return config;
  }, {});
}

function computeYTD(data, filterMonth) {
  return MONTHS.reduce((ytd, month) => {
    if (getMonthNumber(month.value) <= getMonthNumber(filterMonth)) {
      ytd += data[month.value] ? data[month.value] : 0;
    }
    return ytd;
  }, 0);
}

const YTD = () => {
  const { reportPermissions } = useContext(AuthContext);
  const [state, setState] = useState({
    filter: {
      ytd: {
        month:
          new Date().getMonth() > 0
            ? MONTHS[new Date().getMonth() - 1].value
            : '',
        year: new Date().getFullYear(),
      },
    },
    isConfirmDownloadModalOpen: false,
    isAlertMessageOpen: false,
    alertMsg: null,
  });

  const [dataCD, setDataCD] = useState([]);
  const [dataCB, setDataCB] = useState([]);
  const [dataGC, setDataGC] = useState([]);
  const [dataEW, setDataEW] = useState([]);
  const [dataDD, setDataDD] = useState([]);

  const [tableView, setTableView] = useState(null);

  const [logExtraction] = useMutation(EXPORT_REPORTS);

  const {
    data: cdData,
    loading: cdLoading,
    extData: cdPrev,
  } = useQueryFilterSeries(
    GET_TREASURY_BILL_YTD_REPORTS,
    REPORT_PATH_YTD,
    {
      filter: {
        month: {
          start: MONTHS[0].value,
          end: state.filter.ytd.month || MONTHS[0].value,
        },
        year: state.filter.ytd.year,
        accountType: tableView ? tableView : undefined,
      },
      reportType: REPORT_TYPE.CREDIT_DEBIT,
    },
    EXT_DATA
  );

  const {
    data: cbData,
    loading: cbLoading,
    extData: cbPrev,
  } = useQueryFilterSeries(
    GET_TREASURY_BILL_YTD_REPORTS,
    REPORT_PATH_YTD,
    {
      filter: {
        month: {
          start: MONTHS[0].value,
          end: state.filter.ytd.month || MONTHS[0].value,
        },
        year: state.filter.ytd.year,
        accountType: tableView ? tableView : undefined,
      },
      reportType: REPORT_TYPE.CARD_BRAND,
    },
    EXT_DATA
  );

  const {
    data: gcData,
    loading: gcLoading,
    extData: gcPrev,
  } = useQueryFilterSeries(
    GET_TREASURY_BILL_YTD_REPORTS,
    REPORT_PATH_YTD,
    {
      filter: {
        month: {
          start: MONTHS[0].value,
          end: state.filter.ytd.month || MONTHS[0].value,
        },
        year: state.filter.ytd.year,
        accountType: tableView ? tableView : undefined,
      },
      reportType: REPORT_TYPE.GCASH,
    },
    EXT_DATA
  );

  const {
    data: ewData,
    loading: ewLoading,
    extData: ewPrev,
  } = useQueryFilterSeries(
    GET_TREASURY_BILL_YTD_REPORTS,
    REPORT_PATH_YTD,
    {
      filter: {
        month: {
          start: MONTHS[0].value,
          end: state.filter.ytd.month || MONTHS[0].value,
        },
        year: state.filter.ytd.year,
        accountType: tableView ? tableView : undefined,
      },
      reportType: REPORT_TYPE.EWALLET,
    },
    EXT_DATA
  );

  const {
    data: ddData,
    loading: ddLoading,
    extData: ddPrev,
  } = useQueryFilterSeries(
    GET_TREASURY_BILL_YTD_REPORTS,
    REPORT_PATH_YTD,
    {
      filter: {
        month: {
          start: MONTHS[0].value,
          end: state.filter.ytd.month || MONTHS[0].value,
        },
        year: state.filter.ytd.year,
        accountType: tableView ? tableView : undefined,
      },
      reportType: REPORT_TYPE.BANKTRANSFER,
    },
    EXT_DATA
  );

  const [tableConfig, setTableConfig] = useState({});

  useEffect(() => {
    setTableConfig({
      name: {
        headerLabel: 'Report',
        sortable: false,
        renderAs: data =>
          data ? REPORT_NAME.find(name => name.value === data.name).label : '',
      },
      ...MONTHS.reduce((months, current) => {
        if (
          getMonthNumber(current.value) <=
          getMonthNumber(state.filter.ytd.month)
        ) {
          months[current.value] = {
            headerLabel: current.label,
            sortable: false,
            renderAs: data => {
              if (data) {
                let renderValue = data[current.value] || 0;
                return data.name === 'amount'
                  ? numberWithCommas(renderValue, 2)
                  : numberWithCommas(renderValue);
              }
              return '';
            },
          };
        }
        return months;
      }, {}),
      ytd: {
        headerLabel: `YTD
        As of ${state.filter.ytd.month} ${state.filter.ytd.year}`,
        sortable: false,
        renderAs: data => {
          if (data) {
            return numberWithCommas(
              computeYTD(data, state.filter.ytd.month),
              data.name === 'amount' ? 2 : 0
            );
          }
          return '';
        },
      },
      prev1: {
        headerLabel: `Previous Year (${state.filter.ytd.year - 1})`,
        sortable: false,
        renderAs: data => {
          if (data) {
            let renderValue = data.prev1 || 0;
            const fractionDigits = data.name === 'amount' ? 2 : 0;
            return numberWithCommas(renderValue, fractionDigits);
          }
          return '';
        },
      },
      prev2: {
        headerLabel: `Previous Year (${state.filter.ytd.year - 2})`,
        sortable: false,
        renderAs: data => {
          if (data) {
            let renderValue = data.prev2 || 0;
            const fractionDigits = data.name === 'amount' ? 2 : 0;
            return numberWithCommas(renderValue, fractionDigits);
          }
          return '';
        },
      },
      growth1: {
        headerLabel: `${state.filter.ytd.year} vs ${state.filter.ytd.year - 1} 
        %
        (Increase / Decrease)`,
        sortable: false,
        renderAs: data => {
          if (data) {
            return numberWithCommas(
              data.prev1 > 0
                ? ((computeYTD(data, state.filter.ytd.month) - data.prev1) /
                    data.prev1) *
                    100
                : 0,
              2
            );
          }
          return '';
        },
      },
      growth2: {
        headerLabel: `${state.filter.ytd.year} vs ${state.filter.ytd.year - 2} 
        %
        (Increase/Decrease)`,
        sortable: false,
        renderAs: data => {
          if (data) {
            return numberWithCommas(
              data.prev2 > 0
                ? ((computeYTD(data, state.filter.ytd.month) - data.prev2) /
                    data.prev2) *
                    100
                : 0,
              2
            );
          }
          return '';
        },
      },
    });
  }, [state.filter.ytd]);

  useEffect(() => {
    if (state.filter.ytd.month) {
      let creditDebit = cdData.reduce(
        (finalValue, cdValue) => {
          const cdCardType = isValidFromKeys(cdValue.cardType, finalValue)
            ? cdValue.cardType
            : 'Others';
          return {
            ...finalValue,
            [cdCardType]: [
              {
                ...finalValue[cdCardType][0],
                [cdValue.month]:
                  cdValue.amount +
                  (finalValue[cdCardType][0][cdValue.month] || 0),
              },
              {
                ...finalValue[cdCardType][1],
                [cdValue.month]:
                  cdValue.count +
                  (finalValue[cdCardType][1][cdValue.month] || 0),
              },
            ],
          };
        },
        {
          DEBIT: [
            {
              name: 'amount',
              prev1: 0,
              prev2: 0,
            },
            {
              name: 'count',
              prev1: 0,
              prev2: 0,
            },
          ],
          CREDIT: [
            {
              name: 'amount',
              prev1: 0,
              prev2: 0,
            },
            {
              name: 'count',
              prev1: 0,
              prev2: 0,
            },
          ],
          Others: [
            {
              name: 'amount',
              prev1: 0,
              prev2: 0,
            },
            {
              name: 'count',
              prev1: 0,
              prev2: 0,
            },
          ],
        }
      );

      if (cdPrev) {
        creditDebit = cdPrev.reduce((finalValue, cdValue) => {
          let cdType = cdValue.name === 'EPS/Debit Card' ? 'DEBIT' : 'CREDIT';
          let key =
            state.filter.ytd.year - 1 === parseInt(cdValue.year)
              ? 'prev1'
              : 'prev2';

          return {
            ...finalValue,
            [cdType]: [
              {
                ...finalValue[cdType][0],
                [key]: cdValue.amount + (finalValue[cdType][0][key] || 0),
              },
              {
                ...finalValue[cdType][1],
                [key]: cdValue.count + (finalValue[cdType][1][key] || 0),
              },
            ],
          };
        }, creditDebit);
      }

      setDataCD(creditDebit);
    }
  }, [JSON.stringify(cdData), JSON.stringify(cdPrev), cdLoading]);

  useEffect(() => {
    if (state.filter.ytd.month) {
      let cardBrand = cbData.reduce(
        (finalValue, cbValue) => {
          
          const cbCardType = isCreditOrDebit(cbValue.cardType)
            ? cbValue.cardType
            : 'OTHERS';
          const cbCardBrand = isValidFromKeys(cbValue.cardBrand, finalValue)
            ? cbValue.cardBrand
            : 'Others';
          return {
            ...finalValue,
            [cbCardBrand]: {
              ...finalValue[cbCardBrand],
              [cbCardType]: [
                {
                  ...finalValue[cbCardBrand][cbCardType][0],
                  [cbValue.month]:
                    cbValue.amount +
                    (finalValue[cbCardBrand][cbCardType][0][cbValue.month] ||
                      0),
                },
                {
                  ...finalValue[cbCardBrand][cbCardType][1],
                  [cbValue.month]:
                    cbValue.count +
                    (finalValue[cbCardBrand][cbCardType][1][cbValue.month] ||
                      0),
                },
              ],
              CONSOLIDATED: [
                {
                  ...finalValue[cbCardBrand].CONSOLIDATED[0],
                  [cbValue.month]:
                    cbValue.amount +
                    (finalValue[cbCardBrand].CONSOLIDATED[0][cbValue.month] ||
                      0),
                },
                {
                  ...finalValue[cbCardBrand].CONSOLIDATED[1],
                  [cbValue.month]:
                    cbValue.count +
                    (finalValue[cbCardBrand].CONSOLIDATED[1][cbValue.month] ||
                      0),
                },
              ],
            },
          };
        },
        {
          //Added cardbrands not yet in the  coming from
          amex: {
            DEBIT: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
            CREDIT: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
            OTHERS: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
            CONSOLIDATED: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
          },
          jcb: {
            DEBIT: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
            CREDIT: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
            OTHERS: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
            CONSOLIDATED: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
          },
          visa: {
            DEBIT: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
            CREDIT: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
            OTHERS: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
            CONSOLIDATED: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
          },
          mc: {
            DEBIT: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
            CREDIT: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
            OTHERS: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
            CONSOLIDATED: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
          },
          cc_dc: {
            DEBIT: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
            CREDIT: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
            OTHERS: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
            CONSOLIDATED: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
          },
          VISA: {
            DEBIT: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
            CREDIT: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
            OTHERS: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
            CONSOLIDATED: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
          },
          MC: {
            DEBIT: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
            CREDIT: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
            OTHERS: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
            CONSOLIDATED: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
          },
          MASTERCARD: {
            DEBIT: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
            CREDIT: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
            OTHERS: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
            CONSOLIDATED: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
          },
          cc: {
            DEBIT: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
            CREDIT: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
            OTHERS: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
            CONSOLIDATED: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
          },
          Others: {
            DEBIT: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
            CREDIT: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
            OTHERS: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
            CONSOLIDATED: [
              {
                name: 'amount',
                prev1: 0,
                prev2: 0,
              },
              {
                name: 'count',
                prev1: 0,
                prev2: 0,
              },
            ],
          },
        }
      );

      if (cbPrev) {
        cardBrand = cbPrev.reduce((finalValue, cbValue) => {
          const cbPrevCardBrand = isValidFromKeys(cbValue.cardBrand, finalValue)
            ? cbValue.cardBrand
            : 'Others';
          const cbPrevCardType = isCreditOrDebit(cbValue.cardType)
            ? cbValue.cardType
            : 'OTHERS';

          let key =
            state.filter.ytd.year - 1 === parseInt(cbValue.year)
              ? 'prev1'
              : 'prev2';

          return {
            ...finalValue,
            [cbPrevCardBrand]: {
              ...finalValue[cbPrevCardBrand],
              [cbPrevCardType]: [
                {
                  ...finalValue[cbPrevCardBrand][cbPrevCardType][0],
                  [key]:
                    cbValue.amount +
                    (finalValue[cbPrevCardBrand][cbPrevCardType][0][key] || 0),
                },
                {
                  ...finalValue[cbPrevCardBrand][cbPrevCardType][1],
                  [key]:
                    cbValue.count +
                    (finalValue[cbPrevCardBrand][cbPrevCardType][1][key] || 0),
                },
              ],
              CONSOLIDATED: [
                {
                  ...finalValue[cbPrevCardBrand].CONSOLIDATED[0],
                  [key]:
                    cbValue.amount +
                    (finalValue[cbPrevCardBrand].CONSOLIDATED[0][key] || 0),
                },
                {
                  ...finalValue[cbPrevCardBrand].CONSOLIDATED[1],
                  [key]:
                    cbValue.count +
                    (finalValue[cbPrevCardBrand].CONSOLIDATED[1][key] || 0),
                },
              ],
            },
          };
        }, cardBrand);
      }

      setDataCB(cardBrand);
    }
  }, [JSON.stringify(cbData), JSON.stringify(cbPrev), cbLoading]);

  useEffect(() => {
    if (state.filter.ytd.month) {
      let gcash = gcData.reduce(
        (finalValue, gcValue) => {
          return [
            {
              ...finalValue[0],
              [gcValue.month]:
                gcValue.amount + (finalValue[0][gcValue.month] || 0),
            },
            {
              ...finalValue[1],
              [gcValue.month]:
                gcValue.count + (finalValue[1][gcValue.month] || 0),
            },
          ];
        },
        [
          {
            name: 'amount',
            prev1: 0,
            prev2: 0,
          },
          {
            name: 'count',
            prev1: 0,
            prev2: 0,
          },
        ]
      );

      if (gcPrev) {
        gcash = gcPrev.reduce((finalValue, gcValue) => {
          let key =
            state.filter.ytd.year - 1 === parseInt(gcValue.year)
              ? 'prev1'
              : 'prev2';
          return [
            {
              ...finalValue[0],
              [key]: gcValue.amount + (finalValue[0][key] || 0),
            },
            {
              ...finalValue[1],
              [key]: gcValue.count + (finalValue[1][key] || 0),
            },
          ];
        }, gcash);
      }

      setDataGC({ gcash });
    }
  }, [JSON.stringify(gcData), JSON.stringify(gcPrev), gcLoading]);

  useEffect(() => {
    if (state.filter.ytd.month) {
      let ewallet = ewData.reduce(
        (finalValue, ewValue) => {
          return {
            ...finalValue,
            [ewValue.paymentMethod]: [
              {
                ...finalValue[ewValue.paymentMethod][0],
                [ewValue.month]: ewValue.amount,
              },
              {
                ...finalValue[ewValue.paymentMethod][1],
                [ewValue.month]: ewValue.count,
              },
            ],
          };
        },
        {
          'Shopee Pay': [
            {
              name: 'amount',
              prev1: 0,
              prev2: 0,
            },
            {
              name: 'count',
              prev1: 0,
              prev2: 0,
            },
          ],
          Grabpay: [
            {
              name: 'amount',
              prev1: 0,
              prev2: 0,
            },
            {
              name: 'count',
              prev1: 0,
              prev2: 0,
            },
          ],
          Maya: [
            {
              name: 'amount',
              prev1: 0,
              prev2: 0,
            },
            {
              name: 'count',
              prev1: 0,
              prev2: 0,
            },
          ],
          Others: [
            {
              name: 'amount',
              prev1: 0,
              prev2: 0,
            },
            {
              name: 'count',
              prev1: 0,
              prev2: 0,
            },
          ],
        }
      );

      if (ewPrev) {
        ewallet = ewPrev.reduce((finalValue, ewValue) => {
          let key =
            state.filter.ytd.year - 1 === parseInt(ewValue.year)
              ? 'prev1'
              : 'prev2';

          const paymentMethod = isValidFromKeys(
            ewValue.paymentMethod,
            finalValue
          )
            ? ewValue.paymentMethod
            : 'Others';

          return {
            ...finalValue,
            [paymentMethod]: [
              {
                ...finalValue[paymentMethod][0],
                [key]:
                  ewValue.amount + (finalValue[paymentMethod][0][key] || 0),
              },
              {
                ...finalValue[paymentMethod][0],
                [key]: ewValue.count + (finalValue[paymentMethod][1][key] || 0),
              },
            ],
          };
        }, ewallet);
      }
      setDataEW({ ewallet });
    }
  }, [JSON.stringify(ewData), JSON.stringify(ewPrev), ewLoading]);

  useEffect(() => {
    if (state.filter.ytd.month) {
      let dd = ddData.reduce(
        (finalValue, ddValue) => {
          return {
            ...finalValue,
            [ddValue.paymentMethod]: [
              {
                ...finalValue[ddValue.paymentMethod][0],
                [ddValue.month]: ddValue.amount,
              },
              {
                ...finalValue[ddValue.paymentMethod][1],
                [ddValue.month]: ddValue.count,
              },
            ],
          };
        },
        {
          BPI: [
            {
              name: 'amount',
              prev1: 0,
              prev2: 0,
            },
            {
              name: 'count',
              prev1: 0,
              prev2: 0,
            },
          ],
          Unionbank: [
            {
              name: 'amount',
              prev1: 0,
              prev2: 0,
            },
            {
              name: 'count',
              prev1: 0,
              prev2: 0,
            },
          ],
          RCBC: [
            {
              name: 'amount',
              prev1: 0,
              prev2: 0,
            },
            {
              name: 'count',
              prev1: 0,
              prev2: 0,
            },
          ],
          Others: [
            {
              name: 'amount',
              prev1: 0,
              prev2: 0,
            },
            {
              name: 'count',
              prev1: 0,
              prev2: 0,
            },
          ],
        }
      );

      if (ddPrev) {
        dd = ddPrev.reduce((finalValue, ddValue) => {
          const paymentMethod = isValidFromKeys(
            ddValue.paymentMethod,
            finalValue
          )
            ? ddValue.paymentMethod
            : 'Others';

          let key =
            state.filter.ytd.year - 1 === parseInt(ddValue.year)
              ? 'prev1'
              : 'prev2';

          return {
            ...finalValue,
            [paymentMethod]: [
              {
                ...finalValue[paymentMethod][0],
                [key]:
                  ddValue.amount + (finalValue[paymentMethod][0][key] || 0),
              },
              {
                ...finalValue[paymentMethod][1],
                [key]: ddValue.count + (finalValue[paymentMethod][1][key] || 0),
              },
            ],
          };
        }, dd);
      }
      setDataDD({ dd });
    }
  }, [JSON.stringify(ddData), JSON.stringify(ddPrev), ddLoading]);

  return (
    <>
      <Page>
        <Header
          withHome
          title="YTD Reports"
          path={[
            'Reports',
            { label: 'Treasury Bills Reports', to: '/treasury-bill' },
            'YTD Reports',
          ]}
        />
        <DataContainer>
          <TreasuryHeader>
            <GlobalSearch
              onSearch={filter => {
                if (!filter.ytd) {
                  filter = {
                    ytd: {
                      month:
                        new Date().getMonth() > 0
                          ? MONTHS[new Date().getMonth() - 1].value
                          : '',
                      year: new Date().getFullYear(),
                    },
                  };
                }
                setState({ ...state, filter });
              }}
              fields={[
                {
                  label: 'Month - Year',
                  name: 'ytd',
                  type: FIELD_TYPES.YTD,
                  initialValue: state.filter.ytd,
                },
              ]}
            />
            <div style={{ display: 'flex', alignItems: 'flex-end' }}>
              {reportPermissions.Treasury.export && (
                <ExportButton
                  style={{ marginRight: '0px' }}
                  icon="file-csv"
                  iconPosition="left"
                  disabled={false}
                  onClick={() => {
                    if (!state.filter.ytd.month) {
                      setState({
                        ...state,
                        isAlertMessageOpen: true,
                        alertMsg: {
                          header: 'ERROR',
                          variant: 'error',
                          icon: 'times-circle',
                          subHeader:
                            'There is no data for YTD in the month of January',
                          description: 'Kindly choose a different month',
                          confirmText: 'Back to YTD report',
                        },
                      });
                    } else {
                      setState({
                        ...state,
                        isConfirmDownloadModalOpen: true,
                      });
                    }
                  }}
                >
                  CSV
                </ExportButton>
              )}
              <TableViewDropdown
                value={tableView}
                onChange={setTableView}
                options={REPORT_ENTITIES}
              />
            </div>
          </TreasuryHeader>
          {
            <>
              <TreasuryReportTitle>{`${
                REPORT_TYPE_LABEL[REPORT_TYPE.CREDIT_DEBIT].label
              } Reports`}</TreasuryReportTitle>
              <TreasuryTable
                loading={cdLoading}
                minCellWidth={200}
                data={cdLoading ? [undefined] : dataCD.DEBIT}
                config={{
                  name: {
                    ...tableConfig.name,
                    headerLabel:
                      REPORT_TYPE_LABEL[REPORT_TYPE.CREDIT_DEBIT].DEBIT,
                  },
                  ...getFilteredTableConfig(tableConfig),
                }}
              />
              <TreasuryTable
                loading={cdLoading}
                minCellWidth={200}
                data={cdLoading ? [undefined] : dataCD.CREDIT}
                config={{
                  name: {
                    ...tableConfig.name,
                    headerLabel:
                      REPORT_TYPE_LABEL[REPORT_TYPE.CREDIT_DEBIT].CREDIT,
                  },
                  ...getFilteredTableConfig(tableConfig),
                }}
              />
              <TreasuryTable
                loading={cdLoading}
                minCellWidth={200}
                data={cdLoading ? [undefined] : dataCD.Others}
                config={{
                  name: {
                    ...tableConfig.name,
                    headerLabel:
                      REPORT_TYPE_LABEL[REPORT_TYPE.CREDIT_DEBIT].Others,
                  },
                  ...getFilteredTableConfig(tableConfig),
                }}
              />

              <TreasuryReportTitle>{`${REPORT_TYPE_LABEL[REPORT_TYPE.CARD_BRAND].label} Reports`}</TreasuryReportTitle>
              <TreasuryTable
                loading={cbLoading}
                minCellWidth={200}
                data={cbLoading ? [undefined] : dataCB.cc_dc.CREDIT}
                config={{
                  name: {
                    ...tableConfig.name,
                    headerLabel: 'Online Credit Card - Xendit',
                  },
                  ...getFilteredTableConfig(tableConfig),
                }}
              />
              <TreasuryTable
                loading={cbLoading}
                minCellWidth={200}
                data={cbLoading ? [undefined] : dataCB.VISA.CREDIT}
                config={{
                  name: {
                    ...tableConfig.name,
                    headerLabel: 'Visa - Xendit',
                  },
                  ...getFilteredTableConfig(tableConfig),
                }}
              />
              <TreasuryTable
                loading={cbLoading}
                minCellWidth={200}
                data={cbLoading ? [undefined] : dataCB.MASTERCARD.CREDIT}
                config={{
                  name: {
                    ...tableConfig.name,
                    headerLabel: 'Mastercard - Xendit',
                  },
                  ...getFilteredTableConfig(tableConfig),
                }}
              />
              <TreasuryTable
                loading={cbLoading}
                minCellWidth={200}
                data={cbLoading ? [undefined] : dataCB.mc.DEBIT}
                config={{
                  name: {
                    ...tableConfig.name,
                    headerLabel: 'EPS/Debit Card - Mastercard',
                  },
                  ...getFilteredTableConfig(tableConfig),
                }}
              />
              <TreasuryTable
                loading={cbLoading}
                minCellWidth={200}
                data={cbLoading ? [undefined] : dataCB.visa.DEBIT}
                config={{
                  name: {
                    ...tableConfig.name,
                    headerLabel: 'EPS/Debit Card - Visa',
                  },
                  ...getFilteredTableConfig(tableConfig),
                }}
              />
              <TreasuryTable
                loading={cbLoading}
                minCellWidth={200}
                data={cbLoading ? [undefined] : dataCB.amex.DEBIT}
                config={{
                  name: {
                    ...tableConfig.name,
                    headerLabel: 'EPS/Debit Card - Amex',
                  },
                  ...getFilteredTableConfig(tableConfig),
                }}
              />
              <TreasuryTable
                loading={cbLoading}
                minCellWidth={200}
                data={cbLoading ? [undefined] : dataCB.jcb.DEBIT}
                config={{
                  name: {
                    ...tableConfig.name,
                    headerLabel: 'EPS/Debit Card - JCB',
                  },
                  ...getFilteredTableConfig(tableConfig),
                }}
              />
              <TreasuryTable
                loading={cbLoading}
                minCellWidth={200}
                data={cbLoading ? [undefined] : dataCB.mc.CREDIT}
                config={{
                  name: {
                    ...tableConfig.name,
                    headerLabel: 'Credit Card (Straight) - Mastercard',
                  },
                  ...getFilteredTableConfig(tableConfig),
                }}
              />
              <TreasuryTable
                loading={cbLoading}
                minCellWidth={200}
                data={cbLoading ? [undefined] : dataCB.visa.CREDIT}
                config={{
                  name: {
                    ...tableConfig.name,
                    headerLabel: 'Credit Card (Straight) - Visa',
                  },
                  ...getFilteredTableConfig(tableConfig),
                }}
              />
              <TreasuryTable
                loading={cbLoading}
                minCellWidth={200}
                data={cbLoading ? [undefined] : dataCB.amex.CREDIT}
                config={{
                  name: {
                    ...tableConfig.name,
                    headerLabel: 'Credit Card (Straight) - Amex',
                  },
                  ...getFilteredTableConfig(tableConfig),
                }}
              />
              <TreasuryTable
                loading={cbLoading}
                minCellWidth={200}
                data={cbLoading ? [undefined] : dataCB.jcb.CREDIT}
                config={{
                  name: {
                    ...tableConfig.name,
                    headerLabel: 'Credit Card (Straight) - JCB',
                  },
                  ...getFilteredTableConfig(tableConfig),
                }}
              />
              <TreasuryTable
                loading={cbLoading}
                minCellWidth={200}
                data={cbLoading ? [undefined] : dataCB.mc.CONSOLIDATED}
                config={{
                  name: {
                    ...tableConfig.name,
                    headerLabel: 'Mastercard (Total)',
                  },
                  ...getFilteredTableConfig(tableConfig),
                }}
              />
              <TreasuryTable
                loading={cbLoading}
                minCellWidth={200}
                data={cbLoading ? [undefined] : dataCB.visa.CONSOLIDATED}
                config={{
                  name: {
                    ...tableConfig.name,
                    headerLabel: 'Visa (Total)',
                  },
                  ...getFilteredTableConfig(tableConfig),
                }}
              />
              <TreasuryTable
                loading={cbLoading}
                minCellWidth={200}
                data={cbLoading ? [undefined] : dataCB.jcb.CONSOLIDATED}
                config={{
                  name: {
                    ...tableConfig.name,
                    headerLabel: 'JCB (Total)',
                  },
                  ...getFilteredTableConfig(tableConfig),
                }}
              />
              <TreasuryTable
                loading={cbLoading}
                minCellWidth={200}
                data={cbLoading ? [undefined] : dataCB.amex.CONSOLIDATED}
                config={{
                  name: {
                    ...tableConfig.name,
                    headerLabel: 'Amex (Total)',
                  },
                  ...getFilteredTableConfig(tableConfig),
                }}
              />
              <TreasuryTable
                loading={cbLoading}
                minCellWidth={200}
                data={cbLoading ? [undefined] : dataCB.Others.CONSOLIDATED}
                config={{
                  name: {
                    ...tableConfig.name,
                    headerLabel: 'Others (Total)',
                  },
                  ...getFilteredTableConfig(tableConfig),
                }}
              />

              <TreasuryReportTitle>{`${REPORT_TYPE_LABEL[REPORT_TYPE.GCASH].label} Reports`}</TreasuryReportTitle>
              <TreasuryTable
                loading={gcLoading}
                minCellWidth={200}
                data={gcLoading ? [undefined] : dataGC.gcash}
                config={{
                  name: {
                    ...tableConfig.name,
                    headerLabel:
                      REPORT_TYPE_LABEL[REPORT_TYPE.GCASH].BILL_PAYMENT,
                  },
                  ...getFilteredTableConfig(tableConfig),
                }}
              />

              <TreasuryReportTitle>{`${REPORT_TYPE_LABEL[REPORT_TYPE.EWALLET].label} Reports`}</TreasuryReportTitle>
              <TreasuryTable
                loading={ewLoading}
                minCellWidth={200}
                data={ewLoading ? [undefined] : dataEW.ewallet['Shopee Pay']}
                config={{
                  name: {
                    ...tableConfig.name,
                    headerLabel: 'Shopee Pay',
                  },
                  ...getFilteredTableConfig(tableConfig),
                }}
              />

              <TreasuryTable
                loading={ewLoading}
                minCellWidth={200}
                data={ewLoading ? [undefined] : dataEW.ewallet['Grabpay']}
                config={{
                  name: {
                    ...tableConfig.name,
                    headerLabel: 'GrabPay',
                  },
                  ...getFilteredTableConfig(tableConfig),
                }}
              />

              <TreasuryTable
                loading={ewLoading}
                minCellWidth={200}
                data={ewLoading ? [undefined] : dataEW.ewallet['Maya']}
                config={{
                  name: {
                    ...tableConfig.name,
                    headerLabel: 'Maya',
                  },
                  ...getFilteredTableConfig(tableConfig),
                }}
              />

              <TreasuryTable
                loading={ewLoading}
                minCellWidth={200}
                data={ewLoading ? [undefined] : dataEW.ewallet['Others']}
                config={{
                  name: {
                    ...tableConfig.name,
                    headerLabel: 'Others',
                  },
                  ...getFilteredTableConfig(tableConfig),
                }}
              />

              <TreasuryReportTitle>{`${
                REPORT_TYPE_LABEL[REPORT_TYPE.BANKTRANSFER].label
              } Reports`}</TreasuryReportTitle>
              <TreasuryTable
                loading={ewLoading}
                minCellWidth={200}
                data={ewLoading ? [undefined] : dataDD.dd.BPI}
                config={{
                  name: {
                    ...tableConfig.name,
                    headerLabel: 'BPI',
                  },
                  ...getFilteredTableConfig(tableConfig),
                }}
              />

              <TreasuryTable
                loading={ewLoading}
                minCellWidth={200}
                data={ewLoading ? [undefined] : dataDD.dd.Unionbank}
                config={{
                  name: {
                    ...tableConfig.name,
                    headerLabel: 'Union Bank',
                  },
                  ...getFilteredTableConfig(tableConfig),
                }}
              />

              <TreasuryTable
                loading={ewLoading}
                minCellWidth={200}
                data={ewLoading ? [undefined] : dataDD.dd.RCBC}
                config={{
                  name: {
                    ...tableConfig.name,
                    headerLabel: 'RCBC',
                  },
                  ...getFilteredTableConfig(tableConfig),
                }}
              />

              <TreasuryTable
                loading={ewLoading}
                minCellWidth={200}
                data={ewLoading ? [undefined] : dataDD.dd.Others}
                config={{
                  name: {
                    ...tableConfig.name,
                    headerLabel: 'Others',
                  },
                  ...getFilteredTableConfig(tableConfig),
                }}
              />
            </>
          }
        </DataContainer>
      </Page>
      <AlertModal
        isOpen={state.isConfirmDownloadModalOpen}
        title="Export Reports Alert"
        header="ARE YOU SURE?"
        variant="warn"
        icon="exclamation-circle"
        subHeader={
          <span>You are about to export treasury billing as .CSV File.</span>
        }
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() =>
          setState({ ...state, isConfirmDownloadModalOpen: false })
        }
        handleCancel={() =>
          setState({ ...state, isConfirmDownloadModalOpen: false })
        }
        confirmText="Yes"
        handleConfirm={async () => {
          const reportEntity =
            tableView === null
              ? 'Consolidated'
              : REPORT_ENTITIES.find(entity => entity.value === tableView)
                  .label;
          logExtraction({
            variables: {
              data: {
                type: 'treasury',
              },
            },
          });
          const csvCDDebit = await json2CSVYTD(dataCD.DEBIT, {
            name: {
              ...tableConfig.name,
              headerLabel: REPORT_TYPE_LABEL[REPORT_TYPE.CREDIT_DEBIT].DEBIT,
            },
            ...getFilteredTableConfig(tableConfig),
          });
          const csvCDCredit = await json2CSVYTD(dataCD.CREDIT, {
            name: {
              ...tableConfig.name,
              headerLabel: REPORT_TYPE_LABEL[REPORT_TYPE.CREDIT_DEBIT].CREDIT,
            },
            ...getFilteredTableConfig(tableConfig),
          });
          const csvCDOthers = await json2CSVYTD(dataCD.Others, {
            name: {
              ...tableConfig.name,
              headerLabel: REPORT_TYPE_LABEL[REPORT_TYPE.CREDIT_DEBIT].Others,
            },
            ...getFilteredTableConfig(tableConfig),
          });
          const csvCBDMC = await json2CSVYTD(dataCB.mc.DEBIT, {
            name: {
              ...tableConfig.name,
              headerLabel: 'EPS/Debit Card - Mastercard',
            },
            ...getFilteredTableConfig(tableConfig),
          });
          const csvCBXD = await json2CSVYTD(dataCB.cc_dc.CREDIT, {
            name: {
              ...tableConfig.name,
              headerLabel: 'Online Credit Card - Xendit',
            },
            ...getFilteredTableConfig(tableConfig),
          });
          const csvCBXVISA = await json2CSVYTD(dataCB.VISA.CREDIT, {
            name: {
              ...tableConfig.name,
              headerLabel: 'Visa - Xendit',
            },
            ...getFilteredTableConfig(tableConfig),
          });
          const csvCBXMC = await json2CSVYTD(dataCB.MASTERCARD.CREDIT, {
            name: {
              ...tableConfig.name,
              headerLabel: 'Mastercard - Xendit',
            },
            ...getFilteredTableConfig(tableConfig),
          });
          const csvCBDV = await json2CSVYTD(dataCB.visa.DEBIT, {
            name: {
              ...tableConfig.name,
              headerLabel: 'EPS/Debit Card - Visa',
            },
            ...getFilteredTableConfig(tableConfig),
          });
          const csvCBCMC = await json2CSVYTD(dataCB.mc.CREDIT, {
            name: {
              ...tableConfig.name,
              headerLabel: 'Credit Card (Straight) - Mastercard',
            },
            ...getFilteredTableConfig(tableConfig),
          });
          const csvCBCV = await json2CSVYTD(dataCB.visa.CREDIT, {
            name: {
              ...tableConfig.name,
              headerLabel: 'Credit Card (Straight) - Visa',
            },
            ...getFilteredTableConfig(tableConfig),
          });
          const csvCBTMC = await json2CSVYTD(dataCB.mc.CONSOLIDATED, {
            name: {
              ...tableConfig.name,
              headerLabel: 'Mastercard (Total)',
            },
            ...getFilteredTableConfig(tableConfig),
          });
          const csvCBTV = await json2CSVYTD(dataCB.visa.CONSOLIDATED, {
            name: {
              ...tableConfig.name,
              headerLabel: 'Visa (Total)',
            },
            ...getFilteredTableConfig(tableConfig),
          });
          const csvCBOthers = await json2CSVYTD(dataCB.Others.CONSOLIDATED, {
            name: {
              ...tableConfig.name,
              headerLabel: 'Others (Total)',
            },
            ...getFilteredTableConfig(tableConfig),
          });
          const csvGC = await json2CSVYTD(dataGC.gcash, {
            name: {
              ...tableConfig.name,
              headerLabel: REPORT_TYPE_LABEL[REPORT_TYPE.GCASH].BILL_PAYMENT,
            },
            ...getFilteredTableConfig(tableConfig),
          });
          const csvEWSP = await json2CSVYTD(dataEW.ewallet['Shopee Pay'], {
            name: {
              ...tableConfig.name,
              headerLabel: 'Shopee Pay',
            },
            ...getFilteredTableConfig(tableConfig),
          });
          const csvEWGP = await json2CSVYTD(dataEW.ewallet['Grabpay'], {
            name: {
              ...tableConfig.name,
              headerLabel: 'Grabpay',
            },
            ...getFilteredTableConfig(tableConfig),
          });
          const csvEWMY = await json2CSVYTD(dataEW.ewallet['Maya'], {
            name: {
              ...tableConfig.name,
              headerLabel: 'Maya',
            },
            ...getFilteredTableConfig(tableConfig),
          });
          const csvEWOthers = await json2CSVYTD(dataEW.ewallet['Others'], {
            name: {
              ...tableConfig.name,
              headerLabel: 'Others',
            },
            ...getFilteredTableConfig(tableConfig),
          });

          const csvBTBPI = await json2CSVYTD(dataDD.dd.BPI, {
            name: {
              ...tableConfig.name,
              headerLabel: 'BPI',
            },
            ...getFilteredTableConfig(tableConfig),
          });
          const csvBTUB = await json2CSVYTD(dataDD.dd.Unionbank, {
            name: {
              ...tableConfig.name,
              headerLabel: 'Unionbank',
            },
            ...getFilteredTableConfig(tableConfig),
          });
          const csvBTRCBC = await json2CSVYTD(dataDD.dd.RCBC, {
            name: {
              ...tableConfig.name,
              headerLabel: 'RCBC',
            },
            ...getFilteredTableConfig(tableConfig),
          });
          const csvBTOthers = await json2CSVYTD(dataDD.dd.Others, {
            name: {
              ...tableConfig.name,
              headerLabel: 'Others',
            },
            ...getFilteredTableConfig(tableConfig),
          });

          let csvData = `,\n${REPORT_TYPE_LABEL[REPORT_TYPE.CREDIT_DEBIT].label} Reports\n,`;
          csvData += `
          ${csvCDDebit}\n,
          ${csvCDCredit}\n, 
          ${csvCDOthers}\n,`;
          csvData += `,\n${REPORT_TYPE_LABEL[REPORT_TYPE.CARD_BRAND].label} Reports\n,`;
          csvData += `
          ${csvCBXD}\n,
          ${csvCBXVISA}\n,
          ${csvCBXMC}\n,
          ${csvCBDMC}\n,
          ${csvCBDV}\n,
          ${csvCBCMC}\n,
          ${csvCBCV}\n,
          ${csvCBTMC}\n,
          ${csvCBTV}\n,
          ${csvCBOthers}\n,`;
          csvData += `,\n${REPORT_TYPE_LABEL[REPORT_TYPE.GCASH].label} Reports\n,`;
          csvData += `\n${csvGC}\n`;
          csvData += `,\n${REPORT_TYPE_LABEL[REPORT_TYPE.EWALLET].label} Reports\n,`;
          csvData += `
          ${csvEWSP}\n,
          ${csvEWGP}\n,
          ${csvEWMY}\n,
          ${csvEWOthers}\n,`;
          csvData += `,\n${REPORT_TYPE_LABEL[REPORT_TYPE.BANKTRANSFER].label} Reports\n,`;
          csvData += `
          ${csvBTBPI}\n,
          ${csvBTUB}\n,
          ${csvBTRCBC}\n,
          ${csvBTOthers}\n`;

          const fileData = {
            mime: 'text/csv',
            filename: `Treasury Bill YTD ${reportEntity}.csv`,
            contents: csvData,
          };
          const blob = new Blob([fileData.contents], {
            type: fileData.mime,
          });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          document.body.appendChild(a);
          a.download = fileData.filename;
          a.href = url;
          a.click();
          document.body.removeChild(a);
          setState({
            ...state,
            isConfirmDownloadModalOpen: false,
            isAlertMessageOpen: true,
            alertMsg: {
              header: 'SUCCESS',
              variant: 'success',
              icon: 'check-circle',
              subHeader:
                'You exported the treasury billing YTD report as .CSV File successfully.',
              description: 'Kindly check the downloaded file.',
              confirmText: 'Back to YTD Report.',
            },
          });
        }}
      />
      <AlertModal
        isOpen={state.isAlertMessageOpen}
        title="Export Reports Alert"
        header={state.alertMsg && state.alertMsg.header}
        variant={state.alertMsg && state.alertMsg.variant}
        icon={state.alertMsg && state.alertMsg.icon}
        subHeader={<span>{state.alertMsg && state.alertMsg.subHeader}</span>}
        description={state.alertMsg ? state.alertMsg.description : ''}
        confirmText={state.alertMsg ? state.alertMsg.confirmText : ''}
        handleClose={() =>
          setState({
            ...state,
            isAlertMessageOpen: false,
            alertMessage: null,
          })
        }
        handleConfirm={() =>
          setState({
            ...state,
            isAlertMessageOpen: false,
            alertMessage: null,
          })
        }
      />
    </>
  );
};

export default YTD;
