import { gql } from '@apollo/client';

export const GET_TREASURY_BILL_YTD_REPORTS = gql`
  query getTresuryBillReportsYTD(
    $filter: SearchTreasuryInput
    $reportType: TreasuryReportType!
  ) {
    treasuryYTD(filter: $filter, reportType: $reportType) {
      lastKey
      filteredData {
        name
        accountType
        gatewayProcessor
        paymentMethod
        month
        year
        count
        amount
        cardType
        cardBrand
      }
      previousYears {
        year
        count
        amount
        name
        accountType
        cardType
        cardBrand
        paymentMethod
      }
    }
  }
`;

export const GET_TREASURY_BILL_MONTHLY_REPORTS = gql`
  query getTreasuryBillReportsMonthly(
    $filter: SearchTreasuryMonthInput
    $reportType: TreasuryReportType!
  ) {
    monthlyTreasury(filter: $filter, reportType: $reportType) {
      lastKey
      filteredData {
        name
        accountType
        paymentMethod
        paymentGateway
        startMonth
        startYear
        endMonth
        endYear
        count
        amount
        cardType
        cardBrand
      }
    }
  }
`;

export const GET_TREASURY_BILL_MONTHLY_TRANSACTION_REPORTS = gql`
  query getTreasuryBillReportsMonthlyTransaction(
    $filter: SearchTransactionLogsFilterInput!
    $reportType: PaginationReportInput!
  ) {
    treasuryReports(filter: $filter, reportType: $reportType) {
      lastKey
      filteredData {
        fundingSource
        prodDesc
        amountValue
        paymentMethod
        createdAt
      }
    }
  }
`;

export const REPORT_PATH_YTD = 'treasuryYTD';
export const REPORT_PATH_MONTHLY = 'monthlyTreasury';
export const REPORT_PATH_MONTHLY_TRANS = 'treasuryReports';
export const EXT_DATA = 'previousYears';
