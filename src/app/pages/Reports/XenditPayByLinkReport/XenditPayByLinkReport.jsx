import format from 'date-fns/format';
import React, { useContext, useState } from 'react';
import { ExportButton } from '../../../components/Button/ExportButton';
import DataContainer from '../../../components/DataContainer';
import DataTable from '../../../components/DataTable';
import Header from '../../../components/Header';
import GlobalSearch from '../../../components/GlobalSearch';
import { AlertModal } from '../../../components/Modal';
import Page from '../../../components/Page';
import AuthContext from '../../../context/AuthContext/AuthContext';
import { EXPORT_REPORTS } from '../mutation';
import useQueryReportSeries from '../../../hooks/useQueryReportSeries-v2';
import {
  ResponsiveRow,
  TransactionLogHighlight,
  ColumnVisibilityDropdown,
} from '../styled';
import { GET_XENDIT_PAYBYLINK_REPORT, REPORT_PATH } from './query';
import { numberWithCommas } from '../../../components/GlobalSearch/utils';
import { useMutation } from '@apollo/client';
import { FIELD_TYPES } from '../../../components/Form/constants';
import NotificationContext from '../../../context/NotificationContext';

const XenditPayByLinkReport = () => {
  const { reportPermissions } = useContext(AuthContext);

  const { addNotif } = useContext(NotificationContext);

  const [isConfirmDownloadModalOpen, setIsConfirmDownloadModalOpen] =
    useState(false);

  const [isSuccessDownloadModalOpen, setIsSuccessDownloadModalOpen] =
    useState(false);

  const [logExtraction] = useMutation(EXPORT_REPORTS);

  const tableConfig = {
    id: {
      headerLabel: 'Reference ID',
    },
    paymentGateway: {
      headerLabel: 'Payment Gateway',
    },
    paymentLink: {
      headerLabel: 'Payment Link',
    },
    status: {
      headerLabel: 'Status',
    },
    merchantAccount: {
      headerLabel: 'Merchant Account',
    },
    merchantReference: {
      headerLabel: 'Merchant Reference',
    },
    amount: {
      headerLabel: 'Amount',
      renderAs: data => numberWithCommas(data.amount, 2),
    },
    description: {
      headerLabel: 'Description',
    },
    linkType: {
      headerLabel: 'Link Type',
    },
  };

  const [visibleColumns, setVisibleColumns] = useState(
    Object.keys(tableConfig)
  );

  const {
    pagination,
    setNewPagination,
    filter,
    setFilter,
    data,
    loading,
    page,
    setPage,
    isLastPage,
  } = useQueryReportSeries(GET_XENDIT_PAYBYLINK_REPORT, 'payByLinkReport', {
    pagination: {
      startKey: '',
      limit: 10,
    },
  });

  return (
    <>
      <Page>
        <Header
          withHome
          title="Pay-by-Link Report"
          path={['Reports', 'Pay-By-Link Report']}
        />
        <DataContainer>
          <DataTable
            loading={loading}
            data={data}
            minCellWidth={200}
            headerOptions={
              <>
                <GlobalSearch
                  onSearch={filter => {
                    const newPagination = {
                      ...pagination,
                      startKey: '',
                    };
                    setFilter(filter);
                    setNewPagination(newPagination);
                  }}
                  fields={[
                    {
                      label: 'Reference ID',
                      name: 'reference',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Merchant Reference',
                      name: 'merchantReference',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Status',
                      name: 'status',
                      type: FIELD_TYPES.SELECT,
                      options: [
                        { value: 'PENDING', label: 'PENDING' },
                        { value: 'PAID', label: 'PAID' },
                        { value: 'EXPIRED', label: 'EXPIRED' },
                        { value: 'SETTLED', label: 'SETTLED' },
                      ],
                    },
                  ]}
                />
                <ResponsiveRow>
                  {reportPermissions.PayByLinkReport.export && (
                    <ExportButton
                      icon="file-csv"
                      iconPosition="left"
                      disabled={false}
                      onClick={() => {
                        setIsConfirmDownloadModalOpen(true);
                      }}
                    >
                      CSV
                    </ExportButton>
                  )}
                  <ColumnVisibilityDropdown
                    multi
                    showMulti={false}
                    placeholder="Visible Columns"
                    value={visibleColumns}
                    options={Object.keys(tableConfig).map(key => ({
                      value: key,
                      label: tableConfig[key].headerLabel,
                    }))}
                    onChange={setVisibleColumns}
                  />
                </ResponsiveRow>
              </>
            }
            config={Object.keys(tableConfig).reduce((config, key) => {
              if (visibleColumns.includes(key)) {
                config[key] = tableConfig[key];
              }
              return config;
            }, {})}
            pagination={{
              ...pagination,
              startKey: pagination.startKey,
              withStartKeys: true,
              count: 0,
              cursors: [],
              handleChange: setNewPagination,
            }}
            series={{ page, setPage, isLastPage }}
          />
        </DataContainer>
      </Page>
      <AlertModal
        isOpen={isConfirmDownloadModalOpen}
        title="Export Reports Alert"
        header="ARE YOU SURE?"
        variant="warn"
        icon="exclamation-circle"
        subHeader={
          <span>
            You are about to export all filtered records as .CSV File.
          </span>
        }
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() => {
          setIsConfirmDownloadModalOpen(false);
        }}
        handleCancel={() => {
          setIsConfirmDownloadModalOpen(false);
        }}
        confirmText="Yes"
        handleConfirm={async () => {
          const notifTime = new Date().getTime();
          setIsConfirmDownloadModalOpen(false);
          addNotif({
            id: 'XenditPayByLinkReport-' + notifTime,
            notifTime,
            type: 'info',
            title: 'Downloading Report File',
            message: 'Downloading Xendit Pay-By-Link Report',
            isProgress: true,
            progressData: {
              progress: 0,
              isProgressive: false,
            },
            isLocal: true,
            data: {},
            reportDLParams: {
              query: GET_XENDIT_PAYBYLINK_REPORT,
              path: REPORT_PATH,
              variables: {
                filter,
                pagination: {
                  startKey: '',
                  limit: 10,
                },
              },
              onDownload: () => {
                logExtraction({
                  variables: {
                    data: {
                      type: 'paybylinkreport',
                    },
                  },
                });
              },
              tableConfig,
              fileName: `Pay-By-Link Report ${format(new Date(), 'MMDDYYYY')}.csv`,
            },
          });
        }}
      />
      <AlertModal
        isOpen={isSuccessDownloadModalOpen}
        title="Export Reports Alert"
        header="SUCCESS!"
        variant="success"
        icon="check-circle"
        subHeader={
          <span>
            You exported{' '}
            <TransactionLogHighlight>
              {data.length} row(s){' '}
            </TransactionLogHighlight>{' '}
            as .CSV File succesfully.
          </span>
        }
        description="Kindly check the downloaded file."
        confirmText="Back to All Reports"
        handleClose={() => {
          setIsSuccessDownloadModalOpen(false);
        }}
        handleConfirm={() => {
          setIsSuccessDownloadModalOpen(false);
        }}
      />
    </>
  );
};

export default XenditPayByLinkReport;
