import { gql } from '@apollo/client';

export const GET_XENDIT_PAYBYLINK_REPORT = gql`
  query GetPayByLinkReport(
    $filter: PayByLinkTableReportInput
    $pagination: PaginationInput!
  ) {
    payByLinkReport(filter: $filter, pagination: $pagination) {
      lastKey
      count
      filteredData {
        id
        amount
        description
        paymentLink
        expiredAt
        createdAt
        merchantAccount
        merchantReference
        channelId
        paymentGateway
        status
        customer
        updatedAt
        linkType
      }
    }
  }
`;

export const REPORT_PATH = 'payByLinkReport';
