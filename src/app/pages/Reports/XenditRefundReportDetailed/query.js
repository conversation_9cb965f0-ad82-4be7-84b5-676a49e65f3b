import { gql } from '@apollo/client';

export const GET_XENDITREFUND_REPORTS_DETAILED = gql`
  query getXenditRefundDetailedReport(
    $filter: SearchXenditRefundDetailedReport!
    $pagination: PaginationInput!
  ) {
    xenditRefundDetailedReport(filter: $filter, pagination: $pagination) {
      lastKey
      filteredData {
        paymentId
        accountNumber
        mobileNumber
        billType
        paymentMethod
        channelName
        postedTimestamp
        refundDate
        refundReason
        refundAmount
        refundId
        timestamp
        amountValue
        status
        refundApprovalStatus
        gcashTransId
        requestTimeStamp
        refundRejectedTimestamp
        refundStatus
        refundType
        isRefundable
        convenienceFee
        hasConvenienceFee
      }
    }
  }
`;

export const GET_CHANNEL_OPTIONS = gql`
  query getChannelOptions {
    channelsLoose {
      id
      name
      channelId
    }
  }
`;

export const REPORT_PATH = 'xenditRefundDetailedReport';
