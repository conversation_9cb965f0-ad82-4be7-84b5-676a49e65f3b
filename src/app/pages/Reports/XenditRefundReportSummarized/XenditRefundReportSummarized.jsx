import format from 'date-fns/format';
import React, { useContext, useEffect, useState } from 'react';
import { useQuery } from '@apollo/client';
import { ExportButton } from '../../../components/Button/ExportButton';
import DataContainer from '../../../components/DataContainer';
import DataTable from '../../../components/DataTable';
import { FIELD_TYPES } from '../../../components/Form/constants';
import GlobalSearch from '../../../components/GlobalSearch';
import {
  json2CSVYTD,
  numberWithCommas,
} from '../../../components/GlobalSearch/utils';
import Header from '../../../components/Header';
import { AlertModal } from '../../../components/Modal';
import Page from '../../../components/Page';
import AuthContext from '../../../context/AuthContext/AuthContext';
import { useMutation } from '@apollo/client';
import { EXPORT_REPORTS } from '../mutation';
import {
  ColumnVisibilityDropdown,
  ResponsiveRow,
  TransactionLogHighlight,
} from '../styled';
import {
  GET_CHANNEL_OPTIONS,
  GET_XENDIT_REFUND_SUMMARY_REPORTS,
} from './query';

const XenditRefundReportSummarized = () => {
  const [state, setState] = useState({
    filter: {},
  });

  const { reportPermissions } = useContext(AuthContext);

  const { data: channelData, loading: isLoadingChannels } = useQuery(
    GET_CHANNEL_OPTIONS,
    {
      fetchPolicy: 'network-only',
    }
  );

  const { data, loading } = useQuery(GET_XENDIT_REFUND_SUMMARY_REPORTS, {
    variables: {
      filter: state.filter,
    },
    fetchPolicy: 'network-only',
  });

  const [billFooter, setBillFooter] = useState({});
  const [nonBillFooter, setNonBillFooter] = useState({});

  const [isConfirmDownloadModalOpen, setIsConfirmDownloadModalOpen] =
    useState(false);
  const [isSuccessDownloadModalOpen, setIsSuccessDownloadModalOpen] =
    useState(false);

  const [logExtraction] = useMutation(EXPORT_REPORTS);

  const channelOptions =
    !isLoadingChannels && channelData
      ? channelData.channelsLoose.map(channel => ({
          value: channel.id,
          label: channel.name,
        }))
      : [];

  channelOptions.unshift({
    value: null,
    label: 'Any',
  });

  const tableConfig = {
    channelName: {
      headerLabel: 'Channel',
      sortable: true,
    },
    totalApprovedRefundAmount: {
      headerLabel: 'Total Approved Refund Amount (Webtool)',
      sortable: true,
      renderAs: data => numberWithCommas(data.totalApprovedRefundAmount, 2),
    },
    totalApprovedRefundCount: {
      headerLabel: 'Total Approved Refund Count (Webtool)',
      sortable: true,
      renderAs: data => numberWithCommas(data.totalApprovedRefundCount),
    },
    totalForApprovalAmount: {
      headerLabel: 'Total For Approval Amount',
      sortable: true,
      renderAs: data => numberWithCommas(data.totalForApprovalAmount, 2),
    },
    totalForApprovalCount: {
      headerLabel: 'Total For Approval Refund Count',
      sortable: true,
      renderAs: data => numberWithCommas(data.totalForApprovalCount),
    },
    totalAutoRefundAmount: {
      headerLabel: 'Total Approved Refund Amount (Auto)',
      sortable: true,
      renderAs: data => numberWithCommas(data.totalAutoRefundAmount, 2),
    },
    totalAutoRefundCount: {
      headerLabel: 'Total Approved Refund Count (Auto)',
      sortable: true,
      renderAs: data => numberWithCommas(data.totalAutoRefundCount),
    },
  };

  const [visibleColumns, setVisibleColumns] = useState(
    Object.keys(tableConfig)
  );

  useEffect(() => {
    if (
      data?.xenditRefundSummaryReport &&
      data.xenditRefundSummaryReport.Bill
    ) {
      var footer =
        data.xenditRefundSummaryReport &&
        data.xenditRefundSummaryReport.Bill.reduce(
          (finalVal, currentVal) => {
            return {
              amountApproved:
                finalVal.amountApproved +
                parseFloat(currentVal.totalApprovedRefundAmount),
              countApproved:
                finalVal.countApproved +
                parseFloat(currentVal.totalApprovedRefundCount),
              amountApproval:
                finalVal.amountApproval +
                parseFloat(currentVal.totalForApprovalAmount),
              countApproval:
                finalVal.countApproval +
                parseFloat(currentVal.totalForApprovalCount),
              amountAutoApproved:
                finalVal.amountAutoApproved +
                parseFloat(currentVal.totalAutoRefundAmount),
              countAutoApproved:
                finalVal.countAutoApproved +
                parseFloat(currentVal.totalAutoRefundCount),
            };
          },
          {
            amountApproved: 0,
            countApproved: 0,
            amountApproval: 0,
            countApproval: 0,
            amountAutoApproved: 0,
            countAutoApproved: 0,
          }
        );

      setBillFooter({
        channelName: 'Grand Total',
        totalApprovedRefundAmount: numberWithCommas(footer.amountApproved, 2),
        totalApprovedRefundCount: numberWithCommas(footer.countApproved),
        totalForApprovalAmount: numberWithCommas(footer.amountApproval, 2),
        totalForApprovalCount: numberWithCommas(footer.countApproval),
        totalAutoRefundAmount: numberWithCommas(footer.amountAutoApproved, 2),
        totalAutoRefundCount: numberWithCommas(footer.countAutoApproved),
      });
    }
  }, [
    JSON.stringify(
      data?.xenditRefundSummaryReport && data.xenditRefundSummaryReport.Bill
    ),
  ]);

  useEffect(() => {
    if (
      data?.xenditRefundSummaryReport &&
      data.xenditRefundSummaryReport.NonBill
    ) {
      var footer =
        data.xenditRefundSummaryReport &&
        data.xenditRefundSummaryReport.NonBill.reduce(
          (finalVal, currentVal) => {
            return {
              amountApproved:
                finalVal.amountApproved +
                parseFloat(currentVal.totalApprovedRefundAmount),
              countApproved:
                finalVal.countApproved +
                parseFloat(currentVal.totalApprovedRefundCount),
              amountApproval:
                finalVal.amountApproval +
                parseFloat(currentVal.totalForApprovalAmount),
              countApproval:
                finalVal.countApproval +
                parseFloat(currentVal.totalForApprovalCount),
              amountAutoApproved:
                finalVal.amountAutoApproved +
                parseFloat(currentVal.totalAutoRefundAmount),
              countAutoApproved:
                finalVal.countAutoApproved +
                parseFloat(currentVal.totalAutoRefundCount),
            };
          },
          {
            amountApproved: 0,
            countApproved: 0,
            amountApproval: 0,
            countApproval: 0,
            amountAutoApproved: 0,
            countAutoApproved: 0,
          }
        );

      setNonBillFooter({
        channelName: 'Grand Total',
        totalApprovedRefundAmount: numberWithCommas(footer.amountApproved, 2),
        totalApprovedRefundCount: numberWithCommas(footer.countApproved),
        totalForApprovalAmount: numberWithCommas(footer.amountApproval, 2),
        totalForApprovalCount: numberWithCommas(footer.countApproval),
        totalAutoRefundAmount: numberWithCommas(footer.amountAutoApproved, 2),
        totalAutoRefundCount: numberWithCommas(footer.countAutoApproved),
      });
    }
  }, [
    JSON.stringify(
      data?.xenditRefundSummaryReport && data.xenditRefundSummaryReport.NonBill
    ),
  ]);

  return (
    <>
      <Page>
        <Header
          withHome
          title="Xendit Refund Summarized Report"
          path={['Reports', 'Xendit Refund Summarized Report']}
        />
        <DataContainer>
          <>
            <ResponsiveRow>
              <GlobalSearch
                onSearch={filter => {
                  setState({ ...state, filter });
                }}
                fields={[
                  {
                    label: 'Channel',
                    name: 'channelId',
                    type: FIELD_TYPES.SELECT,
                    options: channelOptions,
                  },
                  {
                    label: 'Date Range',
                    name: 'refundRange',
                    type: FIELD_TYPES.DATE_RANGE,
                  },
                ]}
                placeholder="Search here..."
              />
              {reportPermissions.XenditRefundSummaryReport.export && (
                <ExportButton
                  icon="file-csv"
                  iconPosition="left"
                  disabled={loading}
                  onClick={() => setIsConfirmDownloadModalOpen(true)}
                >
                  CSV
                </ExportButton>
              )}
              <ColumnVisibilityDropdown
                multi
                showMulti={false}
                placeholder="Visible Columns"
                value={visibleColumns}
                options={Object.keys(tableConfig).map(key => ({
                  value: key,
                  label: tableConfig[key].headerLabel,
                }))}
                onChange={setVisibleColumns}
              />
            </ResponsiveRow>
          </>
          <h1>Bill</h1>
          <DataTable
            loading={loading}
            minCellWidth={200}
            data={
              data?.xenditRefundSummaryReport &&
              data.xenditRefundSummaryReport.Bill
            }
            config={Object.keys(tableConfig).reduce((config, key) => {
              if (visibleColumns.includes(key)) {
                config[key] = tableConfig[key];
              }
              return config;
            }, {})}
            footer={billFooter}
          />
        </DataContainer>
        <DataContainer>
          <h1>NonBill</h1>
          <DataTable
            loading={loading}
            minCellWidth={200}
            data={
              data?.xenditRefundSummaryReport &&
              data.xenditRefundSummaryReport.NonBill
            }
            config={Object.keys(tableConfig).reduce((config, key) => {
              if (visibleColumns.includes(key)) {
                config[key] = tableConfig[key];
              }
              return config;
            }, {})}
            footer={nonBillFooter}
          />
        </DataContainer>
      </Page>
      <AlertModal
        isOpen={isConfirmDownloadModalOpen}
        title="Export Reports Alert"
        header="ARE YOU SURE?"
        variant="warn"
        icon="exclamation-circle"
        subHeader={
          <span>
            You are about to export all filtered records as .CSV File.
          </span>
        }
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() => setIsConfirmDownloadModalOpen(false)}
        handleCancel={() => setIsConfirmDownloadModalOpen(false)}
        confirmText="Yes"
        handleConfirm={async () => {
          setIsConfirmDownloadModalOpen(false);
          const cardRefundSummaryBill = await json2CSVYTD(
            data?.xenditRefundSummaryReport &&
              data.xenditRefundSummaryReport.Bill,
            tableConfig
          );
          const cardRefundSummaryNonBill = await json2CSVYTD(
            data?.xenditRefundSummaryReport &&
              data.xenditRefundSummaryReport.NonBill,
            tableConfig
          );
          let csvData = `BILL\n${cardRefundSummaryBill}\n,\n,\n NONBILL
          ${cardRefundSummaryNonBill}`;
          const fileData = {
            mime: 'text/csv',
            filename: `Xendit Refund_Summarized ${format(new Date(), 'MMDDYYYY')}.csv`,
            contents: csvData,
          };
          const blob = new Blob([fileData.contents], {
            type: fileData.mime,
          });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          document.body.appendChild(a);
          a.download = fileData.filename;
          a.href = url;
          a.click();
          document.body.removeChild(a);
          logExtraction({
            variables: {
              data: {
                type: 'xenditrefundsummaryreport',
              },
            },
          });
        }}
      />
      <AlertModal
        isOpen={isSuccessDownloadModalOpen}
        title="Export Reports Alert"
        header="SUCCESS!"
        variant="success"
        icon="check-circle"
        subHeader={
          <span>
            You exported{' '}
            <TransactionLogHighlight>
              {data?.length} row(s){' '}
            </TransactionLogHighlight>{' '}
            as .CSV File succesfully.
          </span>
        }
        description="Kindly check the downloaded file."
        confirmText="Back to All Reports"
        handleClose={() => setIsSuccessDownloadModalOpen(false)}
        handleConfirm={() => {
          setIsSuccessDownloadModalOpen(false);
        }}
      />
    </>
  );
};

XenditRefundReportSummarized.propTypes = {};

export default XenditRefundReportSummarized;
