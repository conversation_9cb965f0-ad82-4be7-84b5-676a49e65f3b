import PropTypes from 'prop-types';
import React, { useContext, useEffect, useState } from 'react';
import { useQuery } from '@apollo/client';
import PrimaryButton from '../../components/Button/PrimaryButton';
import DataContainer from '../../components/DataContainer';
import DataHeader from '../../components/DataHeader';
import { FIELD_TYPES } from '../../components/Form/constants';
import FormField from '../../components/Form/FormField';
import Header from '../../components/Header';
import {
  ButtonsContainer,
  PageSubsection,
} from '../../components/InformationPage';
import Loader from '../../components/Loader';
import AlertModal from '../../components/Modal/AlertModal';
import Page from '../../components/Page';
import ConfigContext from '../../context/ConfigContext/ConfigContext';
import useForm from '../../hooks/useForm';
import { useMutation } from '@apollo/client';
import { EDIT_CONFIG } from './mutation';
import { GET_CONFIGS, GET_USED_SUBMERCHANT } from './query';
import * as Yup from 'yup';
import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import ResponsiveContext from '../../context/ResponsiveContext';

const SubMerchantSection = styled.div`
  max-height: 100%;
  margin-bottom: 20px;
`;

const RefundReasonSection = styled.div`
  max-height: 300px;
  overflow: auto;
  margin-bottom: 20px;
`;

const CCRefundReasonSection = styled.div`
  max-height: 300px;
  overflow: auto;
  margin-bottom: 20px;
`;

const PSORPaymentType = styled.div`
  max-height: 300px;
  overflow: auto;
  margin-bottom: 20px;
`;

const SWIPEORPaymentType = styled.div`
  max-height: 300px;
  overflow: auto;
  margin-bottom: 20px;
`;

const AutoMailConfig = styled.div`
  max-height: 600px;
  overflow: auto;
  margin-bottom: 20px;
`;

const ActionFields = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;

  width: ${props => {
    const base = 100 / props.perRow;
    const margin = 20;
    return `calc(${base}% - ${margin}px)`;
  }};

  flex: 1;

  margin-left: ${props => (props.isMobile ? '0px' : '20px')};
  margin-bottom: 20px;
`;

const ActionIcon = styled(FontAwesomeIcon)`
  color: ${props => (props.disabled ? 'gray' : props.color)};
  font-size: 20px;
  cursor: pointer;

  margin-left: 10px;
  &:first-child {
    margin-left: 0;
  }
`;

const ActionButton = styled.button`
  background-color: ${props =>
    props.disabled ? 'gray' : props.backgroundColor};
  color: #fff;
  font-size: ${props => props.theme.fontSize.s};
  cursor: pointer;
  flex: 1;
  border-radius: 5px;
`;

const APIServiceMaintenanceMode = [
  {
    value: false,
    color: 'green',
    label: 'Up and running',
  },
  {
    value: true,
    color: 'red',
    label: 'Maintenance Mode',
  },
];

const EmailNotificationMode = [
  {
    value: false,
    color: 'red',
    label: 'Disable',
  },
  {
    value: true,
    color: 'green',
    label: 'Enable',
  },
];

const PAYMENT_METHODS = {
  gcash: [
    {
      value: 'mynt',
      label: 'Mynt',
      color: 'green',
    },
  ],
  card: [
    {
      value: 'adyen',
      label: 'Adyen',
      color: 'green',
    },
    {
      value: 'ipay88',
      label: 'iPay88',
      color: 'green',
    },
  ],
};

const SystemConfig = ({ history }) => {
  const { isMobile } = useContext(ResponsiveContext);
  const { refetch: refetchConfigContext } = useContext(ConfigContext);
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
  const [isSuccessModalOpen, setIsSuccessModalOpen] = useState(false);
  const [isFailureModalOpen, setIsFailureModalOpen] = useState(false);
  const [nextLocation, setNextLocation] = useState(null);
  const [isLeavingWhileEditing, setIsLeavingWhileEditing] = useState(false);
  const [hasEdited, setHasEdited] = useState(false);
  const [submerchantErrors, setSubmerchantErrors] = useState({
    serviceType: {},
    id: {},
  });

  const [ORPaymentTypeError, setORPaymentTypeError] = useState({
    name: {},
    or: {},
    orVat: {},
    description: {},
  });

  const [SWIPEORPaymentTypeError, setSWIPEORPaymentTypeError] = useState({
    name: {},
    or: {},
    orVat: {},
    description: {},
  });

  //Reports Error
  const [dailyGCashError, setDailyGCashError] = useState({
    email: {},
  });
  const [monthlyGCashError, setMonthlyGCashError] = useState({
    email: {},
  });
  const [ecpayError, setEcpayError] = useState({
    email: {},
  });
  const [globeOneError, setGlobeOneError] = useState({
    email: {},
  });
  const [collectionError, setCollectionError] = useState({
    email: {},
  });
  const [creditCardError, setCreditCardError] = useState({
    email: {},
  });
  const [channelError, setChannelError] = useState({
    email: {},
  });
  const [billingError, setBillingError] = useState({
    email: {},
  });

  const [newSubmerchants, setNewSubmerchants] = useState([
    {
      serviceType: null,
      subMerchantId: null,
      merchant: null,
    },
  ]);

  const [newORs, setNewORs] = useState([
    {
      name: null,
      orVat: null,
      or: null,
      description: null,
    },
  ]);

  const [newSWIPEORs, setNewSWIPEORs] = useState([
    {
      name: null,
      orVat: null,
      or: null,
      description: null,
    },
  ]);

  const [newRefundReason, setNewRefundReason] = useState([
    {
      reason: null,
    },
  ]);

  const [newCCRefundReason, setNewCCRefundReason] = useState([
    {
      reason: null,
    },
  ]);

  const [newIPWhitelist, setNewIPWhitelist] = useState({
    xendit: {
      callback: [''],
    },
  });

  //Reports New
  const [newDailyGCash, setNewDailyGCash] = useState([
    {
      email: null,
    },
  ]);
  const [newMonthlyGCash, setNewMonthlyGCash] = useState([
    {
      email: null,
    },
  ]);
  const [newEcpay, setNewEcpay] = useState([
    {
      email: null,
    },
  ]);
  const [newGlobeOne, setNewGlobeOne] = useState([
    {
      email: null,
    },
  ]);
  const [newCollection, setNewCollection] = useState([
    {
      email: null,
    },
  ]);
  const [newCreditCard, setNewCreditCard] = useState([
    {
      email: null,
    },
  ]);
  const [newChannel, setNewChannel] = useState([
    {
      email: null,
    },
  ]);
  const [newBilling, setNewBilling] = useState([
    {
      email: null,
    },
  ]);

  const [errorMessage, setNewErrorMessage] = useState(null);
  //Report Error Value
  const [reportLabel, setReportLabel] = useState(null);
  const [ORValue, setORValue] = useState(null);
  const [SWIPEORValue, setSWIPEORValue] = useState(null);

  const [newSubmerchantErrors, setNewSubmerchantErrors] = useState([{}]);

  const [newReasonErrors, setNewReasonErrors] = useState([{}]);

  const [newCCReasonErrors, setNewCCReasonErrors] = useState([{}]);
  const [newIPWhitelistingErrors, setNewIPWhitelistingErrors] = useState(['']);

  const [newORErrors, setNewORErrors] = useState([{}]);

  const [newSWIPEORErrors, setNewSWIPEORErrors] = useState([{}]);

  //Reports Errors
  const [newDailyGCashErrors, setnewDailyGCashErrors] = useState([{}]);
  const [newMonthlyGCashErrors, setnewMonthlyGCashErrors] = useState([{}]);
  const [newEcpayErrors, setnewEcpayErrors] = useState([{}]);
  const [newGlobeOneErrors, setnewGlobeOneErrors] = useState([{}]);
  const [newCollectionErrors, setnewCollectionErrors] = useState([{}]);
  const [newCreditCardErrors, setnewCreditCardErrors] = useState([{}]);
  const [newChannelErrors, setnewChannelErrors] = useState([{}]);
  const [newBillingErrors, setnewBillingErrors] = useState([{}]);

  useEffect(() => {
    const unblock = history.block(location => {
      if (isLeavingWhileEditing || !hasEdited) return true;
      setNextLocation(location);
      setIsLeavingWhileEditing(true);

      return false;
    });

    return () => {
      unblock();
    };
  }, [isLeavingWhileEditing, hasEdited]);

  const { data, loading, refetch } = useQuery(GET_CONFIGS, {
    fetchPolicy: 'network-only',
  });

  const [selectedSubMerchant, setSelectedSubMerchant] = useState({
    serviceType: '',
  });

  const [selectedReason, setSelectedReason] = useState({
    reason: '',
  });

  const [selectedCCReason, setSelectedCCReason] = useState({
    reason: '',
  });

  const [selectedIPWhitelist, setSelectedIPWhitelist] = useState({
    callback: '',
  });

  const [selectedOR, setSelectedOR] = useState({
    name: '',
  });

  const [selectedSWIPEOR, setSelectedSWIPEOR] = useState({
    name: '',
  });

  //Reports
  const [selectedDailyGCash, setSelectedDailyGCash] = useState({
    email: '',
  });
  const [selectedMonthlyGCash, setSelectedMonthlyGCash] = useState({
    email: '',
  });
  const [selectedEcpay, setSelectedEcpay] = useState({
    email: '',
  });
  const [selectedGlobeOne, setSelectedGlobeOne] = useState({
    email: '',
  });
  const [selectedCollection, setSelectedCollection] = useState({
    email: '',
  });
  const [selectedCreditCard, setSelectedCreditCard] = useState({
    email: '',
  });
  const [selectedChannel, setSelectedChannel] = useState({
    email: '',
  });
  const [selectedBilling, setSelectedBilling] = useState({
    email: '',
  });

  const { data: usedSubMerchantsData, loading: usedSubMerchantsLoading } =
    useQuery(GET_USED_SUBMERCHANT, {
      variables: { id: selectedSubMerchant.serviceType },
      fetchPolicy: 'network-only',
    });

  const [subMerchantDeleteErr, setSubMerchantDeleteErr] = useState(false);

  useEffect(() => {
    if (!usedSubMerchantsLoading && selectedSubMerchant.serviceType) {
      const cantDelete = usedSubMerchantsData.chanelSubMerchants.exist;
      if (!cantDelete) {
        let { serviceType, id } = submerchantErrors;
        delete serviceType[`serviceType-${selectedSubMerchant.serviceType}`];
        delete id[`subMerchantId-${selectedSubMerchant.serviceType}`];

        setSubmerchantErrors({ id, serviceType });
        onChange.subMerchants(
          values.subMerchants.filter(submerchant => {
            return submerchant.serviceType !== selectedSubMerchant.serviceType;
          })
        );
        updateErrors();
      }

      setSubMerchantDeleteErr(cantDelete);
      setSelectedSubMerchant({ serviceType: '' });
    }
  }, [usedSubMerchantsData]);

  useEffect(() => {
    if (selectedReason.reason) {
      onChange.refundReason(
        values.refundReason.filter(reason => {
          return reason.reason !== selectedReason.reason;
        })
      );

      setSelectedReason({ reason: '' });
    }
  });

  useEffect(() => {
    if (selectedCCReason.reason) {
      onChange.cardRefundReason(
        values.cardRefundReason.filter(reason => {
          return reason.reason !== selectedCCReason.reason;
        })
      );

      setSelectedCCReason({ reason: '' });
    }
  });

  useEffect(() => {
    if (selectedIPWhitelist.callback) {
      let arr = values.configIpWhitelist.xendit.callback.filter(ip => {
        return ip !== selectedIPWhitelist.callback;
      });

      onChange.configIpWhitelist({
        xendit: {
          callback: arr,
        },
      });
      setSelectedIPWhitelist({ callback: '' });
    }
  });

  useEffect(() => {
    if (selectedOR.name) {
      onChange.PSORPaymentType(
        values.PSORPaymentType.filter(paymentType => {
          return paymentType.name !== selectedOR.name;
        })
      );

      setSelectedOR({ name: '' });
    }
  });

  useEffect(() => {
    if (selectedSWIPEOR.name) {
      onChange.swipeORPaymentType(
        values.swipeORPaymentType.filter(paymentType => {
          return paymentType.name !== selectedSWIPEOR.name;
        })
      );

      setSelectedSWIPEOR({ name: '' });
    }
  });

  //Reports
  useEffect(() => {
    if (selectedDailyGCash.email) {
      onChange.dailyContentGCashReportRecipient(
        values.dailyContentGCashReportRecipient.filter(dailyContentGCash => {
          return dailyContentGCash.email !== selectedDailyGCash.email;
        })
      );

      setSelectedDailyGCash({ email: '' });
    }
  });
  useEffect(() => {
    if (selectedMonthlyGCash.email) {
      onChange.monthlyContentGCashReportRecipient(
        values.monthlyContentGCashReportRecipient.filter(
          monthlyContentGCash => {
            return monthlyContentGCash.email !== selectedMonthlyGCash.email;
          }
        )
      );

      setSelectedMonthlyGCash({ email: '' });
    }
  });
  useEffect(() => {
    if (selectedEcpay.email) {
      onChange.ecpayReportRecipient(
        values.ecpayReportRecipient.filter(ecpay => {
          return ecpay.email !== selectedEcpay.email;
        })
      );

      setSelectedEcpay({ email: '' });
    }
  });
  useEffect(() => {
    if (selectedGlobeOne.email) {
      onChange.globeOneReportRecipient(
        values.globeOneReportRecipient.filter(globeOne => {
          return globeOne.email !== selectedGlobeOne.email;
        })
      );

      setSelectedGlobeOne({ email: '' });
    }
  });
  useEffect(() => {
    if (selectedCollection.email) {
      onChange.collectionReportRecipient(
        values.collectionReportRecipient.filter(collection => {
          return collection.email !== selectedCollection.email;
        })
      );

      setSelectedCollection({ email: '' });
    }
  });
  useEffect(() => {
    if (selectedCreditCard.email) {
      onChange.creditCardReportRecipient(
        values.creditCardReportRecipient.filter(creditCard => {
          return creditCard.email !== selectedCreditCard.email;
        })
      );

      setSelectedCreditCard({ email: '' });
    }
  });
  useEffect(() => {
    if (selectedChannel.email) {
      onChange.channelReportRecipient(
        values.channelReportRecipient.filter(channel => {
          return channel.email !== selectedChannel.email;
        })
      );

      setSelectedChannel({ email: '' });
    }
  });
  useEffect(() => {
    if (selectedBilling.email) {
      onChange.billingReportRecipient(
        values.billingReportRecipient.filter(billing => {
          return billing.email !== selectedBilling.email;
        })
      );

      setSelectedBilling({ email: '' });
    }
  });

  const [editConfig, { loading: isEditingConfig }] = useMutation(EDIT_CONFIG, {
    onCompleted: () => {
      refetch();
      refetchConfigContext();
      setIsConfirmModalOpen(false);
      setIsSuccessModalOpen(true);
      setNewSubmerchants([
        {
          serviceType: null,
          subMerchantId: null,
          merchant: null,
        },
      ]);
      setNewRefundReason([
        {
          reason: null,
        },
      ]);
      setNewCCRefundReason([
        {
          reason: null,
        },
      ]);
      setNewIPWhitelist({
        xendit: {
          callback: [''],
        },
      });
      setNewORs([
        {
          name: null,
          orVat: null,
          or: null,
          description: null,
        },
      ]);
      setNewSWIPEORs([
        {
          name: null,
          orVat: null,
          or: null,
          description: null,
        },
      ]);
      //Report set to null
      setNewDailyGCash([
        {
          email: null,
        },
      ]);
      setNewMonthlyGCash([
        {
          email: null,
        },
      ]);
      setNewEcpay([
        {
          email: null,
        },
      ]);
      setNewGlobeOne([
        {
          email: null,
        },
      ]);
      setNewCollection([
        {
          email: null,
        },
      ]);
      setNewCreditCard([
        {
          email: null,
        },
      ]);
      setNewChannel([
        {
          email: null,
        },
      ]);
      setNewBilling([
        {
          email: null,
        },
      ]);
    },
    onError: err => {
      setIsConfirmModalOpen(false);
      setIsFailureModalOpen(true);
      setNewErrorMessage(
        err.networkError.result ? err.networkError.result.message : null
      );

      //Report Check Label
      const checkMessageReport =
        err.networkError.result &&
        err.networkError.result.message &&
        err.networkError.result.message[0].message &&
        err.networkError.result.message[0].message.split(' ') &&
        err.networkError.result.message[0].message.split(' ')[0];

      if (checkMessageReport === `"dailyContentGCashReportRecipient"`) {
        setReportLabel('Swipe Daily Content Gcash Report Recipients');
      } else if (
        checkMessageReport === `"monthlyContentGCashReportRecipient"`
      ) {
        setReportLabel('Swipe Monthly Content Gcash Report Recipients');
      } else if (checkMessageReport === `"ecpayReportRecipient"`) {
        setReportLabel('Ecpay Report Recipients');
      } else if (checkMessageReport === `"globeOneReportRecipient"`) {
        setReportLabel('GlobeOne Report Recipients');
      } else if (checkMessageReport === `"collectionReportRecipient"`) {
        setReportLabel('Credit Card Collection Summary Report Recipients');
      } else if (checkMessageReport === `"creditCardReportRecipient"`) {
        setReportLabel('Gateway for Online Payment Report Recipients');
      } else if (checkMessageReport === `"channelReportRecipient"`) {
        setReportLabel('Channel Report Recipients');
      } else if (checkMessageReport === `"billingReportRecipient"`) {
        setReportLabel('Billing Report Recipients');
      }
    },
  });

  const [initialState, setInitialState] = useState({});

  useEffect(() => {
    if (data && data.configs) {
      var initialStateConfigs = data.configs;
      setInitialState(initialStateConfigs);
    }
  }, [data]);

  const { values, onChange, onBlur, errors, onSubmit, isFormValid } = useForm(
    {
      refreshTime: {
        initialValue: initialState.refreshTime,
      },
      paymentServiceApiMaintenance: {
        initialValue:
          data && data.configs && data.configs.paymentServiceApiMaintenance,
      },
      globeEmailNotification: {
        initialValue:
          data && data.configs && data.configs.globeEmailNotification,
      },
      globeEmailNotificationPatternId: {
        initialValue:
          data && data.configs && data.configs.globeEmailNotificationPatternId,
        nullable: false,
        validation: Yup.string()
          .required('Pattern ID for Globe is required')
          .max(5, 'Must not exceed 5 digits')
          .matches(/^[0-9]*$/, 'Must be a number only'),
      },
      innoveEmailNotification: {
        initialValue:
          data && data.configs && data.configs.innoveEmailNotification,
      },
      innoveEmailNotificationPatternId: {
        initialValue:
          data && data.configs && data.configs.innoveEmailNotificationPatternId,
        validation: Yup.string()
          .required('Pattern ID for Innove is required')
          .max(5, 'Must not exceed 5 digits')
          .matches(/^[0-9]*$/, 'Must be a number only'),
      },
      bayanEmailNotification: {
        initialValue:
          data && data.configs && data.configs.bayanEmailNotification,
      },
      bayanEmailNotificationPatternId: {
        initialValue:
          data && data.configs && data.configs.bayanEmailNotificationPatternId,
        validation: Yup.string()
          .required('Pattern ID for Bayan is required')
          .max(5, 'Must not exceed 5 digits')
          .matches(/^[0-9]*$/, 'Must be a number only'),
      },
      subMerchants: {
        initialValue: data && data.configs && data.configs.subMerchants,
      },
      paymentMethodGcash: {
        initialValue: data && data.configs && data.configs.paymentMethodGcash,
      },
      paymentMethodCard: {
        initialValue: data && data.configs && data.configs.paymentMethodCard,
      },
      amaxLoadConsumer: {
        initialValue: data && data.configs && data.configs.amaxLoadConsumer,
        validation: Yup.string()
          .max(5, 'Must not exceed 5 digits')
          .matches(/^[0-9]*$/, 'Must be a number only')
          .nullable(),
      },
      amaxLoadRetailer: {
        initialValue: data && data.configs && data.configs.amaxLoadRetailer,
        validation: Yup.string()
          .max(5, 'Must not exceed 5 digits')
          .matches(/^[0-9]*$/, 'Must be a number only')
          .nullable(),
      },
      gcashRefundRetries: {
        initialValue: data && data.configs && data.configs.gcashRefundRetries,
      },
      refundReason: {
        initialValue: data && data.configs && data.configs.refundReason,
      },
      cardRefundReason: {
        initialValue: data && data.configs && data.configs.cardRefundReason,
      },
      PSORPaymentType: {
        initialValue: data && data.configs && data.configs.PSORPaymentType,
      },
      configIpWhitelist: {
        initialValue: data && data.configs && data.configs.configIpWhitelist,
      },
      swipeORPaymentType: {
        initialValue: data && data.configs && data.configs.swipeORPaymentType,
      },
      dailyContentGCashReportPatternId: {
        initialValue:
          data && data.configs && data.configs.dailyContentGCashReportPatternId,
        validation: Yup.string()
          .required('Swipe Daily Content Gcash Report PatternId is Required')
          .min(1, 'Minimum should be 1 digit')
          .max(5, 'Must not exceed 5 digits')
          .matches(/^[0-9]*$/, 'Must be a number only')
          .nullable(),
      },
      dailyContentGCashReportRecipient: {
        initialValue:
          data && data.configs && data.configs.dailyContentGCashReportRecipient,
      },
      monthlyContentGCashReportPatternId: {
        initialValue:
          data &&
          data.configs &&
          data.configs.monthlyContentGCashReportPatternId,
        validation: Yup.string()
          .required('Swipe Monthly Content Gcash Report PatternId is Required')
          .min(1, 'Minimum should be 1 digit')
          .max(5, 'Must not exceed 5 digits')
          .matches(/^[0-9]*$/, 'Must be a number only')
          .nullable(),
      },
      monthlyContentGCashReportRecipient: {
        initialValue:
          data &&
          data.configs &&
          data.configs.monthlyContentGCashReportRecipient,
      },
      ecpayReportEmailPatternId: {
        initialValue:
          data && data.configs && data.configs.ecpayReportEmailPatternId,
        validation: Yup.string()
          .required('Ecpay Report PatternId is Required')
          .min(1, 'Minimum should be 1 digit')
          .max(5, 'Must not exceed 5 digits')
          .matches(/^[0-9]*$/, 'Must be a number only')
          .nullable(),
      },
      ecpayReportRecipient: {
        initialValue: data && data.configs && data.configs.ecpayReportRecipient,
      },
      globeOneReportEmailPatternId: {
        initialValue:
          data && data.configs && data.configs.globeOneReportEmailPatternId,
        validation: Yup.string()
          .required('GlobeOne Report PatternId is Required')
          .min(1, 'Minimum should be 1 digit')
          .max(5, 'Must not exceed 5 digits')
          .matches(/^[0-9]*$/, 'Must be a number only')
          .nullable(),
      },
      globeOneReportRecipient: {
        initialValue:
          data && data.configs && data.configs.globeOneReportRecipient,
      },
      collectionReportEmailPatternId: {
        initialValue:
          data && data.configs && data.configs.collectionReportEmailPatternId,
        validation: Yup.string()
          .required(
            'Credit Card Collection Summary Report PatternId is Required'
          )
          .min(1, 'Minimum should be 1 digit')
          .max(5, 'Must not exceed 5 digits')
          .matches(/^[0-9]*$/, 'Must be a number only')
          .nullable(),
      },
      collectionReportRecipient: {
        initialValue:
          data && data.configs && data.configs.collectionReportRecipient,
      },
      creditCardReportEmailPatternId: {
        initialValue:
          data && data.configs && data.configs.creditCardReportEmailPatternId,
        validation: Yup.string()
          .required('Gateway for Online Payment PatternId is Required')
          .min(1, 'Minimum should be 1 digit')
          .max(5, 'Must not exceed 5 digits')
          .matches(/^[0-9]*$/, 'Must be a number only')
          .nullable(),
      },
      creditCardReportRecipient: {
        initialValue:
          data && data.configs && data.configs.creditCardReportRecipient,
      },
      channelReportEmailPatternId: {
        initialValue:
          data && data.configs && data.configs.channelReportEmailPatternId,
        validation: Yup.string()
          .required('Channel Report PatternId is Required')
          .min(1, 'Minimum should be 1 digit')
          .max(5, 'Must not exceed 5 digits')
          .matches(/^[0-9]*$/, 'Must be a number only')
          .nullable(),
      },
      channelReportRecipient: {
        initialValue:
          data && data.configs && data.configs.channelReportRecipient,
      },
      billingReportPatternId: {
        initialValue:
          data && data.configs && data.configs.billingReportPatternId,
        validation: Yup.string()
          .required('Billing Report PatternId is Required')
          .min(1, 'Minimum should be 1 digit')
          .max(5, 'Must not exceed 5 digits')
          .matches(/^[0-9]*$/, 'Must be a number only')
          .nullable(),
      },
      billingReportRecipient: {
        initialValue:
          data && data.configs && data.configs.billingReportRecipient,
      },
    },
    values => {
      let newValues = newSubmerchants.filter(
        newValue =>
          newValue.serviceType && newValue.subMerchantId && newValue.merchant
      );

      let newValuesReason = newRefundReason.filter(newValue => newValue.reason);

      let newValuesCCReason = newCCRefundReason.filter(
        newValue => newValue.reason
      );

      let newIPWhitelistCallback = newIPWhitelist.xendit.callback.filter(
        newValue => newValue
      );

      let newValuesOR = newORs.filter(
        newValue =>
          newValue.name && newValue.or && newValue.orVat && newValue.description
      );

      let newValuesSWIPEOR = newSWIPEORs.filter(
        newValue =>
          newValue.name && newValue.or && newValue.orVat && newValue.description
      );

      //Reports
      let newValuesDailyContentGCash = newDailyGCash.filter(
        newValue => newValue.email
      );
      let newValuesMonthlyContentGCash = newMonthlyGCash.filter(
        newValue => newValue.email
      );
      let newValuesEcpay = newEcpay.filter(newValue => newValue.email);
      let newValuesGlobeOne = newGlobeOne.filter(newValue => newValue.email);
      let newValuesCollection = newCollection.filter(
        newValue => newValue.email
      );
      let newValuesCreditCard = newCreditCard.filter(
        newValue => newValue.email
      );
      let newValuesChannel = newChannel.filter(newValue => newValue.email);
      let newValuesBilling = newBilling.filter(newValue => newValue.email);

      editConfig({
        variables: {
          data: {
            ...values,
            refundReason: [
              ...values.refundReason.map(currentVal => {
                return {
                  reason: currentVal.reason,
                };
              }),
              ...newValuesReason,
            ],
            cardRefundReason: [
              ...values.cardRefundReason.map(currentVal => {
                return {
                  reason: currentVal.reason,
                };
              }),
              ...newValuesCCReason,
            ],
            configIpWhitelist: {
              xendit: {
                callback: [
                  ...values.configIpWhitelist.xendit.callback,
                  ...newIPWhitelistCallback,
                ],
              },
            },
            subMerchants: [
              ...values.subMerchants.map(currentVal => {
                return {
                  serviceType: currentVal.serviceType,
                  subMerchantId: currentVal.subMerchantId,
                  merchant: currentVal.merchant,
                };
              }),
              ...newValues,
            ],
            PSORPaymentType: [
              ...values.PSORPaymentType.map(currentVal => {
                return {
                  name: currentVal.name,
                  or: currentVal.or,
                  orVat: currentVal.orVat,
                  description: currentVal.description,
                };
              }),
              ...newValuesOR,
            ],
            swipeORPaymentType: [
              ...values.swipeORPaymentType.map(currentVal => {
                return {
                  name: currentVal.name,
                  or: currentVal.or,
                  orVat: currentVal.orVat,
                  description: currentVal.description,
                };
              }),
              ...newValuesSWIPEOR,
            ],
            //Reports Mutation
            dailyContentGCashReportRecipient: [
              ...values.dailyContentGCashReportRecipient.map(currentVal => {
                return {
                  email: currentVal.email,
                };
              }),
              ...newValuesDailyContentGCash,
            ],
            monthlyContentGCashReportRecipient: [
              ...values.monthlyContentGCashReportRecipient.map(currentVal => {
                return {
                  email: currentVal.email,
                };
              }),
              ...newValuesMonthlyContentGCash,
            ],
            ecpayReportRecipient: [
              ...values.ecpayReportRecipient.map(currentVal => {
                return {
                  email: currentVal.email,
                };
              }),
              ...newValuesEcpay,
            ],
            globeOneReportRecipient: [
              ...values.globeOneReportRecipient.map(currentVal => {
                return {
                  email: currentVal.email,
                };
              }),
              ...newValuesGlobeOne,
            ],
            collectionReportRecipient: [
              ...values.collectionReportRecipient.map(currentVal => {
                return {
                  email: currentVal.email,
                };
              }),
              ...newValuesCollection,
            ],
            creditCardReportRecipient: [
              ...values.creditCardReportRecipient.map(currentVal => {
                return {
                  email: currentVal.email,
                };
              }),
              ...newValuesCreditCard,
            ],
            channelReportRecipient: [
              ...values.channelReportRecipient.map(currentVal => {
                return {
                  email: currentVal.email,
                };
              }),
              ...newValuesChannel,
            ],
            billingReportRecipient: [
              ...values.billingReportRecipient.map(currentVal => {
                return {
                  email: currentVal.email,
                };
              }),
              ...newValuesBilling,
            ],
          },
        },
      });
      setHasEdited(false);
      refetch();
    }
  );

  useEffect(() => {
    const fields = Object.keys(values);

    if (newSubmerchants[0].serviceType && newSubmerchants[0].subMerchantId) {
      setHasEdited(true);
      return;
    }

    if (newRefundReason[0].reason) {
      setHasEdited(true);
      return;
    }

    if (newCCRefundReason[0].reason) {
      setHasEdited(true);
      return;
    }

    if (newIPWhitelist) {
      setHasEdited(true);
      return;
    }

    if (newORs[0].name && newORs[0].or && newORs[0].orVat) {
      setHasEdited(true);
      return;
    }

    if (newSWIPEORs[0].name && newSWIPEORs[0].or && newSWIPEORs[0].orVat) {
      setHasEdited(true);
      return;
    }

    //Reports Has Edited
    if (newDailyGCash[0].email) {
      setHasEdited(true);
      return;
    }

    if (newMonthlyGCash[0].email) {
      setHasEdited(true);
      return;
    }

    if (newEcpay[0].email) {
      setHasEdited(true);
      return;
    }

    if (newGlobeOne[0].email) {
      setHasEdited(true);
      return;
    }

    if (newGlobeOne[0].email) {
      setHasEdited(true);
      return;
    }

    if (newCollection[0].email) {
      setHasEdited(true);
      return;
    }

    if (newCreditCard[0].email) {
      setHasEdited(true);
      return;
    }

    if (newChannel[0].email) {
      setHasEdited(true);
      return;
    }

    if (newBilling[0].email) {
      setHasEdited(true);
      return;
    }

    if (fields.length && !isLeavingWhileEditing) {
      for (const field of fields) {
        if (initialState[field] !== values[field]) {
          setHasEdited(true);
          return;
        }
      }
      setHasEdited(false);
    }
  }, [
    values,
    JSON.stringify(newSubmerchants),
    JSON.stringify(newRefundReason),
    JSON.stringify(newCCRefundReason),
    JSON.stringify(newIPWhitelist),
    JSON.stringify(newORs),
    JSON.stringify(newSWIPEORs),
    JSON.stringify(newDailyGCash),
    JSON.stringify(newMonthlyGCash),
    JSON.stringify(newEcpay),
    JSON.stringify(newGlobeOne),
    JSON.stringify(newCollection),
    JSON.stringify(newCreditCard),
    JSON.stringify(newChannel),
    JSON.stringify(newBilling),
  ]);

  function handleSubMerchantIDValidation(event) {
    const { target } = event;
    Yup.string()
      .max(32, 'Must not exceed 32 characters')
      .matches(/[^-\s]/, 'Must not be a whitespace')
      .matches(
        /^(?![=,@,+,-])(.+)$/,
        'Input must not begin with this special characters (=,@,+,-)'
      )
      .matches(/^[0-9a-zA-Z-]+$/, 'Must be alphanumeric and dashes')
      .required('Please enter value')
      .validate(target.value)
      .then(() => {
        if (submerchantErrors.id[target.name]) {
          const errorsID = submerchantErrors.id;
          delete errorsID[target.name];
          setSubmerchantErrors({
            ...submerchantErrors,
            id: errorsID,
          });
        }
      })
      .catch(err => {
        if (err.errors.length) {
          setSubmerchantErrors({
            ...submerchantErrors,
            id: {
              ...submerchantErrors.id,
              [target.name]: err.errors[0],
            },
          });
        }
      });
  }

  function handleORPaymentTypeValidation(event) {
    const { target } = event;
    Yup.string()
      .required('Please enter value')
      .max(15, 'Must not exceed 15 characters')
      .matches(/[^\s]/, 'Must not be a whitespace')
      .matches(
        /^(?![=,@,+,-])(.+)$/,
        'Input must not begin with this special characters (=,@,+,-)'
      )
      .matches(/^[0-9a-zA-Z-]+$/, 'Must be alphanumeric and dashes')
      .validate(target.value)
      .then(() => {
        if (ORPaymentTypeError.name[target.name]) {
          const errorsID = ORPaymentTypeError.name;
          delete errorsID[target.name];
          setORPaymentTypeError({
            ...ORPaymentTypeError,
            name: errorsID,
          });
        }
      })
      .catch(err => {
        if (err.errors.length) {
          setORPaymentTypeError({
            ...ORPaymentTypeError,
            name: {
              ...ORPaymentTypeError.name,
              [target.name]: err.errors[0],
            },
          });
        }
      });
  }

  function handleORorValidation(event) {
    const { target } = event;
    Yup.string()
      .max(15, 'Must not exceed 15 characters')
      .required('Please enter value')
      .matches(/[^\s]/, 'Must not be a whitespace')
      .matches(
        /^(?![=,@,+,-])(.+)$/,
        'Input must not begin with this special characters (=,@,+,-)'
      )
      .matches(/^[0-9a-zA-Z-]+$/, 'Must be alphanumeric and dashes')
      .validate(target.value)
      .then(() => {
        if (ORPaymentTypeError.or[target.name]) {
          const errorsID = ORPaymentTypeError.or;
          delete errorsID[target.name];
          setORPaymentTypeError({
            ...ORPaymentTypeError,
            or: errorsID,
          });
        }
      })
      .catch(err => {
        if (err.errors.length) {
          setORPaymentTypeError({
            ...ORPaymentTypeError,
            or: {
              ...ORPaymentTypeError.or,
              [target.name]: err.errors[0],
            },
          });
        }
      });
  }

  function handleORorVatValidation(event) {
    const { target } = event;
    Yup.string()
      .max(25, 'Must not exceed 25 characters')
      .required('Please enter value')
      .matches(/[^\s]/, 'Must not be a whitespace')
      .matches(
        /^(?![=,@,+,-])(.+)$/,
        'Input must not begin with this special characters (=,@,+,-)'
      )
      .matches(/^[0-9a-zA-Z-]+$/, 'Must be alphanumeric and dashes')
      .validate(target.value)
      .then(() => {
        if (ORPaymentTypeError.orVat[target.name]) {
          const errorsID = ORPaymentTypeError.orVat;
          delete errorsID[target.name];
          setORPaymentTypeError({
            ...ORPaymentTypeError,
            orVat: errorsID,
          });
        }
      })
      .catch(err => {
        if (err.errors.length) {
          setORPaymentTypeError({
            ...ORPaymentTypeError,
            orVat: {
              ...ORPaymentTypeError.orVat,
              [target.name]: err.errors[0],
            },
          });
        }
      });
  }

  function handleORDescriptionValidation(event) {
    const { target } = event;
    Yup.string()
      .max(25, 'Must not exceed 25 characters')
      .validate(target.value)
      .then(() => {
        if (ORPaymentTypeError.description[target.name]) {
          const errorsID = ORPaymentTypeError.description;
          delete errorsID[target.name];
          setORPaymentTypeError({
            ...ORPaymentTypeError,
            description: errorsID,
          });
        }
      })
      .catch(err => {
        if (err.errors.length) {
          setORPaymentTypeError({
            ...ORPaymentTypeError,
            description: {
              ...ORPaymentTypeError.description,
              [target.name]: err.errors[0],
            },
          });
        }
      });
  }

  function handleSwipeORPaymentTypeValidation(event) {
    const { target } = event;
    Yup.string()
      .required('Please enter value')
      .min(1, 'Minimum should be 1 character')
      .max(25, 'Must not exceed 25 characters')
      .matches(/[^\s]/, 'Must not be a whitespace')
      .matches(
        /^(?![=,@,+,-])(.+)$/,
        'Input must not begin with this special characters (=,@,+,-)'
      )
      .validate(target.value)
      .then(() => {
        if (SWIPEORPaymentTypeError.name[target.name]) {
          const errorsID = SWIPEORPaymentTypeError.name;
          delete errorsID[target.name];
          setSWIPEORPaymentTypeError({
            ...SWIPEORPaymentTypeError,
            name: errorsID,
          });
        }
      })
      .catch(err => {
        if (err.errors.length) {
          setSWIPEORPaymentTypeError({
            ...SWIPEORPaymentTypeError,
            name: {
              ...SWIPEORPaymentTypeError.name,
              [target.name]: err.errors[0],
            },
          });
        }
      });
  }

  function handleSwipeORorValidation(event) {
    const { target } = event;
    Yup.string()
      .min(1, 'Minimum should be 1 character')
      .max(25, 'Must not exceed 25 characters')
      .required('Please enter value')
      .matches(/[^\s]/, 'Must not be a whitespace')
      .matches(
        /^(?![=,@,+,-])(.+)$/,
        'Input must not begin with this special characters (=,@,+,-)'
      )
      .validate(target.value)
      .then(() => {
        if (SWIPEORPaymentTypeError.or[target.name]) {
          const errorsID = SWIPEORPaymentTypeError.or;
          delete errorsID[target.name];
          setSWIPEORPaymentTypeError({
            ...SWIPEORPaymentTypeError,
            or: errorsID,
          });
        }
      })
      .catch(err => {
        if (err.errors.length) {
          setSWIPEORPaymentTypeError({
            ...SWIPEORPaymentTypeError,
            or: {
              ...SWIPEORPaymentTypeError.or,
              [target.name]: err.errors[0],
            },
          });
        }
      });
  }

  function handleSwipeORorVatValidation(event) {
    const { target } = event;
    Yup.string()
      .min(1, 'Minimum should be 1 character')
      .max(25, 'Must not exceed 25 characters')
      .required('Please enter value')
      .matches(/[^\s]/, 'Must not be a whitespace')
      .matches(
        /^(?![=,@,+,-])(.+)$/,
        'Input must not begin with this special characters (=,@,+,-)'
      )
      .validate(target.value)
      .then(() => {
        if (SWIPEORPaymentTypeError.orVat[target.name]) {
          const errorsID = SWIPEORPaymentTypeError.orVat;
          delete errorsID[target.name];
          setSWIPEORPaymentTypeError({
            ...SWIPEORPaymentTypeError,
            orVat: errorsID,
          });
        }
      })
      .catch(err => {
        if (err.errors.length) {
          setSWIPEORPaymentTypeError({
            ...SWIPEORPaymentTypeError,
            orVat: {
              ...SWIPEORPaymentTypeError.orVat,
              [target.name]: err.errors[0],
            },
          });
        }
      });
  }

  function handleSwipeORDescriptionValidation(event) {
    const { target } = event;
    Yup.string()
      .min(1, 'Minimum should be 1 character')
      .max(50, 'Must not exceed 50 characters')
      .validate(target.value)
      .then(() => {
        if (SWIPEORPaymentTypeError.description[target.name]) {
          const errorsID = SWIPEORPaymentTypeError.description;
          delete errorsID[target.name];
          setSWIPEORPaymentTypeError({
            ...SWIPEORPaymentTypeError,
            description: errorsID,
          });
        }
      })
      .catch(err => {
        if (err.errors.length) {
          setSWIPEORPaymentTypeError({
            ...SWIPEORPaymentTypeError,
            description: {
              ...SWIPEORPaymentTypeError.description,
              [target.name]: err.errors[0],
            },
          });
        }
      });
  }

  //Reports Validation
  function handleDailyContentGCashRecipient(event) {
    const { target } = event;
    Yup.string()
      .nullable()
      .matches(
        /^[a-zA-Z0-9]+@(?:[a-zA-Z0-9]+\.)+[A-Za-z]+$/,
        'Must be an email format'
      )
      .validate(target.value)
      .then(() => {
        if (dailyGCashError.email[target.name]) {
          const errorsID = dailyGCashError.email;
          delete errorsID[target.name];
          setDailyGCashError({
            ...dailyGCashError,
            email: errorsID,
          });
        }
      })
      .catch(err => {
        if (err.errors.length) {
          setDailyGCashError({
            ...dailyGCashError,
            email: {
              ...dailyGCashError.email,
              [target.name]: err.errors[0],
            },
          });
        }
      });
  }

  function handleMonthlyContentGCashRecipient(event) {
    const { target } = event;
    Yup.string()
      .nullable()
      .matches(
        /^[a-zA-Z0-9]+@(?:[a-zA-Z0-9]+\.)+[A-Za-z]+$/,
        'Must be an email format'
      )
      .validate(target.value)
      .then(() => {
        if (monthlyGCashError.email[target.name]) {
          const errorsID = monthlyGCashError.email;
          delete errorsID[target.name];
          setMonthlyGCashError({
            ...monthlyGCashError,
            email: errorsID,
          });
        }
      })
      .catch(err => {
        if (err.errors.length) {
          setMonthlyGCashError({
            ...monthlyGCashError,
            email: {
              ...monthlyGCashError.email,
              [target.name]: err.errors[0],
            },
          });
        }
      });
  }

  function handleEcpayRecipient(event) {
    const { target } = event;
    Yup.string()
      .nullable()
      .matches(
        /^[a-zA-Z0-9]+@(?:[a-zA-Z0-9]+\.)+[A-Za-z]+$/,
        'Must be an email format'
      )
      .validate(target.value)
      .then(() => {
        if (ecpayError.email[target.name]) {
          const errorsID = ecpayError.email;
          delete errorsID[target.name];
          setEcpayError({
            ...ecpayError,
            email: errorsID,
          });
        }
      })
      .catch(err => {
        if (err.errors.length) {
          setEcpayError({
            ...ecpayError,
            email: {
              ...ecpayError.email,
              [target.name]: err.errors[0],
            },
          });
        }
      });
  }

  function handleGlobeOneRecipient(event) {
    const { target } = event;
    Yup.string()
      .nullable()
      .matches(
        /^[a-zA-Z0-9]+@(?:[a-zA-Z0-9]+\.)+[A-Za-z]+$/,
        'Must be an email format'
      )
      .validate(target.value)
      .then(() => {
        if (globeOneError.email[target.name]) {
          const errorsID = globeOneError.email;
          delete errorsID[target.name];
          setGlobeOneError({
            ...globeOneError,
            email: errorsID,
          });
        }
      })
      .catch(err => {
        if (err.errors.length) {
          setGlobeOneError({
            ...globeOneError,
            email: {
              ...globeOneError.email,
              [target.name]: err.errors[0],
            },
          });
        }
      });
  }

  function handleCollectionRecipient(event) {
    const { target } = event;
    Yup.string()
      .nullable()
      .matches(
        /^[a-zA-Z0-9]+@(?:[a-zA-Z0-9]+\.)+[A-Za-z]+$/,
        'Must be an email format'
      )
      .validate(target.value)
      .then(() => {
        if (collectionError.email[target.name]) {
          const errorsID = collectionError.email;
          delete errorsID[target.name];
          setCollectionError({
            ...collectionError,
            email: errorsID,
          });
        }
      })
      .catch(err => {
        if (err.errors.length) {
          setCollectionError({
            ...collectionError,
            email: {
              ...collectionError.email,
              [target.name]: err.errors[0],
            },
          });
        }
      });
  }

  function handleCreditCardRecipient(event) {
    const { target } = event;
    Yup.string()
      .nullable()
      .matches(
        /^[a-zA-Z0-9]+@(?:[a-zA-Z0-9]+\.)+[A-Za-z]+$/,
        'Must be an email format'
      )
      .validate(target.value)
      .then(() => {
        if (creditCardError.email[target.name]) {
          const errorsID = creditCardError.email;
          delete errorsID[target.name];
          setCreditCardError({
            ...creditCardError,
            email: errorsID,
          });
        }
      })
      .catch(err => {
        if (err.errors.length) {
          setCreditCardError({
            ...creditCardError,
            email: {
              ...creditCardError.email,
              [target.name]: err.errors[0],
            },
          });
        }
      });
  }

  function handleChannelRecipient(event) {
    const { target } = event;
    Yup.string()
      .nullable()
      .matches(
        /^[a-zA-Z0-9]+@(?:[a-zA-Z0-9]+\.)+[A-Za-z]+$/,
        'Must be an email format'
      )
      .validate(target.value)
      .then(() => {
        if (channelError.email[target.name]) {
          const errorsID = channelError.email;
          delete errorsID[target.name];
          setChannelError({
            ...channelError,
            email: errorsID,
          });
        }
      })
      .catch(err => {
        if (err.errors.length) {
          setChannelError({
            ...channelError,
            email: {
              ...channelError.email,
              [target.name]: err.errors[0],
            },
          });
        }
      });
  }

  function handleBillingRecipient(event) {
    const { target } = event;
    Yup.string()
      .nullable()
      .matches(
        /^[a-zA-Z0-9]+@(?:[a-zA-Z0-9]+\.)+[A-Za-z]+$/,
        'Must be an email format'
      )
      .validate(target.value)
      .then(() => {
        if (billingError.email[target.name]) {
          const errorsID = billingError.email;
          delete errorsID[target.name];
          setBillingError({
            ...billingError,
            email: errorsID,
          });
        }
      })
      .catch(err => {
        if (err.errors.length) {
          setBillingError({
            ...billingError,
            email: {
              ...billingError.email,
              [target.name]: err.errors[0],
            },
          });
        }
      });
  }

  function handleNewSubMerchantIDValidation(event, index) {
    const { target } = event;
    Yup.string()
      .max(32, 'Must not exceed 32 characters')
      .matches(/[^-\s]/, 'Must not be a whitespace')
      .matches(
        /^(?![=,@,+,-])(.+)$/,
        'Input must not begin with this special characters (=,@,+,-)'
      )
      .matches(/^[0-9a-zA-Z-]+$/, 'Must be alphanumeric and dashes')
      .required('Please enter value')
      .validate(target.value)
      .then(() => {
        if (newSubmerchantErrors[index].subMerchantId) {
          const submerchantError = newSubmerchantErrors.map((val, valIndex) => {
            if (valIndex === index) {
              delete val.subMerchantId;
            }

            return val;
          });

          setNewSubmerchantErrors(submerchantError);
        }
      })
      .catch(err => {
        if (err.errors.length) {
          const submerchantError = newSubmerchantErrors.map((val, valIndex) => {
            if (valIndex === index) {
              return {
                ...val,
                subMerchantId: err.errors[0],
              };
            }

            return val;
          });

          setNewSubmerchantErrors(submerchantError);
        }
      });
  }

  function handleNewMerchantValidation(event) {
    const { target } = event;
    Yup.string()
      .validate(target.value)
      .then(() => {
        if (submerchantErrors.id[target.name]) {
          const errorsID = submerchantErrors.id;
          delete errorsID[target.name];
          setSubmerchantErrors({
            ...submerchantErrors,
            id: errorsID,
          });
        }
      })
      .catch(err => {
        if (err.errors.length) {
          setSubmerchantErrors({
            ...submerchantErrors,
            id: {
              ...submerchantErrors.id,
              [target.name]: err.errors[0],
            },
          });
        }
      });
  }

  function handleNewServiceTypeValidation(event, index) {
    const { target } = event;
    const existsInCurrent = values.subMerchants.find(
      value => value.serviceType === target.value && value.merchant
    );
    const existsInNew = newSubmerchants.find(
      (value, valIndex) =>
        value.serviceType === target.value && valIndex !== index
    );

    let newErrors = newSubmerchantErrors;

    if (existsInCurrent) {
      setNewSubmerchantErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              serviceType: 'Service type already exists',
            };
          }

          return val;
        })
      );

      return;
    }

    if (existsInNew) {
      setNewSubmerchantErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              serviceType: 'Duplicate service type',
            };
          }

          return val;
        })
      );

      return;
    }

    Yup.string()
      .max(32, 'Must not exceed 32 characters')
      .matches(/[^-\s]/, 'Must not be a whitespace')
      .matches(
        /^(?![=,@,+,-])(.+)$/,
        'Input must not begin with this special characters (=,@,+,-)'
      )
      .matches(/^[0-9a-zA-Z-]+$/, 'Must be alphanumeric and dashes')
      .required('Please enter value')
      .validate(target.value)
      .then(() => {
        if (newSubmerchantErrors[index].serviceType) {
          const submerchantError = newSubmerchantErrors.map((val, valIndex) => {
            if (valIndex === index) {
              delete val.serviceType;
            }

            return val;
          });

          setNewSubmerchantErrors(submerchantError);
        }
      })
      .catch(err => {
        if (err.errors.length) {
          const submerchantError = newSubmerchantErrors.map((val, valIndex) => {
            if (valIndex === index) {
              return {
                ...val,
                serviceType: err.errors[0],
              };
            }

            return val;
          });

          setNewSubmerchantErrors(submerchantError);
        }
      });
  }

  function handleNewReasonValidation(event, index) {
    const { target } = event;
    const existsInCurrent = values.refundReason.find(
      value => value.reason === target.value && value
    );
    const existsInNew = newRefundReason.find(
      (value, valIndex) => value.reason === target.value && valIndex !== index
    );

    let newErrors = newReasonErrors;

    if (existsInCurrent) {
      setNewReasonErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              reason: 'Reason already exists',
            };
          }

          return val;
        })
      );

      return;
    }

    if (existsInNew) {
      setNewReasonErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              reason: 'Duplicate reason',
            };
          }

          return val;
        })
      );

      return;
    }

    Yup.string()
      .min(3, 'Minimum should be 3 characters')
      .max(50, 'Must not exceed 50 characters')
      .matches(
        /^(?![=,@,+,-])(.+)$/,
        'Input must not begin with this special characters (=,@,+,-)'
      )
      .matches(
        /^[a-z0-9]+([-_\s]{1}[a-z0-9]+)*$/i,
        'Must be alphanumeric and whitespace or dashes in between'
      )
      .required('Please enter value')
      .validate(target.value)
      .then(() => {
        if (newReasonErrors[index].reason) {
          const reasonError = newReasonErrors.map((val, valIndex) => {
            if (valIndex === index) {
              delete val.reason;
            }

            return val;
          });

          setNewReasonErrors(reasonError);
        }
      })
      .catch(err => {
        if (err.errors.length) {
          const reasonError = newReasonErrors.map((val, valIndex) => {
            if (valIndex === index) {
              return {
                ...val,
                reason: err.errors[0],
              };
            }

            return val;
          });

          setNewReasonErrors(reasonError);
        }
      });
  }

  function handleNewCCReasonValidation(event, index) {
    const { target } = event;
    const existsInCurrent =
      values.cardRefundReason &&
      values.cardRefundReason.find(
        value => value.reason === target.value && value
      );
    const existsInNew = newCCRefundReason.find(
      (value, valIndex) => value.reason === target.value && valIndex !== index
    );

    let newErrors = newCCReasonErrors;

    if (existsInCurrent) {
      setNewCCReasonErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              reason: 'Reason already exists',
            };
          }

          return val;
        })
      );

      return;
    }

    if (existsInNew) {
      setNewCCReasonErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              reason: 'Duplicate reason',
            };
          }

          return val;
        })
      );

      return;
    }

    Yup.string()
      .min(3, 'Minimum should be 3 characters')
      .max(50, 'Must not exceed 50 characters')
      .matches(
        /^(?![=,@,+,-])(.+)$/,
        'Input must not begin with this special characters (=,@,+,-)'
      )
      .matches(
        /^[a-z0-9]+([-_\s]{1}[a-z0-9]+)*$/i,
        'Must be alphanumeric and whitespace or dashes in between'
      )
      .required('Please enter value')
      .validate(target.value)
      .then(() => {
        if (newCCReasonErrors[index].reason) {
          const ccReasonError = newCCReasonErrors.map((val, valIndex) => {
            if (valIndex === index) {
              delete val.reason;
            }

            return val;
          });

          setNewCCReasonErrors(ccReasonError);
        }
      })
      .catch(err => {
        if (err.errors.length) {
          const ccReasonError = newCCReasonErrors.map((val, valIndex) => {
            if (valIndex === index) {
              return {
                ...val,
                reason: err.errors[0],
              };
            }

            return val;
          });

          setNewCCReasonErrors(ccReasonError);
        }
      });
  }

  function handleNewIPWhitelistValidation(event, index) {
    const { target } = event;
    if (target.value.trim().length === 0) return;
    const existsInCurrent =
      values.configIpWhitelist &&
      values.configIpWhitelist.xendit &&
      values.configIpWhitelist.xendit.callback &&
      values.configIpWhitelist.xendit.callback.find(
        value => value === target.value && value
      );
    const existsInNew = newIPWhitelist.xendit.callback.find(
      (value, valIndex) => value === target.value && valIndex !== index
    );

    let newErrors = newIPWhitelistingErrors;
    if (existsInCurrent) {
      let errArr = newErrors.map((val, valIndex) => {
        if (valIndex === index) {
          return 'Duplicate IP Address';
        }

        return val;
      });
      setNewIPWhitelistingErrors(errArr);

      return;
    }

    if (existsInNew) {
      let errArr = newErrors.map((val, valIndex) => {
        if (valIndex === index) {
          return 'Duplicate IP Address';
        }

        return val;
      });
      setNewIPWhitelistingErrors(errArr);

      return;
    }

    Yup.string()
      .max(50, 'Must not exceed 50 characters')
      .matches(
        /^((\d\d?|1\d\d|2([0-4]\d|5[0-5]))\.){3}(\d\d?|1\d\d|2([0-4]\d|5[0-5]))(\/[0-9]+)?$/i,
        'Must be a valid IP address'
      )
      .validate(target.value)
      .then(() => {
        if (newIPWhitelistingErrors[index]) {
          const IPWhitelistingError = newIPWhitelistingErrors.map(
            (val, valIndex) => {
              if (valIndex === index) {
                return '';
              }

              return val;
            }
          );

          setNewIPWhitelistingErrors(IPWhitelistingError);
        }
      })
      .catch(err => {
        if (err.errors.length) {
          const IPWhitelistingError = newIPWhitelistingErrors.map(
            (val, valIndex) => {
              if (valIndex === index) {
                return err.errors[0];
              }

              return val;
            }
          );

          setNewIPWhitelistingErrors(IPWhitelistingError);
        }
      });
  }

  function handleNewPaymentTypeValidation(event, index) {
    const { target } = event;
    Yup.string()
      .max(15, 'Must not exceed 15 characters')
      .required('Please enter value')
      .matches(/[^\s]/, 'Must not be a whitespace')
      .matches(
        /^(?![=,@,+,-])(.+)$/,
        'Input must not begin with this special characters (=,@,+,-)'
      )
      .matches(/^[0-9a-zA-Z-]+$/, 'Must be alphanumeric and dashes')
      .validate(target.value)
      .then(() => {
        if (newORErrors[index].name) {
          const ORError = newORErrors.map((val, valIndex) => {
            if (valIndex === index) {
              delete val.name;
            }

            return val;
          });

          setNewORErrors(ORError);
        }
      })
      .catch(err => {
        if (err.errors.length) {
          const ORError = newORErrors.map((val, valIndex) => {
            if (valIndex === index) {
              return {
                ...val,
                name: err.errors[0],
              };
            }

            return val;
          });

          setNewORErrors(ORError);
        }
      });
  }

  function handleNewORValidation(event, index) {
    const { target } = event;
    const existsInCurrent = values.PSORPaymentType.find(
      value => value.or === target.value
    );
    const existsInNew = newORs.find(
      (value, valIndex) => value.or === target.value && valIndex !== index
    );

    let newErrors = newORErrors;

    if (existsInCurrent) {
      setNewORErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              or: 'OR already exists',
            };
          }

          return val;
        })
      );

      return;
    }

    if (existsInNew) {
      setNewORErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              or: 'Duplicate OR',
            };
          }

          return val;
        })
      );

      return;
    }

    Yup.string()
      .max(15, 'Must not exceed 15 characters')
      .required('Please enter value')
      .matches(/[^\s]/, 'Must not be a whitespace')
      .matches(
        /^(?![=,@,+,-])(.+)$/,
        'Input must not begin with this special characters (=,@,+,-)'
      )
      .matches(/^[0-9a-zA-Z-]+$/, 'Must be alphanumeric and dashes')
      .validate(target.value)
      .then(() => {
        if (newORErrors[index].or) {
          const ORError = newORErrors.map((val, valIndex) => {
            if (valIndex === index) {
              delete val.or;
            }

            return val;
          });

          setNewORErrors(ORError);
        }
      })
      .catch(err => {
        if (err.errors.length) {
          const ORError = newORErrors.map((val, valIndex) => {
            if (valIndex === index) {
              return {
                ...val,
                or: err.errors[0],
              };
            }

            return val;
          });

          setNewORErrors(ORError);
        }
      });
  }

  function handleNewORVATValidation(event, index) {
    const { target } = event;
    Yup.string()
      .max(25, 'Must not exceed 25 characters')
      .required('Please enter value')
      .matches(/[^\s]/, 'Must not be a whitespace')
      .matches(
        /^(?![=,@,+,-])(.+)$/,
        'Input must not begin with this special characters (=,@,+,-)'
      )
      .matches(/^[0-9a-zA-Z-]+$/, 'Must be alphanumeric and dashes')
      .validate(target.value)
      .then(() => {
        if (newORErrors[index].orVat) {
          const ORError = newORErrors.map((val, valIndex) => {
            if (valIndex === index) {
              delete val.orVat;
            }

            return val;
          });

          setNewORErrors(ORError);
        }
      })
      .catch(err => {
        if (err.errors.length) {
          const ORError = newORErrors.map((val, valIndex) => {
            if (valIndex === index) {
              return {
                ...val,
                orVat: err.errors[0],
              };
            }

            return val;
          });

          setNewORErrors(ORError);
        }
      });
  }

  function handleNewORDescriptionValidation(event, index) {
    const { target } = event;
    Yup.string()
      .max(25, 'Must not exceed 25 characters')
      .validate(target.value)
      .then(() => {
        if (newORErrors[index].description) {
          const ORError = newORErrors.map((val, valIndex) => {
            if (valIndex === index) {
              delete val.description;
            }

            return val;
          });

          setNewORErrors(ORError);
        }
      })
      .catch(err => {
        if (err.errors.length) {
          const ORError = newORErrors.map((val, valIndex) => {
            if (valIndex === index) {
              return {
                ...val,
                description: err.errors[0],
              };
            }

            return val;
          });

          setNewORErrors(ORError);
        }
      });
  }

  function handleNewSwipePaymentTypeValidation(event, index) {
    const { target } = event;
    const existsInCurrent = values.swipeORPaymentType.find(
      value => value.name === target.value
    );
    const existsInNew = newSWIPEORs.find(
      (value, valIndex) => value.name === target.value && valIndex !== index
    );

    let newErrors = newSWIPEORErrors;

    if (existsInCurrent) {
      setNewSWIPEORErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              name: 'Payment type already exists',
            };
          }

          return val;
        })
      );

      return;
    }

    if (existsInNew) {
      setNewSWIPEORErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              name: 'Duplicate payment type',
            };
          }

          return val;
        })
      );

      return;
    }

    Yup.string()
      .min(1, 'Minimum should be 1 character')
      .max(25, 'Must not exceed 25 characters')
      .required('Please enter value')
      .matches(/[^\s]/, 'Must not be a whitespace')
      .matches(
        /^(?![=,@,+,-])(.+)$/,
        'Input must not begin with this special characters (=,@,+,-)'
      )
      .validate(target.value)
      .then(() => {
        if (newSWIPEORErrors[index].name) {
          const ORError = newSWIPEORErrors.map((val, valIndex) => {
            if (valIndex === index) {
              delete val.name;
            }

            return val;
          });

          setNewSWIPEORErrors(ORError);
        }
      })
      .catch(err => {
        if (err.errors.length) {
          const ORError = newSWIPEORErrors.map((val, valIndex) => {
            if (valIndex === index) {
              return {
                ...val,
                name: err.errors[0],
              };
            }

            return val;
          });

          setNewSWIPEORErrors(ORError);
        }
      });
  }

  function handleNewSwipeORValidation(event, index) {
    const { target } = event;
    Yup.string()
      .min(1, 'Minimum should be 1 character')
      .max(25, 'Must not exceed 25 characters')
      .required('Please enter value')
      .matches(/[^\s]/, 'Must not be a whitespace')
      .matches(
        /^(?![=,@,+,-])(.+)$/,
        'Input must not begin with this special characters (=,@,+,-)'
      )
      .validate(target.value)
      .then(() => {
        if (newSWIPEORErrors[index].or) {
          const ORError = newSWIPEORErrors.map((val, valIndex) => {
            if (valIndex === index) {
              delete val.or;
            }

            return val;
          });

          setNewSWIPEORErrors(ORError);
        }
      })
      .catch(err => {
        if (err.errors.length) {
          const ORError = newSWIPEORErrors.map((val, valIndex) => {
            if (valIndex === index) {
              return {
                ...val,
                or: err.errors[0],
              };
            }

            return val;
          });

          setNewSWIPEORErrors(ORError);
        }
      });
  }

  function handleNewSwipeORVATValidation(event, index) {
    const { target } = event;
    Yup.string()
      .min(1, 'Minimum should be 1 character')
      .max(25, 'Must not exceed 25 characters')
      .required('Please enter value')
      .matches(/[^\s]/, 'Must not be a whitespace')
      .matches(
        /^(?![=,@,+,-])(.+)$/,
        'Input must not begin with this special characters (=,@,+,-)'
      )
      .validate(target.value)
      .then(() => {
        if (newSWIPEORErrors[index].orVat) {
          const ORError = newSWIPEORErrors.map((val, valIndex) => {
            if (valIndex === index) {
              delete val.orVat;
            }

            return val;
          });

          setNewSWIPEORErrors(ORError);
        }
      })
      .catch(err => {
        if (err.errors.length) {
          const ORError = newSWIPEORErrors.map((val, valIndex) => {
            if (valIndex === index) {
              return {
                ...val,
                orVat: err.errors[0],
              };
            }

            return val;
          });

          setNewSWIPEORErrors(ORError);
        }
      });
  }

  function handleNewSwipeORDescriptionValidation(event, index) {
    const { target } = event;
    Yup.string()
      .min(1, 'Minimum should be 1 character')
      .max(50, 'Must not exceed 50 characters')
      .validate(target.value)
      .then(() => {
        if (newSWIPEORErrors[index].description) {
          const ORError = newSWIPEORErrors.map((val, valIndex) => {
            if (valIndex === index) {
              delete val.description;
            }

            return val;
          });

          setNewSWIPEORErrors(ORError);
        }
      })
      .catch(err => {
        if (err.errors.length) {
          const ORError = newSWIPEORErrors.map((val, valIndex) => {
            if (valIndex === index) {
              return {
                ...val,
                description: err.errors[0],
              };
            }

            return val;
          });

          setNewSWIPEORErrors(ORError);
        }
      });
  }

  //Reports new Recipient Validation
  function handleNewDailyContentGCashRecipient(event, index) {
    const { target } = event;
    const existsInCurrent = values.dailyContentGCashReportRecipient.find(
      value => value.email === target.value
    );
    const existsInNew = newDailyGCash.find(
      (value, valIndex) => value.email === target.value && valIndex !== index
    );

    let newErrors = newDailyGCashErrors;

    if (existsInCurrent) {
      setnewDailyGCashErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              email: 'Email already exists',
            };
          }

          return val;
        })
      );

      return;
    }

    if (existsInNew) {
      setnewDailyGCashErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              email: 'Duplicate email',
            };
          }

          return val;
        })
      );

      return;
    }

    Yup.string()
      .nullable()
      .matches(
        /^[a-zA-Z0-9]+@(?:[a-zA-Z0-9]+\.)+[A-Za-z]+$/,
        'Must be an email format'
      )
      .validate(target.value)
      .then(() => {
        if (newDailyGCashErrors[index].email) {
          const dailyContentGCashError = newDailyGCashErrors.map(
            (val, valIndex) => {
              if (valIndex === index) {
                delete val.email;
              }

              return val;
            }
          );

          setnewDailyGCashErrors(dailyContentGCashError);
        }
      })
      .catch(err => {
        if (err.errors.length) {
          const dailyContentGCashError = newDailyGCashErrors.map(
            (val, valIndex) => {
              if (valIndex === index) {
                return {
                  ...val,
                  email: err.errors[0],
                };
              }

              return val;
            }
          );

          setnewDailyGCashErrors(dailyContentGCashError);
        }
      });
  }

  function handleNewMonthlyContentGCashRecipient(event, index) {
    const { target } = event;
    const existsInCurrent = values.monthlyContentGCashReportRecipient.find(
      value => value.email === target.value
    );
    const existsInNew = newMonthlyGCash.find(
      (value, valIndex) => value.email === target.value && valIndex !== index
    );

    let newErrors = newMonthlyGCashErrors;

    if (existsInCurrent) {
      setnewMonthlyGCashErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              email: 'Email already exists',
            };
          }

          return val;
        })
      );

      return;
    }

    if (existsInNew) {
      setnewMonthlyGCashErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              email: 'Duplicate email',
            };
          }

          return val;
        })
      );

      return;
    }

    Yup.string()
      .nullable()
      .matches(
        /^[a-zA-Z0-9]+@(?:[a-zA-Z0-9]+\.)+[A-Za-z]+$/,
        'Must be an email format'
      )
      .validate(target.value)
      .then(() => {
        if (newMonthlyGCashErrors[index].email) {
          const monthlyContentGCashError = newMonthlyGCashErrors.map(
            (val, valIndex) => {
              if (valIndex === index) {
                delete val.email;
              }

              return val;
            }
          );

          setnewMonthlyGCashErrors(monthlyContentGCashError);
        }
      })
      .catch(err => {
        if (err.errors.length) {
          const monthlyContentGCashError = newMonthlyGCashErrors.map(
            (val, valIndex) => {
              if (valIndex === index) {
                return {
                  ...val,
                  email: err.errors[0],
                };
              }

              return val;
            }
          );

          setnewMonthlyGCashErrors(monthlyContentGCashError);
        }
      });
  }

  function handleNewEcpayRecipient(event, index) {
    const { target } = event;
    const existsInCurrent = values.ecpayReportRecipient.find(
      value => value.email === target.value
    );
    const existsInNew = newEcpay.find(
      (value, valIndex) => value.email === target.value && valIndex !== index
    );

    let newErrors = newEcpayErrors;

    if (existsInCurrent) {
      setnewEcpayErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              email: 'Email already exists',
            };
          }

          return val;
        })
      );

      return;
    }

    if (existsInNew) {
      setnewEcpayErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              email: 'Duplicate email',
            };
          }

          return val;
        })
      );

      return;
    }

    Yup.string()
      .nullable()
      .matches(
        /^[a-zA-Z0-9]+@(?:[a-zA-Z0-9]+\.)+[A-Za-z]+$/,
        'Must be an email format'
      )
      .validate(target.value)
      .then(() => {
        if (newEcpayErrors[index].email) {
          const ecpayError = newEcpayErrors.map((val, valIndex) => {
            if (valIndex === index) {
              delete val.email;
            }

            return val;
          });

          setnewEcpayErrors(ecpayError);
        }
      })
      .catch(err => {
        if (err.errors.length) {
          const ecpayError = newEcpayErrors.map((val, valIndex) => {
            if (valIndex === index) {
              return {
                ...val,
                email: err.errors[0],
              };
            }

            return val;
          });

          setnewEcpayErrors(ecpayError);
        }
      });
  }

  function handleNewGlobeOneRecipient(event, index) {
    const { target } = event;
    const existsInCurrent = values.globeOneReportRecipient.find(
      value => value.email === target.value
    );
    const existsInNew = newGlobeOne.find(
      (value, valIndex) => value.email === target.value && valIndex !== index
    );

    let newErrors = newGlobeOneErrors;

    if (existsInCurrent) {
      setnewGlobeOneErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              email: 'Email already exists',
            };
          }

          return val;
        })
      );

      return;
    }

    if (existsInNew) {
      setnewGlobeOneErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              email: 'Duplicate email',
            };
          }

          return val;
        })
      );

      return;
    }

    Yup.string()
      .nullable()
      .matches(
        /^[a-zA-Z0-9]+@(?:[a-zA-Z0-9]+\.)+[A-Za-z]+$/,
        'Must be an email format'
      )
      .validate(target.value)
      .then(() => {
        if (newGlobeOneErrors[index].email) {
          const globeOneError = newGlobeOneErrors.map((val, valIndex) => {
            if (valIndex === index) {
              delete val.email;
            }

            return val;
          });

          setnewGlobeOneErrors(globeOneError);
        }
      })
      .catch(err => {
        if (err.errors.length) {
          const globeOneError = newGlobeOneErrors.map((val, valIndex) => {
            if (valIndex === index) {
              return {
                ...val,
                email: err.errors[0],
              };
            }

            return val;
          });

          setnewGlobeOneErrors(globeOneError);
        }
      });
  }

  function handleNewCollectionRecipient(event, index) {
    const { target } = event;
    const existsInCurrent = values.collectionReportRecipient.find(
      value => value.email === target.value
    );
    const existsInNew = newCollection.find(
      (value, valIndex) => value.email === target.value && valIndex !== index
    );

    let newErrors = newCollectionErrors;

    if (existsInCurrent) {
      setnewCollectionErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              email: 'Email already exists',
            };
          }

          return val;
        })
      );

      return;
    }

    if (existsInNew) {
      setnewCollectionErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              email: 'Duplicate email',
            };
          }

          return val;
        })
      );

      return;
    }

    Yup.string()
      .nullable()
      .matches(
        /^[a-zA-Z0-9]+@(?:[a-zA-Z0-9]+\.)+[A-Za-z]+$/,
        'Must be an email format'
      )
      .validate(target.value)
      .then(() => {
        if (newCollectionErrors[index].email) {
          const collectionError = newCollectionErrors.map((val, valIndex) => {
            if (valIndex === index) {
              delete val.email;
            }

            return val;
          });

          setnewCollectionErrors(collectionError);
        }
      })
      .catch(err => {
        if (err.errors.length) {
          const collectionError = newCollectionErrors.map((val, valIndex) => {
            if (valIndex === index) {
              return {
                ...val,
                email: err.errors[0],
              };
            }

            return val;
          });

          setnewCollectionErrors(collectionError);
        }
      });
  }

  function handleNewCreditCardRecipient(event, index) {
    const { target } = event;
    const existsInCurrent = values.creditCardReportRecipient.find(
      value => value.email === target.value
    );
    const existsInNew = newCreditCard.find(
      (value, valIndex) => value.email === target.value && valIndex !== index
    );

    let newErrors = newCreditCardErrors;

    if (existsInCurrent) {
      setnewCreditCardErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              email: 'Email already exists',
            };
          }

          return val;
        })
      );

      return;
    }

    if (existsInNew) {
      setnewCreditCardErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              email: 'Duplicate email',
            };
          }

          return val;
        })
      );

      return;
    }

    Yup.string()
      .nullable()
      .matches(
        /^[a-zA-Z0-9]+@(?:[a-zA-Z0-9]+\.)+[A-Za-z]+$/,
        'Must be an email format'
      )
      .validate(target.value)
      .then(() => {
        if (newCreditCardErrors[index].email) {
          const creditCardError = newCreditCardErrors.map((val, valIndex) => {
            if (valIndex === index) {
              delete val.email;
            }

            return val;
          });

          setnewCreditCardErrors(creditCardError);
        }
      })
      .catch(err => {
        if (err.errors.length) {
          const creditCardError = newCreditCardErrors.map((val, valIndex) => {
            if (valIndex === index) {
              return {
                ...val,
                email: err.errors[0],
              };
            }

            return val;
          });

          setnewCreditCardErrors(creditCardError);
        }
      });
  }

  function handleNewChannelRecipient(event, index) {
    const { target } = event;
    const existsInCurrent = values.channelReportRecipient.find(
      value => value.email === target.value
    );
    const existsInNew = newChannel.find(
      (value, valIndex) => value.email === target.value && valIndex !== index
    );

    let newErrors = newChannelErrors;

    if (existsInCurrent) {
      setnewChannelErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              email: 'Email already exists',
            };
          }

          return val;
        })
      );

      return;
    }

    if (existsInNew) {
      setnewChannelErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              email: 'Duplicate email',
            };
          }

          return val;
        })
      );

      return;
    }

    Yup.string()
      .nullable()
      .matches(
        /^[a-zA-Z0-9]+@(?:[a-zA-Z0-9]+\.)+[A-Za-z]+$/,
        'Must be an email format'
      )
      .validate(target.value)
      .then(() => {
        if (newChannelErrors[index].email) {
          const channelError = newChannelErrors.map((val, valIndex) => {
            if (valIndex === index) {
              delete val.email;
            }

            return val;
          });

          setnewChannelErrors(channelError);
        }
      })
      .catch(err => {
        if (err.errors.length) {
          const channelError = newChannelErrors.map((val, valIndex) => {
            if (valIndex === index) {
              return {
                ...val,
                email: err.errors[0],
              };
            }

            return val;
          });

          setnewChannelErrors(channelError);
        }
      });
  }

  function handleNewBillingRecipient(event, index) {
    const { target } = event;
    const existsInCurrent = values.billingReportRecipient.find(
      value => value.email === target.value
    );
    const existsInNew = newBilling.find(
      (value, valIndex) => value.email === target.value && valIndex !== index
    );

    let newErrors = newBillingErrors;

    if (existsInCurrent) {
      setnewBillingErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              email: 'Email already exists',
            };
          }

          return val;
        })
      );

      return;
    }

    if (existsInNew) {
      setnewBillingErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              email: 'Duplicate email',
            };
          }

          return val;
        })
      );

      return;
    }

    Yup.string()
      .nullable()
      .matches(
        /^[a-zA-Z0-9]+@(?:[a-zA-Z0-9]+\.)+[A-Za-z]+$/,
        'Must be an email format'
      )
      .validate(target.value)
      .then(() => {
        if (newBillingErrors[index].email) {
          const billingError = newBillingErrors.map((val, valIndex) => {
            if (valIndex === index) {
              delete val.email;
            }

            return val;
          });

          setnewBillingErrors(billingError);
        }
      })
      .catch(err => {
        if (err.errors.length) {
          const billingError = newBillingErrors.map((val, valIndex) => {
            if (valIndex === index) {
              return {
                ...val,
                email: err.errors[0],
              };
            }

            return val;
          });

          setnewBillingErrors(billingError);
        }
      });
  }

  function updateErrors() {
    setNewSubmerchantErrors(
      newSubmerchantErrors.map((value, index) => {
        if (
          values.subMerchants.find(
            submerchant => submerchant.subMerchantId === value.subMerchantId
          )
        ) {
          return {
            ...value,
            serviceType: 'Service type already exists',
          };
        } else if (
          newSubmerchants.find(
            (submerchant, valIndex) =>
              submerchant.serviceType === value.serviceType &&
              valIndex !== index
          )
        ) {
          return {
            ...value,
            serviceType: 'Duplicate service type',
          };
        } else if (
          value.serviceType === 'Service type already exists' ||
          value.serviceType === 'Duplicate service type'
        ) {
          delete value.serviceType;
        }

        return value;
      })
    );
  }

  return (
    <>
      <Page>
        <Header
          withHome
          path={['System', 'Configuration']}
          title="System Configuration"
        />
        <DataContainer loading={loading}>
          {loading && <Loader fullPage />}
          {!loading && (
            <>
              <DataHeader>
                <DataHeader.Title>WebTool Dashboard</DataHeader.Title>
              </DataHeader>
              <PageSubsection>
                <FormField
                  placeholder=""
                  label="Refresh Time Settings"
                  name="refreshTime"
                  type={FIELD_TYPES.SELECT}
                  value={values.refreshTime}
                  onChange={onChange.refreshTime}
                  onBlur={onBlur.refreshTime}
                  error={errors.refreshTime}
                  options={[
                    { label: '1 minute', value: '1' },
                    { label: '5 minutes', value: '5' },
                    { label: '10 minutes', value: '10' },
                    { label: '15 minutes', value: '15' },
                    { label: '20 minutes', value: '20' },
                    { label: '25 minutes', value: '25' },
                    { label: '30 minutes', value: '30' },
                    { label: '60 minutes', value: '60' },
                  ]}
                  perRow={2}
                  required
                />
              </PageSubsection>
              <DataHeader>
                <DataHeader.Title>Payment API Service</DataHeader.Title>
              </DataHeader>
              <PageSubsection>
                <FormField
                  placeholder=""
                  label="Status"
                  name="paymentServiceApiMaintenance"
                  type={FIELD_TYPES.TOGGLE}
                  value={values.paymentServiceApiMaintenance}
                  onChange={onChange.paymentServiceApiMaintenance}
                  onBlur={onBlur.paymentServiceApiMaintenance}
                  options={APIServiceMaintenanceMode}
                  error={errors.paymentServiceApiMaintenance}
                  perRow={2}
                  required
                />
              </PageSubsection>
              <DataHeader>
                <DataHeader.Title>Email Notification</DataHeader.Title>
              </DataHeader>
              <PageSubsection>
                <FormField
                  placeholder="Pattern ID"
                  label="Globe Email Notification Pattern ID"
                  name="globeEmailNotificationPatternId"
                  type={FIELD_TYPES.TEXT}
                  value={values.globeEmailNotificationPatternId}
                  onChange={onChange.globeEmailNotificationPatternId}
                  onBlur={onBlur.globeEmailNotificationPatternId}
                  error={errors.globeEmailNotificationPatternId}
                  perRow={2}
                  required
                />
                <FormField
                  placeholder=""
                  label="Globe Email Notification Status"
                  name="globeEmailNotification"
                  type={FIELD_TYPES.TOGGLE}
                  value={values.globeEmailNotification}
                  onChange={onChange.globeEmailNotification}
                  onBlur={onBlur.globeEmailNotification}
                  error={errors.globeEmailNotification}
                  options={EmailNotificationMode}
                  perRow={2}
                  required
                />
                <FormField
                  placeholder="Pattern ID"
                  label="Innove Email Notification Pattern ID"
                  name="innoveEmailNotificationPatternId"
                  type={FIELD_TYPES.TEXT}
                  value={values.innoveEmailNotificationPatternId}
                  onChange={onChange.innoveEmailNotificationPatternId}
                  onBlur={onBlur.innoveEmailNotificationPatternId}
                  error={errors.innoveEmailNotificationPatternId}
                  perRow={2}
                  required
                />
                <FormField
                  placeholder=""
                  label="Innove Email Notification Status"
                  name="innoveEmailNotification"
                  type={FIELD_TYPES.TOGGLE}
                  value={values.innoveEmailNotification}
                  onChange={onChange.innoveEmailNotification}
                  onBlur={onBlur.innoveEmailNotification}
                  error={errors.innoveEmailNotification}
                  options={EmailNotificationMode}
                  perRow={2}
                  required
                />
                <FormField
                  placeholder="Pattern ID"
                  label="Bayan Email Notification Pattern ID"
                  name="bayanEmailNotificationPatternId"
                  type={FIELD_TYPES.TEXT}
                  value={values.bayanEmailNotificationPatternId}
                  onChange={onChange.bayanEmailNotificationPatternId}
                  onBlur={onBlur.bayanEmailNotificationPatternId}
                  error={errors.bayanEmailNotificationPatternId}
                  perRow={2}
                  required
                />
                <FormField
                  placeholder=""
                  label="Bayan Email Notification Status"
                  name="bayanEmailNotification"
                  type={FIELD_TYPES.TOGGLE}
                  value={values.bayanEmailNotification}
                  onChange={onChange.bayanEmailNotification}
                  onBlur={onBlur.bayanEmailNotification}
                  error={errors.bayanEmailNotification}
                  options={EmailNotificationMode}
                  perRow={2}
                  required
                />
              </PageSubsection>
              <DataHeader>
                <DataHeader.Title>Auto Email Configuration</DataHeader.Title>
              </DataHeader>
              <AutoMailConfig>
                {!loading &&
                  values &&
                  values.dailyContentGCashReportRecipient &&
                  values.dailyContentGCashReportRecipient.map(
                    (email, index) => (
                      <PageSubsection key={index}>
                        {index === 0 ? (
                          <FormField
                            placeholder=""
                            label={
                              index === 0
                                ? 'Swipe Daily Content Gcash Report PatternId'
                                : ''
                            }
                            name="dailyContentGCashReportPatternId"
                            type={FIELD_TYPES.TEXT}
                            value={values.dailyContentGCashReportPatternId}
                            onChange={onChange.dailyContentGCashReportPatternId}
                            onBlur={onBlur.dailyContentGCashReportPatternId}
                            error={errors.dailyContentGCashReportPatternId}
                            perRow={2.5}
                            required
                          />
                        ) : (
                          <FormField perRow={2.5} />
                        )}
                        <FormField
                          placeholder=""
                          label={
                            index === 0
                              ? 'Swipe Daily Content Gcash Report Recipients'
                              : ''
                          }
                          name={`email-${index}`}
                          type={FIELD_TYPES.TEXT}
                          value={email.email}
                          onChange={event => {
                            handleDailyContentGCashRecipient(event);
                            onChange.dailyContentGCashReportRecipient(
                              values.dailyContentGCashReportRecipient.map(
                                recipient => {
                                  if (recipient.email === email.email) {
                                    return {
                                      email: event.target.value,
                                    };
                                  }

                                  return recipient;
                                }
                              )
                            );
                          }}
                          onBlur={handleDailyContentGCashRecipient}
                          error={dailyGCashError.email[`email-${index}`]}
                          perRow={2.5}
                          required
                        />
                        <ActionFields
                          isMobile={isMobile}
                          fieldsPerRow={2.5}
                          perRow={5}
                        >
                          {isMobile ? (
                            <ActionButton
                              backgroundColor={'red'}
                              onClick={() => {
                                setSelectedDailyGCash(email);
                              }}
                            >
                              Delete
                            </ActionButton>
                          ) : (
                            <ActionIcon
                              icon={'minus-circle'}
                              color={'red'}
                              onClick={() => {
                                setSelectedDailyGCash(email);
                              }}
                            />
                          )}
                        </ActionFields>
                      </PageSubsection>
                    )
                  )}
                {newDailyGCash.map((newRecipient, index) => {
                  return (
                    <PageSubsection key={index}>
                      {index === 0 &&
                      values &&
                      values.dailyContentGCashReportRecipient &&
                      values.dailyContentGCashReportRecipient.length < 1 ? (
                        <FormField
                          placeholder=""
                          label="Swipe Daily Content Gcash Report PatternId"
                          name="dailyContentGCashReportPatternId"
                          type={FIELD_TYPES.TEXT}
                          value={values.dailyContentGCashReportPatternId}
                          onChange={onChange.dailyContentGCashReportPatternId}
                          onBlur={onBlur.dailyContentGCashReportPatternId}
                          error={errors.dailyContentGCashReportPatternId}
                          perRow={2.5}
                          required
                        />
                      ) : (
                        <FormField perRow={2.5} />
                      )}
                      <FormField
                        placeholder="New Recipient"
                        label={
                          index === 0 &&
                          values &&
                          values.dailyContentGCashReportRecipient &&
                          values.dailyContentGCashReportRecipient.length < 1
                            ? 'Swipe Daily Content Gcash Report Recipients'
                            : ''
                        }
                        name={`newRecipient-${index}`}
                        type={FIELD_TYPES.TEXT}
                        value={newRecipient.email}
                        onChange={event => {
                          let newValue = newDailyGCash.map(
                            (newValue, newIndex) => {
                              if (newIndex === index) {
                                return {
                                  email: event.target.value,
                                };
                              }

                              return newValue;
                            }
                          );

                          setNewDailyGCash(newValue);
                          handleNewDailyContentGCashRecipient(event, index);
                        }}
                        onBlur={event =>
                          handleNewDailyContentGCashRecipient(event, index)
                        }
                        error={newDailyGCashErrors[index].email}
                        perRow={2.5}
                        required
                      />
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      >
                        {!newDailyGCashErrors[index].email &&
                          newRecipient.email &&
                          index === newDailyGCash.length - 1 &&
                          (isMobile ? (
                            <ActionButton
                              backgroundColor="green"
                              onClick={() => {
                                setNewDailyGCash([
                                  ...newDailyGCash,
                                  {
                                    email: null,
                                  },
                                ]);
                                setnewDailyGCashErrors([
                                  ...newDailyGCashErrors,
                                  {},
                                ]);
                              }}
                            >
                              Add
                            </ActionButton>
                          ) : (
                            <ActionIcon
                              icon="plus-circle"
                              color="green"
                              onClick={() => {
                                setNewDailyGCash([
                                  ...newDailyGCash,
                                  {
                                    email: null,
                                  },
                                ]);
                                setnewDailyGCashErrors([
                                  ...newDailyGCashErrors,
                                  {},
                                ]);
                              }}
                            />
                          ))}
                        {isMobile ? (
                          <ActionButton
                            backgroundColor="red"
                            onClick={() => {
                              let newValue = newDailyGCash.filter(
                                (email, emailIndex) => {
                                  return emailIndex !== index;
                                }
                              );

                              let newErrors = newDailyGCashErrors.filter(
                                (email, emailIndex) => {
                                  return emailIndex !== index;
                                }
                              );

                              if (newValue.length === 0) {
                                newValue.push({
                                  email: null,
                                });
                                newErrors.push({});
                              }

                              setNewDailyGCash(newValue);
                              setnewDailyGCashErrors(newErrors);
                            }}
                          >
                            Delete
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon="minus-circle"
                            color="red"
                            onClick={() => {
                              let newValue = newDailyGCash.filter(
                                (email, emailIndex) => {
                                  return emailIndex !== index;
                                }
                              );

                              let newErrors = newDailyGCashErrors.filter(
                                (email, emailIndex) => {
                                  return emailIndex !== index;
                                }
                              );

                              if (newValue.length === 0) {
                                newValue.push({
                                  email: null,
                                });
                                newErrors.push({});
                              }

                              setNewDailyGCash(newValue);
                              setnewDailyGCashErrors(newErrors);
                            }}
                          />
                        )}
                      </ActionFields>
                    </PageSubsection>
                  );
                })}
                {!loading &&
                  values &&
                  values.monthlyContentGCashReportRecipient &&
                  values.monthlyContentGCashReportRecipient.map(
                    (email, index) => (
                      <PageSubsection key={index}>
                        {index === 0 ? (
                          <FormField
                            placeholder=""
                            label={
                              index === 0
                                ? 'Swipe Monthly Content Gcash Report PatternId'
                                : ''
                            }
                            name="monthlyContentGCashReportPatternId"
                            type={FIELD_TYPES.TEXT}
                            value={values.monthlyContentGCashReportPatternId}
                            onChange={
                              onChange.monthlyContentGCashReportPatternId
                            }
                            onBlur={onBlur.monthlyContentGCashReportPatternId}
                            error={errors.monthlyContentGCashReportPatternId}
                            perRow={2.5}
                            required
                          />
                        ) : (
                          <FormField perRow={2.5} />
                        )}
                        <FormField
                          placeholder=""
                          label={
                            index === 0
                              ? 'Swipe Monthly Content Gcash Report Recipients'
                              : ''
                          }
                          name={`email-${index}`}
                          type={FIELD_TYPES.TEXT}
                          value={email.email}
                          onChange={event => {
                            handleMonthlyContentGCashRecipient(event);
                            onChange.monthlyContentGCashReportRecipient(
                              values.monthlyContentGCashReportRecipient.map(
                                recipient => {
                                  if (recipient.email === email.email) {
                                    return {
                                      email: event.target.value,
                                    };
                                  }

                                  return recipient;
                                }
                              )
                            );
                          }}
                          onBlur={handleMonthlyContentGCashRecipient}
                          error={monthlyGCashError.email[`email-${index}`]}
                          perRow={2.5}
                          required
                        />
                        <ActionFields
                          isMobile={isMobile}
                          fieldsPerRow={2.5}
                          perRow={5}
                        >
                          {isMobile ? (
                            <ActionButton
                              backgroundColor={'red'}
                              onClick={() => {
                                setSelectedMonthlyGCash(email);
                              }}
                            >
                              Delete
                            </ActionButton>
                          ) : (
                            <ActionIcon
                              icon={'minus-circle'}
                              color={'red'}
                              onClick={() => {
                                setSelectedMonthlyGCash(email);
                              }}
                            />
                          )}
                        </ActionFields>
                      </PageSubsection>
                    )
                  )}
                {newMonthlyGCash.map((newRecipient, index) => {
                  return (
                    <PageSubsection key={index}>
                      {index === 0 &&
                      values &&
                      values.monthlyContentGCashReportRecipient &&
                      values.monthlyContentGCashReportRecipient.length < 1 ? (
                        <FormField
                          placeholder=""
                          label="Swipe Monthly Content Gcash Report PatternId"
                          name="monthlyContentGCashReportPatternId"
                          type={FIELD_TYPES.TEXT}
                          value={values.monthlyContentGCashReportPatternId}
                          onChange={onChange.monthlyContentGCashReportPatternId}
                          onBlur={onBlur.monthlyContentGCashReportPatternId}
                          error={errors.monthlyContentGCashReportPatternId}
                          perRow={2.5}
                          required
                        />
                      ) : (
                        <FormField perRow={2.5} />
                      )}
                      <FormField
                        placeholder="New Recipient"
                        label={
                          index === 0 &&
                          values &&
                          values.monthlyContentGCashReportRecipient &&
                          values.monthlyContentGCashReportRecipient.length < 1
                            ? 'Swipe Monthly Content Gcash Report Recipients'
                            : ''
                        }
                        name={`newRecipient-${index}`}
                        type={FIELD_TYPES.TEXT}
                        value={newRecipient.email}
                        onChange={event => {
                          let newValue = newMonthlyGCash.map(
                            (newValue, newIndex) => {
                              if (newIndex === index) {
                                return {
                                  email: event.target.value,
                                };
                              }

                              return newValue;
                            }
                          );

                          setNewMonthlyGCash(newValue);
                          handleNewMonthlyContentGCashRecipient(event, index);
                        }}
                        onBlur={event =>
                          handleNewMonthlyContentGCashRecipient(event, index)
                        }
                        error={newMonthlyGCashErrors[index].email}
                        perRow={2.5}
                        required
                      />
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      >
                        {!newMonthlyGCashErrors[index].email &&
                          newRecipient.email &&
                          index === newMonthlyGCash.length - 1 &&
                          (isMobile ? (
                            <ActionButton
                              backgroundColor="green"
                              onClick={() => {
                                setNewMonthlyGCash([
                                  ...newMonthlyGCash,
                                  {
                                    email: null,
                                  },
                                ]);
                                setnewMonthlyGCashErrors([
                                  ...newMonthlyGCashErrors,
                                  {},
                                ]);
                              }}
                            >
                              Add
                            </ActionButton>
                          ) : (
                            <ActionIcon
                              icon="plus-circle"
                              color="green"
                              onClick={() => {
                                setNewMonthlyGCash([
                                  ...newMonthlyGCash,
                                  {
                                    email: null,
                                  },
                                ]);
                                setnewMonthlyGCashErrors([
                                  ...newMonthlyGCashErrors,
                                  {},
                                ]);
                              }}
                            />
                          ))}
                        {isMobile ? (
                          <ActionButton
                            backgroundColor="red"
                            onClick={() => {
                              let newValue = newMonthlyGCash.filter(
                                (email, emailIndex) => {
                                  return emailIndex !== index;
                                }
                              );

                              let newErrors = newMonthlyGCashErrors.filter(
                                (email, emailIndex) => {
                                  return emailIndex !== index;
                                }
                              );

                              if (newValue.length === 0) {
                                newValue.push({
                                  email: null,
                                });
                                newErrors.push({});
                              }

                              setNewMonthlyGCash(newValue);
                              setnewMonthlyGCashErrors(newErrors);
                            }}
                          >
                            Delete
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon="minus-circle"
                            color="red"
                            onClick={() => {
                              let newValue = newMonthlyGCash.filter(
                                (email, emailIndex) => {
                                  return emailIndex !== index;
                                }
                              );

                              let newErrors = newMonthlyGCashErrors.filter(
                                (email, emailIndex) => {
                                  return emailIndex !== index;
                                }
                              );

                              if (newValue.length === 0) {
                                newValue.push({
                                  email: null,
                                });
                                newErrors.push({});
                              }

                              setNewMonthlyGCash(newValue);
                              setnewMonthlyGCashErrors(newErrors);
                            }}
                          />
                        )}
                      </ActionFields>
                    </PageSubsection>
                  );
                })}
                {!loading &&
                  values &&
                  values.ecpayReportRecipient &&
                  values.ecpayReportRecipient.map((email, index) => (
                    <PageSubsection key={index}>
                      {index === 0 ? (
                        <FormField
                          placeholder=""
                          label={
                            index === 0 ? 'Ecpay Report Email PatternId' : ''
                          }
                          name="ecpayReportEmailPatternId"
                          type={FIELD_TYPES.TEXT}
                          value={values.ecpayReportEmailPatternId}
                          onChange={onChange.ecpayReportEmailPatternId}
                          onBlur={onBlur.ecpayReportEmailPatternId}
                          error={errors.ecpayReportEmailPatternId}
                          perRow={2.5}
                          required
                        />
                      ) : (
                        <FormField perRow={2.5} />
                      )}
                      <FormField
                        placeholder=""
                        label={index === 0 ? 'Ecpay Report Recipients' : ''}
                        name={`email-${index}`}
                        type={FIELD_TYPES.TEXT}
                        value={email.email}
                        onChange={event => {
                          handleEcpayRecipient(event);
                          onChange.ecpayReportRecipient(
                            values.ecpayReportRecipient.map(recipient => {
                              if (recipient.email === email.email) {
                                return {
                                  email: event.target.value,
                                };
                              }

                              return recipient;
                            })
                          );
                        }}
                        onBlur={handleEcpayRecipient}
                        error={ecpayError.email[`email-${index}`]}
                        perRow={2.5}
                        required
                      />
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      >
                        {isMobile ? (
                          <ActionButton
                            backgroundColor={'red'}
                            onClick={() => {
                              setSelectedEcpay(email);
                            }}
                          >
                            Delete
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon={'minus-circle'}
                            color={'red'}
                            onClick={() => {
                              setSelectedEcpay(email);
                            }}
                          />
                        )}
                      </ActionFields>
                    </PageSubsection>
                  ))}
                {newEcpay.map((newRecipient, index) => {
                  return (
                    <PageSubsection key={index}>
                      {index === 0 &&
                      values &&
                      values.ecpayReportRecipient &&
                      values.ecpayReportRecipient.length < 1 ? (
                        <FormField
                          placeholder=""
                          label="Ecpay Report Email PatternId"
                          name="ecpayReportEmailPatternId"
                          type={FIELD_TYPES.TEXT}
                          value={values.ecpayReportEmailPatternId}
                          onChange={onChange.ecpayReportEmailPatternId}
                          onBlur={onBlur.ecpayReportEmailPatternId}
                          error={errors.ecpayReportEmailPatternId}
                          perRow={2.5}
                          required
                        />
                      ) : (
                        <FormField perRow={2.5} />
                      )}
                      <FormField
                        placeholder="New Recipient"
                        label={
                          index === 0 &&
                          values &&
                          values.ecpayReportRecipient &&
                          values.ecpayReportRecipient.length < 1
                            ? 'Ecpay Report Recipients'
                            : ''
                        }
                        name={`newRecipient-${index}`}
                        type={FIELD_TYPES.TEXT}
                        value={newRecipient.email}
                        onChange={event => {
                          let newValue = newEcpay.map((newValue, newIndex) => {
                            if (newIndex === index) {
                              return {
                                email: event.target.value,
                              };
                            }

                            return newValue;
                          });

                          setNewEcpay(newValue);
                          handleNewEcpayRecipient(event, index);
                        }}
                        onBlur={event => handleNewEcpayRecipient(event, index)}
                        error={newEcpayErrors[index].email}
                        perRow={2.5}
                        required
                      />
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      >
                        {!newEcpayErrors[index].email &&
                          newRecipient.email &&
                          index === newEcpay.length - 1 &&
                          (isMobile ? (
                            <ActionButton
                              backgroundColor="green"
                              onClick={() => {
                                setNewEcpay([
                                  ...newEcpay,
                                  {
                                    email: null,
                                  },
                                ]);
                                setnewEcpayErrors([...newEcpayErrors, {}]);
                              }}
                            >
                              Add
                            </ActionButton>
                          ) : (
                            <ActionIcon
                              icon="plus-circle"
                              color="green"
                              onClick={() => {
                                setNewEcpay([
                                  ...newEcpay,
                                  {
                                    email: null,
                                  },
                                ]);
                                setnewEcpayErrors([...newEcpayErrors, {}]);
                              }}
                            />
                          ))}
                        {isMobile ? (
                          <ActionButton
                            backgroundColor="red"
                            onClick={() => {
                              let newValue = newEcpay.filter(
                                (email, emailIndex) => {
                                  return emailIndex !== index;
                                }
                              );

                              let newErrors = newEcpayErrors.filter(
                                (email, emailIndex) => {
                                  return emailIndex !== index;
                                }
                              );

                              if (newValue.length === 0) {
                                newValue.push({
                                  email: null,
                                });
                                newErrors.push({});
                              }

                              setNewEcpay(newValue);
                              setnewEcpayErrors(newErrors);
                            }}
                          >
                            Delete
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon="minus-circle"
                            color="red"
                            onClick={() => {
                              let newValue = newEcpay.filter(
                                (email, emailIndex) => {
                                  return emailIndex !== index;
                                }
                              );

                              let newErrors = newEcpayErrors.filter(
                                (email, emailIndex) => {
                                  return emailIndex !== index;
                                }
                              );

                              if (newValue.length === 0) {
                                newValue.push({
                                  email: null,
                                });
                                newErrors.push({});
                              }

                              setNewEcpay(newValue);
                              setnewEcpayErrors(newErrors);
                            }}
                          />
                        )}
                      </ActionFields>
                    </PageSubsection>
                  );
                })}
                {!loading &&
                  values &&
                  values.globeOneReportRecipient &&
                  values.globeOneReportRecipient.map((email, index) => (
                    <PageSubsection key={index}>
                      {index === 0 ? (
                        <FormField
                          placeholder=""
                          label={
                            index === 0 ? 'GlobeOne Report Email PatternId' : ''
                          }
                          name="globeOneReportEmailPatternId"
                          type={FIELD_TYPES.TEXT}
                          value={values.globeOneReportEmailPatternId}
                          onChange={onChange.globeOneReportEmailPatternId}
                          onBlur={onBlur.globeOneReportEmailPatternId}
                          error={errors.globeOneReportEmailPatternId}
                          perRow={2.5}
                          required
                        />
                      ) : (
                        <FormField perRow={2.5} />
                      )}
                      <FormField
                        placeholder=""
                        label={index === 0 ? 'GlobeOne Report Recipients' : ''}
                        name={`email-${index}`}
                        type={FIELD_TYPES.TEXT}
                        value={email.email}
                        onChange={event => {
                          handleGlobeOneRecipient(event);
                          onChange.globeOneReportRecipient(
                            values.globeOneReportRecipient.map(recipient => {
                              if (recipient.email === email.email) {
                                return {
                                  email: event.target.value,
                                };
                              }

                              return recipient;
                            })
                          );
                        }}
                        onBlur={handleGlobeOneRecipient}
                        error={globeOneError.email[`email-${index}`]}
                        perRow={2.5}
                        required
                      />
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      >
                        {isMobile ? (
                          <ActionButton
                            backgroundColor={'red'}
                            onClick={() => {
                              setSelectedGlobeOne(email);
                            }}
                          >
                            Delete
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon={'minus-circle'}
                            color={'red'}
                            onClick={() => {
                              setSelectedGlobeOne(email);
                            }}
                          />
                        )}
                      </ActionFields>
                    </PageSubsection>
                  ))}
                {newGlobeOne.map((newRecipient, index) => {
                  return (
                    <PageSubsection key={index}>
                      {index === 0 &&
                      values &&
                      values.globeOneReportRecipient &&
                      values.globeOneReportRecipient.length < 1 ? (
                        <FormField
                          placeholder=""
                          label="GlobeOne Report Email PatternId"
                          name="globeOneReportEmailPatternId"
                          type={FIELD_TYPES.TEXT}
                          value={values.globeOneReportEmailPatternId}
                          onChange={onChange.globeOneReportEmailPatternId}
                          onBlur={onBlur.globeOneReportEmailPatternId}
                          error={errors.globeOneReportEmailPatternId}
                          perRow={2.5}
                          required
                        />
                      ) : (
                        <FormField perRow={2.5} />
                      )}
                      <FormField
                        placeholder="New Recipient"
                        label={
                          index === 0 &&
                          values &&
                          values.globeOneReportRecipient &&
                          values.globeOneReportRecipient.length < 1
                            ? 'GlobeOne Report Recipients'
                            : ''
                        }
                        name={`newRecipient-${index}`}
                        type={FIELD_TYPES.TEXT}
                        value={newRecipient.email}
                        onChange={event => {
                          let newValue = newGlobeOne.map(
                            (newValue, newIndex) => {
                              if (newIndex === index) {
                                return {
                                  email: event.target.value,
                                };
                              }

                              return newValue;
                            }
                          );

                          setNewGlobeOne(newValue);
                          handleNewGlobeOneRecipient(event, index);
                        }}
                        onBlur={event =>
                          handleNewGlobeOneRecipient(event, index)
                        }
                        error={newGlobeOneErrors[index].email}
                        perRow={2.5}
                        required
                      />
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      >
                        {!newGlobeOneErrors[index].email &&
                          newRecipient.email &&
                          index === newGlobeOne.length - 1 &&
                          (isMobile ? (
                            <ActionButton
                              backgroundColor="green"
                              onClick={() => {
                                setNewGlobeOne([
                                  ...newGlobeOne,
                                  {
                                    email: null,
                                  },
                                ]);
                                setnewGlobeOneErrors([
                                  ...newGlobeOneErrors,
                                  {},
                                ]);
                              }}
                            >
                              Add
                            </ActionButton>
                          ) : (
                            <ActionIcon
                              icon="plus-circle"
                              color="green"
                              onClick={() => {
                                setNewGlobeOne([
                                  ...newGlobeOne,
                                  {
                                    email: null,
                                  },
                                ]);
                                setnewGlobeOneErrors([
                                  ...newGlobeOneErrors,
                                  {},
                                ]);
                              }}
                            />
                          ))}
                        {isMobile ? (
                          <ActionButton
                            backgroundColor="red"
                            onClick={() => {
                              let newValue = newGlobeOne.filter(
                                (email, emailIndex) => {
                                  return emailIndex !== index;
                                }
                              );

                              let newErrors = newGlobeOneErrors.filter(
                                (email, emailIndex) => {
                                  return emailIndex !== index;
                                }
                              );

                              if (newValue.length === 0) {
                                newValue.push({
                                  email: null,
                                });
                                newErrors.push({});
                              }

                              setNewGlobeOne(newValue);
                              setnewGlobeOneErrors(newErrors);
                            }}
                          >
                            Delete
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon="minus-circle"
                            color="red"
                            onClick={() => {
                              let newValue = newGlobeOne.filter(
                                (email, emailIndex) => {
                                  return emailIndex !== index;
                                }
                              );

                              let newErrors = newGlobeOneErrors.filter(
                                (email, emailIndex) => {
                                  return emailIndex !== index;
                                }
                              );

                              if (newValue.length === 0) {
                                newValue.push({
                                  email: null,
                                });
                                newErrors.push({});
                              }

                              setNewGlobeOne(newValue);
                              setnewGlobeOneErrors(newErrors);
                            }}
                          />
                        )}
                      </ActionFields>
                    </PageSubsection>
                  );
                })}
                {!loading &&
                  values &&
                  values.collectionReportRecipient &&
                  values.collectionReportRecipient.map((email, index) => (
                    <PageSubsection key={index}>
                      {index === 0 ? (
                        <FormField
                          placeholder=""
                          label={
                            index === 0
                              ? 'Credit Card Collection Summary Report Email PatternId'
                              : ''
                          }
                          name="collectionReportEmailPatternId"
                          type={FIELD_TYPES.TEXT}
                          value={values.collectionReportEmailPatternId}
                          onChange={onChange.collectionReportEmailPatternId}
                          onBlur={onBlur.collectionReportEmailPatternId}
                          error={errors.collectionReportEmailPatternId}
                          perRow={2.5}
                          required
                        />
                      ) : (
                        <FormField perRow={2.5} />
                      )}
                      <FormField
                        placeholder=""
                        label={
                          index === 0
                            ? 'Credit Card Collection Summary Report Recipients'
                            : ''
                        }
                        name={`email-${index}`}
                        type={FIELD_TYPES.TEXT}
                        value={email.email}
                        onChange={event => {
                          handleCollectionRecipient(event);
                          onChange.collectionReportRecipient(
                            values.collectionReportRecipient.map(recipient => {
                              if (recipient.email === email.email) {
                                return {
                                  email: event.target.value,
                                };
                              }

                              return recipient;
                            })
                          );
                        }}
                        onBlur={handleCollectionRecipient}
                        error={collectionError.email[`email-${index}`]}
                        perRow={2.5}
                        required
                      />
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      >
                        {isMobile ? (
                          <ActionButton
                            backgroundColor={'red'}
                            onClick={() => {
                              setSelectedCollection(email);
                            }}
                          >
                            Delete
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon={'minus-circle'}
                            color={'red'}
                            onClick={() => {
                              setSelectedCollection(email);
                            }}
                          />
                        )}
                      </ActionFields>
                    </PageSubsection>
                  ))}
                {newCollection.map((newRecipient, index) => {
                  return (
                    <PageSubsection key={index}>
                      {index === 0 &&
                      values &&
                      values.collectionReportRecipient &&
                      values.collectionReportRecipient.length < 1 ? (
                        <FormField
                          placeholder=""
                          label="Credit Card Collection Summary Report Email PatternId"
                          name="collectionReportEmailPatternId"
                          type={FIELD_TYPES.TEXT}
                          value={values.collectionReportEmailPatternId}
                          onChange={onChange.collectionReportEmailPatternId}
                          onBlur={onBlur.collectionReportEmailPatternId}
                          error={errors.collectionReportEmailPatternId}
                          perRow={2.5}
                          required
                        />
                      ) : (
                        <FormField perRow={2.5} />
                      )}
                      <FormField
                        placeholder="New Recipient"
                        label={
                          index === 0 &&
                          values &&
                          values.collectionReportRecipient &&
                          values.collectionReportRecipient.length < 1
                            ? 'Credit Card Collection Summary Report Recipients'
                            : ''
                        }
                        name={`newRecipient-${index}`}
                        type={FIELD_TYPES.TEXT}
                        value={newRecipient.email}
                        onChange={event => {
                          let newValue = newCollection.map(
                            (newValue, newIndex) => {
                              if (newIndex === index) {
                                return {
                                  email: event.target.value,
                                };
                              }

                              return newValue;
                            }
                          );

                          setNewCollection(newValue);
                          handleNewCollectionRecipient(event, index);
                        }}
                        onBlur={event =>
                          handleNewCollectionRecipient(event, index)
                        }
                        error={newCollectionErrors[index].email}
                        perRow={2.5}
                        required
                      />
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      >
                        {!newCollectionErrors[index].email &&
                          newRecipient.email &&
                          index === newCollection.length - 1 &&
                          (isMobile ? (
                            <ActionButton
                              backgroundColor="green"
                              onClick={() => {
                                setNewCollection([
                                  ...newCollection,
                                  {
                                    email: null,
                                  },
                                ]);
                                setnewCollectionErrors([
                                  ...newCollectionErrors,
                                  {},
                                ]);
                              }}
                            >
                              Add
                            </ActionButton>
                          ) : (
                            <ActionIcon
                              icon="plus-circle"
                              color="green"
                              onClick={() => {
                                setNewCollection([
                                  ...newCollection,
                                  {
                                    email: null,
                                  },
                                ]);
                                setnewCollectionErrors([
                                  ...newCollectionErrors,
                                  {},
                                ]);
                              }}
                            />
                          ))}
                        {isMobile ? (
                          <ActionButton
                            backgroundColor="red"
                            onClick={() => {
                              let newValue = newCollection.filter(
                                (email, emailIndex) => {
                                  return emailIndex !== index;
                                }
                              );

                              let newErrors = newCollectionErrors.filter(
                                (email, emailIndex) => {
                                  return emailIndex !== index;
                                }
                              );

                              if (newValue.length === 0) {
                                newValue.push({
                                  email: null,
                                });
                                newErrors.push({});
                              }

                              setNewCollection(newValue);
                              setnewCollectionErrors(newErrors);
                            }}
                          >
                            Delete
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon="minus-circle"
                            color="red"
                            onClick={() => {
                              let newValue = newCollection.filter(
                                (email, emailIndex) => {
                                  return emailIndex !== index;
                                }
                              );

                              let newErrors = newCollectionErrors.filter(
                                (email, emailIndex) => {
                                  return emailIndex !== index;
                                }
                              );

                              if (newValue.length === 0) {
                                newValue.push({
                                  email: null,
                                });
                                newErrors.push({});
                              }

                              setNewCollection(newValue);
                              setnewCollectionErrors(newErrors);
                            }}
                          />
                        )}
                      </ActionFields>
                    </PageSubsection>
                  );
                })}
                {!loading &&
                  values &&
                  values.creditCardReportRecipient &&
                  values.creditCardReportRecipient.map((email, index) => (
                    <PageSubsection key={index}>
                      {index === 0 ? (
                        <FormField
                          placeholder=""
                          label={
                            index === 0
                              ? 'Gateway for Online Payment Report Email PatternId'
                              : ''
                          }
                          name="creditCardReportEmailPatternId"
                          type={FIELD_TYPES.TEXT}
                          value={values.creditCardReportEmailPatternId}
                          onChange={onChange.creditCardReportEmailPatternId}
                          onBlur={onBlur.creditCardReportEmailPatternId}
                          error={errors.creditCardReportEmailPatternId}
                          perRow={2.5}
                          required
                        />
                      ) : (
                        <FormField perRow={2.5} />
                      )}
                      <FormField
                        placeholder=""
                        label={
                          index === 0
                            ? 'Gateway for Online Payment Report Recipients'
                            : ''
                        }
                        name={`email-${index}`}
                        type={FIELD_TYPES.TEXT}
                        value={email.email}
                        onChange={event => {
                          handleCreditCardRecipient(event);
                          onChange.creditCardReportRecipient(
                            values.creditCardReportRecipient.map(recipient => {
                              if (recipient.email === email.email) {
                                return {
                                  email: event.target.value,
                                };
                              }

                              return recipient;
                            })
                          );
                        }}
                        onBlur={handleCreditCardRecipient}
                        error={creditCardError.email[`email-${index}`]}
                        perRow={2.5}
                        required
                      />
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      >
                        {isMobile ? (
                          <ActionButton
                            backgroundColor={'red'}
                            onClick={() => {
                              setSelectedCreditCard(email);
                            }}
                          >
                            Delete
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon={'minus-circle'}
                            color={'red'}
                            onClick={() => {
                              setSelectedCreditCard(email);
                            }}
                          />
                        )}
                      </ActionFields>
                    </PageSubsection>
                  ))}
                {newCreditCard.map((newRecipient, index) => {
                  return (
                    <PageSubsection key={index}>
                      {index === 0 &&
                      values &&
                      values.creditCardReportRecipient &&
                      values.creditCardReportRecipient.length < 1 ? (
                        <FormField
                          placeholder=""
                          label="Gateway for Online Payment Report Email PatternId"
                          name="creditCardReportEmailPatternId"
                          type={FIELD_TYPES.TEXT}
                          value={values.creditCardReportEmailPatternId}
                          onChange={onChange.creditCardReportEmailPatternId}
                          onBlur={onBlur.creditCardReportEmailPatternId}
                          error={errors.creditCardReportEmailPatternId}
                          perRow={2.5}
                          required
                        />
                      ) : (
                        <FormField perRow={2.5} />
                      )}
                      <FormField
                        placeholder="New Recipient"
                        label={
                          index === 0 &&
                          values &&
                          values.creditCardReportRecipient &&
                          values.creditCardReportRecipient.length < 1
                            ? 'Gateway for Online Payment Report Recipients'
                            : ''
                        }
                        name={`newRecipient-${index}`}
                        type={FIELD_TYPES.TEXT}
                        value={newRecipient.email}
                        onChange={event => {
                          let newValue = newCreditCard.map(
                            (newValue, newIndex) => {
                              if (newIndex === index) {
                                return {
                                  email: event.target.value,
                                };
                              }

                              return newValue;
                            }
                          );

                          setNewCreditCard(newValue);
                          handleNewCreditCardRecipient(event, index);
                        }}
                        onBlur={event =>
                          handleNewCreditCardRecipient(event, index)
                        }
                        error={newCreditCardErrors[index].email}
                        perRow={2.5}
                        required
                      />
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      >
                        {!newCreditCardErrors[index].email &&
                          newRecipient.email &&
                          index === newCreditCard.length - 1 &&
                          (isMobile ? (
                            <ActionButton
                              backgroundColor="green"
                              onClick={() => {
                                setNewCreditCard([
                                  ...newCreditCard,
                                  {
                                    email: null,
                                  },
                                ]);
                                setnewCreditCardErrors([
                                  ...newCreditCardErrors,
                                  {},
                                ]);
                              }}
                            >
                              Add
                            </ActionButton>
                          ) : (
                            <ActionIcon
                              icon="plus-circle"
                              color="green"
                              onClick={() => {
                                setNewCreditCard([
                                  ...newCreditCard,
                                  {
                                    email: null,
                                  },
                                ]);
                                setnewCreditCardErrors([
                                  ...newCreditCardErrors,
                                  {},
                                ]);
                              }}
                            />
                          ))}
                        {isMobile ? (
                          <ActionButton
                            backgroundColor="red"
                            onClick={() => {
                              let newValue = newCreditCard.filter(
                                (email, emailIndex) => {
                                  return emailIndex !== index;
                                }
                              );

                              let newErrors = newCreditCardErrors.filter(
                                (email, emailIndex) => {
                                  return emailIndex !== index;
                                }
                              );

                              if (newValue.length === 0) {
                                newValue.push({
                                  email: null,
                                });
                                newErrors.push({});
                              }

                              setNewCreditCard(newValue);
                              setnewCreditCardErrors(newErrors);
                            }}
                          >
                            Delete
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon="minus-circle"
                            color="red"
                            onClick={() => {
                              let newValue = newCreditCard.filter(
                                (email, emailIndex) => {
                                  return emailIndex !== index;
                                }
                              );

                              let newErrors = newCreditCardErrors.filter(
                                (email, emailIndex) => {
                                  return emailIndex !== index;
                                }
                              );

                              if (newValue.length === 0) {
                                newValue.push({
                                  email: null,
                                });
                                newErrors.push({});
                              }

                              setNewCreditCard(newValue);
                              setnewCreditCardErrors(newErrors);
                            }}
                          />
                        )}
                      </ActionFields>
                    </PageSubsection>
                  );
                })}
                {!loading &&
                  values &&
                  values.channelReportRecipient &&
                  values.channelReportRecipient.map((email, index) => (
                    <PageSubsection key={index}>
                      {index === 0 ? (
                        <FormField
                          placeholder=""
                          label={
                            index === 0 ? 'Channel Report Email PatternId' : ''
                          }
                          name="channelReportEmailPatternId"
                          type={FIELD_TYPES.TEXT}
                          value={values.channelReportEmailPatternId}
                          onChange={onChange.channelReportEmailPatternId}
                          onBlur={onBlur.channelReportEmailPatternId}
                          error={errors.channelReportEmailPatternId}
                          perRow={2.5}
                          required
                        />
                      ) : (
                        <FormField perRow={2.5} />
                      )}
                      <FormField
                        placeholder=""
                        label={index === 0 ? 'Channel Report Recipients' : ''}
                        name={`email-${index}`}
                        type={FIELD_TYPES.TEXT}
                        value={email.email}
                        onChange={event => {
                          handleChannelRecipient(event);
                          onChange.channelReportRecipient(
                            values.channelReportRecipient.map(recipient => {
                              if (recipient.email === email.email) {
                                return {
                                  email: event.target.value,
                                };
                              }

                              return recipient;
                            })
                          );
                        }}
                        onBlur={handleChannelRecipient}
                        error={channelError.email[`email-${index}`]}
                        perRow={2.5}
                        required
                      />
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      >
                        {isMobile ? (
                          <ActionButton
                            backgroundColor={'red'}
                            onClick={() => {
                              setSelectedChannel(email);
                            }}
                          >
                            Delete
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon={'minus-circle'}
                            color={'red'}
                            onClick={() => {
                              setSelectedChannel(email);
                            }}
                          />
                        )}
                      </ActionFields>
                    </PageSubsection>
                  ))}
                {newChannel.map((newRecipient, index) => {
                  return (
                    <PageSubsection key={index}>
                      {index === 0 &&
                      values &&
                      values.channelReportRecipient &&
                      values.channelReportRecipient.length < 1 ? (
                        <FormField
                          placeholder=""
                          label="Channel Report Email PatternId"
                          name="channelReportEmailPatternId"
                          type={FIELD_TYPES.TEXT}
                          value={values.channelReportEmailPatternId}
                          onChange={onChange.channelReportEmailPatternId}
                          onBlur={onBlur.channelReportEmailPatternId}
                          error={errors.channelReportEmailPatternId}
                          perRow={2.5}
                          required
                        />
                      ) : (
                        <FormField perRow={2.5} />
                      )}
                      <FormField
                        placeholder="New Recipient"
                        label={
                          index === 0 &&
                          values &&
                          values.channelReportRecipient &&
                          values.channelReportRecipient.length < 1
                            ? 'Channel Report Recipients'
                            : ''
                        }
                        name={`newRecipient-${index}`}
                        type={FIELD_TYPES.TEXT}
                        value={newRecipient.email}
                        onChange={event => {
                          let newValue = newChannel.map(
                            (newValue, newIndex) => {
                              if (newIndex === index) {
                                return {
                                  email: event.target.value,
                                };
                              }

                              return newValue;
                            }
                          );

                          setNewChannel(newValue);
                          handleNewChannelRecipient(event, index);
                        }}
                        onBlur={event =>
                          handleNewChannelRecipient(event, index)
                        }
                        error={newChannelErrors[index].email}
                        perRow={2.5}
                        required
                      />
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      >
                        {!newChannelErrors[index].email &&
                          newRecipient.email &&
                          index === newChannel.length - 1 &&
                          (isMobile ? (
                            <ActionButton
                              backgroundColor="green"
                              onClick={() => {
                                setNewChannel([
                                  ...newChannel,
                                  {
                                    email: null,
                                  },
                                ]);
                                setnewChannelErrors([...newChannelErrors, {}]);
                              }}
                            >
                              Add
                            </ActionButton>
                          ) : (
                            <ActionIcon
                              icon="plus-circle"
                              color="green"
                              onClick={() => {
                                setNewChannel([
                                  ...newChannel,
                                  {
                                    email: null,
                                  },
                                ]);
                                setnewChannelErrors([...newChannelErrors, {}]);
                              }}
                            />
                          ))}
                        {isMobile ? (
                          <ActionButton
                            backgroundColor="red"
                            onClick={() => {
                              let newValue = newChannel.filter(
                                (email, emailIndex) => {
                                  return emailIndex !== index;
                                }
                              );

                              let newErrors = newChannelErrors.filter(
                                (email, emailIndex) => {
                                  return emailIndex !== index;
                                }
                              );

                              if (newValue.length === 0) {
                                newValue.push({
                                  email: null,
                                });
                                newErrors.push({});
                              }

                              setNewChannel(newValue);
                              setnewChannelErrors(newErrors);
                            }}
                          >
                            Delete
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon="minus-circle"
                            color="red"
                            onClick={() => {
                              let newValue = newChannel.filter(
                                (email, emailIndex) => {
                                  return emailIndex !== index;
                                }
                              );

                              let newErrors = newChannelErrors.filter(
                                (email, emailIndex) => {
                                  return emailIndex !== index;
                                }
                              );

                              if (newValue.length === 0) {
                                newValue.push({
                                  email: null,
                                });
                                newErrors.push({});
                              }

                              setNewChannel(newValue);
                              setnewChannelErrors(newErrors);
                            }}
                          />
                        )}
                      </ActionFields>
                    </PageSubsection>
                  );
                })}
                {!loading &&
                  values &&
                  values.billingReportRecipient &&
                  values.billingReportRecipient.map((email, index) => (
                    <PageSubsection key={index}>
                      {index === 0 ? (
                        <FormField
                          placeholder=""
                          label={index === 0 ? 'Billing Report PatternId' : ''}
                          name="billingReportPatternId"
                          type={FIELD_TYPES.TEXT}
                          value={values.billingReportPatternId}
                          onChange={onChange.billingReportPatternId}
                          onBlur={onBlur.billingReportPatternId}
                          error={errors.billingReportPatternId}
                          perRow={2.5}
                          required
                        />
                      ) : (
                        <FormField perRow={2.5} />
                      )}
                      <FormField
                        placeholder=""
                        label={index === 0 ? 'Billing Report Recipients' : ''}
                        name={`email-${index}`}
                        type={FIELD_TYPES.TEXT}
                        value={email.email}
                        onChange={event => {
                          handleBillingRecipient(event);
                          onChange.billingReportRecipient(
                            values.billingReportRecipient.map(recipient => {
                              if (recipient.email === email.email) {
                                return {
                                  email: event.target.value,
                                };
                              }

                              return recipient;
                            })
                          );
                        }}
                        onBlur={handleBillingRecipient}
                        error={billingError.email[`email-${index}`]}
                        perRow={2.5}
                        required
                      />
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      >
                        {isMobile ? (
                          <ActionButton
                            backgroundColor={'red'}
                            onClick={() => {
                              setSelectedBilling(email);
                            }}
                          >
                            Delete
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon={'minus-circle'}
                            color={'red'}
                            onClick={() => {
                              setSelectedBilling(email);
                            }}
                          />
                        )}
                      </ActionFields>
                    </PageSubsection>
                  ))}
                {newBilling.map((newRecipient, index) => {
                  return (
                    <PageSubsection key={index}>
                      {index === 0 &&
                      values &&
                      values.billingReportRecipient &&
                      values.billingReportRecipient.length < 1 ? (
                        <FormField
                          placeholder=""
                          label="Billing Report PatternId"
                          name="billingReportPatternId"
                          type={FIELD_TYPES.TEXT}
                          value={values.billingReportPatternId}
                          onChange={onChange.billingReportPatternId}
                          onBlur={onBlur.billingReportPatternId}
                          error={errors.billingReportPatternId}
                          perRow={2.5}
                          required
                        />
                      ) : (
                        <FormField perRow={2.5} />
                      )}
                      <FormField
                        placeholder="New Recipient"
                        label={
                          index === 0 &&
                          values &&
                          values.billingReportRecipient &&
                          values.billingReportRecipient.length < 1
                            ? 'Billing Report Recipients'
                            : ''
                        }
                        name={`newRecipient-${index}`}
                        type={FIELD_TYPES.TEXT}
                        value={newRecipient.email}
                        onChange={event => {
                          let newValue = newBilling.map(
                            (newValue, newIndex) => {
                              if (newIndex === index) {
                                return {
                                  email: event.target.value,
                                };
                              }

                              return newValue;
                            }
                          );

                          setNewBilling(newValue);
                          handleNewBillingRecipient(event, index);
                        }}
                        onBlur={event =>
                          handleNewBillingRecipient(event, index)
                        }
                        error={newBillingErrors[index].email}
                        perRow={2.5}
                        required
                      />
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      >
                        {!newBillingErrors[index].email &&
                          newRecipient.email &&
                          index === newBilling.length - 1 &&
                          (isMobile ? (
                            <ActionButton
                              backgroundColor="green"
                              onClick={() => {
                                setNewBilling([
                                  ...newBilling,
                                  {
                                    email: null,
                                  },
                                ]);
                                setnewBillingErrors([...newBillingErrors, {}]);
                              }}
                            >
                              Add
                            </ActionButton>
                          ) : (
                            <ActionIcon
                              icon="plus-circle"
                              color="green"
                              onClick={() => {
                                setNewBilling([
                                  ...newBilling,
                                  {
                                    email: null,
                                  },
                                ]);
                                setnewBillingErrors([...newBillingErrors, {}]);
                              }}
                            />
                          ))}
                        {isMobile ? (
                          <ActionButton
                            backgroundColor="red"
                            onClick={() => {
                              let newValue = newBilling.filter(
                                (email, emailIndex) => {
                                  return emailIndex !== index;
                                }
                              );

                              let newErrors = newBillingErrors.filter(
                                (email, emailIndex) => {
                                  return emailIndex !== index;
                                }
                              );

                              if (newValue.length === 0) {
                                newValue.push({
                                  email: null,
                                });
                                newErrors.push({});
                              }

                              setNewBilling(newValue);
                              setnewBillingErrors(newErrors);
                            }}
                          >
                            Delete
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon="minus-circle"
                            color="red"
                            onClick={() => {
                              let newValue = newBilling.filter(
                                (email, emailIndex) => {
                                  return emailIndex !== index;
                                }
                              );

                              let newErrors = newBillingErrors.filter(
                                (email, emailIndex) => {
                                  return emailIndex !== index;
                                }
                              );

                              if (newValue.length === 0) {
                                newValue.push({
                                  email: null,
                                });
                                newErrors.push({});
                              }

                              setNewBilling(newValue);
                              setnewBillingErrors(newErrors);
                            }}
                          />
                        )}
                      </ActionFields>
                    </PageSubsection>
                  );
                })}
              </AutoMailConfig>
              <DataHeader>
                <DataHeader.Title>GCash Refund Reasons</DataHeader.Title>
              </DataHeader>
              <RefundReasonSection>
                {!loading &&
                  values &&
                  values.refundReason &&
                  values.refundReason.map((reason, index) => (
                    <PageSubsection key={reason.reason + '-' + index}>
                      <FormField
                        placeholder=""
                        label="Reason"
                        name={`reason-${reason.reason}`}
                        type={FIELD_TYPES.TEXT}
                        value={reason.reason}
                        readOnly={true}
                        perRow={2}
                        required
                      />
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      >
                        {isMobile ? (
                          <ActionButton
                            backgroundColor={'red'}
                            onClick={() => {
                              setSelectedReason(reason);
                            }}
                          >
                            Delete
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon={'minus-circle'}
                            color={'red'}
                            onClick={() => {
                              setSelectedReason(reason);
                            }}
                          />
                        )}
                      </ActionFields>
                    </PageSubsection>
                  ))}
                {newRefundReason.map((newReason, index) => {
                  return (
                    <PageSubsection key={index}>
                      <FormField
                        placeholder="Reason"
                        label="Reason"
                        name={`newReason-${index}`}
                        type={FIELD_TYPES.TEXT}
                        value={newReason.reason}
                        onChange={event => {
                          let newValue = newRefundReason.map(
                            (newValue, newIndex) => {
                              if (newIndex === index) {
                                return {
                                  reason: event.target.value,
                                };
                              }

                              return newValue;
                            }
                          );

                          setNewRefundReason(newValue);
                          handleNewReasonValidation(event, index);
                        }}
                        onBlur={event =>
                          handleNewReasonValidation(event, index)
                        }
                        error={newReasonErrors[index].reason}
                        perRow={2}
                        required
                      />
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      >
                        {!newReasonErrors[index].reason &&
                          newReason.reason &&
                          index === newRefundReason.length - 1 &&
                          (isMobile ? (
                            <ActionButton
                              backgroundColor="green"
                              onClick={() => {
                                setNewRefundReason([
                                  ...newRefundReason,
                                  {
                                    reason: null,
                                  },
                                ]);
                                setNewReasonErrors([...newReasonErrors, {}]);
                              }}
                            >
                              Add
                            </ActionButton>
                          ) : (
                            <ActionIcon
                              icon="plus-circle"
                              color="green"
                              onClick={() => {
                                setNewRefundReason([
                                  ...newRefundReason,
                                  {
                                    reason: null,
                                  },
                                ]);
                                setNewReasonErrors([...newReasonErrors, {}]);
                              }}
                            />
                          ))}
                        {isMobile ? (
                          <ActionButton
                            backgroundColor="red"
                            onClick={() => {
                              let newValue = newRefundReason.filter(
                                (reason, reasonIndex) => {
                                  return reasonIndex !== index;
                                }
                              );

                              let newErrors = newReasonErrors.filter(
                                (reason, reasonIndex) => {
                                  return reasonIndex !== index;
                                }
                              );

                              if (newValue.length === 0) {
                                newValue.push({
                                  reason: null,
                                });
                                newErrors.push({});
                              }

                              setNewRefundReason(newValue);
                              setNewReasonErrors(newErrors);
                            }}
                          >
                            Delete
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon="minus-circle"
                            color="red"
                            onClick={() => {
                              let newValue = newRefundReason.filter(
                                (reason, reasonIndex) => {
                                  return reasonIndex !== index;
                                }
                              );

                              let newErrors = newReasonErrors.filter(
                                (reason, reasonIndex) => {
                                  return reasonIndex !== index;
                                }
                              );

                              if (newValue.length === 0) {
                                newValue.push({
                                  reason: null,
                                });
                                newErrors.push({});
                              }

                              setNewRefundReason(newValue);
                              setNewReasonErrors(newErrors);
                            }}
                          />
                        )}
                      </ActionFields>
                    </PageSubsection>
                  );
                })}
              </RefundReasonSection>
              <DataHeader>
                <DataHeader.Title>Adyen Refund Reasons</DataHeader.Title>
              </DataHeader>
              <CCRefundReasonSection>
                {!loading &&
                  values &&
                  values.cardRefundReason &&
                  values.cardRefundReason.map((reason, index) => (
                    <PageSubsection key={reason.reason + '-' + index}>
                      <FormField
                        placeholder=""
                        label="Reason"
                        name={`reason-${reason.reason}`}
                        type={FIELD_TYPES.TEXT}
                        value={reason.reason}
                        readOnly={true}
                        perRow={2}
                        required
                      />
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      >
                        {isMobile ? (
                          <ActionButton
                            backgroundColor={'red'}
                            onClick={() => {
                              setSelectedCCReason(reason);
                            }}
                          >
                            Delete
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon={'minus-circle'}
                            color={'red'}
                            onClick={() => {
                              setSelectedCCReason(reason);
                            }}
                          />
                        )}
                      </ActionFields>
                    </PageSubsection>
                  ))}
                {newCCRefundReason.map((newReason, index) => {
                  return (
                    <PageSubsection key={index}>
                      <FormField
                        placeholder="Reason"
                        label="Reason"
                        name={`newReason-${index}`}
                        type={FIELD_TYPES.TEXT}
                        value={newReason.reason}
                        onChange={event => {
                          let newValue = newCCRefundReason.map(
                            (newValue, newIndex) => {
                              if (newIndex === index) {
                                return {
                                  reason: event.target.value,
                                };
                              }

                              return newValue;
                            }
                          );

                          setNewCCRefundReason(newValue);
                          handleNewCCReasonValidation(event, index);
                        }}
                        onBlur={event =>
                          handleNewCCReasonValidation(event, index)
                        }
                        error={newCCReasonErrors[index].reason}
                        perRow={2}
                        required
                      />
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      >
                        {!newCCReasonErrors[index].reason &&
                          newReason.reason &&
                          index === newCCRefundReason.length - 1 &&
                          (isMobile ? (
                            <ActionButton
                              backgroundColor="green"
                              onClick={() => {
                                setNewCCRefundReason([
                                  ...newCCRefundReason,
                                  {
                                    reason: null,
                                  },
                                ]);
                                setNewCCReasonErrors([
                                  ...newCCReasonErrors,
                                  {},
                                ]);
                              }}
                            >
                              Add
                            </ActionButton>
                          ) : (
                            <ActionIcon
                              icon="plus-circle"
                              color="green"
                              onClick={() => {
                                setNewCCRefundReason([
                                  ...newCCRefundReason,
                                  {
                                    reason: null,
                                  },
                                ]);
                                setNewCCReasonErrors([
                                  ...newCCReasonErrors,
                                  {},
                                ]);
                              }}
                            />
                          ))}
                        {isMobile ? (
                          <ActionButton
                            backgroundColor="red"
                            onClick={() => {
                              let newValue = newCCRefundReason.filter(
                                (reason, reasonIndex) => {
                                  return reasonIndex !== index;
                                }
                              );

                              let newErrors = newCCReasonErrors.filter(
                                (reason, reasonIndex) => {
                                  return reasonIndex !== index;
                                }
                              );

                              if (newValue.length === 0) {
                                newValue.push({
                                  reason: null,
                                });
                                newErrors.push({});
                              }

                              setNewCCRefundReason(newValue);
                              setNewCCReasonErrors(newErrors);
                            }}
                          >
                            Delete
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon="minus-circle"
                            color="red"
                            onClick={() => {
                              let newValue = newCCRefundReason.filter(
                                (reason, reasonIndex) => {
                                  return reasonIndex !== index;
                                }
                              );

                              let newErrors = newCCReasonErrors.filter(
                                (reason, reasonIndex) => {
                                  return reasonIndex !== index;
                                }
                              );

                              if (newValue.length === 0) {
                                newValue.push({
                                  reason: null,
                                });
                                newErrors.push({});
                              }

                              setNewCCRefundReason(newValue);
                              setNewCCReasonErrors(newErrors);
                            }}
                          />
                        )}
                      </ActionFields>
                    </PageSubsection>
                  );
                })}
              </CCRefundReasonSection>
              <DataHeader>
                <DataHeader.Title>SubMerchants</DataHeader.Title>
              </DataHeader>
              <SubMerchantSection>
                {!loading &&
                  values &&
                  values.subMerchants &&
                  values.subMerchants.map((merchant, index) => (
                    <PageSubsection key={merchant.serviceType + '-' + index}>
                      <FormField
                        placeholder=""
                        label="Merchant"
                        name={`serviceType-${merchant.merchant}`}
                        type={FIELD_TYPES.TEXT}
                        value={merchant.merchant}
                        readOnly={true}
                        perRow={3.5}
                        required
                      />
                      <FormField
                        placeholder="Service Type"
                        label="Service Type"
                        name={`serviceType-${merchant.serviceType}`}
                        type={FIELD_TYPES.TEXT}
                        value={merchant.serviceType}
                        readOnly={true}
                        perRow={3.5}
                        required
                      />
                      <FormField
                        placeholder="SubMerchant ID"
                        label="SubMerchant ID"
                        name={`subMerchantId-${merchant.serviceType}`}
                        type={FIELD_TYPES.TEXT}
                        value={merchant.subMerchantId}
                        onChange={event => {
                          handleSubMerchantIDValidation(event);
                          onChange.subMerchants(
                            values.subMerchants.map(submerchant => {
                              if (
                                submerchant.serviceType === merchant.serviceType
                              ) {
                                return {
                                  serviceType: merchant.serviceType,
                                  subMerchantId: event.target.value,
                                  merchant: merchant.merchant,
                                };
                              }

                              return submerchant;
                            })
                          );
                        }}
                        onBlur={handleSubMerchantIDValidation}
                        error={
                          submerchantErrors.id[
                            `subMerchantId-${merchant.serviceType}`
                          ]
                        }
                        perRow={3.5}
                        required
                      />
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      >
                        {isMobile ? (
                          <ActionButton
                            backgroundColor={
                              usedSubMerchantsLoading ? 'gray' : 'red'
                            }
                            onClick={() => {
                              if (!usedSubMerchantsLoading) {
                                setSelectedSubMerchant(merchant);
                              }
                            }}
                          >
                            Delete
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon={
                              usedSubMerchantsLoading
                                ? 'spinner'
                                : 'minus-circle'
                            }
                            color={usedSubMerchantsLoading ? 'gray' : 'red'}
                            onClick={() => {
                              if (!usedSubMerchantsLoading) {
                                setSelectedSubMerchant(merchant);
                              }
                            }}
                          />
                        )}
                      </ActionFields>
                    </PageSubsection>
                  ))}
                {newSubmerchants.map((newSubmerchant, index) => {
                  return (
                    <PageSubsection key={index}>
                      <FormField
                        placeholder=""
                        label="Merchant"
                        name={`newServiceType-${index}`}
                        type={FIELD_TYPES.SELECT}
                        value={newSubmerchant.merchant}
                        onChange={event => {
                          let newValue = newSubmerchants.map(
                            (newValue, newIndex) => {
                              if (newIndex === index) {
                                return {
                                  serviceType: newSubmerchant.serviceType,
                                  subMerchantId: newSubmerchant.subMerchantId,
                                  merchant: event,
                                };
                              }

                              return newValue;
                            }
                          );

                          setNewSubmerchants(newValue);
                        }}
                        options={[
                          { value: null, label: 'None' },
                          { value: 'Globe', label: 'Globe' },
                          { value: 'ECPay', label: 'ECPay' },
                        ]}
                        onBlur={event =>
                          handleNewMerchantValidation(event, index)
                        }
                        error={newSubmerchantErrors[index].merchant}
                        perRow={3.5}
                        required
                      />
                      <FormField
                        placeholder="Service Type"
                        label="Service Type"
                        name={`newServiceType-${index}`}
                        type={FIELD_TYPES.TEXT}
                        value={newSubmerchant.serviceType}
                        onChange={event => {
                          let newValue = newSubmerchants.map(
                            (newValue, newIndex) => {
                              if (newIndex === index) {
                                return {
                                  serviceType: event.target.value,
                                  subMerchantId: newSubmerchant.subMerchantId,
                                  merchant: newSubmerchant.merchant,
                                };
                              }

                              return newValue;
                            }
                          );

                          setNewSubmerchants(newValue);
                          handleNewServiceTypeValidation(event, index);
                        }}
                        onBlur={event =>
                          handleNewServiceTypeValidation(event, index)
                        }
                        error={newSubmerchantErrors[index].serviceType}
                        perRow={3.5}
                        required
                      />
                      <FormField
                        placeholder="SubMerchant ID"
                        label="SubMerchant ID"
                        name={`newSubMerchantId-${index}`}
                        type={FIELD_TYPES.TEXT}
                        value={newSubmerchant.subMerchantId}
                        onChange={event => {
                          let newValue = newSubmerchants.map(
                            (newValue, newIndex) => {
                              if (newIndex === index) {
                                return {
                                  serviceType: newSubmerchant.serviceType,
                                  subMerchantId: event.target.value,
                                  merchant: newSubmerchant.merchant,
                                };
                              }

                              return newValue;
                            }
                          );

                          setNewSubmerchants(newValue);
                          handleNewSubMerchantIDValidation(event, index);
                        }}
                        onBlur={event =>
                          handleNewSubMerchantIDValidation(event, index)
                        }
                        error={newSubmerchantErrors[index].subMerchantId}
                        perRow={3.5}
                        required
                      />
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      >
                        {!(
                          newSubmerchantErrors[index].serviceType ||
                          newSubmerchantErrors[index].subMerchantId ||
                          newSubmerchantErrors[index].merchant
                        ) &&
                          newSubmerchant.serviceType &&
                          newSubmerchant.subMerchantId &&
                          newSubmerchant.merchant &&
                          index === newSubmerchants.length - 1 &&
                          (isMobile ? (
                            <ActionButton
                              backgroundColor="green"
                              onClick={() => {
                                setNewSubmerchants([
                                  ...newSubmerchants,
                                  {
                                    serviceType: null,
                                    subMerchantId: null,
                                    merchant: null,
                                  },
                                ]);
                                setNewSubmerchantErrors([
                                  ...newSubmerchantErrors,
                                  {},
                                ]);
                              }}
                            >
                              Add
                            </ActionButton>
                          ) : (
                            <ActionIcon
                              icon="plus-circle"
                              color="green"
                              onClick={() => {
                                setNewSubmerchants([
                                  ...newSubmerchants,
                                  {
                                    serviceType: null,
                                    subMerchantId: null,
                                    merchant: null,
                                  },
                                ]);
                                setNewSubmerchantErrors([
                                  ...newSubmerchantErrors,
                                  {},
                                ]);
                              }}
                            />
                          ))}
                        {isMobile ? (
                          <ActionButton
                            backgroundColor="red"
                            onClick={() => {
                              let newValue = newSubmerchants.filter(
                                (submerchant, submerchantIndex) => {
                                  return submerchantIndex !== index;
                                }
                              );

                              let newErrors = newSubmerchantErrors.filter(
                                (submerchant, submerchantIndex) => {
                                  return submerchantIndex !== index;
                                }
                              );

                              if (newValue.length === 0) {
                                newValue.push({
                                  serviceType: null,
                                  subMerchantId: null,
                                  merchant: null,
                                });
                                newErrors.push({});
                              }

                              setNewSubmerchants(newValue);
                              setNewSubmerchantErrors(newErrors);
                            }}
                          >
                            Delete
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon="minus-circle"
                            color="red"
                            onClick={() => {
                              let newValue = newSubmerchants.filter(
                                (submerchant, submerchantIndex) => {
                                  return submerchantIndex !== index;
                                }
                              );

                              let newErrors = newSubmerchantErrors.filter(
                                (submerchant, submerchantIndex) => {
                                  return submerchantIndex !== index;
                                }
                              );

                              if (newValue.length === 0) {
                                newValue.push({
                                  serviceType: null,
                                  subMerchantId: null,
                                  merchant: null,
                                });
                                newErrors.push({});
                              }

                              setNewSubmerchants(newValue);
                              setNewSubmerchantErrors(newErrors);
                            }}
                          />
                        )}
                      </ActionFields>
                    </PageSubsection>
                  );
                })}
              </SubMerchantSection>
              <DataHeader>
                <DataHeader.Title>PS OR PAYMENT TYPE</DataHeader.Title>
              </DataHeader>
              <PSORPaymentType>
                {!loading &&
                  values &&
                  values.PSORPaymentType &&
                  values.PSORPaymentType.map((OR, index) => (
                    <PageSubsection key={index}>
                      <FormField
                        placeholder="NAME (VMS SKU/category)"
                        label="NAME (VMS SKU/category)"
                        name={`name-${OR.description}`}
                        type={FIELD_TYPES.TEXT}
                        value={OR.name}
                        onChange={event => {
                          handleORPaymentTypeValidation(event, index);
                          onChange.PSORPaymentType(
                            values.PSORPaymentType.map((ORVal, valIndex) => {
                              if (
                                OR.name === ORVal.name &&
                                index === valIndex
                              ) {
                                setORValue(event.target.value);
                                return {
                                  or: OR.or,
                                  name: event.target.value,
                                  orVat: OR.orVat,
                                  description: OR.description,
                                };
                              }

                              return ORVal;
                            })
                          );
                        }}
                        onBlur={handleORPaymentTypeValidation}
                        error={
                          ORPaymentTypeError.name[`name-${OR.description}`]
                        }
                        perRow={4.5}
                        required
                      />
                      <FormField
                        placeholder="OR (Payment Type)"
                        label="OR (Payment Type)"
                        name={`or-${OR.name}`}
                        type={FIELD_TYPES.TEXT}
                        value={OR.or}
                        onChange={event => {
                          handleORorValidation(event);
                          onChange.PSORPaymentType(
                            values.PSORPaymentType.map((ORVal, valIndex) => {
                              if (
                                ORVal.name === OR.name &&
                                index === valIndex
                              ) {
                                return {
                                  or: event.target.value,
                                  name: OR.name,
                                  orVat: OR.orVat,
                                  description: OR.description,
                                };
                              }

                              return ORVal;
                            })
                          );
                        }}
                        onBlur={handleORorValidation}
                        error={ORPaymentTypeError.or[`or-${OR.name}`]}
                        perRow={4.5}
                        required
                      />
                      <FormField
                        placeholder="ORVAT (vat type)"
                        label="ORVAT (vat type)"
                        name={`orVat-${OR.name}`}
                        type={FIELD_TYPES.TEXT}
                        value={OR.orVat}
                        onChange={event => {
                          handleORorVatValidation(event);
                          onChange.PSORPaymentType(
                            values.PSORPaymentType.map((ORVal, valIndex) => {
                              if (
                                ORVal.name === OR.name &&
                                index === valIndex
                              ) {
                                return {
                                  or: OR.or,
                                  name: OR.name,
                                  orVat: event.target.value,
                                  description: OR.description,
                                };
                              }

                              return ORVal;
                            })
                          );
                        }}
                        onBlur={handleORorVatValidation}
                        error={ORPaymentTypeError.orVat[`orVat-${OR.name}`]}
                        perRow={4.5}
                        required
                      />
                      <FormField
                        placeholder="DESCRIPTION"
                        label="DESCRIPTION"
                        name={`description-${OR.name}`}
                        type={FIELD_TYPES.TEXT}
                        value={OR.description}
                        onChange={event => {
                          handleORDescriptionValidation(event);
                          onChange.PSORPaymentType(
                            values.PSORPaymentType.map((ORVal, valIndex) => {
                              if (
                                ORVal.name === OR.name &&
                                index === valIndex
                              ) {
                                return {
                                  or: OR.or,
                                  name: OR.name,
                                  orVat: OR.orVat,
                                  description: event.target.value,
                                };
                              }

                              return ORVal;
                            })
                          );
                        }}
                        onBlur={handleORDescriptionValidation}
                        error={
                          ORPaymentTypeError.description[
                            `description-${OR.name}`
                          ]
                        }
                        perRow={4.5}
                      />
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      >
                        {isMobile ? (
                          <ActionButton
                            backgroundColor={'red'}
                            onClick={() => {
                              setSelectedOR(OR);
                            }}
                          >
                            Delete
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon={'minus-circle'}
                            color={'red'}
                            onClick={() => {
                              setSelectedOR(OR);
                            }}
                          />
                        )}
                      </ActionFields>
                    </PageSubsection>
                  ))}
                {newORs.map((newOR, index) => {
                  return (
                    <PageSubsection key={index}>
                      <FormField
                        placeholder="NAME (VMS SKU/category)"
                        label="NAME (VMS SKU/category)"
                        name={`newOR-${index}`}
                        type={FIELD_TYPES.TEXT}
                        value={newOR.name}
                        onChange={event => {
                          let newValue = newORs.map((newValue, newIndex) => {
                            if (newIndex === index) {
                              return {
                                name: event.target.value,
                                or: newOR.or,
                                orVat: newOR.orVat,
                                description: newOR.description,
                              };
                            }

                            return newValue;
                          });

                          setNewORs(newValue);
                          handleNewPaymentTypeValidation(event, index);
                        }}
                        onBlur={event =>
                          handleNewPaymentTypeValidation(event, index)
                        }
                        error={newORErrors[index].name}
                        perRow={4.5}
                        required
                      />
                      <FormField
                        placeholder="OR (Payment Type)"
                        label="OR (Payment Type)"
                        name={`newOR-${index}`}
                        type={FIELD_TYPES.TEXT}
                        value={newOR.or}
                        onChange={event => {
                          let newValue = newORs.map((newValue, newIndex) => {
                            if (newIndex === index) {
                              return {
                                name: newOR.name,
                                or: event.target.value,
                                orVat: newOR.orVat,
                                description: newOR.description,
                              };
                            }

                            return newValue;
                          });

                          setNewORs(newValue);
                          handleNewORValidation(event, index);
                        }}
                        onBlur={event => handleNewORValidation(event, index)}
                        error={newORErrors[index].or}
                        perRow={4.5}
                        required
                      />
                      <FormField
                        placeholder="ORVAT (vat type)"
                        label="ORVAT (vat type)"
                        name={`newOR-${index}`}
                        type={FIELD_TYPES.TEXT}
                        value={newOR.orVat}
                        onChange={event => {
                          let newValue = newORs.map((newValue, newIndex) => {
                            if (newIndex === index) {
                              return {
                                name: newOR.name,
                                or: newOR.or,
                                orVat: event.target.value,
                                description: newOR.description,
                              };
                            }

                            return newValue;
                          });

                          setNewORs(newValue);
                          handleNewORVATValidation(event, index);
                        }}
                        onBlur={event => handleNewORVATValidation(event, index)}
                        error={newORErrors[index].orVat}
                        perRow={4.5}
                        required
                      />
                      <FormField
                        placeholder="DESCRIPTION"
                        label="DESCRIPTION"
                        name={`newOR-${index}`}
                        type={FIELD_TYPES.TEXT}
                        value={newOR.description}
                        onChange={event => {
                          let newValue = newORs.map((newValue, newIndex) => {
                            if (newIndex === index) {
                              return {
                                name: newOR.name,
                                or: newOR.or,
                                orVat: newOR.orVat,
                                description: event.target.value,
                              };
                            }

                            return newValue;
                          });

                          setNewORs(newValue);
                          handleNewORDescriptionValidation(event, index);
                        }}
                        onBlur={event =>
                          handleNewORDescriptionValidation(event, index)
                        }
                        error={newORErrors[index].description}
                        perRow={4.5}
                      />
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      >
                        {!(
                          newORErrors[index].name ||
                          newORErrors[index].or ||
                          newORErrors[index].orVat ||
                          newORErrors[index].description
                        ) &&
                          newOR.name &&
                          newOR.or &&
                          newOR.orVat &&
                          index === newORs.length - 1 &&
                          (isMobile ? (
                            <ActionButton
                              backgroundColor="green"
                              onClick={() => {
                                setNewORs([
                                  ...newORs,
                                  {
                                    name: null,
                                    or: null,
                                    orVat: null,
                                    description: null,
                                  },
                                ]);
                                setNewORErrors([...newORErrors, {}]);
                              }}
                            >
                              Add
                            </ActionButton>
                          ) : (
                            <ActionIcon
                              icon="plus-circle"
                              color="green"
                              onClick={() => {
                                setNewORs([
                                  ...newORs,
                                  {
                                    name: null,
                                    or: null,
                                    orVat: null,
                                    description: null,
                                  },
                                ]);
                                setNewORErrors([...newORErrors, {}]);
                              }}
                            />
                          ))}
                        {isMobile ? (
                          <ActionButton
                            backgroundColor="red"
                            onClick={() => {
                              let newValue = newORs.filter((OR, ORIndex) => {
                                return ORIndex !== index;
                              });

                              let newErrors = newORErrors.filter(
                                (OR, ORIndex) => {
                                  return ORIndex !== index;
                                }
                              );

                              if (newValue.length === 0) {
                                newValue.push({
                                  name: null,
                                  or: null,
                                  orVat: null,
                                  description: null,
                                });
                                newErrors.push({});
                              }

                              setNewORs(newValue);
                              setNewORErrors(newErrors);
                            }}
                          >
                            Delete
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon="minus-circle"
                            color="red"
                            onClick={() => {
                              let newValue = newORs.filter((OR, ORIndex) => {
                                return ORIndex !== index;
                              });

                              let newErrors = newORErrors.filter(
                                (OR, ORIndex) => {
                                  return ORIndex !== index;
                                }
                              );

                              if (newValue.length === 0) {
                                newValue.push({
                                  name: null,
                                  or: null,
                                  orVat: null,
                                  description: null,
                                });
                                newErrors.push({});
                              }

                              setNewORs(newValue);
                              setNewORErrors(newErrors);
                            }}
                          />
                        )}
                      </ActionFields>
                    </PageSubsection>
                  );
                })}
              </PSORPaymentType>
              <DataHeader>
                <DataHeader.Title>SWIPE OR PAYMENT TYPE</DataHeader.Title>
              </DataHeader>
              <SWIPEORPaymentType>
                {!loading &&
                  values &&
                  values.swipeORPaymentType &&
                  values.swipeORPaymentType.map((OR, index) => (
                    <PageSubsection key={index}>
                      <FormField
                        placeholder="NAME (VMS SKU/category)"
                        label="NAME (VMS SKU/category)"
                        name={`name-${OR.description}`}
                        type={FIELD_TYPES.TEXT}
                        value={OR.name}
                        onChange={event => {
                          handleSwipeORPaymentTypeValidation(event, index);
                          onChange.swipeORPaymentType(
                            values.swipeORPaymentType.map((ORVal, valIndex) => {
                              if (
                                OR.name === ORVal.name &&
                                index === valIndex
                              ) {
                                setSWIPEORValue(event.target.value);
                                return {
                                  or: OR.or,
                                  name: event.target.value,
                                  orVat: OR.orVat,
                                  description: OR.description,
                                };
                              }

                              return ORVal;
                            })
                          );
                        }}
                        onBlur={handleSwipeORPaymentTypeValidation}
                        error={
                          SWIPEORPaymentTypeError.name[`name-${OR.description}`]
                        }
                        perRow={4.5}
                        required
                      />
                      <FormField
                        placeholder="OR (Payment Type)"
                        label="OR (Payment Type)"
                        name={`or-${OR.name}`}
                        type={FIELD_TYPES.TEXT}
                        value={OR.or}
                        onChange={event => {
                          handleSwipeORorValidation(event);
                          onChange.swipeORPaymentType(
                            values.swipeORPaymentType.map((ORVal, valIndex) => {
                              if (
                                ORVal.name === OR.name &&
                                index === valIndex
                              ) {
                                return {
                                  or: event.target.value,
                                  name: OR.name,
                                  orVat: OR.orVat,
                                  description: OR.description,
                                };
                              }

                              return ORVal;
                            })
                          );
                        }}
                        onBlur={handleSwipeORorValidation}
                        error={SWIPEORPaymentTypeError.or[`or-${OR.name}`]}
                        perRow={4.5}
                        required
                      />
                      <FormField
                        placeholder="ORVAT (vat type)"
                        label="ORVAT (vat type)"
                        name={`orVat-${OR.name}`}
                        type={FIELD_TYPES.TEXT}
                        value={OR.orVat}
                        onChange={event => {
                          handleSwipeORorVatValidation(event);
                          onChange.swipeORPaymentType(
                            values.swipeORPaymentType.map((ORVal, valIndex) => {
                              if (
                                ORVal.name === OR.name &&
                                index === valIndex
                              ) {
                                return {
                                  or: OR.or,
                                  name: OR.name,
                                  orVat: event.target.value,
                                  description: OR.description,
                                };
                              }

                              return ORVal;
                            })
                          );
                        }}
                        onBlur={handleSwipeORorVatValidation}
                        error={
                          SWIPEORPaymentTypeError.orVat[`orVat-${OR.name}`]
                        }
                        perRow={4.5}
                        required
                      />
                      <FormField
                        placeholder="DESCRIPTION"
                        label="DESCRIPTION"
                        name={`description-${OR.name}`}
                        type={FIELD_TYPES.TEXT}
                        value={OR.description}
                        onChange={event => {
                          handleSwipeORDescriptionValidation(event);
                          onChange.swipeORPaymentType(
                            values.swipeORPaymentType.map((ORVal, valIndex) => {
                              if (
                                ORVal.name === OR.name &&
                                index === valIndex
                              ) {
                                return {
                                  or: OR.or,
                                  name: OR.name,
                                  orVat: OR.orVat,
                                  description: event.target.value,
                                };
                              }

                              return ORVal;
                            })
                          );
                        }}
                        onBlur={handleSwipeORDescriptionValidation}
                        error={
                          SWIPEORPaymentTypeError.description[
                            `description-${OR.name}`
                          ]
                        }
                        perRow={4.5}
                      />
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      >
                        {isMobile ? (
                          <ActionButton
                            backgroundColor={'red'}
                            onClick={() => {
                              setSelectedSWIPEOR(OR);
                            }}
                          >
                            Delete
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon={'minus-circle'}
                            color={'red'}
                            onClick={() => {
                              setSelectedSWIPEOR(OR);
                            }}
                          />
                        )}
                      </ActionFields>
                    </PageSubsection>
                  ))}
                {newSWIPEORs.map((newOR, index) => {
                  return (
                    <PageSubsection key={index}>
                      <FormField
                        placeholder="NAME (VMS SKU/category)"
                        label="NAME (VMS SKU/category)"
                        name={`newOR-${index}`}
                        type={FIELD_TYPES.TEXT}
                        value={newOR.name}
                        onChange={event => {
                          let newValue = newSWIPEORs.map(
                            (newValue, newIndex) => {
                              if (newIndex === index) {
                                return {
                                  name: event.target.value,
                                  or: newOR.or,
                                  orVat: newOR.orVat,
                                  description: newOR.description,
                                };
                              }

                              return newValue;
                            }
                          );

                          setNewSWIPEORs(newValue);
                          handleNewSwipePaymentTypeValidation(event, index);
                        }}
                        onBlur={event =>
                          handleNewSwipePaymentTypeValidation(event, index)
                        }
                        error={newSWIPEORErrors[index].name}
                        perRow={4.5}
                        required
                      />
                      <FormField
                        placeholder="OR (Payment type)"
                        label="OR (Payment Type)"
                        name={`newOR-${index}`}
                        type={FIELD_TYPES.TEXT}
                        value={newOR.or}
                        onChange={event => {
                          let newValue = newSWIPEORs.map(
                            (newValue, newIndex) => {
                              if (newIndex === index) {
                                return {
                                  name: newOR.name,
                                  or: event.target.value,
                                  orVat: newOR.orVat,
                                  description: newOR.description,
                                };
                              }

                              return newValue;
                            }
                          );

                          setNewSWIPEORs(newValue);
                          handleNewSwipeORValidation(event, index);
                        }}
                        onBlur={event =>
                          handleNewSwipeORValidation(event, index)
                        }
                        error={newSWIPEORErrors[index].or}
                        perRow={4.5}
                        required
                      />
                      <FormField
                        placeholder="ORVAT (vat type)"
                        label="ORVAT (vat type)"
                        name={`newOR-${index}`}
                        type={FIELD_TYPES.TEXT}
                        value={newOR.orVat}
                        onChange={event => {
                          let newValue = newSWIPEORs.map(
                            (newValue, newIndex) => {
                              if (newIndex === index) {
                                return {
                                  name: newOR.name,
                                  or: newOR.or,
                                  orVat: event.target.value,
                                  description: newOR.description,
                                };
                              }

                              return newValue;
                            }
                          );

                          setNewSWIPEORs(newValue);
                          handleNewSwipeORVATValidation(event, index);
                        }}
                        onBlur={event =>
                          handleNewSwipeORVATValidation(event, index)
                        }
                        error={newSWIPEORErrors[index].orVat}
                        perRow={4.5}
                        required
                      />
                      <FormField
                        placeholder="DESCRIPTION"
                        label="DESCRIPTION"
                        name={`newOR-${index}`}
                        type={FIELD_TYPES.TEXT}
                        value={newOR.description}
                        onChange={event => {
                          let newValue = newSWIPEORs.map(
                            (newValue, newIndex) => {
                              if (newIndex === index) {
                                return {
                                  name: newOR.name,
                                  or: newOR.or,
                                  orVat: newOR.orVat,
                                  description: event.target.value,
                                };
                              }

                              return newValue;
                            }
                          );

                          setNewSWIPEORs(newValue);
                          handleNewSwipeORDescriptionValidation(event, index);
                        }}
                        onBlur={event =>
                          handleNewSwipeORDescriptionValidation(event, index)
                        }
                        error={newSWIPEORErrors[index].description}
                        perRow={4.5}
                      />
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      >
                        {!(
                          newSWIPEORErrors[index].name ||
                          newSWIPEORErrors[index].or ||
                          newSWIPEORErrors[index].orVat ||
                          newSWIPEORErrors[index].description
                        ) &&
                          newOR.name &&
                          newOR.or &&
                          newOR.orVat &&
                          index === newSWIPEORs.length - 1 &&
                          (isMobile ? (
                            <ActionButton
                              backgroundColor="green"
                              onClick={() => {
                                setNewSWIPEORs([
                                  ...newSWIPEORs,
                                  {
                                    name: null,
                                    or: null,
                                    orVat: null,
                                    description: null,
                                  },
                                ]);
                                setNewSWIPEORErrors([...newSWIPEORErrors, {}]);
                              }}
                            >
                              Add
                            </ActionButton>
                          ) : (
                            <ActionIcon
                              icon="plus-circle"
                              color="green"
                              onClick={() => {
                                setNewSWIPEORs([
                                  ...newSWIPEORs,
                                  {
                                    name: null,
                                    or: null,
                                    orVat: null,
                                    description: null,
                                  },
                                ]);
                                setNewSWIPEORErrors([...newSWIPEORErrors, {}]);
                              }}
                            />
                          ))}
                        {isMobile ? (
                          <ActionButton
                            backgroundColor="red"
                            onClick={() => {
                              let newValue = newSWIPEORs.filter(
                                (OR, ORIndex) => {
                                  return ORIndex !== index;
                                }
                              );

                              let newErrors = newSWIPEORErrors.filter(
                                (OR, ORIndex) => {
                                  return ORIndex !== index;
                                }
                              );

                              if (newValue.length === 0) {
                                newValue.push({
                                  name: null,
                                  or: null,
                                  orVat: null,
                                  description: null,
                                });
                                newErrors.push({});
                              }

                              setNewSWIPEORs(newValue);
                              setNewSWIPEORErrors(newErrors);
                            }}
                          >
                            Delete
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon="minus-circle"
                            color="red"
                            onClick={() => {
                              let newValue = newSWIPEORs.filter(
                                (OR, ORIndex) => {
                                  return ORIndex !== index;
                                }
                              );

                              let newErrors = newSWIPEORErrors.filter(
                                (OR, ORIndex) => {
                                  return ORIndex !== index;
                                }
                              );

                              if (newValue.length === 0) {
                                newValue.push({
                                  name: null,
                                  or: null,
                                  orVat: null,
                                  description: null,
                                });
                                newErrors.push({});
                              }

                              setNewSWIPEORs(newValue);
                              setNewSWIPEORErrors(newErrors);
                            }}
                          />
                        )}
                      </ActionFields>
                    </PageSubsection>
                  );
                })}
              </SWIPEORPaymentType>
              <DataHeader>
                <DataHeader.Title>AMAX</DataHeader.Title>
              </DataHeader>
              <PageSubsection>
                <FormField
                  placeholder=""
                  label="Amax Load Consumer"
                  name="amaxLoadConsumer"
                  type={FIELD_TYPES.TEXT}
                  value={values.amaxLoadConsumer}
                  onChange={onChange.amaxLoadConsumer}
                  onBlur={onBlur.amaxLoadConsumer}
                  error={errors.amaxLoadConsumer}
                  perRow={2}
                />
                <FormField
                  placeholder=""
                  label="Amax Load Retailer"
                  name="amaxLoadRetailer"
                  type={FIELD_TYPES.TEXT}
                  value={values.amaxLoadRetailer}
                  onChange={onChange.amaxLoadRetailer}
                  onBlur={onBlur.amaxLoadRetailer}
                  error={errors.amaxLoadRetailer}
                  perRow={2}
                />
              </PageSubsection>
              <DataHeader>
                <DataHeader.Title>Refund</DataHeader.Title>
              </DataHeader>
              <PageSubsection>
                <FormField
                  placeholder=""
                  label="Allowed Retry"
                  name="retry"
                  type={FIELD_TYPES.SELECT}
                  value={values.gcashRefundRetries}
                  onChange={onChange.gcashRefundRetries}
                  onBlur={onBlur.gcashRefundRetries}
                  error={errors.gcashRefundRetries}
                  options={[
                    { label: '0', value: '0' },
                    { label: '1', value: '1' },
                    { label: '2', value: '2' },
                    { label: '3', value: '3' },
                    { label: '4', value: '4' },
                    { label: '5', value: '5' },
                  ]}
                  perRow={2}
                  required
                />
              </PageSubsection>
              <PageSubsection>
                <p
                  style={{
                    fontSize: 13,
                    marginTop: -20,
                    marginBottom: 20,
                    color: '#777777',
                    fontStyle: 'italic',
                  }}
                >
                  Set value to 0 to turn off
                </p>
              </PageSubsection>
              <DataHeader>
                <DataHeader.Title>Payment Methods</DataHeader.Title>
              </DataHeader>
              <PageSubsection>
                <FormField
                  placeholder=""
                  label="GCash"
                  name="paymentMethodGcash"
                  type={FIELD_TYPES.TOGGLE}
                  value={values.paymentMethodGcash}
                  onChange={onChange.paymentMethodGcash}
                  onBlur={onBlur.paymentMethodGcash}
                  error={errors.paymentMethodGcash}
                  options={PAYMENT_METHODS.gcash}
                  perRow={2}
                  required
                />
                <FormField
                  placeholder=""
                  label="Card"
                  name="paymentMethodCard"
                  type={FIELD_TYPES.TOGGLE}
                  value={values.paymentMethodCard}
                  onChange={onChange.paymentMethodCard}
                  onBlur={onBlur.paymentMethodCard}
                  error={errors.paymentMethodCard}
                  options={PAYMENT_METHODS.card}
                  perRow={2}
                  required
                />
              </PageSubsection>
              <DataHeader>
                <DataHeader.Title>
                  Xendit Callback IP Whitelisting
                </DataHeader.Title>
              </DataHeader>
              <CCRefundReasonSection>
                {!loading &&
                  values &&
                  values.configIpWhitelist &&
                  values.configIpWhitelist.xendit &&
                  values.configIpWhitelist.xendit.callback &&
                  values.configIpWhitelist.xendit.callback.map((ip, index) => {
                    return (
                      <PageSubsection key={ip + '-' + index}>
                        <FormField
                          placeholder=""
                          label="IP Address"
                          name={`ip-${ip}`}
                          type={FIELD_TYPES.TEXT}
                          value={ip}
                          readOnly={true}
                          perRow={2}
                        />
                        <ActionFields
                          isMobile={isMobile}
                          fieldsPerRow={2.5}
                          perRow={5}
                        >
                          {isMobile ? (
                            <ActionButton
                              backgroundColor={'red'}
                              onClick={() => {
                                setSelectedIPWhitelist({
                                  callback: ip,
                                });
                              }}
                            >
                              Delete
                            </ActionButton>
                          ) : (
                            <ActionIcon
                              icon={'minus-circle'}
                              color={'red'}
                              onClick={() => {
                                setSelectedIPWhitelist({
                                  callback: ip,
                                });
                              }}
                            />
                          )}
                        </ActionFields>
                      </PageSubsection>
                    );
                  })}
                {newIPWhitelist &&
                  newIPWhitelist.xendit &&
                  newIPWhitelist.xendit.callback &&
                  newIPWhitelist.xendit.callback.map((newIP, index) => {
                    return (
                      <PageSubsection key={index}>
                        <FormField
                          placeholder="IP Address"
                          label="IP Address"
                          name={`newIP-${index}`}
                          type={FIELD_TYPES.TEXT}
                          value={newIP}
                          onChange={event => {
                            let newValue = newIPWhitelist.xendit.callback.map(
                              (newValue, newIndex) => {
                                if (newIndex === index) {
                                  return event.target.value;
                                }

                                return newValue;
                              }
                            );
                            setNewIPWhitelist({
                              xendit: {
                                callback: newValue,
                              },
                            });
                            handleNewIPWhitelistValidation(event, index);
                          }}
                          onBlur={event =>
                            handleNewIPWhitelistValidation(event, index)
                          }
                          error={newIPWhitelistingErrors[index]}
                          perRow={2}
                        />
                        <ActionFields
                          isMobile={isMobile}
                          fieldsPerRow={2.5}
                          perRow={5}
                        >
                          {!newIPWhitelistingErrors[index] &&
                            newIP &&
                            index ===
                              newIPWhitelist.xendit.callback.length - 1 &&
                            (isMobile ? (
                              <ActionButton
                                backgroundColor="green"
                                onClick={() => {
                                  setNewIPWhitelist({
                                    xendit: {
                                      callback: [
                                        ...newIPWhitelist.xendit.callback,
                                        '',
                                      ],
                                    },
                                  });
                                  setNewIPWhitelistingErrors([
                                    ...newIPWhitelistingErrors,
                                    '',
                                  ]);
                                }}
                              >
                                Add
                              </ActionButton>
                            ) : (
                              <ActionIcon
                                icon="plus-circle"
                                color="green"
                                onClick={() => {
                                  setNewIPWhitelist({
                                    xendit: {
                                      callback: [
                                        ...newIPWhitelist.xendit.callback,
                                        '',
                                      ],
                                    },
                                  });
                                  setNewIPWhitelistingErrors([
                                    ...newIPWhitelistingErrors,
                                    '',
                                  ]);
                                }}
                              />
                            ))}
                          {isMobile ? (
                            <ActionButton
                              backgroundColor="red"
                              onClick={() => {
                                if (
                                  newIPWhitelist.xendit.callback.length !== 1
                                ) {
                                  let arr =
                                    newIPWhitelist.xendit.callback.filter(
                                      (ip, ipIndex) => {
                                        return ipIndex !== index;
                                      }
                                    );

                                  let newValue = {
                                    xendit: {
                                      callback: [...arr],
                                    },
                                  };

                                  let arrErr = newIPWhitelistingErrors.filter(
                                    (ip, ipIndex) => {
                                      return ipIndex !== index;
                                    }
                                  );

                                  let newErrors = [...arrErr];

                                  setNewIPWhitelist(newValue);
                                  setNewIPWhitelistingErrors(newErrors);
                                } else if (
                                  newIPWhitelist.xendit.callback.length === 1
                                ) {
                                  setNewIPWhitelist({
                                    xendit: {
                                      callback: [''],
                                    },
                                  });
                                  setNewIPWhitelistingErrors(['']);
                                }
                              }}
                            >
                              Delete
                            </ActionButton>
                          ) : (
                            <ActionIcon
                              icon="minus-circle"
                              color="red"
                              onClick={() => {
                                if (
                                  newIPWhitelist.xendit.callback.length !== 1
                                ) {
                                  let arr =
                                    newIPWhitelist.xendit.callback.filter(
                                      (ip, ipIndex) => {
                                        return ipIndex !== index;
                                      }
                                    );

                                  let newValue = {
                                    xendit: {
                                      callback: [...arr],
                                    },
                                  };

                                  let arrErr = newIPWhitelistingErrors.filter(
                                    (ip, ipIndex) => {
                                      return ipIndex !== index;
                                    }
                                  );

                                  let newErrors = [...arrErr];

                                  setNewIPWhitelist(newValue);
                                  setNewIPWhitelistingErrors(newErrors);
                                } else if (
                                  newIPWhitelist.xendit.callback.length === 1
                                ) {
                                  setNewIPWhitelist({
                                    xendit: {
                                      callback: [''],
                                    },
                                  });
                                  setNewIPWhitelistingErrors(['']);
                                }
                              }}
                            />
                          )}
                        </ActionFields>
                      </PageSubsection>
                    );
                  })}
              </CCRefundReasonSection>
              <PageSubsection>
                <ButtonsContainer style={{ justifyContent: 'flex-end' }}>
                  <PrimaryButton
                    disabled={
                      !isFormValid ||
                      !hasEdited ||
                      Object.keys(submerchantErrors.id).length > 0 ||
                      Object.keys(ORPaymentTypeError.name).length > 0 ||
                      Object.keys(ORPaymentTypeError.or).length > 0 ||
                      Object.keys(ORPaymentTypeError.orVat).length > 0 ||
                      Object.keys(ORPaymentTypeError.description).length > 0 ||
                      Object.keys(SWIPEORPaymentTypeError.name).length > 0 ||
                      Object.keys(SWIPEORPaymentTypeError.or).length > 0 ||
                      Object.keys(SWIPEORPaymentTypeError.orVat).length > 0 ||
                      Object.keys(SWIPEORPaymentTypeError.description).length >
                        0 ||
                      newSubmerchantErrors.find(
                        newError =>
                          newError.subMerchantId || newError.serviceType
                      ) ||
                      newReasonErrors.find(newError => newError.reason) ||
                      newIPWhitelistingErrors.find(newError => newError) ||
                      newCCReasonErrors.find(newError => newError.reason) ||
                      newORErrors.find(
                        newError =>
                          newError.name ||
                          newError.or ||
                          newError.orVat ||
                          newError.description
                      ) ||
                      newSWIPEORErrors.find(
                        newError =>
                          newError.name ||
                          newError.or ||
                          newError.orVat ||
                          newError.description
                      ) ||
                      newSubmerchants.find(
                        newSubmerchant =>
                          newSubmerchant.serviceType &&
                          newSubmerchant.subMerchantId &&
                          newSubmerchant.merchant === null
                      ) ||
                      newORs.find(
                        newOR => newOR.name && newOR.or && newOR.orVat === null
                      ) ||
                      newSWIPEORs.find(
                        newOR => newOR.name && newOR.or && newOR.orVat === null
                      ) ||
                      //Report Apply Validation
                      Object.keys(dailyGCashError.email).length > 0 ||
                      Object.keys(monthlyGCashError.email).length > 0 ||
                      Object.keys(ecpayError.email).length > 0 ||
                      Object.keys(globeOneError.email).length > 0 ||
                      Object.keys(collectionError.email).length > 0 ||
                      Object.keys(creditCardError.email).length > 0 ||
                      Object.keys(channelError.email).length > 0 ||
                      Object.keys(billingError.email).length > 0 ||
                      newDailyGCashErrors.find(newError => newError.email) ||
                      newMonthlyGCashErrors.find(newError => newError.email) ||
                      newEcpayErrors.find(newError => newError.email) ||
                      newGlobeOneErrors.find(newError => newError.email) ||
                      newCollectionErrors.find(newError => newError.email) ||
                      newCreditCardErrors.find(newError => newError.email) ||
                      newChannelErrors.find(newError => newError.email) ||
                      newBillingErrors.find(newError => newError.email)
                    }
                    onClick={() => {
                      setIsConfirmModalOpen(true);
                    }}
                  >
                    Apply
                  </PrimaryButton>
                </ButtonsContainer>
              </PageSubsection>
            </>
          )}
        </DataContainer>
      </Page>
      <AlertModal
        isOpen={isConfirmModalOpen}
        title="Save Changes Alert"
        variant="warn"
        icon="exclamation-circle"
        header="ARE YOU SURE?"
        subHeader="You are about to save changes for the system configurations."
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone',
        ]}
        confirmLoading={isEditingConfig}
        confirmText="Yes"
        handleConfirm={() => {
          onSubmit();
        }}
        handleClose={() => {
          setIsConfirmModalOpen(false);
        }}
      />
      <AlertModal
        isOpen={isLeavingWhileEditing}
        title="New Account Status"
        icon="question-circle"
        variant="warn"
        header="THERE ARE UNSAVED CHANGES."
        subHeader="Are you sure you want to leave this page without saving?"
        description="Your changes will be lost if you don't save them."
        handleClose={() => setIsLeavingWhileEditing(false)}
        cancelText="Discard Changes"
        confirmText="Go Back"
        handleCancel={() => {
          if (nextLocation) history.push(nextLocation);
        }}
        handleConfirm={() => {
          setIsLeavingWhileEditing(false);
        }}
      />
      <AlertModal
        isOpen={isSuccessModalOpen}
        title="Save Changes Alert"
        variant="success"
        icon="check-circle"
        header="SUCCESS!"
        subHeader="Changes have been saved successfully."
        description=""
        confirmText="Go back"
        handleConfirm={() => {
          setIsSuccessModalOpen(false);
        }}
        handleClose={() => setIsSuccessModalOpen(false)}
      />
      <AlertModal
        isOpen={isFailureModalOpen}
        title="Save Changes Alert"
        variant="error"
        icon="times-circle"
        header="OH, SNAP!"
        subHeader={
          (errorMessage ===
            `Duplicate OR Payment Type: [${ORValue || SWIPEORValue}]` &&
            errorMessage) ||
          (errorMessage &&
            errorMessage[0].message &&
            errorMessage[0].message.split(' ') &&
            errorMessage[0].message.split(' ')[0] &&
            reportLabel &&
            `${reportLabel} Email exceed more than 20 limit
            `) ||
          'There was a problem on saving changes on the System Configuration.'
        }
        description={'Please go back and try saving it again.'}
        confirmText="Go back"
        handleConfirm={() => {
          setIsFailureModalOpen(false);
        }}
        handleClose={() => setIsFailureModalOpen(false)}
      />
      <AlertModal
        isOpen={subMerchantDeleteErr}
        title="Deletion Error Alert"
        variant="error"
        icon="times-circle"
        header="OH, SNAP!"
        subHeader={
          "SubMerchant is currently in used by a Channel, you can't delete this one."
        }
        description={'Please remove the SubMerchant to the Channel first.'}
        confirmText="Go back"
        handleConfirm={() => {
          setSubMerchantDeleteErr(false);
        }}
        handleClose={() => setSubMerchantDeleteErr(false)}
      />
    </>
  );
};

SystemConfig.propTypes = {
  history: PropTypes.object,
};

export default SystemConfig;
