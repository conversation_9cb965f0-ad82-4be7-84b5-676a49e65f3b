import { gql } from '@apollo/client';

export const EDIT_CONFIG = gql`
  mutation editConfig($data: UpdateConfigurations!) {
    updateConfig(data: $data) {
      refreshTime
      paymentServiceApiMaintenance
      globeEmailNotification
      globeEmailNotificationPatternId
      innoveEmailNotification
      innoveEmailNotificationPatternId
      bayanEmailNotification
      bayanEmailNotificationPatternId
      subMerchants {
        serviceType
        subMerchantId
        merchant
      }
      gcashRefundRetries
      refundReason {
        reason
      }
      cardRefundReason {
        reason
      }
      PSORPaymentType {
        description
        name
        or
        orVat
      }
      swipeORPaymentType {
        description
        name
        or
        orVat
      }
      dailyContentGCashReportRecipient {
        email
      }
      monthlyContentGCashReportRecipient {
        email
      }
      ecpayReportRecipient {
        email
      }
      globeOneReportRecipient {
        email
      }
      collectionReportRecipient {
        email
      }
      creditCardReportRecipient {
        email
      }
      channelReportRecipient {
        email
      }
      billingReportRecipient {
        email
      }
      configIpWhitelist {
        xendit {
          callback
        }
      }
    }
  }
`;
