import { gql } from '@apollo/client';

export const GET_CONFIGS = gql`
  query getConfig {
    configs {
      refreshTime
      paymentServiceApiMaintenance
      globeEmailNotification
      globeEmailNotificationPatternId
      innoveEmailNotification
      innoveEmailNotificationPatternId
      bayanEmailNotification
      bayanEmailNotificationPatternId
      subMerchants {
        serviceType
        subMerchantId
        merchant
      }
      paymentMethodGcash
      paymentMethodCard
      amaxLoadRetailer
      amaxLoadConsumer
      gcashRefundRetries
      refundReason {
        reason
      }
      cardRefundReason {
        reason
      }
      PSORPaymentType {
        description
        name
        or
        orVat
      }
      swipeORPaymentType {
        description
        name
        or
        orVat
      }
      dailyContentGCashReportPatternId
      dailyContentGCashReportRecipient {
        email
      }
      monthlyContentGCashReportPatternId
      monthlyContentGCashReportRecipient {
        email
      }
      ecpayReportEmailPatternId
      ecpayReportRecipient {
        email
      }
      globeOneReportEmailPatternId
      globeOneReportRecipient {
        email
      }
      collectionReportEmailPatternId
      collectionReportRecipient {
        email
      }
      creditCardReportEmailPatternId
      creditCardReportRecipient {
        email
      }
      channelReportEmailPatternId
      channelReportRecipient {
        email
      }
      billingReportPatternId
      billingReportRecipient {
        email
      }
      configIpWhitelist {
        xendit {
          callback
        }
      }
    }
  }
`;

export const GET_USED_SUBMERCHANT = gql`
  query getUsedSubMerchant($id: String!) {
    chanelSubMerchants(id: $id) {
      exist
    }
  }
`;
