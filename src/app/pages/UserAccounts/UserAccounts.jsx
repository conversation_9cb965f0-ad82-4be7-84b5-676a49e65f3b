import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import format from 'date-fns/format';
import { json2csv } from 'json-2-csv';
import PropTypes from 'prop-types';
import React, { useContext, useEffect, useState } from 'react';
import { useQuery } from '@apollo/client';
import * as Yup from 'yup';
import ActionButtons from '../../components/ActionButtons';
import CreateButton from '../../components/Button/CreateButton';
import FileButtonUsers from '../../components/Button/FileButtonUsers';
import DataContainer from '../../components/DataContainer';
import DataTable from '../../components/DataTable';
import { FIELD_TYPES } from '../../components/Form/constants';
import { Required } from '../../components/Form/FormField';
import GlobalSearch from '../../components/GlobalSearch';
import Header from '../../components/Header/Header';
import { AlertModal, FormModal } from '../../components/Modal';
import Page from '../../components/Page';
import AuthContext from '../../context/AuthContext/AuthContext';
import { useMutation } from '@apollo/client';
import sanitize from '../../utils/sanitize';
import {
  ADD_USER,
  DELETE_USER,
  DELETE_USERS,
  EXPORT_USERS,
  IMPORT_USERS,
  IMPORT_BULK_DEPROVISIONING_USERS,
  DEACTIVATE_USERS,
} from './mutation';
import { GET_USER_ACCOUNTS_INFORMATION } from './query';
import { formatSubHeader, formatDescription } from '../../utils/getLabel';

const UserAccounts = ({ history }) => {
  const { authUser, permissions } = useContext(AuthContext);

  const [state, setState] = useState({
    isAddUserModalOpen: false,
    isConfirmAddUserModalOpen: false,
    isSuccessAddUserModalOpen: false,

    isSuccessAddUsersModalOpen: false,
    isFailureAddUsersModalOpen: false,

    isLeavingPageWhileAdding: false,

    isConfirmDeleteUserModalOpen: false,
    isSuccessDeleteUserModalOpen: false,
    isFailureDeleteUserModalOpen: false,

    isConfirmDeleteUsersModalOpen: false,
    isSuccessDeleteUsersModalOpen: false,
    isFailureDeleteUsersModalOpen: false,

    isConfirmDeactivateUsersModalOpen: false,
    isSuccessDeactivateUsersModalOpen: false,
    isFailureDeactivateUsersModalOpen: false,

    isConfirmDeactivateUserModalOpen: false,
    isSuccessDeactivateUserModalOpen: false,
    isFailureDeactivateUserModalOpen: false,

    isUpdateUserError: false,
    isUpdateUserSuccess: false,

    isFailureToDeactivateSelectedRowHasIsActive: false,

    isConfirmDownloadModalOpen: false,

    nextLocation: null,
    addUserError: null,
    selectedUser: null,
    importFileError: null,
    uploadErrorMessage: null,

    filter: {},
    pagination: {
      startKey: '',
      limit: 10,
    },
    selectedRows: {},
    status: false,
  });

  const [isFileInvalid, setIsFileInvalid] = useState(false);
  const [userCount, setUserCount] = useState(0);

  const { data, loading, refetch } = useQuery(GET_USER_ACCOUNTS_INFORMATION, {
    variables: {
      filter: state.filter,
      pagination: state.pagination,
    },
    fetchPolicy: 'network-only',
  });

  useEffect(() => {
    setState({ ...state, selectedRows: {} });
  }, [state.filter]);

  const [addUser, { loading: isAddingUser }] = useMutation(ADD_USER, {
    onCompleted: () => {
      setState({
        ...state,
        isConfirmAddUserModalOpen: false,
        isSuccessAddUserModalOpen: true,
        isAddUserModalOpen: false,
        pagination: {
          ...state.pagination,
          startKey: '',
        },
      });
      refetch();
    },
    onError: err => {
      setState({
        ...state,
        isConfirmAddUserModalOpen: false,
        addUserError: err.networkError.result
          ? err.networkError.result.message
          : null,
      });
    },
  });

  const [deleteUser, { loading: isDeletingUser }] = useMutation(DELETE_USER, {
    onCompleted: () => {
      const selectedRows = { ...state.selectedRows };
      if (selectedRows[state.selectedUser.id]) {
        delete selectedRows[state.selectedUser.id];
      }
      setState({
        ...state,
        isConfirmDeleteUserModalOpen: false,
        isSuccessDeleteUserModalOpen: true,
        selectedRows,
        selectedUser: null,
      });
      refetch();
    },
    onError: () => {
      setState({
        ...state,
        isConfirmDeleteUserModalOpen: false,
        isFailureDeleteUserModalOpen: true,
      });
    },
  });

  const [exportUsers, { loading: isExportingUsers }] = useMutation(
    EXPORT_USERS,
    {
      onCompleted: async data => {
        if (data.downloadUsers) {
          const keys = Object.keys(data.downloadUsers[0]).filter(
            key => key !== '__typename'
          );

          const csv = await json2csv(data.downloadUsers, {
            keys,
          });

          const fileData = {
            mime: 'text/csv',
            filename: 'user-accounts.csv',
            contents: csv,
          };
          const blob = new Blob([fileData.contents], {
            type: fileData.mime,
          });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          document.body.appendChild(a);
          a.download = fileData.filename;
          a.href = url;
          a.click();
          document.body.removeChild(a);
        }
      },
    }
  );
  const [importUsers, { loading: isImportingUsers }] = useMutation(
    IMPORT_USERS,
    {
      onCompleted: async () => {
        refetch();
        setState({ ...state, isSuccessAddUsersModalOpen: true });
      },
      onError: async err => {
        setState({
          ...state,
          isFailureAddUsersModalOpen: true,
          importFileError: err.networkError.result
            ? err.networkError.result.message
            : null,
        });
      },
    }
  );

  const [
    importBulkDeprovisioningUsers,
    {
      data: BulkDeprovisioningData,
      loading: isImportingBulkDeprovisioningUsers,
    },
  ] = useMutation(IMPORT_BULK_DEPROVISIONING_USERS, {
    onCompleted: async () => {
      refetch();
      setState({ ...state, isUpdateUserSuccess: true });
    },
    onError: async err => {
      setState({
        ...state,
        isUpdateUserError: true,
        importFileError: err.networkError.result.message
          ? err.networkError.result.message
          : null,
      });
    },
  });

  const [deleteUsers, { loading: isDeletingUsers }] = useMutation(
    DELETE_USERS,
    {
      onCompleted: () => {
        setState({
          ...state,
          isConfirmDeleteUsersModalOpen: false,
          isSuccessDeleteUsersModalOpen: true,
          selectedRows: {},
          pagination: {
            ...state.pagination,
            startKey: '',
          },
        });

        refetch();
      },
      onError: () => {
        setState({
          ...state,
          isConfirmDeleteUsersModalOpen: false,
          isFailureDeleteUsersModalOpen: true,
        });
      },
    }
  );

  const [deactivateUser, { loading: isDeactivatingUser }] = useMutation(
    DEACTIVATE_USERS,
    {
      onCompleted: () => {
        const selectedRows = { ...state.selectedRows };
        if (selectedRows[state.selectedUser.id]) {
          delete selectedRows[state.selectedUser.id];
        }
        setState({
          ...state,
          isConfirmDeactivateUserModalOpen: false,
          isSuccessDeactivateUserModalOpen: true,
          selectedRows,
          selectedUser: null,
        });
        refetch();
      },
      onError: () => {
        setState({
          ...state,
          isConfirmDeactivateUserModalOpen: false,
          isFailureDeactivateUserModalOpen: true,
        });
      },
    }
  );

  const [deactivateUsers, { loading: isDeactivateUsersLoading }] = useMutation(
    DEACTIVATE_USERS,
    {
      onCompleted: () => {
        setState({
          ...state,
          isConfirmDeactivateUsersModalOpen: false,
          isSuccessDeactivateUsersModalOpen: true,
          selectedRows: {},
          pagination: {
            ...state.pagination,
            startKey: '',
          },
        });

        refetch();
      },
      onError: () => {
        setState({
          ...state,
          isConfirmDeactivateUsersModalOpen: false,
          isFailureDeactivateUsersModalOpen: true,
        });
      },
    }
  );

  useEffect(() => {
    const unblock = history.block(location => {
      if (state.isLeavingPageWhileAdding || !state.isAddUserModalOpen) {
        return true;
      }
      setState({
        ...state,
        nextLocation: location,
        isLeavingPageWhileAdding: true,
      });
      return false;
    });

    return () => {
      unblock();
    };
  }, [state.isLeavingPageWhileAdding, state.isAddUserModalOpen]);

  const roleOptions =
    !loading && data && data.rolesLoose
      ? data.rolesLoose.map(role => ({
          value: role.id,
          label: role.name,
        }))
      : [];

  return (
    <>
      <Page>
        <Header
          withHome
          title="User Accounts"
          path={['User Mgt.', 'User Accounts']}
        />
        <DataContainer>
          <DataTable
            selected={state.selectedRows}
            setSelected={rows => {
              const selectedRows = { ...state.selectedRows };
              for (const row of rows) {
                if (selectedRows[row.id]) {
                  delete selectedRows[row.id];
                } else {
                  selectedRows[row.id] = row;
                }
              }

              let status = false;
              Object.keys(selectedRows).map(function (key) {
                if (!selectedRows[key].isActive) {
                  status = true;
                }
                return status;
              });

              setState({ ...state, selectedRows, status: status });
            }}
            loading={loading}
            data={data && data.users && data.users.filteredData}
            headerOptions={
              <>
                <GlobalSearch
                  onSearch={filter => {
                    setState({
                      ...state,
                      pagination: {
                        ...state.pagination,
                        startKey: '',
                      },
                      filter,
                    });
                  }}
                  fields={[
                    {
                      label: 'Email Address',
                      name: 'email',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Full Name',
                      name: 'name',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'User Role',
                      name: 'roleId',
                      type: FIELD_TYPES.MULTISELECT,
                      options: roleOptions,
                    },
                    {
                      label: 'Group',
                      name: 'group',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Status',
                      name: 'isActive',
                      type: FIELD_TYPES.SELECT,
                      options: [
                        { label: 'Any', value: null },
                        { label: 'Active', value: true },
                        { label: 'Inactive', value: false },
                      ],
                    },
                    {
                      label: 'Date Created',
                      name: 'createdAt',
                      type: FIELD_TYPES.DATE_RANGE,
                    },
                  ]}
                  placeholder={'Search Users'}
                />

                <div style={{ display: 'flex' }}>
                  {(permissions.User.export || permissions.User.import) && (
                    <FileButtonUsers
                      icon="file-csv"
                      iconPosition="left"
                      disabled={loading}
                      loading={
                        isExportingUsers ||
                        isImportingUsers ||
                        isImportingBulkDeprovisioningUsers
                      }
                      onImport={
                        permissions.User.import &&
                        (file => {
                          importUsers({ variables: { file } });
                        })
                      }
                      onImportUpdate={
                        permissions.User.deactivate &&
                        permissions.User.import &&
                        (file => {
                          let fileName = file.name.split('.').pop();
                          var reader = new FileReader();
                          reader.onload = function () {
                            var rowsR = this.result.split('\r').length;
                            setUserCount(rowsR - 1);
                            if (file.size > 5000000) {
                              setState({
                                ...state,
                                uploadErrorMessage:
                                  'File must not be more than 5mb',
                              });
                              setIsFileInvalid(true);
                            } else if (fileName !== 'csv') {
                              setState({
                                ...state,
                                uploadErrorMessage: 'File must be a CSV',
                              });
                              setIsFileInvalid(true);
                            } else {
                              importBulkDeprovisioningUsers({
                                variables: { file },
                              });
                            }
                          };
                          reader.readAsText(file);
                        })
                      }
                      onTemplateUpdate={() => {
                        const fileData = {
                          mime: 'text/csv',
                          filename: 'user-status-update-template.csv',
                          contents:
                            '*Email,status,\n <EMAIL>,inactive\n <EMAIL>,active\n',
                        };
                        const blob = new Blob([fileData.contents], {
                          type: fileData.mime,
                        });
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        document.body.appendChild(a);
                        a.download = fileData.filename;
                        a.href = url;
                        a.click();
                        document.body.removeChild(a);
                      }}
                      onExport={
                        permissions.User.export &&
                        (() => {
                          setState({
                            ...state,
                            isConfirmDownloadModalOpen: true,
                          });
                        })
                      }
                      onTemplate={() => {
                        const fileData = {
                          mime: 'text/csv',
                          filename: 'user-accounts-template.csv',
                          contents:
                            '*Full_Name,*Email,*Mobile_Number,*User_Role,Channel,*Group,*Division,*Department\nTest1,<EMAIL>,**********,Super Admin,Super Admin,Backend Dev,Backend Dev,Backend Dev\n',
                        };
                        const blob = new Blob([fileData.contents], {
                          type: fileData.mime,
                        });
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        document.body.appendChild(a);
                        a.download = fileData.filename;
                        a.href = url;
                        a.click();
                        document.body.removeChild(a);
                      }}
                    >
                      CSV{' '}
                      <FontAwesomeIcon
                        style={{ marginLeft: 10 }}
                        icon="angle-down"
                      />
                    </FileButtonUsers>
                  )}
                  {permissions.User.create && (
                    <CreateButton
                      icon="plus"
                      onClick={() => {
                        setState({ ...state, isAddUserModalOpen: true });
                      }}
                    >
                      Add User
                    </CreateButton>
                  )}
                  {permissions.User.delete && (
                    <CreateButton
                      disabled={!Object.keys(state.selectedRows).length}
                      style={{ marginLeft: 10 }}
                      icon="trash-alt"
                      onClick={() => {
                        setState({
                          ...state,
                          isConfirmDeleteUsersModalOpen: true,
                        });
                      }}
                    >
                      {Object.keys(state.selectedRows).length} For Delete
                    </CreateButton>
                  )}
                  {permissions.User.deactivate && (
                    <CreateButton
                      disabled={!Object.keys(state.selectedRows).length}
                      style={{ marginLeft: 10 }}
                      icon="times-circle"
                      onClick={() => {
                        setState({
                          ...state,
                          isConfirmDeactivateUsersModalOpen: true,
                        });
                      }}
                    >
                      {Object.keys(state.selectedRows).length} For Deactivate
                    </CreateButton>
                  )}
                </div>
              </>
            }
            pagination={{
              ...state.pagination,
              count: data && data.users ? data.users.count : 0,
              cursors: data && data.users ? data.users.cursors : [],
              handleChange: pagination => {
                setState({ ...state, pagination });
              },
            }}
            config={{
              email: {
                headerLabel: 'Email Address',
                sortable: true,
                onClick: data => {
                  history.push('/user-accounts/' + data.id);
                },
              },
              name: { headerLabel: 'Full Name', sortable: true },
              role: {
                headerLabel: 'User Role',
                sortable: data => (data.role ? data.role.name : ''),
                renderAs: data => (data.role ? data.role.name : ''),
              },
              group: { headerLabel: 'Group', sortable: true },
              status: {
                headerLabel: 'Status',
                sortable: data => (data.isActive ? 'Active' : 'Inactive'),
                renderAs: data => (data.isActive ? 'Active' : 'Inactive'),
              },
              createdAt: {
                headerLabel: 'Date Created',
                sortable: true,
                renderAs: data =>
                  format(new Date(data.createdAt), 'MM/DD/YYYY - hh:mm:ss A'),
                colSpan: 3,
              },
              actions: {
                // eslint-disable-next-line react/display-name
                renderAs: data => (
                  <ActionButtons
                    crossIcon
                    checkIcon={
                      permissions.User.deactivate &&
                      (() => {
                        setState({
                          ...state,
                          selectedUser: data,
                          isConfirmDeactivateUserModalOpen: true,
                        });
                      })
                    }
                    handleView={() => {
                      history.push(`/user-accounts/${data.id}`);
                    }}
                    handleEdit={
                      permissions.User.update &&
                      (() => {
                        history.push(`/user-accounts/${data.id}`, {
                          isEditing: true,
                        });
                      })
                    }
                    handleDelete={
                      permissions.User.delete &&
                      (() => {
                        setState({
                          ...state,
                          selectedUser: data,
                          isConfirmDeleteUserModalOpen: true,
                        });
                      })
                    }
                    disabled={{
                      delete: authUser.id === data.id,
                      checkIcon: !data.isActive,
                    }}
                  />
                ),
              },
            }}
          />
        </DataContainer>
      </Page>
      {state.isAddUserModalOpen && (
        <FormModal
          isOpen={state.isAddUserModalOpen}
          width="600px"
          handleClose={() =>
            setState({ ...state, isLeavingPageWhileAdding: true })
          }
          title="Add New Account"
          instructions={
            <span>
              To create new account, please fill out the required
              <Required>*</Required> fields.
            </span>
          }
          submitText="Create Account"
          handleSubmit={values => {
            setState({
              ...state,
              selectedUser: values,
              isConfirmAddUserModalOpen: true,
            });
          }}
          fields={{
            name: {
              type: FIELD_TYPES.TEXT,
              label: 'Full Name',
              placeholder: 'First and last name',
              validation: Yup.string()
                .max(100, 'Must not exceed 100 characters')
                .required('Please enter value')
                .matches(/[^-\s]/, 'Must not be a whitespace')
                .matches(
                  /^(?![=,@,+,-])(.+)$/,
                  'Input must not begin with this special characters (=,@,+,-)'
                ),
              required: true,
              initialValue: '',
            },
            email: {
              type: FIELD_TYPES.EMAIL,
              label: 'Email',
              placeholder: 'Email',
              validation: Yup.string()
                .email('Please enter a valid email')
                .max(250, 'Must not exceed 250 characters')
                .required('Please enter value'),
              required: true,
              initialValue: '',
            },
            mobileNumber: {
              type: FIELD_TYPES.MOBILE_NUMBER,
              label: 'Mobile Number',
              placeholder: '915 - 234 - 3456',
              validation: Yup.string()
                .matches(
                  /^\d{10}$/,
                  'Incorrect format. Please enter correct format'
                )
                .required('Please enter a value'),
              required: true,
              initialValue: '',
            },
            roleId: {
              type: FIELD_TYPES.SELECT,
              label: 'User Role',
              placeholder: '-- Select --',
              validation: Yup.string().required('Please enter a value'),
              options: roleOptions,
              readOnly: loading,
              required: true,
              initialValue: null,
            },
            group: {
              type: FIELD_TYPES.TEXT,
              label: 'Group',
              placeholder: 'Group',
              validation: Yup.string()
                .max(250, 'Must not exceed 250 characters')
                .required('Please enter a value')
                .matches(/[^-\s]/, 'Must not be a whitespace')
                .matches(
                  /^(?![=,@,+,-])(.+)$/,
                  'Input must not begin with this special characters (=,@,+,-)'
                ),
              required: true,
              initialValue: '',
            },
            division: {
              type: FIELD_TYPES.TEXT,
              label: 'Division',
              placeholder: 'Division',
              validation: Yup.string()
                .max(250, 'Must not exceed 250 characters')
                .required('Please enter a value')
                .matches(/[^-\s]/, 'Must not be a whitespace')
                .matches(
                  /^(?![=,@,+,-])(.+)$/,
                  'Input must not begin with this special characters (=,@,+,-)'
                ),
              required: true,
              initialValue: '',
            },
            department: {
              type: FIELD_TYPES.TEXT,
              label: 'Department',
              placeholder: 'Department',
              validation: Yup.string()
                .max(250, 'Must not exceed 250 characters')
                .required('Please enter a value')
                .matches(/[^-\s]/, 'Must not be a whitespace')
                .matches(
                  /^(?![=,@,+,-])(.+)$/,
                  'Input must not begin with this special characters (=,@,+,-)'
                ),
              required: true,
              initialValue: '',
            },
          }}
        />
      )}
      <AlertModal
        isOpen={state.isConfirmAddUserModalOpen}
        title="New Account Alert"
        icon="exclamation-circle"
        variant="warn"
        header="ARE YOU SURE?"
        subHeader="You are about to create a new Account."
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        confirmLoading={isAddingUser}
        confirmText="Yes"
        handleConfirm={() => {
          addUser({ variables: { data: sanitize(state.selectedUser) } });
        }}
        handleClose={() => {
          setState({
            ...state,
            isConfirmAddUserModalOpen: false,
          });
        }}
      />
      <AlertModal
        isOpen={state.isSuccessAddUserModalOpen}
        title="New Account Alert"
        handleClose={() => {
          setState({ ...state, isSuccessAddUserModalOpen: false });
        }}
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader="Account has been created successfully."
        description="Notification will be sent to User’s email address and or mobile number."
        confirmText="Go to User Accounts"
        handleConfirm={() => {
          setState({ ...state, isSuccessAddUserModalOpen: false });
        }}
      />
      <AlertModal
        isOpen={state.isSuccessAddUsersModalOpen}
        title="Upload File"
        handleClose={() => {
          setState({ ...state, isSuccessAddUsersModalOpen: false });
        }}
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader="Accounts has been created successfully."
        description="Notification will be sent to Users' email address and or mobile number."
        confirmText="Go to User Accounts"
        handleConfirm={() => {
          setState({ ...state, isSuccessAddUsersModalOpen: false });
        }}
      />
      <AlertModal
        isOpen={state.isFailureAddUsersModalOpen}
        title="Upload File"
        handleClose={() => {
          setState({ ...state, isFailureAddUsersModalOpen: false });
        }}
        icon="times-circle"
        variant="error"
        header="OH SNAP!"
        subHeader={
          state.importFileError ? formatSubHeader(state.importFileError) : ''
        }
        description={
          state.importFileError ? formatDescription(state.importFileError) : ''
        }
        confirmText="Try Again"
        handleConfirm={() => {
          setState({ ...state, isFailureAddUsersModalOpen: false });
        }}
      />
      <AlertModal
        isOpen={state.isUpdateUserSuccess}
        title="Update Account Alert"
        handleClose={() => {
          setState({ ...state, isUpdateUserSuccess: false });
        }}
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader={`${
          BulkDeprovisioningData &&
          BulkDeprovisioningData.uploadUsersStatusUpdate.validUsers.length
        } out of ${userCount} user(s) has been updated `}
        description="Kindly check the updated accounts"
        confirmText="Go to User Accounts"
        handleConfirm={() => {
          setState({ ...state, isUpdateUserSuccess: false });
        }}
      />
      <AlertModal
        isOpen={state.isUpdateUserError}
        title="Upload File"
        handleClose={() => {
          setState({ ...state, isUpdateUserError: false });
        }}
        icon="times-circle"
        variant="error"
        header="OH SNAP!"
        subHeader={
          state.importFileError ? state.importFileError : 'Invalid File Import'
        }
        description={'Please check the file and import again.'}
        confirmText="Try Again"
        handleConfirm={() => {
          setState({ ...state, isUpdateUserError: false });
        }}
      />
      <AlertModal
        isOpen={state.isLeavingPageWhileAdding}
        title="New Account Alert"
        icon="question-circle"
        variant="warn"
        header="SAVE ACCOUNT?"
        subHeader="You are about to leave without saving New Account."
        description="Your entry will be lost if you don't save it"
        handleClose={() =>
          setState({ ...state, isLeavingPageWhileAdding: false })
        }
        cancelText="Discard Entry"
        confirmText="Go Back"
        handleCancel={() => {
          if (state.nextLocation) {
            history.push(state.nextLocation);
          } else {
            setState({
              ...state,
              isLeavingPageWhileAdding: false,
              isAddUserModalOpen: false,
            });
          }
        }}
        handleConfirm={() => {
          setState({ ...state, isLeavingPageWhileAdding: false });
        }}
      />
      <AlertModal
        isOpen={!!state.addUserError}
        title="New Account Alert"
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader={
          state.addUserError === 'USER_EMAIL_ALREADY_EXISTS'
            ? 'Email address is already registered.'
            : 'There was a problem on saving New Account.'
        }
        description={
          state.addUserError === 'USER_EMAIL_ALREADY_EXISTS'
            ? 'Please try another.'
            : 'Please go back and try saving it again.'
        }
        handleClose={() => setState({ ...state, addUserError: null })}
        confirmText="Go Back"
        handleConfirm={() => {
          setState({ ...state, addUserError: null });
        }}
      />
      <AlertModal
        isOpen={state.isConfirmDeleteUserModalOpen}
        title="Delete Account Alert"
        icon="exclamation-circle"
        variant="warn"
        header="ARE YOU SURE?"
        subHeader="You are about to delete an Account."
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() =>
          setState({ ...state, isConfirmDeleteUserModalOpen: false })
        }
        selectLabel="Reason"
        options={[
          'Inactive for too long',
          'Resigned / Terminated',
          'Others',
        ].map(reason => ({
          value: reason,
          label: reason,
        }))}
        confirmLoading={isDeletingUser}
        confirmText="Yes"
        handleConfirm={value => {
          deleteUser({
            variables: {
              data: { reasonToDelete: value },
              where: { id: state.selectedUser.id },
            },
          });
        }}
      />
      <AlertModal
        isOpen={state.isSuccessDeleteUserModalOpen}
        title="Delete Account Alert"
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader="Account has been deleted successfully."
        description="Notifications will be sent to each users' email address and or mobile number that are affected."
        handleClose={() =>
          setState({ ...state, isSuccessDeleteUserModalOpen: false })
        }
        confirmText="Go to All User Accounts"
        handleConfirm={() =>
          setState({ ...state, isSuccessDeleteUserModalOpen: false })
        }
      />
      <AlertModal
        isOpen={state.isFailureDeleteUserModalOpen}
        title="Delete Account Alert"
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader="There was a problem on deleting the Account."
        description="Please go back and try deleting again."
        handleClose={() =>
          setState({ ...state, isFailureDeleteUserModalOpen: false })
        }
        confirmText="Go Back"
        handleConfirm={() => {
          setState({ ...state, isFailureDeleteUserModalOpen: false });
        }}
      />
      <AlertModal
        isOpen={state.isConfirmDeleteUsersModalOpen}
        title="Delete Account Alert"
        icon="exclamation-circle"
        variant="warn"
        header="ARE YOU SURE?"
        subHeader={`You are about to delete ${Object.keys(state.selectedRows).length} Account(s).`}
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() =>
          setState({ ...state, isConfirmDeleteUsersModalOpen: false })
        }
        selectLabel="Reason"
        options={[
          'Inactive for too long',
          'Resigned / Terminated',
          'Others',
        ].map(reason => ({
          value: reason,
          label: reason,
        }))}
        confirmLoading={isDeletingUsers}
        confirmText="Yes"
        handleConfirm={value => {
          deleteUsers({
            variables: {
              data: { reasonToDelete: value },
              where: { ids: Object.keys(state.selectedRows) },
            },
          });
        }}
      />
      <AlertModal
        isOpen={state.isSuccessDeleteUsersModalOpen}
        title="Delete Account Alert"
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader="Account(s) have been deleted successfully."
        description="Notifications will be sent to each users' email address and or mobile number that are affected."
        handleClose={() =>
          setState({ ...state, isSuccessDeleteUsersModalOpen: false })
        }
        confirmText="Go to All User Accounts"
        handleConfirm={() =>
          setState({ ...state, isSuccessDeleteUsersModalOpen: false })
        }
      />
      <AlertModal
        isOpen={state.isFailureDeleteUsersModalOpen}
        title="Delete Account Alert"
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader="There was a problem on deleting the Account(s)."
        description="Please go back and try deleting again."
        handleClose={() =>
          setState({ ...state, isFailureDeleteUsersModalOpen: false })
        }
        confirmText="Go Back"
        handleConfirm={() => {
          setState({ ...state, isFailureDeleteUsersModalOpen: false });
        }}
      />

      <AlertModal
        isOpen={state.isConfirmDeactivateUserModalOpen}
        title="Deactivate Account Alert"
        icon="exclamation-circle"
        variant="warn"
        header="ARE YOU SURE?"
        subHeader="You are about to deactivate an Account."
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() =>
          setState({ ...state, isConfirmDeactivateUserModalOpen: false })
        }
        selectLabel="Reason"
        options={[
          'Resigned',
          'Long leaves',
          'Transferred/Moved to new office/unit/group',
          'New assignment/responsibilities',
        ].map(reason => ({ value: reason, label: reason }))}
        confirmLoading={isDeactivatingUser}
        confirmText="Yes"
        handleConfirm={value => {
          deactivateUser({
            variables: {
              data: { reasonToDeactivate: value },
              where: { ids: state.selectedUser.id },
            },
          });
        }}
      />
      <AlertModal
        isOpen={state.isSuccessDeactivateUserModalOpen}
        title="Deactivate Account Alert"
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader="Account has been deactivated successfully."
        description=""
        handleClose={() =>
          setState({ ...state, isSuccessDeactivateUserModalOpen: false })
        }
        confirmText="Go to All User Accounts"
        handleConfirm={() =>
          setState({ ...state, isSuccessDeactivateUserModalOpen: false })
        }
      />

      <AlertModal
        isOpen={state.isFailureDeactivateUserModalOpen}
        title="Deactivate Account Alert"
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader="There was a problem on deactivating the Account."
        description="Please go back and try deactivating again."
        handleClose={() =>
          setState({ ...state, isFailureDeactivateUserModalOpen: false })
        }
        confirmText="Go Back"
        handleConfirm={() => {
          setState({ ...state, isFailureDeactivateUserModalOpen: false });
        }}
      />

      <AlertModal
        isOpen={state.isConfirmDeactivateUsersModalOpen}
        title="Deactivate Account Alert"
        icon="exclamation-circle"
        variant="warn"
        header="ARE YOU SURE?"
        subHeader={`You are about to deactivate ${Object.keys(state.selectedRows).length} Account(s).`}
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() =>
          setState({ ...state, isConfirmDeactivateUsersModalOpen: false })
        }
        selectLabel="Reason"
        options={[
          'Resigned',
          'Long leaves',
          'Transferred/Moved to new office/unit/group',
          'New assignment/responsibilities',
        ].map(reason => ({ value: reason, label: reason }))}
        confirmLoading={isDeactivateUsersLoading}
        confirmText="Yes"
        handleConfirm={value => {
          if (!state.status) {
            deactivateUsers({
              variables: {
                data: { reasonToDeactivate: value },
                where: { ids: Object.keys(state.selectedRows) },
              },
            });
          } else if (state.status) {
            setState({
              ...state,
              isFailureToDeactivateSelectedRowHasIsActive: true,
            });
          }
        }}
      />
      <AlertModal
        isOpen={state.isSuccessDeactivateUsersModalOpen}
        title="Deactivate Account Alert"
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader="Account(s) have been deactivated successfully."
        description=""
        handleClose={() =>
          setState({ ...state, isSuccessDeactivateUsersModalOpen: false })
        }
        confirmText="Go to All User Accounts"
        handleConfirm={() =>
          setState({ ...state, isSuccessDeactivateUsersModalOpen: false })
        }
      />
      <AlertModal
        isOpen={state.isFailureDeactivateUsersModalOpen}
        title="Deactivate Account Alert"
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader="There was a problem on deactivating the Account(s)."
        description="Please go back and try deactivating again."
        handleClose={() =>
          setState({ ...state, isFailureDeactivateUsersModalOpen: false })
        }
        confirmText="Go Back"
        handleConfirm={() => {
          setState({ ...state, isFailureDeactivateUsersModalOpen: false });
        }}
      />

      <AlertModal
        isOpen={state.isFailureToDeactivateSelectedRowHasIsActive}
        title="Deactivate Account Alert"
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader="There was a problem on deactivating the Account(s). Selected Account(s) has already inactive and unable to proceed."
        description="Please go back and try deactivating again."
        handleClose={() =>
          setState({
            ...state,
            isFailureToDeactivateSelectedRowHasIsActive: false,
          })
        }
        confirmText="Go Back"
        handleConfirm={() => {
          setState({
            ...state,
            isFailureToDeactivateSelectedRowHasIsActive: false,
          });
        }}
      />

      <AlertModal
        isOpen={state.isConfirmDownloadModalOpen}
        title="Export Reports Alert"
        header="ARE YOU SURE?"
        variant="warn"
        icon="exclamation-circle"
        subHeader={
          <span>You are about to export all User Accounts as .CSV File.</span>
        }
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() =>
          setState({ ...state, isConfirmDownloadModalOpen: false })
        }
        handleCancel={() =>
          setState({ ...state, isConfirmDownloadModalOpen: false })
        }
        confirmText="Yes"
        handleConfirm={() => {
          setState({ ...state, isConfirmDownloadModalOpen: false });
          exportUsers();
        }}
      />

      <AlertModal
        isOpen={isFileInvalid}
        title="Upload Alert"
        header="Upload Error!"
        variant="error"
        icon="times-circle"
        subHeader={<span>{state.uploadErrorMessage}</span>}
        description="Kindly check the uploaded file."
        confirmText="Back to Accounts"
        handleClose={() => {
          setIsFileInvalid(false);
        }}
        handleConfirm={() => {
          setIsFileInvalid(false);
        }}
      />
    </>
  );
};

UserAccounts.propTypes = {
  history: PropTypes.object,
};

export default UserAccounts;
