import { gql } from '@apollo/client';

export const ADD_USER = gql`
  mutation AddUser($data: RegisterUser!) {
    createUser(data: $data) {
      id
      email
      name
      role {
        id
        name
      }
      group
      mobileNumber
      channel
      department
      division
      isActive
      createdAt
    }
  }
`;

export const DELETE_USER = gql`
  mutation deleteUser($data: DeleteUser!, $where: UserPrimary!) {
    deleteUser(data: $data, where: $where) {
      id
    }
  }
`;

export const DELETE_USERS = gql`
  mutation deleteUsers($data: DeleteUser!, $where: Ids!) {
    deleteUsers(data: $data, where: $where) {
      unprocessedItems
    }
  }
`;

export const DEACTIVATE_USERS = gql`
  mutation deactivateUser($data: DeactivateUser!, $where: Ids!) {
    deactivateUser(data: $data, where: $where) {
      unprocessedItems
    }
  }
`;

export const EXPORT_USERS = gql`
  mutation downloadUsers {
    downloadUsers {
      id
      emailAddress
      name
      roleName
      roleDescription
      group
      permission
      createdAt
      loginTime
      status
    }
  }
`;

export const IMPORT_USERS = gql`
  mutation importUsers($file: Upload!) {
    uploadUsers(file: $file) {
      filename
    }
  }
`;

export const IMPORT_BULK_DEPROVISIONING_USERS = gql`
  mutation importBulkDeprovisioningUsers($file: Upload!) {
    uploadUsersStatusUpdate(file: $file) {
      filename
      validUsers {
        email
      }
      invalidUsers {
        email
      }
    }
  }
`;
