import { gql } from '@apollo/client';

export const GET_USER_ACCOUNTS_INFORMATION = gql`
  query getUsers(
    $filter: UsersFilter!
    $pagination: PaginationInput!
  ) {
    rolesLoose {
      id
      name
    }

    channelsLoose {
      id
      name
    }

    users(filter: $filter, pagination: $pagination) {
      cursors
      count
      lastKey
      filteredData {
        id
        email
        name
        role {
          id
          name
        }
        group
        channel
        department
        division
        isActive
        createdAt
      }
    }
  }
`;
