import format from 'date-fns/format';
import PropTypes from 'prop-types';
import React, { useContext, useEffect, useMemo, useState } from 'react';
import { useQuery } from '@apollo/client';
import styled from 'styled-components';
import * as Yup from 'yup';
import PrimaryButton from '../../components/Button/PrimaryButton';
import SecondaryButton from '../../components/Button/SecondaryButton';
import DataContainer from '../../components/DataContainer';
import DataHeader from '../../components/DataHeader';
import { FIELD_TYPES } from '../../components/Form/constants';
import FormField, {
  FormFieldLabel,
  StyledFormField,
} from '../../components/Form/FormField';
import Header from '../../components/Header';
import {
  ButtonsContainer,
  PageSubsection,
  SubsectionTitle,
} from '../../components/InformationPage';
import Loader from '../../components/Loader';
import { AlertModal } from '../../components/Modal';
import NotFound from '../../components/NotFound/NotFound';
import Page from '../../components/Page';
import Row from '../../components/Row';
import AuthContext from '../../context/AuthContext/AuthContext';
import ResponsiveContext from '../../context/ResponsiveContext';
import useForm from '../../hooks/useForm';
import { useMutation } from '@apollo/client';
import getDiff from '../../utils/getDiff';
import sanitize from '../../utils/sanitize';
import { EDIT_USER } from './mutation';
import { GET_USER_INFORMATION, GET_CHANNEL_BILLTYPE } from './query';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

const StaticValue = styled.div`
  font-weight: 300;
  font-size: ${props => props.theme.fontSize.s};
`;

const ActionFields = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;

  width: ${props => {
    const base = 100 / props.perRow;
    const margin = 20;
    return `calc(${base}% - ${margin}px)`;
  }};

  flex: 1;

  margin-left: ${props => (props.isMobile ? '0px' : '20px')};
  margin-bottom: 20px;
`;

const ActionIcon = styled(FontAwesomeIcon)`
  color: ${props => (props.disabled ? 'gray' : props.color)};
  font-size: 20px;
  cursor: pointer;

  margin-left: 10px;
  &:first-child {
    margin-left: 0;
  }
`;

const ActionButton = styled.button`
  background-color: ${props =>
    props.disabled ? 'gray' : props.backgroundColor};
  color: #fff;
  font-size: ${props => props.theme.fontSize.s};
  cursor: pointer;
  flex: 1;
  border-radius: 5px;
`;

const UserInformation = ({ location, history, match }) => {
  const { permissions } = useContext(AuthContext);
  const { isMobile } = useContext(ResponsiveContext);

  const [state, setState] = useState({
    isEditing: false,
    nextLocation: null,
    isConfirmEditUserModalOpen: false,
    isSuccessEditUserModalOpen: false,
    isFailureEditUserModalOpen: false,

    billType: '',
    checkLenght: false,

    editUserError: null,

    isLeavingWhileEditing: false,

    selectedUser: null,
  });

  const [newAssignedChannel, setNewAssignedChannel] = useState([
    {
      channelId: null,
    },
  ]);

  const [newChannelErrors, setNewChannelErrors] = useState([{}]);

  const [selectedChannel, setSelectedChannel] = useState({
    channelId: '',
  });

  const [newCardAssignedChannel, setNewCardAssignedChannel] = useState([
    {
      channelId: null,
    },
  ]);

  const [newEWalletAssignedChannel, setNewEWalletAssignedChannel] = useState([
    {
      channelId: null,
    },
  ]);

  const [
    newPostPaymentConfigAssignedChannels,
    setNewPostPaymentConfigAssignedChannels,
  ] = useState([
    {
      channelId: null,
    },
  ]);

  const [newCardChannelErrors, setNewCardChannelErrors] = useState([{}]);
  const [newEWalletChannelErrors, setNewEWalletChannelErrors] = useState([{}]);
  const [
    newPostPaymentConfigChannelErrors,
    setNewPostPaymentConfigChannelErrors,
  ] = useState([{}]);

  const [selectedCardChannel, setSelectedCardChannel] = useState({
    channelId: '',
  });

  const [selectedEWalletChannel, setSelectedEWalletChannel] = useState({
    channelId: '',
  });

  const [
    selectedPostPaymentConfigChannel,
    setSelectedPostPaymentConfigChannel,
  ] = useState({
    channelId: '',
  });

  let newValues = newAssignedChannel.filter(newValue => newValue.channelId);

  let newCardValues = newCardAssignedChannel.filter(
    newValue => newValue.channelId
  );

  let newEWalletValues = newEWalletAssignedChannel.filter(
    newValue => newValue.channelId
  );

  let newPostPaymentConfigValues = newPostPaymentConfigAssignedChannels.filter(
    newValue => newValue.channelId
  );

  // initialize isEditing
  // this takes affect when user clicks on edit button from table pages
  useEffect(() => {
    if (selectedChannel.channelId) {
      onChange.assignedChannels(
        values.assignedChannels.filter(channel => {
          return channel.channelId !== selectedChannel.channelId;
        })
      );

      setSelectedChannel({ channelId: '' });
    }
  });

  useEffect(() => {
    if (selectedCardChannel.channelId) {
      onChange.cardAssignedChannels(
        values.cardAssignedChannels.filter(channel => {
          return channel.channelId !== selectedCardChannel.channelId;
        })
      );

      setSelectedCardChannel({ channelId: '' });
    }
  });

  useEffect(() => {
    if (selectedEWalletChannel.channelId) {
      onChange.ewalletAssignedChannels(
        values.ewalletAssignedChannels.filter(channel => {
          return channel.channelId !== selectedEWalletChannel.channelId;
        })
      );

      setSelectedEWalletChannel({ channelId: '' });
    }
  });

  useEffect(() => {
    if (selectedPostPaymentConfigChannel.channelId) {
      onChange.postPaymentConfigChannels(
        values.postPaymentConfigChannels.filter(channel => {
          return (
            channel.channelId !== selectedPostPaymentConfigChannel.channelId
          );
        })
      );

      setSelectedEWalletChannel({ channelId: '' });
    }
  });

  useEffect(() => {
    if (location.state && location.state.isEditing !== undefined) {
      setState({ ...state, isEditing: location.state.isEditing });
    }
  }, []);

  useEffect(() => {
    const unblock = history.block(location => {
      if (state.isLeavingWhileEditing || !state.isEditing) return true;
      setState({
        ...state,
        nextLocation: location,
        isLeavingWhileEditing: true,
      });
      return false;
    });

    return () => {
      unblock();
    };
  }, [state.isLeavingWhileEditing, state.isEditing]);

  const { data, loading, refetch } = useQuery(GET_USER_INFORMATION, {
    variables: {
      where: {
        id: match.params.id,
      },
    },
    fetchPolicy: 'network-only',
  });

  const [editUser, { loading: isEditingUser }] = useMutation(EDIT_USER, {
    onCompleted: () => {
      setState({
        ...state,
        isConfirmEditUserModalOpen: false,
        isSuccessEditUserModalOpen: true,
        isEditing: false,
      });
      setNewAssignedChannel([
        {
          channelId: null,
        },
      ]);
      setNewCardAssignedChannel([
        {
          channelId: null,
        },
      ]);
      refetch();
    },
    onError: err => {
      setState({
        ...state,
        editUserError: err.networkError.result
          ? err.networkError.result.message
          : null,
        isConfirmEditUserModalOpen: false,
        isFailureEditUserModalOpen: true,
      });
    },
  });

  const roleOptions =
    data && data.rolesLoose
      ? data.rolesLoose.map(role => ({
          value: role.id,
          label: role.name,
        }))
      : [];

  const deactivationOptions = [
    'Resigned',
    'Long leaves',
    'Transferred/Moved to new office/unit/group',
    'New assignment/responsibilities',
  ].map(reason => ({ value: reason, label: reason }));

  const { fields, initialValue } = useMemo(() => {
    const fields = {
      name: {
        validation: Yup.string()
          .max(100, 'Must not exceed 100 characters')
          .required('Please enter a value')
          .matches(/[^-\s]/, 'Must not be a whitespace')
          .matches(
            /^(?![=,@,+,-])(.+)$/,
            'Input must not begin with this special characters (=,@,+,-)'
          ),
      },
      email: {
        validation: Yup.string()
          .email('Must be an email')
          .max(250, 'Must not exceed 250 characters')
          .required('Please enter a value'),
      },
      roleId: { validation: Yup.string().required('Please enter a value') },
      department: {
        validation: Yup.string()
          .max(250, 'Must not exceed 250 characters')
          .nullable()
          .required('Please enter a value')
          .matches(/[^-\s]/, 'Must not be a whitespace')
          .matches(
            /^(?![=,@,+,-])(.+)$/,
            'Input must not begin with this special characters (=,@,+,-)'
          ),
      },
      division: {
        validation: Yup.string()
          .max(250, 'Must not exceed 250 characters')
          .nullable()
          .required('Please enter a value')
          .matches(/[^-\s]/, 'Must not be a whitespace')
          .matches(
            /^(?![=,@,+,-])(.+)$/,
            'Input must not begin with this special characters (=,@,+,-)'
          ),
      },
      group: {
        validation: Yup.string()
          .max(250, 'Must not exceed 250 characters')
          .required('Please enter a value')
          .matches(/[^-\s]/, 'Must not be a whitespace')
          .matches(
            /^(?![=,@,+,-])(.+)$/,
            'Input must not begin with this special characters (=,@,+,-)'
          ),
      },
      assignedChannels: {},
      cardAssignedChannels: {},
      ewalletAssignedChannels: {},
      postPaymentConfigChannels: {},
      isActive: { validation: Yup.bool() },
      reasonToDeactivate: {
        validation: values =>
          values.isActive
            ? Yup.string().nullable()
            : Yup.string().required('Please enter a value').nullable(),
      },
      mobileNumber: {
        validation: Yup.string()
          .matches(/^\d{10}$/, 'Incorrect format. Please enter correct format')
          .required('Please enter a value'),
      },
      billType: {
        validation: Yup.string().nullable().required('Please select a value'),
      },
      notificationSettings: {
        initialValue: [],
      },
    };

    const initialValue = {};
    if (!loading && data && data.user) {
      for (const name of Object.keys(fields)) {
        if (Object.prototype.hasOwnProperty.call(data.user, name)) {
          fields[name].initialValue = data.user[name];
        } else if (name === 'roleId') {
          fields[name].initialValue = data.user.role ? data.user.role.id : null;
        } else if (name === 'channel') {
          fields[name].initialValue = data.user.channel
            ? data.user.channel.id
            : null;
        } else if (name === 'notificationSettings') {
          const notificationSettings = [];
          if (data.user.smsNotif) notificationSettings.push('SMS');
          if (data.user.emailNotif) notificationSettings.push('EMAIL');
          fields[name].initialValue = notificationSettings;
        }
        initialValue[name] = fields[name].initialValue;
      }
    }
    return { fields, initialValue };
  }, [data]);

  const { values, onChange, onBlur, errors, onSubmit } = useForm(
    fields,
    values => {
      setState({
        ...state,
        isConfirmEditUserModalOpen: true,
        selectedUser: values,
      });
    }
  );

  // To get values of bill type when changing
  const { data: dataChannel } = useQuery(GET_CHANNEL_BILLTYPE, {
    variables: {
      billType: values.billType,
    },
    fetchPolicy: 'network-only',
  });

  const channelOptions =
    dataChannel && dataChannel.channelsBillType
      ? [
          { value: null, label: 'None' },
          ...dataChannel.channelsBillType.map(channel => ({
            value: channel.id,
            label: channel.name,
          })),
        ]
      : [];

  function handleNewChannelAssignedValidation(event, index) {
    const existsInCurrent = values.assignedChannels.find(
      value => value.channelId === event
    );
    const existsInNew = newAssignedChannel.find(
      (value, valIndex) => value.channelId === event && valIndex !== index
    );

    let newErrors = newChannelErrors;

    if (existsInCurrent) {
      setNewChannelErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              channelId: 'Channel already exists',
            };
          }

          return val;
        })
      );

      return;
    }

    if (existsInNew) {
      setNewChannelErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              channelId: 'Duplicate channel',
            };
          }

          return val;
        })
      );

      return;
    }

    if (event === null) {
      setNewChannelErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              channelId: 'Please select a channel',
            };
          }

          return val;
        })
      );

      return;
    }

    Yup.string()
      .nullable()
      .validate(event)
      .then(() => {
        if (newChannelErrors[index].channelId) {
          const channelError = newChannelErrors.map((val, valIndex) => {
            if (valIndex === index) {
              delete val.channelId;
            }

            return val;
          });

          setNewChannelErrors(channelError);
        }
      })
      .catch(err => {
        if (err.errors) {
          const channelError = newChannelErrors.map((val, valIndex) => {
            if (valIndex === index) {
              return {
                ...val,
                channelId: err.errors[0],
              };
            }

            return val;
          });

          setNewChannelErrors(channelError);
        }
      });
  }
  function handleNewCardChannelAssignedValidation(event, index) {
    const existsInCurrent = values.cardAssignedChannels.find(
      value => value.channelId === event
    );
    const existsInNew = newCardAssignedChannel.find(
      (value, valIndex) => value.channelId === event && valIndex !== index
    );

    let newErrors = newCardChannelErrors;

    if (existsInCurrent) {
      setNewCardChannelErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              channelId: 'Channel already exists',
            };
          }

          return val;
        })
      );

      return;
    }

    if (existsInNew) {
      setNewCardChannelErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              channelId: 'Duplicate channel',
            };
          }

          return val;
        })
      );

      return;
    }

    if (event === null) {
      setNewCardChannelErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              channelId: 'Please select a channel',
            };
          }

          return val;
        })
      );

      return;
    }

    Yup.string()
      .nullable()
      .validate(event)
      .then(() => {
        if (newCardChannelErrors[index].channelId) {
          const channelError = newCardChannelErrors.map((val, valIndex) => {
            if (valIndex === index) {
              delete val.channelId;
            }

            return val;
          });

          setNewCardChannelErrors(channelError);
        }
      })
      .catch(err => {
        if (err.errors) {
          const channelError = newCardChannelErrors.map((val, valIndex) => {
            if (valIndex === index) {
              return {
                ...val,
                channelId: err.errors[0],
              };
            }

            return val;
          });

          setNewCardChannelErrors(channelError);
        }
      });
  }

  function handleNewEWalletChannelAssignedValidation(event, index) {
    const existsInCurrent = values.ewalletAssignedChannels.find(
      value => value.channelId === event
    );
    const existsInNew = newEWalletAssignedChannel.find(
      (value, valIndex) => value.channelId === event && valIndex !== index
    );

    let newErrors = newEWalletChannelErrors;

    if (existsInCurrent) {
      setNewEWalletChannelErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              channelId: 'Channel already exists',
            };
          }

          return val;
        })
      );

      return;
    }

    if (existsInNew) {
      setNewEWalletChannelErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              channelId: 'Duplicate channel',
            };
          }

          return val;
        })
      );

      return;
    }

    if (event === null) {
      setNewEWalletChannelErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              channelId: 'Please select a channel',
            };
          }

          return val;
        })
      );

      return;
    }

    Yup.string()
      .nullable()
      .validate(event)
      .then(() => {
        if (newEWalletChannelErrors[index].channelId) {
          const channelError = newEWalletChannelErrors.map((val, valIndex) => {
            if (valIndex === index) {
              delete val.channelId;
            }

            return val;
          });

          setNewEWalletChannelErrors(channelError);
        }
      })
      .catch(err => {
        if (err.errors) {
          const channelError = newEWalletChannelErrors.map((val, valIndex) => {
            if (valIndex === index) {
              return {
                ...val,
                channelId: err.errors[0],
              };
            }

            return val;
          });

          setNewEWalletChannelErrors(channelError);
        }
      });
  }

  function handlePostPaymentConfigChannelAssignedValidation(event, index) {
    const existsInCurrent = values.postPaymentConfigChannels.find(
      value => value.channelId === event
    );
    const existsInNew = newPostPaymentConfigAssignedChannels.find(
      (value, valIndex) => value.channelId === event && valIndex !== index
    );

    let newErrors = newPostPaymentConfigChannelErrors;

    if (existsInCurrent) {
      setNewPostPaymentConfigChannelErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              channelId: 'Channel already exists',
            };
          }

          return val;
        })
      );

      return;
    }

    if (existsInNew) {
      setNewPostPaymentConfigChannelErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              channelId: 'Duplicate channel',
            };
          }

          return val;
        })
      );

      return;
    }

    if (event === null) {
      setNewPostPaymentConfigChannelErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              channelId: 'Please select a channel',
            };
          }

          return val;
        })
      );

      return;
    }

    Yup.string()
      .nullable()
      .validate(event)
      .then(() => {
        if (newPostPaymentConfigChannelErrors[index].channelId) {
          const channelError = newPostPaymentConfigChannelErrors.map(
            (val, valIndex) => {
              if (valIndex === index) {
                delete val.channelId;
              }

              return val;
            }
          );

          setNewPostPaymentConfigChannelErrors(channelError);
        }
      })
      .catch(err => {
        if (err.errors) {
          const channelError = newPostPaymentConfigChannelErrors.map(
            (val, valIndex) => {
              if (valIndex === index) {
                return {
                  ...val,
                  channelId: err.errors[0],
                };
              }

              return val;
            }
          );

          setNewPostPaymentConfigChannelErrors(channelError);
        }
      });
  }

  const backButton = (
    <SecondaryButton
      onClick={() => {
        history.push('/user-accounts');
      }}
    >
      Back to All Users
    </SecondaryButton>
  );

  const editButtonGroup = permissions.User.update && (
    <Row>
      {!state.isEditing && (
        <PrimaryButton
          icon="pen"
          onClick={() => setState({ ...state, isEditing: true })}
        >
          Edit
        </PrimaryButton>
      )}
      <PrimaryButton
        icon="save"
        disabled={
          !state.isEditing ||
          newChannelErrors.find(newError => newError.channelId)
        }
        onClick={() => {
          onSubmit();
        }}
      >
        Save
      </PrimaryButton>
    </Row>
  );

  return (
    <>
      <Page>
        <Header
          withHome
          path={[
            'User Mgt.',
            { label: 'User Accounts', to: '/user-accounts' },
            data && data.user ? data.user.name : '',
          ]}
          title={data && data.user ? data.user.name : ''}
        />
        <DataContainer loading={loading}>
          {loading && <Loader fullPage />}
          {!loading && !data.user && <NotFound />}
          {!loading && data.user && (
            <>
              <DataHeader>
                <DataHeader.Title>User Information</DataHeader.Title>
              </DataHeader>
              <PageSubsection>
                <StyledFormField
                  verticalGap={20}
                  horizontalGap={isMobile ? 0 : 40}
                  row
                  perRow={isMobile ? 1 : 2}
                >
                  <FormFieldLabel>Last Login Time:</FormFieldLabel>
                  <StaticValue>
                    {data && data.user && data.user.loginTime
                      ? format(data.user.loginTime, 'MM/DD/YYYY - hh:mm:ss A')
                      : 'None'}
                  </StaticValue>
                </StyledFormField>
              </PageSubsection>
              <SubsectionTitle>STATUS</SubsectionTitle>
              <PageSubsection>
                <FormField
                  label="Active Account"
                  name="isActive"
                  type={FIELD_TYPES.CHECKBOX}
                  value={values.isActive}
                  onChange={onChange.isActive}
                  onBlur={onBlur.isActive}
                  error={errors.isActive}
                  readOnly={!state.isEditing}
                  row
                  noErrors
                  perRow={2}
                />
                <FormField
                  label="Reason to Deactivate"
                  name="reasonToDeactivate"
                  type={FIELD_TYPES.SELECT}
                  value={values.isActive ? '-' : values.reasonToDeactivate}
                  onChange={onChange.reasonToDeactivate}
                  onBlur={onBlur.reasonToDeactivate}
                  error={errors.reasonToDeactivate}
                  readOnly={!state.isEditing || values.isActive}
                  placeholder={
                    values.isActive || (!state.isEditing && values.isActive)
                      ? '-'
                      : '-- Select --'
                  }
                  required
                  options={deactivationOptions}
                  perRow={2}
                />
              </PageSubsection>
              <SubsectionTitle>GENERAL</SubsectionTitle>
              <PageSubsection>
                <FormField
                  label="Full name"
                  name="name"
                  type={FIELD_TYPES.TEXT}
                  value={values.name}
                  onChange={onChange.name}
                  onBlur={onBlur.name}
                  error={errors.name}
                  readOnly={!state.isEditing}
                  perRow={2}
                  required
                />
                <FormField
                  label="Bill Type"
                  name="billType"
                  type={FIELD_TYPES.SELECT}
                  value={values.billType}
                  options={[
                    { label: 'None', value: null },
                    { label: 'Bill', value: 'Bill' },
                    { label: 'NonBill', value: 'NonBill' },
                    { label: 'Both', value: 'Both' },
                  ]}
                  onChange={onChange.billType}
                  onBlur={onBlur.billType}
                  error={errors.billType}
                  readOnly={!state.isEditing}
                  perRow={2}
                  required
                />
                <FormField
                  label="User Role"
                  name="roleId"
                  type={FIELD_TYPES.SELECT}
                  value={values.roleId}
                  onChange={onChange.roleId}
                  onBlur={onBlur.roleId}
                  error={errors.roleId}
                  readOnly={!state.isEditing}
                  options={roleOptions}
                  perRow={2}
                  required
                />
                <FormField
                  label="Group"
                  name="group"
                  type={FIELD_TYPES.TEXT}
                  value={values.group}
                  onChange={onChange.group}
                  onBlur={onBlur.group}
                  error={errors.group}
                  readOnly={!state.isEditing}
                  perRow={2}
                  required
                />
                <FormField
                  label="Division"
                  name="division"
                  type={FIELD_TYPES.TEXT}
                  value={values.division}
                  onChange={onChange.division}
                  onBlur={onBlur.division}
                  error={errors.division}
                  readOnly={!state.isEditing}
                  perRow={2}
                  required
                />
                <FormField
                  label="Department"
                  name="department"
                  type={FIELD_TYPES.TEXT}
                  value={values.department}
                  onChange={onChange.department}
                  onBlur={onBlur.department}
                  error={errors.department}
                  readOnly={!state.isEditing}
                  perRow={2}
                  required
                />
              </PageSubsection>
              <SubsectionTitle>GCASH ASSIGNED CHANNELS</SubsectionTitle>
              {!loading &&
                values &&
                values.assignedChannels &&
                values.assignedChannels.map((channel, index) => (
                  <PageSubsection key={channel.channelId + '-' + index}>
                    <FormField
                      placeholder=""
                      label="Channel"
                      name={`channel-${channel.name}`}
                      type={FIELD_TYPES.TEXT}
                      value={channel.name}
                      readOnly={!state.isEditing}
                      perRow={2}
                      required
                    />
                    {state.isEditing ? (
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      >
                        {isMobile ? (
                          <ActionButton
                            backgroundColor={'red'}
                            onClick={() => {
                              setSelectedChannel(channel);
                            }}
                          >
                            Delete
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon={'minus-circle'}
                            color={'red'}
                            onClick={() => {
                              setSelectedChannel(channel);
                            }}
                          />
                        )}
                      </ActionFields>
                    ) : (
                      ''
                    )}
                  </PageSubsection>
                ))}
              {!state.isEditing && values.billType && (
                <PageSubsection>
                  <FormField
                    placeholder="Channel"
                    label="Channel"
                    name={`newChannel`}
                    type={FIELD_TYPES.TEXT}
                    value={'None'}
                    readOnly={!state.isEditing}
                    perRow={2}
                    required
                  />
                </PageSubsection>
              )}
              {state.isEditing &&
                values.billType &&
                newAssignedChannel.map((newChannel, index) => {
                  return (
                    <PageSubsection key={index}>
                      <FormField
                        placeholder="Channel"
                        label="Channel"
                        name={`channel-${index}`}
                        type={FIELD_TYPES.SELECT}
                        value={newChannel.channelId}
                        options={channelOptions}
                        onChange={event => {
                          const name = channelOptions.find(
                            data => data.value === event
                          );
                          let newValue = newAssignedChannel.map(
                            (newValue, newIndex) => {
                              if (newIndex === index) {
                                return {
                                  channelId: event,
                                  name: name.label,
                                };
                              }
                              return newValue;
                            }
                          );

                          setNewAssignedChannel(newValue);
                          handleNewChannelAssignedValidation(event, index);
                        }}
                        onBlur={event =>
                          handleNewChannelAssignedValidation(event, index)
                        }
                        error={newChannelErrors[index].channelId}
                        perRow={2}
                        readOnly={!state.isEditing}
                        required
                      />
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      >
                        {!newChannelErrors[index].channelId &&
                          newChannel.channelId &&
                          index === newAssignedChannel.length - 1 &&
                          (isMobile ? (
                            <ActionButton
                              backgroundColor="green"
                              onClick={() => {
                                setNewAssignedChannel([
                                  ...newAssignedChannel,
                                  {
                                    channelId: null,
                                  },
                                ]);
                                setNewChannelErrors([...newChannelErrors, {}]);
                              }}
                            >
                              Add
                            </ActionButton>
                          ) : (
                            <ActionIcon
                              icon="plus-circle"
                              color="green"
                              onClick={() => {
                                setNewAssignedChannel([
                                  ...newAssignedChannel,
                                  {
                                    channelId: null,
                                  },
                                ]);
                                setNewChannelErrors([...newChannelErrors, {}]);
                              }}
                            />
                          ))}
                        {isMobile ? (
                          <ActionButton
                            backgroundColor="red"
                            onClick={() => {
                              let newValue = newAssignedChannel.filter(
                                (channel, channelIndex) => {
                                  return channelIndex !== index;
                                }
                              );

                              let newErrors = newChannelErrors.filter(
                                (channel, channelIndex) => {
                                  return channelIndex !== index;
                                }
                              );

                              if (newValue.length === 0) {
                                newValue.push({
                                  channelId: null,
                                });
                                newErrors.push({});
                              }

                              setNewAssignedChannel(newValue);
                              setNewChannelErrors(newErrors);
                            }}
                          >
                            Delete
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon="minus-circle"
                            color="red"
                            onClick={() => {
                              let newValue = newAssignedChannel.filter(
                                (channel, channelIndex) => {
                                  return channelIndex !== index;
                                }
                              );

                              let newErrors = newChannelErrors.filter(
                                (channel, channelIndex) => {
                                  return channelIndex !== index;
                                }
                              );

                              if (newValue.length === 0) {
                                newValue.push({
                                  channelId: null,
                                });
                                newErrors.push({});
                              }

                              setNewAssignedChannel(newValue);
                              setNewChannelErrors(newErrors);
                            }}
                          />
                        )}
                      </ActionFields>
                    </PageSubsection>
                  );
                })}
              {!values.billType && (
                <center>
                  <p style={{ fontSize: 20, color: 'red' }}>
                    Please select a billtype to gcash assign channels
                  </p>
                </center>
              )}
              <SubsectionTitle>ADYEN ASSIGNED CHANNELS</SubsectionTitle>
              {!loading &&
                values &&
                values.cardAssignedChannels &&
                values.cardAssignedChannels.map((channel, index) => (
                  <PageSubsection key={channel.channelId + '-' + index}>
                    <FormField
                      placeholder=""
                      label="Channel"
                      name={`channel-${channel.name}`}
                      type={FIELD_TYPES.TEXT}
                      value={channel.name}
                      readOnly={!state.isEditing}
                      perRow={2}
                      required
                    />
                    {state.isEditing ? (
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      >
                        {isMobile ? (
                          <ActionButton
                            backgroundColor={'red'}
                            onClick={() => {
                              setSelectedCardChannel(channel);
                            }}
                          >
                            Delete
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon={'minus-circle'}
                            color={'red'}
                            onClick={() => {
                              setSelectedCardChannel(channel);
                            }}
                          />
                        )}
                      </ActionFields>
                    ) : (
                      ''
                    )}
                  </PageSubsection>
                ))}
              {!state.isEditing && values.billType && (
                <PageSubsection>
                  <FormField
                    placeholder="Channel"
                    label="Channel"
                    name={`newChannel`}
                    type={FIELD_TYPES.TEXT}
                    value={'None'}
                    readOnly={!state.isEditing}
                    perRow={2}
                    required
                  />
                </PageSubsection>
              )}
              {state.isEditing &&
                values.billType &&
                newCardAssignedChannel.map((newChannel, index) => {
                  return (
                    <PageSubsection key={index}>
                      <FormField
                        placeholder="Channel"
                        label="Channel"
                        name={`channel-${index}`}
                        type={FIELD_TYPES.SELECT}
                        value={newChannel.channelId}
                        options={channelOptions}
                        onChange={event => {
                          const name = channelOptions.find(
                            data => data.value === event
                          );
                          let newValue = newCardAssignedChannel.map(
                            (newValue, newIndex) => {
                              if (newIndex === index) {
                                return {
                                  channelId: event,
                                  name: name.label,
                                };
                              }
                              return newValue;
                            }
                          );

                          setNewCardAssignedChannel(newValue);
                          handleNewCardChannelAssignedValidation(event, index);
                        }}
                        onBlur={event =>
                          handleNewCardChannelAssignedValidation(event, index)
                        }
                        error={newCardChannelErrors[index].channelId}
                        perRow={2}
                        readOnly={!state.isEditing}
                        required
                      />
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      >
                        {!newCardChannelErrors[index].channelId &&
                          newChannel.channelId &&
                          index === newCardAssignedChannel.length - 1 &&
                          (isMobile ? (
                            <ActionButton
                              backgroundColor="green"
                              onClick={() => {
                                setNewCardAssignedChannel([
                                  ...newCardAssignedChannel,
                                  {
                                    channelId: null,
                                  },
                                ]);
                                setNewCardChannelErrors([
                                  ...newCardChannelErrors,
                                  {},
                                ]);
                              }}
                            >
                              Add
                            </ActionButton>
                          ) : (
                            <ActionIcon
                              icon="plus-circle"
                              color="green"
                              onClick={() => {
                                setNewCardAssignedChannel([
                                  ...newCardAssignedChannel,
                                  {
                                    channelId: null,
                                  },
                                ]);
                                setNewCardChannelErrors([
                                  ...newCardChannelErrors,
                                  {},
                                ]);
                              }}
                            />
                          ))}
                        {isMobile ? (
                          <ActionButton
                            backgroundColor="red"
                            onClick={() => {
                              let newValue = newCardAssignedChannel.filter(
                                (channel, channelIndex) => {
                                  return channelIndex !== index;
                                }
                              );

                              let newErrors = newCardChannelErrors.filter(
                                (channel, channelIndex) => {
                                  return channelIndex !== index;
                                }
                              );

                              if (newValue.length === 0) {
                                newValue.push({
                                  channelId: null,
                                });
                                newErrors.push({});
                              }

                              setNewCardAssignedChannel(newValue);
                              setNewCardChannelErrors(newErrors);
                            }}
                          >
                            Delete
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon="minus-circle"
                            color="red"
                            onClick={() => {
                              let newValue = newCardAssignedChannel.filter(
                                (channel, channelIndex) => {
                                  return channelIndex !== index;
                                }
                              );

                              let newErrors = newCardChannelErrors.filter(
                                (channel, channelIndex) => {
                                  return channelIndex !== index;
                                }
                              );

                              if (newValue.length === 0) {
                                newValue.push({
                                  channelId: null,
                                });
                                newErrors.push({});
                              }

                              setNewCardAssignedChannel(newValue);
                              setNewCardChannelErrors(newErrors);
                            }}
                          />
                        )}
                      </ActionFields>
                    </PageSubsection>
                  );
                })}
              {!values.billType && (
                <center>
                  <p style={{ fontSize: 20, color: 'red' }}>
                    Please select a billtype to credit card assign channels
                  </p>
                </center>
              )}
              <SubsectionTitle>XENDIT ASSIGNED CHANNELS</SubsectionTitle>
              {!loading &&
                values &&
                values.ewalletAssignedChannels &&
                values.ewalletAssignedChannels.map((channel, index) => (
                  <PageSubsection key={channel.channelId + '-' + index}>
                    <FormField
                      placeholder=""
                      label="Channel"
                      name={`channel-${channel.name}`}
                      type={FIELD_TYPES.TEXT}
                      value={channel.name}
                      readOnly={!state.isEditing}
                      perRow={2}
                      required
                    />
                    {state.isEditing ? (
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      >
                        {isMobile ? (
                          <ActionButton
                            backgroundColor={'red'}
                            onClick={() => {
                              setSelectedEWalletChannel(channel);
                            }}
                          >
                            Delete
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon={'minus-circle'}
                            color={'red'}
                            onClick={() => {
                              setSelectedEWalletChannel(channel);
                            }}
                          />
                        )}
                      </ActionFields>
                    ) : (
                      ''
                    )}
                  </PageSubsection>
                ))}
              {!state.isEditing && values.billType && (
                <PageSubsection>
                  <FormField
                    placeholder="Channel"
                    label="Channel"
                    name={`newChannel`}
                    type={FIELD_TYPES.TEXT}
                    value={'None'}
                    readOnly={!state.isEditing}
                    perRow={2}
                    required
                  />
                </PageSubsection>
              )}
              {state.isEditing &&
                values.billType &&
                newEWalletAssignedChannel.map((newChannel, index) => {
                  return (
                    <PageSubsection key={index}>
                      <FormField
                        placeholder="Channel"
                        label="Channel"
                        name={`channel-${index}`}
                        type={FIELD_TYPES.SELECT}
                        value={newChannel.channelId}
                        options={channelOptions}
                        onChange={event => {
                          const name = channelOptions.find(
                            data => data.value === event
                          );
                          let newValue = newEWalletAssignedChannel.map(
                            (newValue, newIndex) => {
                              if (newIndex === index) {
                                return {
                                  channelId: event,
                                  name: name.label,
                                };
                              }
                              return newValue;
                            }
                          );

                          setNewEWalletAssignedChannel(newValue);
                          handleNewEWalletChannelAssignedValidation(
                            event,
                            index
                          );
                        }}
                        onBlur={event =>
                          handleNewEWalletChannelAssignedValidation(
                            event,
                            index
                          )
                        }
                        error={newEWalletChannelErrors[index].channelId}
                        perRow={2}
                        readOnly={!state.isEditing}
                        required
                      />
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      >
                        {!newEWalletChannelErrors[index].channelId &&
                          newChannel.channelId &&
                          index === newEWalletAssignedChannel.length - 1 &&
                          (isMobile ? (
                            <ActionButton
                              backgroundColor="green"
                              onClick={() => {
                                setNewEWalletAssignedChannel([
                                  ...newEWalletAssignedChannel,
                                  {
                                    channelId: null,
                                  },
                                ]);
                                setNewEWalletChannelErrors([
                                  ...newEWalletChannelErrors,
                                  {},
                                ]);
                              }}
                            >
                              Add
                            </ActionButton>
                          ) : (
                            <ActionIcon
                              icon="plus-circle"
                              color="green"
                              onClick={() => {
                                setNewEWalletAssignedChannel([
                                  ...newEWalletAssignedChannel,
                                  {
                                    channelId: null,
                                  },
                                ]);
                                setNewEWalletChannelErrors([
                                  ...newEWalletChannelErrors,
                                  {},
                                ]);
                              }}
                            />
                          ))}
                        {isMobile ? (
                          <ActionButton
                            backgroundColor="red"
                            onClick={() => {
                              let newValue = newEWalletAssignedChannel.filter(
                                (channel, channelIndex) => {
                                  return channelIndex !== index;
                                }
                              );

                              let newErrors = newEWalletChannelErrors.filter(
                                (channel, channelIndex) => {
                                  return channelIndex !== index;
                                }
                              );

                              if (newValue.length === 0) {
                                newValue.push({
                                  channelId: null,
                                });
                                newErrors.push({});
                              }

                              setNewEWalletAssignedChannel(newValue);
                              setNewEWalletChannelErrors(newErrors);
                            }}
                          >
                            Delete
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon="minus-circle"
                            color="red"
                            onClick={() => {
                              let newValue = newEWalletAssignedChannel.filter(
                                (channel, channelIndex) => {
                                  return channelIndex !== index;
                                }
                              );

                              let newErrors = newEWalletChannelErrors.filter(
                                (channel, channelIndex) => {
                                  return channelIndex !== index;
                                }
                              );

                              if (newValue.length === 0) {
                                newValue.push({
                                  channelId: null,
                                });
                                newErrors.push({});
                              }

                              setNewEWalletAssignedChannel(newValue);
                              setNewEWalletChannelErrors(newErrors);
                            }}
                          />
                        )}
                      </ActionFields>
                    </PageSubsection>
                  );
                })}
              {!values.billType && (
                <center>
                  <p style={{ fontSize: 20, color: 'red' }}>
                    Please select a billtype to e-wallet assign channels
                  </p>
                </center>
              )}
              <SubsectionTitle>
                PAYMENT MODE MANAGEMENT ASSIGNED CHANNELS
              </SubsectionTitle>
              {!loading &&
                values &&
                values.postPaymentConfigChannels &&
                values.postPaymentConfigChannels.map((channel, index) => (
                  <PageSubsection key={channel.channelId + '-' + index}>
                    <FormField
                      placeholder=""
                      label="Channel"
                      name={`channel-${channel.name}`}
                      type={FIELD_TYPES.TEXT}
                      value={channel.name}
                      readOnly={!state.isEditing}
                      perRow={2}
                      required
                    />
                    {state.isEditing ? (
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      >
                        {isMobile ? (
                          <ActionButton
                            backgroundColor={'red'}
                            onClick={() => {
                              setSelectedPostPaymentConfigChannel(channel);
                            }}
                          >
                            Delete
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon={'minus-circle'}
                            color={'red'}
                            onClick={() => {
                              setSelectedPostPaymentConfigChannel(channel);
                            }}
                          />
                        )}
                      </ActionFields>
                    ) : (
                      ''
                    )}
                  </PageSubsection>
                ))}
              {!state.isEditing && values.billType && (
                <PageSubsection>
                  <FormField
                    placeholder="Channel"
                    label="Channel"
                    name={`newChannel`}
                    type={FIELD_TYPES.TEXT}
                    value={'None'}
                    readOnly={!state.isEditing}
                    perRow={2}
                    required
                  />
                </PageSubsection>
              )}
              {state.isEditing &&
                values.billType &&
                newPostPaymentConfigAssignedChannels.map(
                  (newChannel, index) => {
                    return (
                      <PageSubsection key={index}>
                        <FormField
                          placeholder="Channel"
                          label="Channel"
                          name={`channel-${index}`}
                          type={FIELD_TYPES.SELECT}
                          value={newChannel.channelId}
                          options={channelOptions}
                          onChange={event => {
                            const name = channelOptions.find(
                              data => data.value === event
                            );
                            let newValue =
                              newPostPaymentConfigAssignedChannels.map(
                                (newValue, newIndex) => {
                                  if (newIndex === index) {
                                    return {
                                      channelId: event,
                                      name: name.label,
                                    };
                                  }
                                  return newValue;
                                }
                              );

                            setNewPostPaymentConfigAssignedChannels(newValue);
                            handlePostPaymentConfigChannelAssignedValidation(
                              event,
                              index
                            );
                          }}
                          onBlur={event => {
                            handlePostPaymentConfigChannelAssignedValidation(
                              event,
                              index
                            );
                          }}
                          error={
                            newPostPaymentConfigChannelErrors[index].channelId
                          }
                          perRow={2}
                          readOnly={!state.isEditing}
                          required
                        />
                        <ActionFields
                          isMobile={isMobile}
                          fieldsPerRow={2.5}
                          perRow={5}
                        >
                          {!newPostPaymentConfigChannelErrors[index]
                            .channelId &&
                            newChannel.channelId &&
                            index ===
                              newPostPaymentConfigAssignedChannels.length - 1 &&
                            (isMobile ? (
                              <ActionButton
                                backgroundColor="green"
                                onClick={() => {
                                  setNewPostPaymentConfigAssignedChannels([
                                    ...newPostPaymentConfigAssignedChannels,
                                    {
                                      channelId: null,
                                    },
                                  ]);
                                  setNewPostPaymentConfigChannelErrors([
                                    ...newPostPaymentConfigChannelErrors,
                                    {},
                                  ]);
                                }}
                              >
                                Add
                              </ActionButton>
                            ) : (
                              <ActionIcon
                                icon="plus-circle"
                                color="green"
                                onClick={() => {
                                  setNewPostPaymentConfigAssignedChannels([
                                    ...newPostPaymentConfigAssignedChannels,
                                    {
                                      channelId: null,
                                    },
                                  ]);
                                  setNewPostPaymentConfigChannelErrors([
                                    ...newPostPaymentConfigChannelErrors,
                                    {},
                                  ]);
                                }}
                              />
                            ))}
                          {isMobile ? (
                            <ActionButton
                              backgroundColor="red"
                              onClick={() => {
                                let newValue =
                                  newPostPaymentConfigAssignedChannels.filter(
                                    (channel, channelIndex) => {
                                      return channelIndex !== index;
                                    }
                                  );

                                let newErrors =
                                  newPostPaymentConfigChannelErrors.filter(
                                    (channel, channelIndex) => {
                                      return channelIndex !== index;
                                    }
                                  );

                                if (newValue.length === 0) {
                                  newValue.push({
                                    channelId: null,
                                  });
                                  newErrors.push({});
                                }

                                setNewPostPaymentConfigAssignedChannels(
                                  newValue
                                );
                                setNewPostPaymentConfigChannelErrors(newErrors);
                              }}
                            >
                              Delete
                            </ActionButton>
                          ) : (
                            <ActionIcon
                              icon="minus-circle"
                              color="red"
                              onClick={() => {
                                let newValue =
                                  newPostPaymentConfigAssignedChannels.filter(
                                    (channel, channelIndex) => {
                                      return channelIndex !== index;
                                    }
                                  );

                                let newErrors =
                                  newPostPaymentConfigChannelErrors.filter(
                                    (channel, channelIndex) => {
                                      return channelIndex !== index;
                                    }
                                  );

                                if (newValue.length === 0) {
                                  newValue.push({
                                    channelId: null,
                                  });
                                  newErrors.push({});
                                }

                                setNewPostPaymentConfigAssignedChannels(
                                  newValue
                                );
                                setNewPostPaymentConfigChannelErrors(newErrors);
                              }}
                            />
                          )}
                        </ActionFields>
                      </PageSubsection>
                    );
                  }
                )}
              {!values.billType && (
                <center>
                  <p style={{ fontSize: 20, color: 'red' }}>
                    Please select a bill type to assign channels
                  </p>
                </center>
              )}
              <SubsectionTitle>SECURITY</SubsectionTitle>
              <PageSubsection>
                <FormField
                  label="Email address"
                  name="email"
                  type={FIELD_TYPES.EMAIL}
                  value={values.email}
                  onChange={onChange.email}
                  onBlur={onBlur.email}
                  error={errors.email}
                  readOnly={!state.isEditing}
                  perRow={2}
                  required
                />
                <FormField
                  label="Mobile Number"
                  name="mobileNumber"
                  type={FIELD_TYPES.MOBILE_NUMBER}
                  value={values.mobileNumber}
                  onChange={onChange.mobileNumber}
                  onBlur={onBlur.mobileNumber}
                  error={errors.mobileNumber}
                  readOnly={!state.isEditing}
                  perRow={2}
                  required
                />
              </PageSubsection>
              <SubsectionTitle>NOTIFICATION SETTINGS</SubsectionTitle>
              <PageSubsection>
                <FormField
                  name="notificationSettings"
                  type={FIELD_TYPES.CHECKBOX_GROUP}
                  value={values.notificationSettings}
                  onChange={onChange.notificationSettings}
                  onBlur={onBlur.notificationSettings}
                  error={errors.notificationSettings}
                  readOnly={!state.isEditing}
                  options={[
                    { value: 'EMAIL', label: 'EMAIL' },
                    { value: 'SMS', label: 'SMS' },
                  ]}
                />
              </PageSubsection>
              <PageSubsection>
                <ButtonsContainer>
                  {isMobile ? (
                    <>
                      {editButtonGroup}
                      {backButton}
                    </>
                  ) : (
                    <>
                      {backButton}
                      {editButtonGroup}
                    </>
                  )}
                </ButtonsContainer>
              </PageSubsection>
            </>
          )}
        </DataContainer>
      </Page>
      <AlertModal
        isOpen={state.isConfirmEditUserModalOpen}
        title="Save Changes Alert"
        variant="warn"
        icon="exclamation-circle"
        header="ARE YOU SURE?"
        subHeader="You are about to save changes on the Account."
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone',
        ]}
        confirmText="Yes"
        confirmLoading={isEditingUser}
        handleConfirm={() => {
          const value = getDiff(initialValue, sanitize(state.selectedUser));

          if (value.notificationSettings) {
            const { notificationSettings } = value;
            value.smsNotif = notificationSettings.includes('SMS');
            value.emailNotif = notificationSettings.includes('EMAIL');
            delete value.notificationSettings;
          }

          editUser({
            variables: {
              data: {
                ...value,
                billType: values.billType,
                assignedChannels: [
                  ...values.assignedChannels.map(currentVal => {
                    return {
                      channelId: currentVal.channelId,
                      name: currentVal.name,
                    };
                  }),
                  ...newValues,
                ],
                cardAssignedChannels: [
                  ...values.cardAssignedChannels.map(currentVal => {
                    return {
                      channelId: currentVal.channelId,
                      name: currentVal.name,
                    };
                  }),
                  ...newCardValues,
                ],
                ewalletAssignedChannels: [
                  ...values.ewalletAssignedChannels.map(currentVal => {
                    return {
                      channelId: currentVal.channelId,
                      name: currentVal.name,
                    };
                  }),
                  ...newEWalletValues,
                ],
                postPaymentConfigChannels: [
                  ...values.postPaymentConfigChannels.map(currentVal => {
                    return {
                      channelId: currentVal.channelId,
                      name: currentVal.name,
                    };
                  }),
                  ...newPostPaymentConfigValues,
                ],
              },
              where: { id: data.user.id },
            },
          });
        }}
        handleClose={() =>
          setState({ ...state, isConfirmEditUserModalOpen: false })
        }
      />
      <AlertModal
        isOpen={state.isSuccessEditUserModalOpen}
        title="Save Changes Alert"
        variant="success"
        icon="check-circle"
        header="SUCCESS!"
        subHeader="Changes have been saved successfully."
        description="Notification will be sent on the User's email address and or mobile number regarding the changes on the Account."
        confirmText="Go to All User Accounts"
        handleConfirm={() => {
          history.push('/user-accounts');
        }}
        handleClose={() =>
          setState({ ...state, isSuccessEditUserModalOpen: false })
        }
      />
      <AlertModal
        isOpen={state.isFailureEditUserModalOpen}
        title="Save Changes Alert"
        variant="error"
        icon="times-circle"
        header="OH, SNAP!"
        subHeader={
          state.editUserError === 'USER_EMAIL_ALREADY_EXISTS'
            ? 'Email is already registered.'
            : 'There was a problem on saving changes on the Account.'
        }
        description={
          state.editUserError === 'USER_EMAIL_ALREADY_EXISTS'
            ? 'Please try again.'
            : 'Please go back and try saving it again.'
        }
        confirmText="Go back"
        handleConfirm={() => {
          setState({
            ...state,
            isFailureEditUserModalOpen: false,
            editUserError: null,
          });
        }}
        handleClose={() =>
          setState({
            ...state,
            isFailureEditUserModalOpen: false,
            editUserError: null,
          })
        }
      />
      <AlertModal
        isOpen={state.isLeavingWhileEditing}
        title="New Account Status"
        icon="question-circle"
        variant="warn"
        header="THERE ARE UNSAVED CHANGES."
        subHeader="Are you sure you want to leave this page without saving?"
        description="Your changes will be lost if you don't save them."
        handleClose={() => setState({ ...state, isLeavingWhileEditing: false })}
        cancelText="Discard Changes"
        confirmText="Go Back"
        handleCancel={() => {
          if (state.nextLocation) {
            history.push(state.nextLocation);
          }
        }}
        handleConfirm={() => {
          setState({ ...state, isLeavingWhileEditing: false });
        }}
      />
    </>
  );
};

UserInformation.propTypes = {
  location: PropTypes.object,
  history: PropTypes.object,
  match: PropTypes.object,
};

export default UserInformation;
