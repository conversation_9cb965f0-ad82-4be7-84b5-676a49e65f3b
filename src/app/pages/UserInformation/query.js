import { gql } from '@apollo/client';

export const GET_USER_INFORMATION = gql`
  query getUser($where: UserPrimary!) {
    user(where: $where) {
      id
      name
      email
      role {
        id
        name
      }
      department
      division
      loginTime
      isActive
      group
      channel
      reasonToDeactivate
      mobileNumber
      createdAt
      smsNotif
      emailNotif
      assignedChannels {
        channelId
        name
      }
      cardAssignedChannels {
        channelId
        name
      }
      ewalletAssignedChannels {
        channelId
        name
      }
      postPaymentConfigChannels {
        channelId
        name
      }
      billType
    }

    rolesLoose {
      id
      name
    }

    channelsLoose {
      id
      name
    }
  }
`;

export const GET_CHANNEL_BILLTYPE = gql`
  query getChannelsBillType($billType: BillTypeOption!) {
    channelsBillType(billType: $billType) {
      id
      name
    }
  }
`;
