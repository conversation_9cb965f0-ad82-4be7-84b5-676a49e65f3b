import format from 'date-fns/format';
import PropTypes from 'prop-types';
import React, { useContext, useEffect, useMemo, useState } from 'react';
import { useQuery } from '@apollo/client';
import styled from 'styled-components';
import * as Yup from 'yup';
import PrimaryButton from '../../components/Button/PrimaryButton';
import SecondaryButton from '../../components/Button/SecondaryButton';
import Checkbox from '../../components/Checkbox';
import DataContainer from '../../components/DataContainer';
import DataHeader from '../../components/DataHeader';
import DataTable from '../../components/DataTable';
import { FIELD_TYPES } from '../../components/Form/constants';
import FormField from '../../components/Form/FormField';
import Header from '../../components/Header';
import {
  ButtonsContainer,
  PageSubsection,
  SubsectionTitle,
} from '../../components/InformationPage';
import Loader from '../../components/Loader';
import { AlertModal } from '../../components/Modal';
import NotFound from '../../components/NotFound/NotFound';
import Page from '../../components/Page';
import Row from '../../components/Row';
import AuthContext from '../../context/AuthContext/AuthContext';
import ResponsiveContext from '../../context/ResponsiveContext';
import useForm from '../../hooks/useForm';
import { useMutation } from '@apollo/client';
import getDiff from '../../utils/getDiff';
import { EDIT_ROLE } from './mutation';
import { GET_ROLE_INFORMATION } from './query';

const PermissionLabel = styled.span`
  color: #009cde;
  font-weight: bold;
`;

const PERMISSIONS = {
  User: [],
  Role: [],
  Channel: [],
  Mid: [],
  Provider: [],
  Bank: [],
  Transaction: [],
  EndGameReport: [],
  PayByLink: [],
  LoadORReport: [],
  ContentGcashReport: [],
  ContentFraudReport: [],
  GcashRefundRequest: [],
  GcashRefundApproval: [],
  XenditRefundRequest: [],
  XenditRefundApproval: [],
  CardRefundRequest: [],
  CardRefundApproval: [],
  GcashRefundDetailedReport: [],
  GcashRefundSummaryReport: [],
  CardRefundDetailedReport: [],
  CardRefundSummaryReport: [],
  XenditRefundDetailedReport: [],
  XenditRefundSummaryReport: [],
  DropinSimulator: [],
  ADADeclinedReport: [],
  ADASummaryReport: [],
  InstallmentReport: [],
  InstallmentMid: [],
  ECPay: [],
  Failed: [],
  Billing: [],
  Gateway: [],
  Collection: [],
  Wireline: [],
  MonthlyGenerated: [],
  Treasury: [],
  LukeBatchFile: [],
  Audit: [],
  Archive: [],
  Config: [],
  BillLinerConfig: [],
  PayByLinkModule: [],
  PayByLinkReport: [],
  PostPaymentConfig: [],
  GCashBindingReport: [],
  ConvenienceFee: [],
  ConvenienceFeeBrand: [],
};

const ACTIONS = [
  'view',
  'create',
  'update',
  'delete',
  'export',
  'import',
  'deactivate',
];

const DASHBOARD_TILES = [
  { label: 'Overall Transactions', value: 'transactions' },
  { label: 'Online CC and GCash', value: 'onlineCCGCash' },
  { label: 'Revenue per Channel', value: 'revenuePerChannel' },
  { label: 'System Notifications', value: 'notifications' },
  {
    label: 'Overall Transaction per Channel',
    value: 'transactionsPerChannel',
  },
  { label: 'Transactions Percentage', value: 'transactionsPercentage' },
  { label: 'Channel and Payment Gateway Status', value: 'gatewayStatus' },
  { label: 'Performace', value: 'performance' },
  { label: 'Adyen Monitoring Uptime', value: 'adyen' },
  { label: 'Channel Transaction', value: 'channelTransaction' },
  { label: 'User Management Summary', value: 'userMgmt' },
];

const UserRoleInformation = ({ match, location, history }) => {
  const { permissions } = useContext(AuthContext);
  const { isMobile } = useContext(ResponsiveContext);

  const [state, setState] = useState({
    isEditing: false,
    nextLocation: null,
    isConfirmEditUserRoleModalOpen: false,
    isSuccessEditUserRoleModalOpen: false,
    isFailureEditUserRoleModalOpen: false,
    isEditingUserRole: false,

    isLeavingWhileEditing: false,

    selectedRole: null,
    dashboardPermissions: [],
    permissions: [],
    reportPermissions: [],
    systemPermissions: [],

    editRoleError: null,
  });

  useEffect(() => {
    if (location.state && location.state.isEditing !== undefined) {
      setState({ ...state, isEditing: location.state.isEditing });
    }
  }, []);

  useEffect(() => {
    const unblock = history.block(location => {
      if (state.isLeavingWhileEditing || !state.isEditing) return true;
      setState({
        ...state,
        nextLocation: location,
        isLeavingWhileEditing: true,
      });
      return false;
    });

    return () => {
      unblock();
    };
  }, [state.isLeavingWhileEditing, state.isEditing]);

  const { data, loading } = useQuery(GET_ROLE_INFORMATION, {
    variables: { where: { id: match.params.id } },
    fetchPolicy: 'network-only',
    notifyOnNetworkStatusChange: true,
  });

  const [editRole, { loading: isEditingRole }] = useMutation(EDIT_ROLE, {
    onCompleted: () => {
      setState({
        ...state,
        isConfirmEditUserRoleModalOpen: false,
        isSuccessEditUserRoleModalOpen: true,
        isEditing: false,
      });
    },
    onError: err => {
      setState({
        ...state,
        isConfirmEditUserRoleModalOpen: false,
        isFailureEditUserRoleModalOpen: true,
        editRoleError: err.networkError.result
          ? err.networkError.result.message
          : null,
      });
    },
  });

  useEffect(() => {
    if (data?.role && !state.permissions.length) {
      const entities = [
        {
          label: 'User Account',
          value: 'User',
          features: ['import', 'export', 'deactivate'],
        },
        { label: 'User Role', value: 'Role', features: ['import'] },
        { label: 'Channel Management', value: 'Channel', features: [] },
        { label: 'MID Management', value: 'Mid', features: ['export'] },
        { label: 'Pay by Link', value: 'PayByLinkModule', features: [] },
        {
          label: 'GCash Enrolled Payment Methods',
          value: 'GCashBindingReport',
          features: ['export'],
        },
        {
          label: 'Payment Mode Management',
          value: 'PostPaymentConfig',
          features: [],
        },
        {
          label: 'GCash Refund Request',
          value: 'GcashRefundRequest',
          features: [],
        },
        {
          label: 'GCash Refund Approval',
          value: 'GcashRefundApproval',
          features: [],
        },
        {
          label: 'Adyen Refund Request',
          value: 'CardRefundRequest',
          features: [],
        },
        {
          label: 'Adyen Refund Approval',
          value: 'CardRefundApproval',
          features: [],
        },
        {
          label: 'Xendit Refund Request',
          value: 'XenditRefundRequest',
          features: [],
        },
        {
          label: 'Xendit Refund Approval',
          value: 'XenditRefundApproval',
          features: [],
        },
        { label: 'Convenience Fee', value: 'ConvenienceFee', features: [] },
        {
          label: 'Convenience Fee Brands',
          value: 'ConvenienceFeeBrand',
          features: [],
        },
        { label: 'Installment', value: 'InstallmentMid', features: [] },
        { label: 'Provider Management', value: 'Provider', features: [] },
        { label: 'Bank Code Management', value: 'Bank', features: ['import'] },
      ];

      const reports = [
        {
          label: 'Transaction Logs',
          reportName: 'Transaction',
        },
        { label: 'EndGame Transaction Logs', reportName: 'EndGameReport' },
        { label: 'Channel Transactions', reportName: 'ChannelReport' },
        { label: 'OR Report', reportName: 'LoadORReport' },
        { label: 'Content-GCash Reports', reportName: 'ContentGcashReport' },
        { label: 'Swipe Transaction Logs', reportName: 'ContentFraudReport' },
        {
          label: 'GCash Refund Report Detailed',
          reportName: 'GcashRefundDetailedReport',
        },
        {
          label: 'GCash Refund Report Summarized',
          reportName: 'GcashRefundSummaryReport',
        },
        {
          label: 'Adyen Refund Detail Report',
          reportName: 'CardRefundDetailedReport',
        },
        {
          label: 'Adyen Refund Summary Report',
          reportName: 'CardRefundSummaryReport',
        },
        {
          label: 'Xendit Refund Detailed Report',
          reportName: 'XenditRefundDetailedReport',
        },

        {
          label: 'Xendit Refund Summary Report',
          reportName: 'XenditRefundSummaryReport',
        },
        {
          label: 'ADA Declined Rate Detailed Report',
          reportName: 'ADADeclinedReport',
        },
        {
          label: 'ADA Declined Rate Summary Report',
          reportName: 'ADASummaryReport',
        },
        { label: 'GOTS Reports', reportName: 'GotsReport' },
        {
          label: 'Installment Report',
          reportName: 'InstallmentReport',
        },
        { label: 'ECPay Wallet', reportName: 'ECPay' },
        { label: 'GlobeBBand Shop', reportName: 'PayByLink' },
        { label: 'GlobeOne Logs', reportName: 'GlobeOne' },
        { label: 'Failed Postings', reportName: 'Failed' },
        { label: 'Billing Reports', reportName: 'Billing' },
        { label: 'Gateway for Online Payments', reportName: 'Gateway' },
        { label: 'Credit Card Collection Summary', reportName: 'Collection' },
        { label: 'Revenue Accounting Report', reportName: 'Wireline' },
        { label: 'Monthly Generated', reportName: 'MonthlyGenerated' },
        { label: 'Treasury Bills', reportName: 'Treasury' },
        { label: 'Batch Files', reportName: 'LukeBatchFile' },
        { label: 'Pay By Link', reportName: 'PayByLinkReport' },
        {
          label: 'Dropin Simulator',
          reportName: 'DropinSimulator',
        },
      ];

      const systems = [
        { label: 'Audit Trail', value: 'Audit' },
        { label: 'Archive', value: 'Archive' },
        { label: 'Configuration', value: 'Config' },
        {
          label: 'Bill Liner Config',
          value: 'BillLinerConfig',
          features: [],
        },
      ];

      if (data.role.permissions === null) data.role.permissions = '';

      if (data.role.permissions) {
        let dashboardPermissions = [
          DASHBOARD_TILES.reduce(
            (previous, current) => {
              return {
                ...previous,
                [current.value]: data.role.permissions.Dashboard.includes(
                  current.value
                ),
              };
            },
            {
              permission: 'View',
            }
          ),
        ];

        let permissions = entities.map(entity => {
          const arrPermissions = data.role.permissions[entity.value];

          const featurePermission = { ...entity };

          if (arrPermissions) {
            arrPermissions.map(permission => {
              featurePermission[permission] = true;
              return null;
            });
          }

          return featurePermission;
        });

        let reportPermissions = reports.map(report => {
          const arrPermissions = data.role.permissions[report.reportName];

          const reportPermission = { ...report };

          if (arrPermissions) {
            arrPermissions.map(permission => {
              reportPermission[permission] = true;
              return null;
            });
          }

          return reportPermission;
        });

        let systemPermissions = systems.map(system => {
          const arrPermissions = data.role.permissions[system.value];

          const systemPermission = { ...system };

          if (arrPermissions) {
            arrPermissions.map(permission => {
              systemPermission[permission] = true;
              return null;
            });
          }

          return systemPermission;
        });

        setState(state => ({
          ...state,
          dashboardPermissions,
          permissions,
          reportPermissions,
          systemPermissions,
        }));
      }
    }
  }, [JSON.stringify(data)]);

  const fields = useMemo(() => {
    const fields = {
      name: {
        validation: Yup.string()
          .max(100, 'Must not exceed 100 characters')
          .required('Please enter a value')
          .matches(/[^-\s]/, 'Must not be a whitespace')
          .matches(
            /^(?![=,@,+,-])(.+)$/,
            'Input must not begin with this special characters (=,@,+,-)'
          ),
      },
      code: {
        validation: Yup.string()
          .max(100, 'Must not exceed 100 characters')
          .required('Please enter a value')
          .matches(/[^-\s]/, 'Must not be a whitespace')
          .matches(
            /^(?![=,@,+,-])(.+)$/,
            'Input must not begin with this special characters (=,@,+,-)'
          ),
      },
      notes: {
        validation: Yup.string()
          .max(250, 'Must not exceed 250 characters')
          .nullable(),
      },
      isActive: {},
      notificationSettings: {
        initialValue: [],
      },
    };

    if (!loading && data && data.role) {
      for (const name of Object.keys(fields)) {
        if (Object.prototype.hasOwnProperty.call(data.role, name)) {
          fields[name].initialValue = data.role[name];
        } else if (name === 'notificationSettings') {
          const notificationSettings = [];
          if (data.role.smsNotif) notificationSettings.push('SMS');
          if (data.role.emailNotif) notificationSettings.push('EMAIL');
          fields[name].initialValue = notificationSettings;
        }
      }
    }

    return fields;
  }, [JSON.stringify(data)]);

  const { values, onChange, onBlur, errors, onSubmit } = useForm(
    fields,
    values => {
      setState({
        ...state,
        isConfirmEditUserRoleModalOpen: true,
        selectedRole: values,
      });
    }
  );

  function handleDashboardPermission(event) {
    const { value, checked } = event.currentTarget;
    setState({
      ...state,
      dashboardPermissions: [
        {
          ...state.dashboardPermissions[0],
          [value]: checked,
        },
      ],
    });
  }

  function handleChangePermission(event, data) {
    const { value, checked } = event.currentTarget;
    if (value === 'all') {
      setState({
        ...state,
        permissions: state.permissions.map(permission =>
          data.value === permission.value
            ? {
                ...permission,
                view: checked,
                create: checked,
                update: checked,
                delete: checked,
                deactivate:
                  checked && permission.features.indexOf('deactivate') >= 0,
                export: checked && permission.features.indexOf('export') >= 0,
                import: checked && permission.features.indexOf('import') >= 0,
              }
            : permission
        ),
      });
    } else if (value === 'view' && !checked) {
      setState({
        ...state,
        permissions: state.permissions.map(permission =>
          data.value === permission.value
            ? {
                ...permission,
                view: false,
                create: false,
                update: false,
                delete: false,
                deactivate: false,
                export: false,
                import: false,
              }
            : permission
        ),
      });
    } else {
      setState({
        ...state,
        permissions: state.permissions.map(permission =>
          data.label === permission.label
            ? { ...permission, [value]: !permission[value], view: true }
            : permission
        ),
      });
    }
  }

  function handleChangeReportPermission(event, data) {
    const { value, checked } = event.currentTarget;
    if (value === 'view' && !checked) {
      setState({
        ...state,
        reportPermissions: state.reportPermissions.map(permission =>
          data.reportName === permission.reportName
            ? {
                ...permission,
                view: false,
                export: false,
                import: false,
              }
            : permission
        ),
      });
    } else {
      setState({
        ...state,
        reportPermissions: state.reportPermissions.map(permission =>
          data.reportName === permission.reportName
            ? { ...permission, [value]: !permission[value], view: true }
            : permission
        ),
      });
    }
  }

  function handleChangeSystemPermission(event, data) {
    const { value, checked } = event.currentTarget;
    if (value === 'view' && !checked) {
      setState({
        ...state,
        systemPermissions: state.systemPermissions.map(permission =>
          data.value === permission.value
            ? {
                ...permission,
                view: false,
                export: false,
                update: false,
                create: false,
                delete: false,
              }
            : permission
        ),
      });
    } else {
      setState({
        ...state,
        systemPermissions: state.systemPermissions.map(permission =>
          data.label === permission.label
            ? { ...permission, [value]: !permission[value], view: true }
            : permission
        ),
      });
    }
  }

  const backButton = (
    <SecondaryButton
      onClick={() => {
        history.push('/user-roles');
      }}
    >
      Back to All Users Roles
    </SecondaryButton>
  );

  const editButtonGroup = permissions.Role.update && (
    <Row>
      {!state.isEditing && (
        <PrimaryButton
          icon="pen"
          onClick={() => setState({ ...state, isEditing: true })}
        >
          Edit
        </PrimaryButton>
      )}
      <PrimaryButton
        icon="save"
        disabled={!state.isEditing}
        onClick={() => {
          onSubmit();
        }}
      >
        Save
      </PrimaryButton>
    </Row>
  );

  return (
    <>
      <Page>
        <Header
          path={[
            'User Management',
            { label: 'User Roles', to: '/user-roles' },
            data?.role && data.role.name ? data.role.name : '',
          ]}
          withHome
          title={data?.role && data.role.name ? data.role.name : ''}
        />
        <DataContainer loading={loading}>
          {loading && <Loader fullPage />}
          {!loading && !data.role && <NotFound />}
          {!loading && data.role && (
            <>
              <DataHeader>
                <DataHeader.Title>User Role Information</DataHeader.Title>
              </DataHeader>
              <SubsectionTitle>STATUS</SubsectionTitle>
              <PageSubsection>
                <FormField
                  label="Active Account"
                  name="isActive"
                  type={FIELD_TYPES.CHECKBOX}
                  value={values.isActive}
                  onChange={onChange.isActive}
                  onBlur={onBlur.isActive}
                  error={errors.isActive}
                  readOnly={!state.isEditing}
                  row
                  noErrors
                  perRow={2}
                />
                <FormField
                  label="Last Updated:"
                  isStatic
                  value={format(data.role.updatedAt, 'MM/DD/YYYY - hh:mm:ss A')}
                  row
                  perRow={2}
                  verticalG1ap={0}
                />
              </PageSubsection>
              <SubsectionTitle>GENERAL</SubsectionTitle>
              <PageSubsection>
                <FormField
                  label="Current no. Of Users Per Role"
                  isStatic
                  value={data.role.numberOfUsers}
                  row
                  perRow={2}
                />
              </PageSubsection>
              <PageSubsection>
                <FormField
                  label="Role Name"
                  name="name"
                  type={FIELD_TYPES.TEXT}
                  value={values.name}
                  onChange={onChange.name}
                  onBlur={onBlur.name}
                  error={errors.name}
                  readOnly={!state.isEditing}
                  perRow={2}
                  required
                />
                <FormField
                  label="Role ID"
                  name="code"
                  type={FIELD_TYPES.TEXT}
                  value={values.code}
                  onChange={onChange.code}
                  onBlur={onBlur.code}
                  error={errors.code}
                  readOnly={!state.isEditing}
                  perRow={2}
                  required
                />
                <FormField
                  label="Notes"
                  name="notes"
                  type={FIELD_TYPES.TEXT}
                  value={values.notes}
                  onChange={onChange.notes}
                  onBlur={onBlur.notes}
                  error={errors.notes}
                  readOnly={!state.isEditing}
                  perRow={1}
                  horizontalGap={0}
                />
              </PageSubsection>
              <DataHeader>
                <DataHeader.Title>Permissions</DataHeader.Title>
              </DataHeader>
              <PageSubsection>
                <DataTable
                  data={state.dashboardPermissions}
                  config={DASHBOARD_TILES.reduce(
                    (previous, current) => {
                      return {
                        ...previous,
                        [current.value]: {
                          headerLabel: current.label,
                          renderAs: data => (
                            <Checkbox
                              value={current.value}
                              name={'dashboard-' + current.label}
                              checked={data[current.value]}
                              onChange={event => {
                                handleDashboardPermission(event, data);
                              }}
                              disabled={!state.isEditing}
                            />
                          ),
                        },
                      };
                    },
                    {
                      permission: {
                        headerLabel: 'Dashboard',
                        renderAs: data => (
                          <PermissionLabel>{data.permission}</PermissionLabel>
                        ),
                      },
                    }
                  )}
                />
              </PageSubsection>
              <PageSubsection style={{ marginTop: 40 }}>
                <DataTable
                  data={state.permissions}
                  config={{
                    label: {
                      headerLabel: 'Modules',
                      renderAs: data => (
                        <PermissionLabel>{data.label}</PermissionLabel>
                      ),
                    },
                    view: {
                      headerLabel: 'View',
                      renderAs: data => (
                        <Checkbox
                          value="view"
                          name={'permissions-' + data.label}
                          checked={data.view}
                          onChange={event =>
                            handleChangePermission(event, data)
                          }
                          disabled={!state.isEditing}
                        />
                      ),
                    },
                    add: {
                      headerLabel: 'Add',
                      renderAs: data => (
                        <Checkbox
                          value="create"
                          name={'permissions-' + data.label}
                          checked={data.create}
                          onChange={event =>
                            handleChangePermission(event, data)
                          }
                          disabled={!state.isEditing}
                        />
                      ),
                    },
                    update: {
                      headerLabel: 'Update',
                      renderAs: data => (
                        <Checkbox
                          value="update"
                          name={'permissions-' + data.label}
                          checked={data.update}
                          onChange={event =>
                            handleChangePermission(event, data)
                          }
                          disabled={!state.isEditing}
                        />
                      ),
                    },
                    delete: {
                      headerLabel: 'Delete',
                      renderAs: data => (
                        <Checkbox
                          value="delete"
                          name={'permissions-' + data.label}
                          checked={data.delete}
                          onChange={event =>
                            handleChangePermission(event, data)
                          }
                          disabled={!state.isEditing}
                        />
                      ),
                    },
                    export: {
                      headerLabel: 'Export',
                      renderAs: data => (
                        <Checkbox
                          value="export"
                          name={'permissions-' + data.label}
                          checked={data.export}
                          onChange={event =>
                            handleChangePermission(event, data)
                          }
                          disabled={
                            !state.isEditing ||
                            data.features.indexOf('export') < 0
                          }
                        />
                      ),
                    },
                    import: {
                      headerLabel: 'Import',
                      renderAs: data => (
                        <Checkbox
                          value="import"
                          name={'permissions-' + data.label}
                          checked={data.import}
                          onChange={event =>
                            handleChangePermission(event, data)
                          }
                          disabled={
                            !state.isEditing ||
                            data.features.indexOf('import') < 0
                          }
                        />
                      ),
                    },
                    deactivate: {
                      headerLabel: 'Deactivate',
                      renderAs: data => (
                        <Checkbox
                          value="deactivate"
                          name={'permissions-' + data.label}
                          checked={data.deactivate}
                          onChange={event =>
                            handleChangePermission(event, data)
                          }
                          disabled={
                            !state.isEditing || data.label !== 'User Account'
                          }
                        />
                      ),
                    },
                    all: {
                      headerLabel: 'All',
                      renderAs: data => (
                        <Checkbox
                          value="all"
                          name={'permissions-' + data.label}
                          checked={
                            data.view &&
                            data.create &&
                            data.update &&
                            data.delete &&
                            (data.features.indexOf('deactivate') >= 0
                              ? data.deactivate
                              : true) &&
                            (data.features.indexOf('export') >= 0
                              ? data.export
                              : true) &&
                            (data.features.indexOf('import') >= 0
                              ? data.import
                              : true)
                          }
                          onChange={event =>
                            handleChangePermission(event, data)
                          }
                          disabled={!state.isEditing}
                        />
                      ),
                    },
                  }}
                />
              </PageSubsection>
              <PageSubsection style={{ marginTop: 40 }}>
                <DataTable
                  data={state.reportPermissions}
                  config={{
                    label: {
                      headerLabel: 'Report',
                      renderAs: data => (
                        <PermissionLabel>{data.label}</PermissionLabel>
                      ),
                    },
                    view: {
                      headerLabel: 'View',
                      renderAs: data => (
                        <Checkbox
                          value="view"
                          name={'permissions-' + data.label}
                          checked={data.view}
                          onChange={event =>
                            handleChangeReportPermission(event, data)
                          }
                          disabled={!state.isEditing}
                        />
                      ),
                    },
                    export: {
                      headerLabel: 'Export',
                      renderAs: data => (
                        <Checkbox
                          value="export"
                          name={'permissions-' + data.label}
                          checked={data.export}
                          onChange={event =>
                            handleChangeReportPermission(event, data)
                          }
                          disabled={
                            !state.isEditing ||
                            data.label === 'Dropin Simulator'
                          }
                        />
                      ),
                    },
                    import: {
                      headerLabel: 'Import',
                      renderAs: data => (
                        <Checkbox
                          value="import"
                          name={'permissions-' + data.label}
                          checked={data.import}
                          onChange={event =>
                            handleChangeReportPermission(event, data)
                          }
                          disabled={
                            !state.isEditing ||
                            data.label !== 'Transaction Logs'
                          }
                        />
                      ),
                    },
                  }}
                />
              </PageSubsection>
              <PageSubsection style={{ marginTop: 40 }}>
                <DataTable
                  data={state.systemPermissions}
                  config={{
                    label: {
                      headerLabel: 'System',
                      renderAs: data => (
                        <PermissionLabel>{data.label}</PermissionLabel>
                      ),
                    },
                    view: {
                      headerLabel: 'View',
                      renderAs: data => (
                        <Checkbox
                          value="view"
                          name={'permissions-' + data.label}
                          checked={data.view}
                          onChange={event =>
                            handleChangeSystemPermission(event, data)
                          }
                          disabled={!state.isEditing}
                        />
                      ),
                    },
                    export: {
                      headerLabel: 'Export',
                      renderAs: data => (
                        <Checkbox
                          value="export"
                          name={'permissions-' + data.label}
                          checked={data.export}
                          onChange={event =>
                            handleChangeSystemPermission(event, data)
                          }
                          disabled={
                            !state.isEditing ||
                            data.value === 'Config' ||
                            data.value === 'BillLinerConfig'
                          }
                        />
                      ),
                    },
                    update: {
                      headerLabel: 'Update',
                      renderAs: data => (
                        <Checkbox
                          value="update"
                          name={'permissions-' + data.label}
                          checked={data.update}
                          onChange={event =>
                            handleChangeSystemPermission(event, data)
                          }
                          disabled={!state.isEditing || data.value !== 'Config'}
                        />
                      ),
                    },
                    create: {
                      headerLabel: 'Create',
                      renderAs: data => (
                        <Checkbox
                          value="create"
                          name={'permissions-' + data.label}
                          checked={data.create}
                          onChange={event =>
                            handleChangeSystemPermission(event, data)
                          }
                          disabled={
                            !state.isEditing || data.value !== 'BillLinerConfig'
                          }
                        />
                      ),
                    },
                    delete: {
                      headerLabel: 'Delete',
                      renderAs: data => (
                        <Checkbox
                          value="delete"
                          name={'permissions-' + data.label}
                          checked={data.delete}
                          onChange={event =>
                            handleChangeSystemPermission(event, data)
                          }
                          disabled={
                            !state.isEditing || data.value !== 'BillLinerConfig'
                          }
                        />
                      ),
                    },
                  }}
                />
              </PageSubsection>
              <PageSubsection>
                <ButtonsContainer>
                  {isMobile ? (
                    <>
                      {editButtonGroup}
                      {backButton}
                    </>
                  ) : (
                    <>
                      {backButton}
                      {editButtonGroup}
                    </>
                  )}
                </ButtonsContainer>
              </PageSubsection>
            </>
          )}
        </DataContainer>
      </Page>
      <AlertModal
        isOpen={state.isConfirmEditUserRoleModalOpen}
        title="Save Changes Alert"
        variant="warn"
        icon="exclamation-circle"
        header="ARE YOU SURE?"
        subHeader="You are about to save changes on the User Role."
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone',
        ]}
        confirmText="Yes"
        confirmLoading={isEditingRole}
        handleConfirm={() => {
          const selectedRole = { ...PERMISSIONS };

          state.permissions.map(permission => {
            selectedRole[permission.value] = [];
            ACTIONS.map(action => {
              if (permission[action]) {
                selectedRole[permission.value].push(action);
              }
              return action;
            });
            return permission;
          });

          state.reportPermissions.map(reportPermission => {
            selectedRole[reportPermission.reportName] = [];
            ACTIONS.map(action => {
              if (reportPermission[action]) {
                selectedRole[reportPermission.reportName].push(action);
              }
              return action;
            });
            return reportPermission;
          });

          state.systemPermissions.map(systemPermission => {
            selectedRole[systemPermission.value] = [];
            ACTIONS.map(action => {
              if (systemPermission[action]) {
                selectedRole[systemPermission.value].push(action);
              }
              return action;
            });
            return systemPermission;
          });

          const value = { ...state.selectedRole };
          value.smsNotif = value.notificationSettings.includes('SMS');
          value.emailNotif = value.notificationSettings.includes('EMAIL');
          delete value.notificationSettings;

          const dashboardPermissions = DASHBOARD_TILES.reduce(
            (finalVal, current) => {
              if (state.dashboardPermissions[0][current.value]) {
                return [...finalVal, current.value];
              }
              return finalVal;
            },
            ['view']
          );

          editRole({
            variables: {
              data: {
                ...getDiff(data.role, value),
                permissions: {
                  Dashboard: dashboardPermissions,
                  ...selectedRole,
                },
              },
              where: { id: data.role.id },
            },
          });
        }}
        handleClose={() =>
          setState({ ...state, isConfirmEditUserRoleModalOpen: false })
        }
      />
      <AlertModal
        isOpen={state.isSuccessEditUserRoleModalOpen}
        title="Save Changes Alert"
        variant="success"
        icon="check-circle"
        header="SUCCESS!"
        subHeader="Changes have been saved successfully."
        description="Notification will be sent on the Role's related Accounts regarding changes."
        confirmText="Go to All User Roles"
        handleConfirm={() => {
          history.push('/user-roles');
        }}
        handleClose={() =>
          setState({ ...state, isSuccessEditUserRoleModalOpen: false })
        }
      />
      <AlertModal
        isOpen={state.isFailureEditUserRoleModalOpen}
        title="Save Changes Alert"
        variant="error"
        icon="times-circle"
        header="OH, SNAP!"
        subHeader={
          state.editRoleError === 'ROLE_ID_ALREADY_EXISTS'
            ? 'Role ID already exists.'
            : 'There was a problem on saving changes on the User Role.'
        }
        description={
          state.editRoleError === 'ROLE_ID_ALREADY_EXISTS'
            ? 'Please input a unique Role ID.'
            : 'Please go back and try saving it again.'
        }
        confirmText="Go back"
        handleConfirm={() => {
          setState({ ...state, isFailureEditUserRoleModalOpen: false });
        }}
        handleClose={() =>
          setState({ ...state, isFailureEditUserRoleModalOpen: false })
        }
      />
      <AlertModal
        isOpen={state.isLeavingWhileEditing}
        title="New Account Status"
        icon="question-circle"
        variant="warn"
        header="THERE ARE UNSAVED CHANGES."
        subHeader="Are you sure you want to leave this page without saving?"
        description="Your changes will be lost if you don't save them."
        handleClose={() => setState({ ...state, isLeavingWhileEditing: false })}
        cancelText="Discard Changes"
        confirmText="Go Back"
        handleCancel={() => {
          if (state.nextLocation) {
            history.push(state.nextLocation);
          }
        }}
        handleConfirm={() => {
          setState({ ...state, isLeavingWhileEditing: false });
        }}
      />
    </>
  );
};

UserRoleInformation.propTypes = {
  history: PropTypes.object,
  location: PropTypes.object,
  match: PropTypes.object,
};

export default UserRoleInformation;
