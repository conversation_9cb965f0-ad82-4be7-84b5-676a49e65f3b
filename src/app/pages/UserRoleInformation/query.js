import { gql } from '@apollo/client';

export const GET_ROLE_INFORMATION = gql`
  query getRoleInformation($where: RolePrimary!) {
    role(where: $where) {
      id
      name
      code
      isActive
      notes
      numberOfUsers
      permissions {
        Dashboard
        User
        Role
        Channel
        Mid
        Provider
        Bank
        Transaction
        ChannelReport
        GotsReport
        Failed
        Billing
        Gateway
        Collection
        Wireline
        MonthlyGenerated
        Treasury
        LukeBatchFile
        Audit
        Archive
        Config
        ECPay
        GlobeOne
        PayByLink
        LoadORReport
        ContentGcashReport
        ContentFraudReport
        GcashRefundRequest
        GcashRefundApproval
        CardRefundRequest
        CardRefundApproval
        XenditRefundRequest
        XenditRefundApproval
        GcashRefundDetailedReport
        GcashRefundSummaryReport
        CardRefundDetailedReport
        CardRefundSummaryReport
        XenditRefundDetailedReport
        XenditRefundSummaryReport
        InstallmentReport
        InstallmentMid
        ADADeclinedReport
        ADASummaryReport
        EndGameReport
        DropinSimulator
        BillLinerConfig
        PayByLinkModule
        PayByLinkReport
        PostPaymentConfig
        GCashBindingReport
        ConvenienceFee
        ConvenienceFeeBrand
      }
      updatedAt
      smsNotif
      emailNotif
    }
  }
`;
