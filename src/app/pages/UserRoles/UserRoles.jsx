import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import format from 'date-fns/format';
import PropTypes from 'prop-types';
import React, { useContext, useEffect, useState } from 'react';
import { useQuery } from '@apollo/client';
import * as Yup from 'yup';
import ActionButtons from '../../components/ActionButtons';
import CreateButton from '../../components/Button/CreateButton';
import FileButton from '../../components/Button/FileButton';
import DataContainer from '../../components/DataContainer';
import DataTable from '../../components/DataTable';
import { FIELD_TYPES } from '../../components/Form/constants';
import { Required } from '../../components/Form/FormField';
import GlobalSearch from '../../components/GlobalSearch';
import Header from '../../components/Header/Header';
import { FormModal } from '../../components/Modal';
import AlertModal from '../../components/Modal/AlertModal';
import Page from '../../components/Page';
import AuthContext from '../../context/AuthContext/AuthContext';
import { useMutation } from '@apollo/client';
import sanitize from '../../utils/sanitize';
import { ADD_ROLE, DELETE_ROLE, DELETE_ROLES, IMPORT_ROLES } from './mutation';
import { GET_USER_ROLES } from './query';
import { formatSubHeader, formatDescription } from '../../utils/getLabel';

const UserRoles = ({ history }) => {
  const { permissions } = useContext(AuthContext);

  const [state, setState] = useState({
    isAddUserRoleModalOpen: false,

    isConfirmAddUserRoleModalOpen: false,
    isSuccessAddUserRoleModalOpen: false,
    isFailureAddUserRoleModalOpen: false,
    isLeavingPageWhileAdding: false,

    isSuccessAddRolesModalOpen: false,
    isFailureAddRolesModalOpen: false,

    isConfirmDeleteUserRoleModalOpen: false,
    isSuccessDeleteUserRoleModalOpen: false,
    isFailureDeleteUserRoleModalOpen: false,

    isConfirmDeleteUserRolesModalOpen: false,
    isSuccessDeleteUserRolesModalOpen: false,
    isFailureDeleteUserRolesModalOpen: false,

    deleteUserRoleError: null,
    deleteUserRolesError: null,
    addRoleError: null,
    importFileError: null,

    nextLocation: null,

    selectedUserRole: null,

    filter: {},
    pagination: {
      limit: 10,
      startKey: '',
    },

    selectedRows: {},
  });

  useEffect(() => {
    const unblock = history.block(location => {
      if (state.isLeavingPageWhileAdding || !state.isAddUserRoleModalOpen) {
        return true;
      }
      setState({
        ...state,
        nextLocation: location,
        isLeavingPageWhileAdding: true,
      });
      return false;
    });

    return () => {
      unblock();
    };
  }, [state.isLeavingPageWhileAdding, state.isAddUserRoleModalOpen]);

  const { data, loading, refetch } = useQuery(GET_USER_ROLES, {
    variables: {
      filter: state.filter,
      pagination: state.pagination,
    },
    fetchPolicy: 'network-only',
  });

  const [addRole, { loading: isAddingRole }] = useMutation(ADD_ROLE, {
    onCompleted: data => {
      setState({
        ...state,
        selectedUserRole: data.createRole,
        isConfirmAddUserRoleModalOpen: false,
        isSuccessAddUserRoleModalOpen: true,
        isAddUserRoleModalOpen: false,
        pagination: {
          ...state.pagination,
          startKey: '',
        },
      });
      refetch();
    },
    onError: err => {
      setState({
        ...state,
        addRoleError: err.networkError.result
          ? err.networkError.result.message
          : null,
        isConfirmAddUserRoleModalOpen: false,
        isFailureAddUserRoleModalOpen: true,
      });
    },
  });

  const [importRoles, { loading: isImportingRoles }] = useMutation(
    IMPORT_ROLES,
    {
      onCompleted: async () => {
        refetch();
        setState({ ...state, isSuccessAddRolesModalOpen: true });
      },
      onError: async err => {
        setState({
          ...state,
          isFailureAddRolesModalOpen: true,
          importFileError: err.networkError.result
            ? err.networkError.result.message
            : null,
        });
      },
    }
  );

  const [deleteRole, { loading: isDeletingRole }] = useMutation(DELETE_ROLE, {
    onCompleted: () => {
      const selectedRows = { ...state.selectedRows };
      if (selectedRows[state.selectedUserRole.id]) {
        delete selectedRows[state.selectedUserRole.id];
      }
      setState({
        ...state,
        isConfirmDeleteUserRoleModalOpen: false,
        isSuccessDeleteUserRoleModalOpen: true,
        selectedRows,
        selectedUserRole: null,
      });
      refetch();
    },
    onError: err => {
      setState({
        ...state,
        deleteUserRoleError: err.networkError.result
          ? err.networkError.result.message
          : null,
        isConfirmDeleteUserRoleModalOpen: false,
        isFailureDeleteUserRoleModalOpen: true,
      });
    },
  });

  const [deleteRoles, { loading: isDeletingRoles }] = useMutation(
    DELETE_ROLES,
    {
      onCompleted: () => {
        setState({
          ...state,
          isConfirmDeleteUserRolesModalOpen: false,
          isSuccessDeleteUserRolesModalOpen: true,
          selectedRows: {},
          pagination: {
            ...state.pagination,
            startKey: '',
          },
        });
        refetch();
      },
      onError: err => {
        setState({
          ...state,
          deleteUserRolesError: err.networkError.result
            ? err.networkError.result.message
            : null,
          isConfirmDeleteUserRolesModalOpen: false,
          isFailureDeleteUserRolesModalOpen: true,
        });
      },
    }
  );

  useEffect(() => {
    setState({ ...state, selectedRows: {} });
  }, [state.filter]);

  return (
    <>
      <Page>
        <Header
          withHome
          path={['User Mgt.', 'User Roles']}
          title="User Roles"
        />
        <DataContainer>
          <DataTable
            selected={state.selectedRows}
            setSelected={rows => {
              const selectedRows = { ...state.selectedRows };
              for (const row of rows) {
                if (selectedRows[row.id]) {
                  delete selectedRows[row.id];
                } else {
                  selectedRows[row.id] = row;
                }
              }
              setState({ ...state, selectedRows });
            }}
            loading={loading}
            data={data && data.roles && data.roles.filteredData}
            headerOptions={
              <>
                <GlobalSearch
                  onSearch={filter => {
                    setState({
                      ...state,
                      filter,
                      pagination: { ...state.pagination, startKey: '' },
                    });
                  }}
                  fields={[
                    {
                      label: 'Role Id',
                      name: 'code',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Role Name',
                      name: 'name',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Notes with',
                      name: 'notes',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Status',
                      name: 'isActive',
                      type: FIELD_TYPES.SELECT,
                      options: [
                        { label: 'Any', value: null },
                        { label: 'Active', value: true },
                        { label: 'Inactive', value: false },
                      ],
                    },
                    {
                      label: 'Date Created',
                      name: 'createdAt',
                      type: FIELD_TYPES.DATE_RANGE,
                    },
                  ]}
                  placeholder={'Search User Roles'}
                />
                <div style={{ display: 'flex' }}>
                  {permissions.Role.import && (
                    <FileButton
                      icon="file-csv"
                      iconPosition="left"
                      disabled={loading}
                      loading={isImportingRoles}
                      onImport={file => {
                        importRoles({ variables: { file } });
                      }}
                      onTemplate={() => {
                        const fileData = {
                          mime: 'text/csv',
                          filename: 'user-roles-template.csv',
                          contents:
                            '*Role_Name,*Role_ID,Notes,*Permissions\nSample Testing1,Testing Admin,This is Super Admin,User.view|User.create|User.update|User.delete\n',
                        };
                        const blob = new Blob([fileData.contents], {
                          type: fileData.mime,
                        });
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        document.body.appendChild(a);
                        a.download = fileData.filename;
                        a.href = url;
                        a.click();
                        document.body.removeChild(a);
                      }}
                    >
                      CSV{' '}
                      <FontAwesomeIcon
                        style={{ marginLeft: 10 }}
                        icon="angle-down"
                      />
                    </FileButton>
                  )}
                  {permissions.Role.create && (
                    <CreateButton
                      icon="plus"
                      onClick={() => {
                        setState({ ...state, isAddUserRoleModalOpen: true });
                      }}
                    >
                      Add User Role
                    </CreateButton>
                  )}
                  {permissions.Role.delete && (
                    <CreateButton
                      disabled={!Object.keys(state.selectedRows).length}
                      style={{ marginLeft: 10 }}
                      icon="trash-alt"
                      onClick={() => {
                        setState({
                          ...state,
                          isConfirmDeleteUserRolesModalOpen: true,
                        });
                      }}
                    >
                      {Object.keys(state.selectedRows).length} selected
                    </CreateButton>
                  )}
                </div>
              </>
            }
            pagination={{
              ...state.pagination,
              count: data && data.roles ? data.roles.count : 0,
              cursors: data && data.roles ? data.roles.cursors : [],
              handleChange: pagination => {
                setState({ 
                  ...state, 
                  pagination});
              },
            }}
            config={{
              code: {
                headerLabel: 'Role ID',
                sortable: true,
                onClick: data => {
                  history.push('/user-roles/' + data.id);
                },
              },
              name: { headerLabel: 'Role Name', sortable: true },
              numberOfUsers: {
                headerLabel: 'No. of Users',
                sortable: true,
              },
              notes: { headerLabel: 'Notes', sortable: true },
              isActive: {
                headerLabel: 'Status',
                sortable: true,
                renderAs: data => (data.isActive ? 'Active' : 'Inactive'),
              },
              createdAt: {
                headerLabel: 'Date Created',
                sortable: true,
                renderAs: data =>
                  format(new Date(data.createdAt), 'MM/DD/YYYY - hh:mm:ss A'),
              },
              actions: {
                // eslint-disable-next-line
                renderAs: data => (
                  <ActionButtons
                    handleView={() => {
                      history.push(`/user-roles/${data.id}`);
                    }}
                    handleEdit={
                      permissions.Role.update &&
                      (() => {
                        history.push(`/user-roles/${data.id}`, {
                          isEditing: true,
                        });
                      })
                    }
                    handleDelete={
                      permissions.Role.delete &&
                      (() => {
                        setState({
                          ...state,
                          isConfirmDeleteUserRoleModalOpen: true,
                          selectedUserRole: data,
                        });
                      })
                    }
                  />
                ),
              },
            }}
          />
        </DataContainer>
      </Page>
      {state.isAddUserRoleModalOpen && (
        <FormModal
          isOpen={state.isAddUserRoleModalOpen}
          width="600px"
          handleClose={() =>
            setState({ ...state, isLeavingPageWhileAdding: true })
          }
          title="Add New User Role"
          instructions={
            <span>
              To create new user role, please fill out the required
              <Required>*</Required> fields.
            </span>
          }
          submitText="Create Role"
          handleSubmit={values => {
            setState({
              ...state,
              isConfirmAddUserRoleModalOpen: true,
              selectedUserRole: values,
            });
          }}
          fields={{
            name: {
              type: FIELD_TYPES.TEXT,
              label: 'Name',
              placeholder: 'Administrator',
              validation: Yup.string()
                .max(100, 'Must not exceed 100 characters')
                .required('Please enter a value')
                .matches(/[^-\s]/, 'Must not be a whitespace')
                .matches(
                  /^(?![=,@,+,-])(.+)$/,
                  'Input must not begin with this special characters (=,@,+,-)'
                ),
              initialValue: '',
              required: true,
            },
            code: {
              type: FIELD_TYPES.TEXT,
              label: 'Role ID',
              placeholder: '001-Admin',
              validation: Yup.string()
                .max(100, 'Must not exceed 100 characters')
                .required('Please enter a value')
                .matches(/[^-\s]/, 'Must not be a whitespace')
                .matches(
                  /^(?![=,@,+,-])(.+)$/,
                  'Input must not begin with this special characters (=,@,+,-)'
                ),
              initialValue: '',
              required: true,
            },
            notes: {
              type: FIELD_TYPES.TEXT,
              label: 'Notes',
              placeholder: 'Can access everything',
              validation: Yup.string().max(
                250,
                'Must not exceed 250 characters'
              ),
              initialValue: '',
              horizontalGap: 0,
              perRow: 1,
            },
          }}
        />
      )}
      <AlertModal
        isOpen={state.isConfirmAddUserRoleModalOpen}
        title="New User Role Alert"
        icon="exclamation-circle"
        variant="warn"
        header="ARE YOU SURE?"
        subHeader="You are about to create a New User Role."
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        confirmLoading={isAddingRole}
        confirmText="Yes"
        handleConfirm={async () => {
          addRole({
            variables: {
              data: sanitize(state.selectedUserRole),
            },
          });
        }}
        handleClose={() => {
          setState({
            ...state,
            isAddUserRoleModalOpen: false,
            isConfirmAddUserRoleModalOpen: false,
          });
        }}
      />
      <AlertModal
        isOpen={state.isSuccessAddUserRoleModalOpen}
        title="New User Role Alert"
        handleClose={() => {
          setState({ ...state, isSuccessAddUserRoleModalOpen: false });
        }}
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader="User Role has been created successfully."
        description="Editing of permissions on the User role is now enabled."
        confirmText="Edit Role Permissions"
        handleConfirm={() => {
          history.push(`/user-roles/${state.selectedUserRole.id}`, {
            isEditing: true,
          });
        }}
      />
      <AlertModal
        isOpen={state.isLeavingPageWhileAdding}
        title="New User Role Alert"
        icon="question-circle"
        variant="warn"
        header="SAVE USER ROLE?"
        subHeader="You are about to leave without saving New User Role."
        description="Your entry will be lost if you don't save it"
        handleClose={() =>
          setState({ ...state, isLeavingPageWhileAdding: false })
        }
        cancelText="Discard Entry"
        confirmText="Go Back"
        handleCancel={() => {
          if (state.nextLocation) {
            history.push(state.nextLocation);
          } else {
            setState({
              ...state,
              isLeavingPageWhileAdding: false,
              isAddUserRoleModalOpen: false,
            });
          }
        }}
        handleConfirm={() => {
          setState({ ...state, isLeavingPageWhileAdding: false });
        }}
      />
      <AlertModal
        isOpen={state.isFailureAddUserRoleModalOpen}
        title="New User Role Alert"
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader={
          state.addRoleError === 'ROLE_ID_ALREADY_EXISTS'
            ? 'Role ID already exists.'
            : 'There was a problem on saving New User Role.'
        }
        description={
          state.addRoleError === 'ROLE_ID_ALREADY_EXISTS'
            ? 'Please input a unique Role ID.'
            : 'Please go back and try saving it again.'
        }
        handleClose={() =>
          setState({ ...state, isFailureAddUserRoleModalOpen: false })
        }
        confirmText="Go Back"
        handleConfirm={() => {
          setState({ ...state, isFailureAddUserRoleModalOpen: false });
        }}
      />
      <AlertModal
        isOpen={state.isConfirmDeleteUserRoleModalOpen}
        title="Delete User Role Alert"
        icon="exclamation-circle"
        variant="warn"
        header="ARE YOU SURE?"
        subHeader="You are about to delete a User Role."
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() =>
          setState({ ...state, isConfirmDeleteUserRoleModalOpen: false })
        }
        selectLabel="Reason"
        options={['Inactive for too long', 'No longer in use', 'Others'].map(
          reason => ({
            value: reason,
            label: reason,
          })
        )}
        confirmLoading={isDeletingRole}
        confirmText="Yes"
        handleConfirm={async reason => {
          deleteRole({
            variables: {
              data: { reasonToDelete: reason },
              where: { id: state.selectedUserRole.id },
            },
          });
        }}
      />
      <AlertModal
        isOpen={state.isSuccessDeleteUserRoleModalOpen}
        title="Delete User Role Alert"
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader="User Role has been deleted successfully."
        description="User Role will be permanently deleted on the system."
        handleClose={() =>
          setState({ ...state, isSuccessDeleteUserRoleModalOpen: false })
        }
        confirmText="Go to All User Roles"
        handleConfirm={() =>
          setState({ ...state, isSuccessDeleteUserRoleModalOpen: false })
        }
      />
      <AlertModal
        isOpen={state.isFailureDeleteUserRoleModalOpen}
        title="Delete User Role Alert"
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader={
          state.deleteUserRoleError === 'COULD_NOT_DELETE_DATA'
            ? 'Unfortunately, you cannot delete the User Role.'
            : 'Something went wrong while deleting the User Role.'
        }
        description={
          state.deleteUserRoleError === 'COULD_NOT_DELETE_DATA'
            ? `${
                state.selectedUserRole && state.selectedUserRole.numberOfUsers
              } User Account(s) will be affected. Kindly Change the User Account's Role to proceed.`
            : 'Please go back and try again.'
        }
        handleClose={() =>
          setState({
            ...state,
            isFailureDeleteUserRoleModalOpen: false,
            deleteUserRoleError: null,
          })
        }
        confirmText="Go to All User Roles"
        handleConfirm={() => {
          setState({
            ...state,
            isFailureDeleteUserRoleModalOpen: false,
            deleteUserRoleError: null,
          });
        }}
      />
      <AlertModal
        isOpen={state.isConfirmDeleteUserRolesModalOpen}
        title="Delete User Role Alert"
        icon="exclamation-circle"
        variant="warn"
        header="ARE YOU SURE?"
        subHeader={`You are about to delete ${Object.keys(state.selectedRows).length} Role(s).`}
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() =>
          setState({ ...state, isConfirmDeleteUserRolesModalOpen: false })
        }
        selectLabel="Reason"
        options={['Inactive for too long', 'No longer in use', 'Others'].map(
          reason => ({
            value: reason,
            label: reason,
          })
        )}
        confirmLoading={isDeletingRoles}
        confirmText="Yes"
        handleConfirm={async reason => {
          deleteRoles({
            variables: {
              data: { reasonToDelete: reason },
              where: { ids: Object.keys(state.selectedRows) },
            },
          });
        }}
      />
      <AlertModal
        isOpen={state.isSuccessDeleteUserRolesModalOpen}
        title="Delete User Role Alert"
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader="User Role(s) has been deleted successfully."
        description="User Role(s) will be permanently deleted on the system."
        handleClose={() =>
          setState({ ...state, isSuccessDeleteUserRolesModalOpen: false })
        }
        confirmText="Go to All User Roles"
        handleConfirm={() =>
          setState({ ...state, isSuccessDeleteUserRolesModalOpen: false })
        }
      />
      <AlertModal
        isOpen={state.isFailureDeleteUserRolesModalOpen}
        title="Delete User Role Alert"
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader={
          state.deleteUserRolesError === 'COULD_NOT_DELETE_DATA'
            ? 'Unfortunately, you cannot delete some of the User Role(s).'
            : 'Something went wrong while deleting the User Role(s).'
        }
        description={
          state.deleteUserRolesError === 'COULD_NOT_DELETE_DATA'
            ? `Some User Account(s) will be affected. Kindly Change the User Account's Role to proceed.`
            : 'Please go back and try again.'
        }
        handleClose={() =>
          setState({
            ...state,
            isFailureDeleteUserRolesModalOpen: false,
            deleteUserRolesError: null,
          })
        }
        confirmText="Go to All User Roles"
        handleConfirm={() => {
          setState({
            ...state,
            isFailureDeleteUserRolesModalOpen: false,
            deleteUserRolesError: null,
          });
        }}
      />
      <AlertModal
        isOpen={state.isSuccessAddRolesModalOpen}
        title="Upload File"
        handleClose={() => {
          setState({ ...state, isSuccessAddRolesModalOpen: false });
        }}
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader="Roles has been created successfully."
        description="You can now add users under new roles."
        confirmText="Go to User Roles"
        handleConfirm={() => {
          setState({ ...state, isSuccessAddRolesModalOpen: false });
        }}
      />
      <AlertModal
        isOpen={state.isFailureAddRolesModalOpen}
        title="Upload File"
        handleClose={() => {
          setState({ ...state, isFailureAddRolesModalOpen: false });
        }}
        icon="times-circle"
        variant="error"
        header="OH SNAP!"
        subHeader={
          state.importFileError ? formatSubHeader(state.importFileError) : ''
        }
        description={
          state.importFileError ? formatDescription(state.importFileError) : ''
        }
        confirmText="Try Again"
        handleConfirm={() => {
          setState({ ...state, isFailureAddRolesModalOpen: false });
        }}
      />
    </>
  );
};

UserRoles.propTypes = {
  history: PropTypes.object,
};

export default UserRoles;
