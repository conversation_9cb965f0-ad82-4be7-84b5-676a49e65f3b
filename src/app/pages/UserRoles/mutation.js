import { gql } from '@apollo/client';

export const ADD_ROLE = gql`
  mutation addRole($data: CreateRole!) {
    createRole(data: $data) {
      id
      code
      name
      notes
      isActive
      createdAt
      numberOfUsers
    }
  }
`;

export const DELETE_ROLE = gql`
  mutation deleteRole($data: DeleteRole!, $where: RolePrimary!) {
    deleteRole(data: $data, where: $where) {
      id
    }
  }
`;

export const DELETE_ROLES = gql`
  mutation deleteRoles($data: DeleteRole!, $where: Ids!) {
    deleteRoles(data: $data, where: $where) {
      unprocessedItems
    }
  }
`;

export const IMPORT_ROLES = gql`
  mutation importRoles($file: Upload!) {
    uploadRoles(file: $file) {
      filename
    }
  }
`;
