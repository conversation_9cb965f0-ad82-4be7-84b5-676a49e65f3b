import { gql } from '@apollo/client';

export const GET_USER_ROLES = gql`
  query getUserRoles(
    $filter: RolesFilter
    $pagination: PaginationInput!
  ) {
    roles(filter: $filter, pagination: $pagination) {
      cursors
      count
      lastKey
      filteredData {
        id
        code
        name
        notes
        isActive
        createdAt
        numberOfUsers
        permissions {
          Dashboard
          User
          Role
          Channel
          Mid
          Provider
          Bank
          Transaction
          Failed
          Billing
          Gateway
          Collection
          Wireline
          MonthlyGenerated
          Treasury
          LukeBatchFile
          Audit
          Archive
          Config
          ECPay
          GlobeOne
          PayByLink
          ContentGcashReport
          ContentFraudReport
          GcashRefundRequest
          GcashRefundApproval
          CardRefundRequest
          CardRefundApproval
          XenditRefundRequest
          XenditRefundApproval
          GcashRefundDetailedReport
          GcashRefundSummaryReport
          CardRefundDetailedReport
          CardRefundSummaryReport
          XenditRefundDetailedReport
          XenditRefundSummaryReport
          InstallmentReport
          InstallmentMid
          ADADeclinedReport
          ADASummaryReport
          EndGameReport
          BillLinerConfig
          PayByLinkModule
          PayByLinkReport
          PostPaymentConfig
          GCashBindingReport
          ConvenienceFee
          ConvenienceFeeBrand
        }
      }
    }
  }
`;
