import React, { useContext, useState } from 'react';
import { useMutation } from '@apollo/client';
import Page from '../../components/Page';
import Header from '../../components/Header';
import DataContainer from '../../components/DataContainer';
import DataHeader from '../../components/DataHeader';
import { AlertModal } from '../../components/Modal';
import AuthContext from '../../context/AuthContext/AuthContext';
import { FormGrid } from './styled';
import {
  PayByLinkChannelDropdown,
  Toast,
  PayByLinkRequestForm,
  PayByLinkResponseForm,
} from './components';
import {
  useToast,
  useCopyToClipboard,
  useChannels,
} from './hooks';
import { CREATE_PAYBYLINK } from './mutation';

const XenditPayByLink = () => {
  const { permissions } = useContext(AuthContext);
  const { channelOptions, channel, handleSetChannel, creds } = useChannels();
  const { showToast, closeToast, toastMessage, setMessage } = useToast();
  
  const [state, setState] = useState({
    paymentLinkResponse: null,
    error: null,
  });

  const [createPayByLink, { loading: isSubmitting }] = useMutation(CREATE_PAYBYLINK, {
    onCompleted: (result) => {
      console.log('PayByLink created successfully:', result);
      setState({
        ...state,
        paymentLinkResponse: result.createPayByLinkModule,
        error: null,
      });
    },
    onError: (err) => {
      console.error('PayByLink creation error:', err);
      setState({
        ...state,
        error: err,
        paymentLinkResponse: null,
      });
    },
  });

  const handleSubmit = (values) => {
    const mutationData = {
      channelId: channel,
      amountValue: parseFloat(values.amountDue),
      description: values.description || '',
      emailAddress: values.email || '',
    };
    createPayByLink({
      variables: { data: mutationData },
    });
  };

  const { copyToClipboard } = useCopyToClipboard(
    state.paymentLinkResponse,
    setMessage
  );

  return (
    <>
      <Page>
        {showToast && <Toast message={toastMessage} onClose={closeToast} />}
        <Header withHome title="Pay-by-Link" path={['Pay-by-Link']} />
        <DataContainer>
          <DataHeader>
            <DataHeader.Title>GENERATE PAYMENT LINK</DataHeader.Title>
          </DataHeader>
          <PayByLinkChannelDropdown
            channelOptions={channelOptions}
            channel={channel}
            handleSetChannel={handleSetChannel}
          />
          <FormGrid>
            {channel && permissions.PayByLinkModule.create && (
              <PayByLinkRequestForm
                isSubmitting={isSubmitting}
                handleSubmit={handleSubmit}
              />
            )}
            {state.paymentLinkResponse && (
              <PayByLinkResponseForm
                paymentLinkResponse={state.paymentLinkResponse}
                copyToClipboard={copyToClipboard}
              />
            )}
          </FormGrid>
        </DataContainer>
      </Page>
      
      <AlertModal
        isOpen={state.error}
        title="Generate Payment Link Alert"
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader="There was a problem with generating a payment link."
        description={state.error?.message || ''}
        handleConfirm={() => setState({ ...state, error: null })}
        handleClose={() => setState({ ...state, error: null })}
        confirmText="Go Back"
      />
    </>
  );
};

export default XenditPayByLink;