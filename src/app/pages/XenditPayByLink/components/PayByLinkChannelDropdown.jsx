import React from 'react';
import {
  ChannelDropdown,
  ChannelSelectionLabel,
  ChannelDropdownWrapper,
} from './styled';
import PropTypes from 'prop-types';

const PayByLinkChannelDropdown = ({
  channelOptions,
  channel,
  handleSetChannel,
}) => {
  return (
    <>
      <ChannelSelectionLabel>Select a channel:</ChannelSelectionLabel>
      <ChannelDropdownWrapper>
        <ChannelDropdown
          options={channelOptions}
          placeholder="------"
          onChange={handleSetChannel}
          value={channel}
        />
      </ChannelDropdownWrapper>
    </>
  );
};

export { PayByLinkChannelDropdown };

PayByLinkChannelDropdown.propTypes = {
  channelOptions: PropTypes.array,
  channel: PropTypes.string,
  handleSetChannel: PropTypes.func,
};
