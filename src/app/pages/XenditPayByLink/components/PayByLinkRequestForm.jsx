import React, { useEffect } from 'react';
import FormField from '../../../components/Form/FormField';
import { FIELD_TYPES } from '../../../components/Form/constants';
import { GeneratePaymentLinkButton, PaymentLinkRequestForm } from '../styled';
import { usePayByLinkForm } from '../hooks/usePayByLinkForm';
import PropTypes from 'prop-types';

const PayByLinkRequestForm = ({
  paymentGateway,
  isSubmitting,
  handleSubmit,
}) => {
  const { values, onChange, onBlur, errors, reset, onSubmit } =
    usePayByLinkForm(handleSubmit);

  useEffect(() => reset(), [paymentGateway]);

  return (
    <PaymentLinkRequestForm onSubmit={onSubmit}>
      <FormField
        label="Amount Due"
        name="amount_due"
        type={FIELD_TYPES.TEXT}
        value={values.amountDue}
        onChange={onChange.amountDue}
        onBlur={onBlur.amountDue}
        error={errors.amountDue}
        readOnly={false}
        perRow={1}
        required
      />
      <FormField
        label="Description"
        name="description"
        type={FIELD_TYPES.TEXT}
        value={values.description}
        onChange={onChange.description}
        onBlur={onBlur.description}
        error={errors.description}
        readOnly={false}
        perRow={1}
      />
      <FormField
        label="Email"
        name="email"
        type={FIELD_TYPES.EMAIL}
        value={values.email}
        onChange={onChange.email}
        onBlur={onBlur.email}
        error={errors.email}
        readOnly={false}
        perRow={1}
      />
      <GeneratePaymentLinkButton
        type="submit"
        loading={isSubmitting}
        disabled={isSubmitting}
      >
        Generate Payment Link
      </GeneratePaymentLinkButton>
    </PaymentLinkRequestForm>
  );
};

PayByLinkRequestForm.propTypes = {
  paymentGateway: PropTypes.string,
  isSubmitting: PropTypes.bool,
  handleSubmit: PropTypes.func,
};

export { PayByLinkRequestForm };
