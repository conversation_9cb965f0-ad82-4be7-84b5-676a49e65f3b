import React from 'react';
import {
  PaymentLinkResponseForm,
  PaymentLinkURLField,
  CopyPaymentLink,
} from '../styled';
import FormField from '../../../components/Form/FormField';
import { FIELD_TYPES } from '../../../components/Form/constants';
import PropTypes from 'prop-types';

const PayByLinkResponseForm = ({ paymentLinkResponse, copyToClipboard }) => {
  return (
    <PaymentLinkResponseForm>
      <FormField
        label="External ID"
        name="external_id"
        type={FIELD_TYPES.TEXT}
        value={paymentLinkResponse.externalId}
        onChange={null}
        onBlur={null}
        readOnly={true}
        perRow={1}
        required
      />
      <FormField
        label="Payment Gateway"
        name="payment_gateway"
        type={FIELD_TYPES.TEXT}
        value={paymentLinkResponse.paymentGateway}
        onChange={null}
        onBlur={null}
        readOnly={true}
        perRow={1}
        required
      />
      <PaymentLinkURLField>
        <FormField
          label="Payment Link"
          name="payment_link"
          type={FIELD_TYPES.TEXT}
          value={paymentLinkResponse.paymentLink}
          onChange={null}
          onBlur={null}
          readOnly={true}
          perRow={1}
          required
        />
        <CopyPaymentLink
          type="button"
          onClick={copyToClipboard}
          icon="copy"
          iconPosition="right"
        ></CopyPaymentLink>
      </PaymentLinkURLField>

      <FormField
        label="Status"
        name="status"
        type={FIELD_TYPES.TEXT}
        value={paymentLinkResponse.status}
        onChange={null}
        onBlur={null}
        readOnly={true}
        perRow={1}
        required
      />
      <FormField
        label="Merchant Account"
        name="merchant_account"
        type={FIELD_TYPES.TEXT}
        value={paymentLinkResponse.merchantAccount}
        onChange={null}
        onBlur={null}
        readOnly={true}
        perRow={1}
        required
      />
      <FormField
        label="Merchant Reference"
        name="merchant_reference"
        type={FIELD_TYPES.TEXT}
        value={paymentLinkResponse.merchantReference}
        onChange={null}
        onBlur={null}
        readOnly={true}
        perRow={1}
        required
      />
      <FormField
        label="Amount"
        name="amount"
        type={FIELD_TYPES.TEXT}
        value={paymentLinkResponse.amount}
        onChange={null}
        onBlur={null}
        readOnly={true}
        perRow={1}
        required
      />
      <FormField
        label="Description"
        name="description"
        type={FIELD_TYPES.TEXT}
        value={paymentLinkResponse.description}
        onChange={null}
        onBlur={null}
        readOnly={true}
        perRow={1}
        required
      />
      <FormField
        label="Link Type"
        name="link_type"
        type={FIELD_TYPES.TEXT}
        value={paymentLinkResponse.linkType}
        onChange={null}
        onBlur={null}
        readOnly={true}
        perRow={1}
        required
      />
    </PaymentLinkResponseForm>
  );
};

export { PayByLinkResponseForm };

PayByLinkResponseForm.propTypes = {
  paymentLinkResponse: PropTypes.shape({
    externalId: PropTypes.string.isRequired,
    paymentGateway: PropTypes.string.isRequired,
    paymentLink: PropTypes.string.isRequired,
    status: PropTypes.string.isRequired,
    merchantAccount: PropTypes.string.isRequired,
    merchantReference: PropTypes.string.isRequired,
    amount: PropTypes.string.isRequired,
    description: PropTypes.string,
    linkType: PropTypes.string.isRequired,
  }).isRequired,
  copyToClipboard: PropTypes.func,
};
