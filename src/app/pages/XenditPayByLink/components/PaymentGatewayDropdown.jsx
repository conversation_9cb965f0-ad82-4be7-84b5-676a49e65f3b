import React from 'react';
import {
  PaymentGatewaySelectionLabel,
  PaymentGatewayDropdownWrapper,
} from '../styled';
import Dropdown from '../../../components/Dropdown';

import PropTypes from 'prop-types';

const PaymentGatewayDropdown = ({
  paymentGateway,
  paymentGatewayOptions,
  handlePaymentGatewaySelection,
}) => {
  return (
    <>
      <PaymentGatewaySelectionLabel>
        Select a payment gateway:
      </PaymentGatewaySelectionLabel>
      <PaymentGatewayDropdownWrapper>
        <Dropdown
          options={paymentGatewayOptions}
          onChange={handlePaymentGatewaySelection}
          value={paymentGateway}
          placeholder="------"
        />
      </PaymentGatewayDropdownWrapper>
    </>
  );
};

export { PaymentGatewayDropdown };

PaymentGatewayDropdown.propTypes = {
  paymentGateway: PropTypes.string,
  paymentGatewayOptions: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      value: PropTypes.string.isRequired,
    })
  ).isRequired,
  handlePaymentGatewaySelection: PropTypes.func,
};
