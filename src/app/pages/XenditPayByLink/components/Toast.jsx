import React from 'react';
import PropTypes from 'prop-types';
import { Toast as BaseToast } from '../styled';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

const Toast = ({ message, onClose }) => {
  return (
    <BaseToast>
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <FontAwesomeIcon
          icon="check-square"
          style={{
            color: `rgb(79, 174, 81)`,
            height: '1.3rem',
            width: '1.3rem',
            marginRight: '0.5rem',
          }}
        />
        <span style={{ alignSelf: 'center' }}>{message}</span>
      </div>
      <button
        onClick={onClose}
        style={{
          justifySelf: 'center',
          padding: '0.6rem 1.8rem',
          borderRadius: '6px',
          backgroundColor: `rgb(55, 118, 220)`,
          alignSelf: 'center',
          border: 'none',
          color: 'white',
          fontSize: '0.7rem',
          fontWeight: 'bold',
        }}
      >
        OK
      </button>
    </BaseToast>
  );
};

export { Toast };

Toast.propTypes = {
  message: PropTypes.string,
  onClose: PropTypes.func,
};
