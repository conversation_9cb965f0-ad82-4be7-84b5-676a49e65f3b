import styled from 'styled-components';
import Dropdown from '../../../components/Dropdown';

export const ChannelDropdown = styled(Dropdown)`
  border: 0;
  border-bottom: 1px solid #6a6a6a;
  padding: 0;
  display: flex;
  width: 100%;
  margin-bottom: 1rem;
  height: 30px;

  &:focus {
    border-color: #0090e1;
  }
`;

export const ChannelDropdownLabel = styled.div`
  display: flex;
  align-items: center;
  width: 20%;
  color: #4a4a4a;

  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    width: 30%;
  }
`;

export const ChannelSelectionLabel = styled.label`
  font-size: 14px;
  margin-bottom: 0.5rem;
  display: block;
  margin-left: 40px;
`;

export const ChannelDropdownWrapper = styled.div`
  width: 300px;
  margin: 0 0 40px 40px;
`;
