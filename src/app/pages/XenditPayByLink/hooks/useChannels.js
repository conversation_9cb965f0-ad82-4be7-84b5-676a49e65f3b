import { useState } from 'react';
import { GET_CHANNEL_OPTIONS } from '../query';
import { useQuery } from '@apollo/client';

export const useChannels = () => {
  const [channel, setChannel] = useState('');
  const [creds, setCreds] = useState(null);

  const { data } = useQuery(GET_CHANNEL_OPTIONS, {
    fetchPolicy: 'network-only',
  });

  const mappedChannels =
    data?.payByLinkChannels?.map(channel => ({
      value: channel.id,
      label: channel.name,
      clientId: channel.clientId,
      clientSecret: channel.clientSecret,
    })) || [];

  const handleSetChannel = v => {
    setChannel(v);
    const selectedChannel = mappedChannels.find(c => c.value === v);
    setCreds({
      clientId: selectedChannel.clientId,
      clientSecret: selectedChannel.clientSecret,
    });
  };

  return {
    channelOptions: mappedChannels,
    channel,
    handleSetChannel,
    creds,
  };
};
