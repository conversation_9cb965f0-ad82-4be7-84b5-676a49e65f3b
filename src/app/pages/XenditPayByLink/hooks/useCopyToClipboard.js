export const useCopyToClipboard = (paymentLinkResponse, setMessage) => {
  const copyToClipboard = () => {
    let paymentLink = '';

    if (paymentLinkResponse) {
      paymentLink = paymentLinkResponse.paymentLink;
    }

    navigator.clipboard
      .writeText(paymentLink)
      .then(() => {
        setMessage('Your payment link has been copied to clipboard.');
      })
      .catch(err => {
        console.error('Failed to copy link: ', err);
      });
  };

  return { copyToClipboard };
};
