import { useMemo } from 'react';
import { FIELD_TYPES } from '../../../components/Form/constants';
import * as Yup from 'yup';
import useForm from '../../../hooks/useForm';

export const usePayByLinkForm = handleSubmit => {
  const { fields } = useMemo(() => {
    const fields = {
      amountDue: {
        type: FIELD_TYPES.TEXT,
        label: 'Amount Due',
        validation: Yup.string()
          .matches(
            /^\d*(\.\d+)?$/,
            'Invalid input! Allowed characters are positive number or number with decimal placed only'
          )
          .required('Please enter a value')
          .test('AmountValidation', 'Value cannot be 0.', value => {
            if (value <= 0) {
              return false;
            }
            return true;
          }),
        required: true,
      },
      description: {
        type: FIELD_TYPES.TEXT,
        label: 'Description',
        validation: Yup.string().max(100, 'Must not exceed 100 characters'),
      },
      email: {
        type: FIELD_TYPES.EMAIL,
        label: 'Email',
        placeholder: 'Enter a valid email address',
        validation: Yup.string()
          .email('Must be an email')
          .max(250, 'Must not exceed 250 characters'),
        initialValue: '',
      },
    };

    const initialValue = {};

    return { fields, initialValue };
  });

  const { values, onChange, onBlur, errors, reset, onSubmit } = useForm(
    fields,
    handleSubmit
  );

  return {
    values,
    onChange,
    onBlur,
    errors,
    reset,
    onSubmit,
  };
};
