import { useState } from 'react';

export const usePayByLinkMutation = creds => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [paymentLinkResponse, setPaymentLinkResponse] = useState(null);
  const [error, setError] = useState(null);

  let api = import.meta.env.VITE_REACT_APP_PS_API_URL;
  const isLocal = import.meta.env.VITE_IS_LOCAL;

  async function generateAccessToken(clientId, clientSecret) {
    try {
      if (isLocal === 'true') {
        const result = import.meta.env.VITE_COGNITO_ACCESS_TOKEN;
        return result;
      } else {
        const secret = encodeURIComponent(clientSecret);
        const response = await fetch(
          `${api}/auth/api/v1/accesstoken?clientId=${clientId}&clientSecret=${secret}`
        );
        const result = await response.json();
        return result.results.accessToken;
      }
    } catch (error) {
      console.log('Error:', error);
    }
  }

  async function createPayByLinkSession(accessToken, formValues) {
    try {
      if (!accessToken) {
        throw new Error('No access token provided!');
      }

      const data = {
        command: {
          name: 'PayByLinkPaymentSession',
          payload: {
            gatewayProcessor: 'generic',
            paymentInfo: {
              paymentMethod: 'xendit',
              type: 'PAY_BY_LINK',
              description: formValues.description,
              reusability: 'ONE_TIME_USE',
              linkDuration: 259200, // Equivalent to three days (in seconds)
            },
            settlementInfos: [
              {
                emailAddress: formValues.email,
                amountValue: formValues.amountDue,
                transactionType: 'N',
              },
            ],
          },
        },
      };

      const response = await fetch(`${api}/api/command`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Request-Method': 'POST',
          Authorization: 'Bearer ' + accessToken,
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (result.error) {
        throw new Error(result.error.details);
      }

      return result;
    } catch (error) {
      setError(error);
    }
  }

  async function handleSubmit(values) {
    setIsSubmitting(true);

    /**
     *  Generate access token using info from selected channel
     */

    const { clientId, clientSecret } = creds;
    const accessToken = await generateAccessToken(clientId, clientSecret);

    /**
     * Use the access token to start a pay-by-link payment session
     */

    const response = await createPayByLinkSession(accessToken, values);

    if (response && response?.data) {
      setPaymentLinkResponse({
        externalId: response.data.id,
        paymentGateway: response.data.paymentGateway,
        paymentLink: response.data.paymentLink,
        status: response.data.status,
        amountDue: response.data.amount,
        merchantAccount: response.data.merchantAccount,
        merchantReference: response.data.merchantReference,
        description: response.data.description,
        linkType: '', // What's the value here?
      });
    }

    setIsSubmitting(false);
  }

  return {
    isSubmitting,
    handleSubmit,
    paymentLinkResponse,
    error,
    setError,
  };
};
