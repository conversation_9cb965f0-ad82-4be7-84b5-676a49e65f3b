import { useState } from 'react';

export const usePaymentGatewaySelection = () => {
  const [paymentGateway, setPaymentGateway] = useState('');

  const paymentGatewayOptions = [{ label: 'Xendit', value: 'xen' }];

  const handlePaymentGatewaySelection = value => {
    setPaymentGateway(value);
  };

  return {
    paymentGateway,
    paymentGatewayOptions,
    handlePaymentGatewaySelection,
  };
};
