import { useState } from 'react';

export const useToast = () => {
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');

  const setMessage = message => {
    setToastMessage(message);
    setShowToast(true);
    setTimeout(() => setShowToast(false), 3000); // Auto-hide after 3 seconds
  };

  const closeToast = () => {
    setShowToast(false);
  };

  return {
    showToast,
    closeToast,
    toastMessage,
    setMessage,
  };
};
