import { gql } from '@apollo/client';

export const GET_CHANNEL_OPTIONS = gql`
  query getPayByLinkChannels {
    payByLinkChannels {
      id
      name
      channelId
      clientId
      clientSecret
    }
  }
`;

export const GET_CHANNEL_INFORMATION = gql`
  query getChannelInformation($where: ChannelPrimary!) {
    channelDropInSimulator(where: $where) {
      clientId
      clientSecret
    }
  }
`;
