import styled, { keyframes } from 'styled-components';
import PrimaryButton from '../../components/Button/PrimaryButton';

export const Input = styled.input`
  text-align: left;
  border: none;
  flex: 1;
  margin: 0 10px;
  background-color: white;
  cursor: pointer;
`;

export const InputBar = styled.div`
  flex: 1;
  display: flex;
  flex-direction: row;
  border: 1px solid #ced4da;
  background-color: #ffffff;
  border-radius: 2px;
  padding: 5px 10px;
  font-size: 14px;
  font-weight: 300;
  align-items: center;
  cursor: ${props => (props.noFields ? 'initial' : 'pointer')};
  color: initial;
  border-radius: 5px;
  height: 36px;

  ${Input} {
    background-color: white;
    cursor: pointer;
  }
`;

export const PaymentGatewaySelectionLabel = styled.label`
  font-size: 14px;
  margin-bottom: 0.5rem;
  display: block;
  margin-left: 40px;
`;

export const PaymentGatewayDropdownWrapper = styled.div`
  width: 300px;
  margin: 0 0 40px 40px;
`;

export const FormGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
`;

export const Form = styled.form`
  display: flex;
  flex-direction: column;
  margin-top: 1rem;
  padding: 3rem 0;
  width: 400px;
`;

export const GeneratePaymentLinkButton = styled(PrimaryButton)`
  margin-left: 40px;
`;

export const PaymentLinkRequestForm = styled.form`
  display: flex;
  flex-direction: column;
  width: 30rem;
`;

export const PaymentLinkResponseForm = styled.form`
  display: flex;
  flex-direction: column;
  width: 30rem;
`;

export const PaymentLinkURLField = styled.div`
  display: flex;
  align-items: center;
`;

export const CopyPaymentLink = styled(PrimaryButton)`
  margin-left: 0.5rem;
`;

const popUp = keyframes`
from {
  opacity: 0;
  transform: scale(0.9);
}
to {
  opacity: 1;
  transform: scale(1);
}
`;

export const Toast = styled.div`
  animation: ${popUp} 0.2s ease forwards;
  border: 1px solid rgb(55, 118, 220);
  position: absolute;
  top: 1rem;
  right: 2rem;
  padding: 1rem 2rem;
  display: grid;
  grid-template-columns: 1fr;
  grid-auto-rows: auto;
  background-color: white;
  border-radius: 5px;
  height: 8rem;
  row-gap: 0.8rem;
  font-size: 0.8rem;
  color: rgb(65, 65, 65);
  z-index: 2;
`;
