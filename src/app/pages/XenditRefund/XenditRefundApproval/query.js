import { gql } from '@apollo/client';

export const GET_XENDITREFUNDAPPROVAL = gql`
  query getXenditRefundApproval(
    $filter: SearchXenditRefund!
    $pagination: PaginationInput!
  ) {
    xenditRefundApproval(filter: $filter, pagination: $pagination) {
      lastKey
      filteredData {
        paymentId
        accountNumber
        channelName
        timestamp
        postedTimestamp
        amountValue
        status
        refundAmount
        refundReason
        refundApprovalStatus
        paymentMethod
        finalAmount
        createDateTime
        transactionId
      }
    }
  }
`;

export const GET_XENDITREFUNDHISTORY = gql`
  query getXenditRefundApprovalModuleHistory(
    $filter: SearchXenditRefund!
    $pagination: PaginationInput!
  ) {
    xenditRefundApprovalModuleHistory(
      filter: $filter
      pagination: $pagination
    ) {
      lastKey
      filteredData {
        paymentId
        accountNumber
        channelName
        timestamp
        postedTimestamp
        amountValue
        status
        refundAmount
        refundReason
        refundApprovalStatus
        refundRejectedTimestamp
      }
    }
  }
`;

export const GET_CHANNEL_OPTIONS = gql`
  query getChannelOptions {
    channelsLoose {
      id
      name
      channelId
    }
  }
`;

export const GET_REFUND_REASON = gql`
  query getCardRefundReason {
    getCardRefundReason {
      reason
    }
  }
`;

export const REPORT_PATH = 'xenditRefundApproval';
