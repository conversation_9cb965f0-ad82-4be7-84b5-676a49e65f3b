import format from 'date-fns/format';
import PropTypes from 'prop-types';
import * as Yup from 'yup';
import React, { useState, useEffect } from 'react';
import PrimaryButton from '../../../components/Button/PrimaryButton';
import DataContainer from '../../../components/DataContainer';
import DataTable from '../../../components/DataTable';
import { FIELD_TYPES } from '../../../components/Form/constants';
import GlobalSearch from '../../../components/GlobalSearch';
import Header from '../../../components/Header';
import { AlertModal, FormModal } from '../../../components/Modal';
import { AlertModalIcon, RequestButtonContainer } from '../styled';
import Page from '../../../components/Page';
import { useMutation } from '@apollo/client';
import sanitize from '../../../utils/sanitize';
import { useQuery } from '@apollo/client';
import { REQUEST_REFUND } from './mutation';
import useQueryReportSeries from '../../../hooks/useQueryReportSeries-v2';
import { ColumnVisibilityDropdown, ResponsiveRow } from '../../Reports/styled';
import {
  GET_XENDITREFUNDREQUEST,
  REPORT_PATH,
  GET_CHANNEL_OPTIONS,
} from './query';
import { numberWithCommas } from '../../../components/GlobalSearch/utils';

const XenditRefundRequest = ({ history }) => {
  const [state, setState] = useState({
    isRequestRefundModalOpen: false,
    isConfirmRequestRefundModalOpen: false,
    isSuccesRequestRefundOpen: false,
    isFailureRequestRefundOpen: false,

    days: 0,

    paymentIdValue: null,
    transactionIdValue: null,
    amountPaid: 0,
    selectedPostingDate: null,
    selectedPaymentMethod: null,

    isLeavingPageWhileAdding: false,

    selectedRequestRefund: null,
    nextLocation: null,
    requestRefundError: null,
  });

  const {
    pagination,
    setNewPagination,
    setFilter,
    data,
    loading,
    page,
    setPage,
    isLastPage,
    refetch,
    loadData,
    loadDataOnError,
  } = useQueryReportSeries(GET_XENDITREFUNDREQUEST, REPORT_PATH, {
    pagination: {
      startKey: '',
      limit: 10,
    },
  });

  const { data: channelData, loading: isLoadingChannels } = useQuery(
    GET_CHANNEL_OPTIONS,
    {
      fetchPolicy: 'network-only',
    }
  );

  /*
  const { data: refundReasonData, loading: refundReasonLoading } = useQuery(
    GET_REFUND_REASON,
    {
      fetchPolicy: 'network-only',
    }
  );*/

  useEffect(() => {
    console.log('isRequestingRefund -', isRequestingRefund);
    if (isRequestingRefund) {
      loadData();
    }
  });

  const channelOptions =
    !isLoadingChannels && channelData
      ? channelData.channelsLoose.map(channel => ({
          value: channel.id,
          label: channel.name,
        }))
      : [];

  channelOptions.unshift({
    value: null,
    label: 'Any',
  });

  const refundReasonOptions = [
    { label: 'Fraudulent', value: 'FRAUDULENT' },
    { label: 'Duplicate', value: 'DUPLICATE' },
    { label: 'Requested by Customer', value: 'REQUESTED_BY_CUSTOMER' },
    { label: 'Cancellation', value: 'CANCELLATION' },
    { label: 'Others', value: 'OTHERS' },
  ];

  useEffect(() => {
    const unblock = history.block(location => {
      if (state.isLeavingPageWhileAdding || !state.isRequestRefundModalOpen) {
        return true;
      }
      setState({
        ...state,
        nextLocation: location,
        isLeavingPageWhileAdding: true,
      });
      return false;
    });

    return () => {
      unblock();
    };
  }, [state.isLeavingPageWhileAdding, state.isRequestRefundModalOpen]);

  const [requestRefund, { loading: isRequestingRefund }] = useMutation(
    REQUEST_REFUND,
    {
      onCompleted: () => {
        setState({
          ...state,
          isConfirmRequestRefundModalOpen: false,
          isSuccesRequestRefundOpen: true,
          isRequestRefundModalOpen: false,
        });
        refetch();
      },
      onError: err => {
        setState({
          ...state,
          requestRefundError: err.networkError.result
            ? err.networkError.result.message
            : null,
          isConfirmRequestRefundModalOpen: false,
          isFailureRequestRefundOpen: true,
        });
        loadDataOnError();
      },
    }
  );

  const tableConfig = {
    paymentId: {
      headerLabel: 'Payment ID:',
      sortable: true,
    },
    accountNumber: {
      headerLabel: 'Account No.',
      sortable: true,
    },
    channelName: {
      headerLabel: 'Channel',
      sortable: true,
    },
    createDateTime: {
      headerLabel: 'Transaction Date',
      sortable: true,
      renderAs: data => format(data.createDateTime, 'MM/DD/YYYY - hh:mm:ss A'),
    },
    postedTimestamp: {
      headerLabel: 'Date Posted/Authorised',
      sortable: true,
      renderAs: data => format(data.postedTimestamp, 'MM/DD/YYYY - hh:mm:ss A'),
    },
    amountValue: {
      headerLabel: 'Amount Paid',
      sortable: true,
      renderAs: data =>
        numberWithCommas(
          !data.finalAmount ? data.amountValue : data.finalAmount,
          2
        ),
    },
    status: {
      headerLabel: 'Payment Status',
      sortable: true,
    },
    refundStatus: {
      headerLabel: 'Refund Status',
      sortable: true,
    },
    refundAmount: {
      headerLabel: 'Refund Amount',
      sortable: true,
      renderAs: data =>
        data.refundAmount === null
          ? ''
          : numberWithCommas(data.refundAmount, 2),
    },
    refundReason: {
      headerLabel: 'Refund Reason',
      sortable: true,
    },
    refundDate: {
      headerLabel: 'Refund Date',
      sortable: true,
      renderAs: data =>
        data.refundDate
          ? format(data.refundDate, 'MM/DD/YYYY - hh:mm:ss A')
          : '',
    },
    refundApprovalStatus: {
      renderAs: data => (
        <RequestButtonContainer>
          <PrimaryButton
            onClick={() =>
              setState({
                paymentIdValue: data.paymentId,
                amountPaid: !data.finalAmount
                  ? data.amountValue
                  : data.finalAmount,
                transactionIdValue: data.transactionId,
                selectedPostingDate: data.postedTimestamp,
                selectedPaymentMethod: data.paymentMethod,
                isRequestRefundModalOpen: true,
              })
            }
            disabled={
              data.refundApprovalStatus === 'For Approval' ||
              data.refundApprovalStatus === 'Approved'
            }
          >
            {(data.refundApprovalStatus === 'For Approval' && 'For Approval') ||
              (data.refundApprovalStatus === 'Approved' && 'Approved') ||
              'Request Refund'}
          </PrimaryButton>
        </RequestButtonContainer>
      ),
    },
  };

  const [visibleColumns, setVisibleColumns] = useState(
    Object.keys(tableConfig)
  );

  return (
    <>
      <Page>
        <Header
          withHome
          title="Xendit Refund Request"
          path={['Xendit Refund', 'Xendit Refund Request']}
        />
        <DataContainer>
          <DataTable
            loading={loading}
            data={data}
            minCellWidth={200}
            headerOptions={
              <>
                <GlobalSearch
                  onSearch={filter => {
                    const newPagination = {
                      ...pagination,
                      startKey: '',
                    };
                    setFilter(filter);
                    setNewPagination(newPagination);
                  }}
                  fields={[
                    {
                      label: 'Payment ID',
                      name: 'paymentId',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Channel',
                      name: 'channelId',
                      type: FIELD_TYPES.SELECT,
                      options: channelOptions,
                      isKey: true,
                    },
                    {
                      label: 'Refund Status',
                      name: 'refundStatus',
                      type: FIELD_TYPES.SELECT,
                      options: [
                        { value: null, label: 'Any' },
                        { value: 'PartialRefund', label: 'Partial Refund' },
                        { value: 'FullRefund', label: 'Full Refund' },
                        {
                          value: 'ForApproval',
                          label: 'For Approval',
                        },
                        {
                          value: 'Processing',
                          label: 'Processing',
                        },
                        {
                          value: 'Rejected',
                          label: 'Rejected',
                        },
                        {
                          value: 'Requested',
                          label: 'Requested',
                        },
                        {
                          value: 'ForRequest',
                          label: 'For Request',
                        },
                      ],
                    },
                    {
                      label: 'Payment Status',
                      name: 'status',
                      type: FIELD_TYPES.SELECT,
                      options: [
                        { value: null, label: 'Any' },
                        { value: 'PAYMENT_POSTING', label: 'PAYMENT_POSTING' },
                        { value: 'FALLOUT_POSTED', label: 'FALLOUT_POSTED' },
                        {
                          value: 'CARD_AUTHORISED',
                          label: 'CARD_AUTHORISED',
                        },
                      ],
                    },
                    {
                      label: 'Date Range',
                      name: 'createDateTime',
                      type: FIELD_TYPES.DATE_RANGE,
                    },
                  ]}
                  placeholder="Search entries here..."
                />
                <ResponsiveRow>
                  <ColumnVisibilityDropdown
                    multi
                    showMulti={false}
                    placeholder="Visible Columns"
                    value={visibleColumns}
                    options={Object.keys(tableConfig).map(key => ({
                      value: key,
                      label: tableConfig[key].headerLabel,
                    }))}
                    onChange={setVisibleColumns}
                  />
                </ResponsiveRow>
              </>
            }
            config={Object.keys(tableConfig).reduce((config, key) => {
              if (visibleColumns.includes(key)) {
                config[key] = tableConfig[key];
              }
              return config;
            }, {})}
            pagination={{
              ...pagination,
              startKey: pagination.startKey,
              withStartKeys: true,
              count: 0,
              cursors: [],
              handleChange: setNewPagination,
            }}
            series={{ page, setPage, isLastPage }}
          />
        </DataContainer>
      </Page>
      {state.isRequestRefundModalOpen && (
        <FormModal
          isOpen={state.isRequestRefundModalOpen}
          width="450px"
          handleClose={() =>
            setState({
              ...state,
              isLeavingPageWhileAdding: true,
              isRequestRefundModalOpen: false,
            })
          }
          title="REFUND CONFIRMATION"
          instructions={
            <span>
              <center>
                <AlertModalIcon icon="exclamation-circle" variant="warn" />
              </center>
              <b style={{ fontSize: 20 }}>
                <center>ARE YOU SURE?</center>
              </b>
              <center style={{ fontSize: 17 }}>
                You are about to submit a transaction.
              </center>
              <b style={{ fontSize: 15 }}>
                <br />
                <center>
                  Paid Amount: {numberWithCommas(state.amountPaid, 2)}
                </center>
              </b>
            </span>
          }
          submitText="Yes"
          changeListener={(name, data, values, setValues) => {
            if (name === 'channelName') {
              let { paymentId, refundAmount, refundReason } = values;

              setValues({
                ...values,
                paymentId,
                refundAmount,
                refundReason,
              });
            }
          }}
          handleSubmit={values => {
            setState({
              ...state,
              selectedRequestRefund: {
                paymentId: values.paymentId,
                refundAmount: values.refundAmount,
                refundReason:
                  values.refundReason === 'Others'
                    ? values.others
                    : values.refundReason,
                transactionId: state.transactionIdValue,
              },
              isConfirmRequestRefundModalOpen: true,
            });
          }}
          fields={{
            paymentId: {
              initialValue: state.paymentIdValue,
            },
            transactionId: {
              initialValue: state.transactionId,
            },
            refundAmount: {
              type: FIELD_TYPES.TEXT,
              label: 'Refund Amount',
              placeholder: 'Refund Amount',
              validation: Yup.string()
                .matches(
                  /^\d*(\.\d{0,2})?$/,
                  'Invalid input! Allowed characters are positive number or number with no more than 2 decimal places only'
                )
                .required('Please enter a value')
                .test(
                  'PartialAmountValidation',
                  'Cannot process partially refund a Maya payment method within 24 hours',
                  value => {
                    const paymentMethod = state.selectedPaymentMethod;
                    const postingDate = new Date(state.selectedPostingDate);
                    const amount = state.amountPaid;
                    const currentDate = new Date();
                    const one_day = 1000 * 60 * 60 * 24;
                    const dateDiff =
                      currentDate.getTime() - postingDate.getTime();
                    /* 
                      If Payment method is paymaya and refund is partial, 
                      it can only be refunded after 24hrs
                    */
                    if (
                      paymentMethod === 'Maya' &&
                      value < Number(amount) &&
                      value > 0 &&
                      one_day > dateDiff
                    ) {
                      return false;
                    }
                    return true;
                  }
                )
                .test(
                  'AmountValidation',
                  'Value should not be 0 and not greater than the amount paid',
                  value => {
                    const amount = state.amountPaid;
                    if (Number(value) === 0 || value > Number(amount)) {
                      return false;
                    }
                    return true;
                  }
                ),
              required: true,
              perRow: 1.1,
            },
            refundReason: {
              type: FIELD_TYPES.SELECT,
              label: 'Refund Reason',
              placeholder: 'Refund Reason',
              options: refundReasonOptions,
              validation: Yup.string()
                .nullable()
                .required('Please select a value'),
              required: true,
              perRow: 1.1,
            },
            /*
            others: {
              typeWhen: data =>
                data.refundReason ===
                refundReasonOptions[refundReasonOptions.length - 1].value
                  ? FIELD_TYPES.TEXT
                  : '',
              labelWhen: data =>
                data.refundReason ===
                refundReasonOptions[refundReasonOptions.length - 1].value
                  ? 'Others'
                  : '',
              placeholder: '',
              disableWhen: data =>
                !(
                  data.refundReason ===
                  refundReasonOptions[refundReasonOptions.length - 1].value
                ),
              requiredWhen: data =>
                data.refundReason ===
                refundReasonOptions[refundReasonOptions.length - 1].value,
              validation: data =>
                data.refundReason ===
                refundReasonOptions[refundReasonOptions.length - 1].value
                  ? Yup.string()
                      .min(3, 'Minimum should be 3 characters')
                      .max(50, 'Must not exceed 50 characters')
                      .required('Please enter value')
                  : Yup.string().nullable(),
              perRow: 1.1,
              initialValue: '',
            },
            */
          }}
        />
      )}

      <AlertModal
        isOpen={state.isConfirmRequestRefundModalOpen}
        title="Request Refund Alert"
        icon="exclamation-circle"
        variant="warn"
        header="ARE YOU SURE?"
        subHeader="You are about to Request a Refund."
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        confirmLoading={isRequestingRefund}
        confirmText="Yes"
        handleConfirm={() => {
          requestRefund({
            variables: {
              data: sanitize(state.selectedRequestRefund),
            },
          });
        }}
        handleClose={() => {
          setState({
            ...state,
            isConfirmRequestRefundModalOpen: false,
          });
        }}
      />

      <AlertModal
        isOpen={state.isSuccesRequestRefundOpen}
        title="Request Refund Success"
        handleClose={() => {
          setState({ ...state, isSuccesRequestRefundOpen: false });
        }}
        icon="check-circle"
        variant="success"
        header="SUCCESS!"
        subHeader="Refund has been requested successfully."
        description=""
        confirmText="Go to Refund Request Module"
        handleConfirm={() => {
          setState({ ...state, isSuccesRequestRefundOpen: false });
        }}
      />

      <AlertModal
        isOpen={state.isFailureRequestRefundOpen}
        title="Request Refund Alert"
        icon="times-circle"
        variant="error"
        header="OH, SNAP!"
        subHeader={
          (state.requestRefundError === 'Request for refund already filed' &&
            'Request for refund already filed.') ||
          (state.requestRefundError === 'Request for refund already approved' &&
            'Request for refund already approved') ||
          (state.requestRefundError ===
            'Refund Amount is greater than Payment amount' &&
            'Refund Amount is greater than Payment amount') ||
          state.requestRefundError === ''
        }
        description={
          (state.requestRefundError === 'Request for refund already filed' &&
            'Request for refund already filed.') ||
          (state.requestRefundError === 'Request for refund already approved' &&
            'Request for refund already approved')
            ? 'Please refresh the page to see updated results.'
            : 'Please go back and try requesting it again.'
        }
        handleClose={() =>
          setState({ ...state, isFailureRequestRefundOpen: false })
        }
        confirmText="Go Back"
        handleConfirm={() => {
          setState({ ...state, isFailureRequestRefundOpen: false });
        }}
      />
    </>
  );
};

XenditRefundRequest.propTypes = {
  history: PropTypes.object,
};

export default XenditRefundRequest;
