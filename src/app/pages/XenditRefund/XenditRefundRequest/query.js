import { gql } from '@apollo/client';

export const GET_XENDITREFUNDREQUEST = gql`
  query getXenditRefundRequest(
    $filter: SearchXenditRefund!
    $pagination: PaginationInput!
  ) {
    xenditRefundRequest(filter: $filter, pagination: $pagination) {
      lastKey
      filteredData {
        paymentId
        accountNumber
        channelName
        timestamp
        postedTimestamp
        amountValue
        status
        refundAmount
        refundReason
        refundApprovalStatus
        refundStatus
        refundDate
        paymentMethod
        finalAmount
        refundId
        transactionId
        createDateTime
      }
    }
  }
`;

export const GET_CHANNEL_OPTIONS = gql`
  query getChannelOptions {
    channelsLoose {
      id
      name
      channelId
    }
  }
`;

export const GET_REFUND_REASON = gql`
  query getCardRefundReason {
    getCardRefundReason {
      reason
    }
  }
`;

export const REPORT_PATH = 'xenditRefundRequest';
