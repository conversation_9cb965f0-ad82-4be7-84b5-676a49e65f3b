import React, { useContext } from 'react';
import { BrowserRouter, Redirect, Route, Switch } from 'react-router-dom';
import PrivateRoute from './components/PrivateRoute';
import AuthContext from './context/AuthContext/AuthContext';
import Page404 from './pages/404';
import ADASimulation from './pages/ADASimulation';
import Archive from './pages/Archive';
import ArchiveAuditLogs from './pages/Archive/AuditLogs';
import RudyDBReferenceCodes from './pages/Archive/RudyDBReferenceCodes';
import RudyDBTransactionLogs from './pages/Archive/RudyDBTransactionLogs';
import ArchiveSwipeTransactionLogs from './pages/Archive/SwipeTransactionLogs';
import ArchiveTransactionLogs from './pages/Archive/TransactionLogs';
import AuditTrail from './pages/AuditTrail/AuditTrail';
import AuditTrailInformation from './pages/AuditTrailInformation';
import BankCodeManagement from './pages/BankCodeManagement/BankCodeManagement';
import BillLinerConfig from './pages/BillLinerConfiguration';
import CardRefundApproval from './pages/CardRefund/CardRefundApproval';
import CardRefundRequest from './pages/CardRefund/CardRefundRequest';
import ChannelInformation from './pages/ChannelInformation';
import ChannelManagement from './pages/ChannelManagement';
import ChannelVerification from './pages/ChannelVerification/ChannelVerification';
import Checkout from './pages/Checkout';
import Dashboard from './pages/Dashboard';
import DropinSimulation from './pages/DropinSimulation';
import FAQ from './pages/FAQ/FAQ';
import GCashRefundApproval from './pages/GCashRefund/GCashRefundApproval';
import GCashRefundRequest from './pages/GCashRefund/GCashRefundRequest';
import Installment from './pages/Installment/Installment';
import Login from './pages/Login';
import LoginCallback from './pages/Login/LoginCallback';
import addMid from './pages/MID/AddMID';
import MID from './pages/MID/MID';
import MIDInformation from './pages/MIDInformation/MIDInformation';
import Notifications from './pages/Notifications';
import PaymentStatus from './pages/PaymentStatus';
import ProviderManagement from './pages/ProviderManagement';
import ADADetailed from './pages/Reports/ADADeclinedReport';
import ADASummary from './pages/Reports/ADASummaryReport';
import BatchFiles from './pages/Reports/BatchFiles';
import Billing from './pages/Reports/Billing';
import CardDetailed from './pages/Reports/CardDetailedReport';
import CardSummary from './pages/Reports/CardSummaryReport';
import ChannelTransactions from './pages/Reports/ChannelTransactions/ChannelTransactions';
import ContentGcash from './pages/Reports/ContentGCashReports';
import EndGameTransactionLogs from './pages/Reports/EndGameTransactionLogs';
import FailedPostings from './pages/Reports/FailedPostings';
import GatewayCollection from './pages/Reports/GatewayCollection';
import GatewayCreditCard from './pages/Reports/GatewayCreditCard';
import GCashECPay from './pages/Reports/GCashECPay';
import GCashReportDetailed from './pages/Reports/GCashRefundReportDetailed';
import GCashReportSummarized from './pages/Reports/GCashRefundReportSummarized';
import GlobeOne from './pages/Reports/GlobeOne';
import GotsReport from './pages/Reports/GotsReport/GotsReport';
import InstallmentReport from './pages/Reports/InstallmentReport';
import LoadORReport from './pages/Reports/LoadORReport';
import MonthlyGenerated from './pages/Reports/MonthlyGenerated';
import MGGatewayCollection from './pages/Reports/MonthlyGenerated/MGGatewayCollection';
import CollectionCompany from './pages/Reports/MonthlyGenerated/MGGatewayCollection/CollectionCompany';
import CollectionMID from './pages/Reports/MonthlyGenerated/MGGatewayCollection/CollectionMID';
import MGGatewayCreditCard from './pages/Reports/MonthlyGenerated/MGGatewayCreditCard';
import MGRevenueWireless from './pages/Reports/MonthlyGenerated/MGRevenueWireless';
import WirelessMode from './pages/Reports/MonthlyGenerated/MGRevenueWireless/WirelessMode';
import WirelessType from './pages/Reports/MonthlyGenerated/MGRevenueWireless/WirelessType';
import PayByLink from './pages/Reports/PayByLink';
import RevenueAccountingWireline from './pages/Reports/RevenueAccountingReport';
import SwipeTransactionLogs from './pages/Reports/SwipeTransactionLogs';
import TransactionLogs from './pages/Reports/TransactionLogs';
import TreasuryBill from './pages/Reports/TreasuryBill';
import Monthly from './pages/Reports/TreasuryBill/Monthly/Monthly';
import YTD from './pages/Reports/TreasuryBill/YTD/YTD';
import XenditDetailed from './pages/Reports/XenditRefundReportDetailed';
import XenditSummary from './pages/Reports/XenditRefundReportSummarized';
import SystemConfig from './pages/SystemConfig';
import UserAccounts from './pages/UserAccounts';
import UserInformation from './pages/UserInformation';
import UserRoleInformation from './pages/UserRoleInformation';
import UserRoles from './pages/UserRoles';
import XenditRefundApproval from './pages/XenditRefund/XenditRefundApproval';
import XenditRefundRequest from './pages/XenditRefund/XenditRefundRequest';
import XenditPayByLink from './pages/XenditPayByLink';
import XenditPayByLinkReport from './pages/Reports/XenditPayByLinkReport';
import PaymentModeManagement from './pages/PaymentModeManagement';
import PaymentMode from './pages/PaymentMode';
import GCashEnrolledPaymentMethods from './pages/GCashEnrolledPaymentMethods';
import ConvenienceFee from './pages/ConvenienceFee';
import ConvenienceFeeBrands from './pages/ConvenienceFeeBrands';

const Router = () => {
  const { permissions, reportPermissions, dashboardPermissions, me } =
    useContext(AuthContext);

  return (
    <BrowserRouter>
      <Switch>
        <Route exact path={'/login'} component={Login} />
        <Route exact path={'/callback'} component={LoginCallback} />
        <PrivateRoute exact path={'/'} component={Dashboard} />
        <PrivateRoute exact path={'/notifications'} component={Notifications} />
        {permissions.User.view && (
          <PrivateRoute
            exact
            path={'/user-accounts'}
            component={UserAccounts}
          />
        )}

        {permissions.User.view && (
          <PrivateRoute
            exact
            path={'/user-accounts/:id'}
            component={UserInformation}
          />
        )}

        {permissions.Audit.view && (
          <PrivateRoute exact path={'/audit-trail'} component={AuditTrail} />
        )}
        {permissions.Audit.view && (
          <PrivateRoute
            exact
            path={'/audit-trail/:id'}
            component={AuditTrailInformation}
          />
        )}
        {permissions.Role.view && (
          <PrivateRoute exact path={'/user-roles'} component={UserRoles} />
        )}
        {permissions.Role.view && (
          <PrivateRoute
            exact
            path={'/user-roles/:id'}
            component={UserRoleInformation}
          />
        )}
        {permissions.Channel.view && (
          <PrivateRoute
            exact
            path={'/channels-management'}
            component={ChannelManagement}
          />
        )}
        {permissions.Channel.view && (
          <PrivateRoute
            exact
            path={'/channels-management/:id'}
            component={ChannelInformation}
          />
        )}
        {permissions.PostPaymentConfig.view && (
          <PrivateRoute
            exact
            path={'/payment-mode-management'}
            component={PaymentModeManagement}
          />
        )}
        {permissions.GCashBindingReport.view && (
          <PrivateRoute
            exact
            path={'/gcash-enrolled-payment-methods'}
            component={GCashEnrolledPaymentMethods}
          />
        )}
        {permissions.PostPaymentConfig.create && (
          <PrivateRoute
            exact
            path={'/payment-mode-management/add'}
            component={PaymentMode}
          />
        )}
        {permissions.ConvenienceFee.view && (
          <PrivateRoute
            exact
            path={'/convenience-fee'}
            component={ConvenienceFee}
          />
        )}
        {permissions.ConvenienceFeeBrand.view && (
          <PrivateRoute
            exact
            path={'/convenience-fee-brands'}
            component={ConvenienceFeeBrands}
          />
        )}

        {permissions.Mid.view && (
          <PrivateRoute exact path={'/mid'} component={MID} />
        )}
        {permissions.Mid.view && (
          <PrivateRoute exact path={'/mid/:id'} component={MIDInformation} />
        )}

        {permissions.Mid.create && (
          <PrivateRoute exact path={'/addmid/'} component={addMid} />
        )}

        {permissions.PayByLinkModule.view && (
          <PrivateRoute
            exact
            path={'/pay-by-link'}
            component={XenditPayByLink}
          />
        )}
        {permissions.GcashRefundRequest.view && (
          <PrivateRoute
            exact
            path={'/gcash-refund-request'}
            component={GCashRefundRequest}
          />
        )}
        {permissions.GcashRefundApproval.view && (
          <PrivateRoute
            exact
            path={'/gcash-refund-approval'}
            component={GCashRefundApproval}
          />
        )}
        {permissions.XenditRefundRequest.view && (
          <PrivateRoute
            exact
            path={'/xendit-refund-request'}
            component={XenditRefundRequest}
          />
        )}
        {permissions.XenditRefundApproval.view && (
          <PrivateRoute
            exact
            path={'/xendit-refund-approval'}
            component={XenditRefundApproval}
          />
        )}
        {permissions.CardRefundRequest.view && (
          <PrivateRoute
            exact
            path={'/adyen-refund-request'}
            component={CardRefundRequest}
          />
        )}
        {permissions.CardRefundApproval.view && (
          <PrivateRoute
            exact
            path={'/adyen-refund-approval'}
            component={CardRefundApproval}
          />
        )}
        {permissions.Provider.view && (
          <PrivateRoute
            exact
            path={'/provider-management'}
            component={ProviderManagement}
          />
        )}
        {permissions.InstallmentMid.view && (
          <Route exact path={'/installment-mid'} component={Installment} />
        )}
        {permissions.Bank.view && (
          <PrivateRoute
            exact
            path={'/bankcode-management'}
            component={BankCodeManagement}
          />
        )}
        {reportPermissions.Transaction.view && (
          <PrivateRoute
            exact
            path="/transaction-logs"
            component={TransactionLogs}
          />
        )}
        {reportPermissions.EndGameReport.view && (
          <PrivateRoute
            exact
            path="/endgame-transaction-logs"
            component={EndGameTransactionLogs}
          />
        )}
        {reportPermissions.ChannelReport.view && (
          <PrivateRoute
            exact
            path="/channel-transactions"
            component={ChannelTransactions}
          />
        )}
        {reportPermissions.LoadORReport.view && (
          <PrivateRoute exact path="/or-report" component={LoadORReport} />
        )}
        {reportPermissions.GotsReport.view && (
          <PrivateRoute exact path="/gots-report" component={GotsReport} />
        )}
        {reportPermissions.InstallmentReport.view && (
          <PrivateRoute
            exact
            path="/installment-report"
            component={InstallmentReport}
          />
        )}
        {reportPermissions.ECPay.view && (
          <PrivateRoute
            exact
            path="/gcash-ecpay-wallet"
            component={GCashECPay}
          />
        )}
        {reportPermissions.PayByLink.view && (
          <PrivateRoute exact path="/paybylink" component={PayByLink} />
        )}
        {reportPermissions.GlobeOne.view && (
          <PrivateRoute exact path="/globe-one-logs" component={GlobeOne} />
        )}
        {reportPermissions.ContentGcashReport.view && (
          <PrivateRoute
            exact
            path="/content-gcash-reports"
            component={ContentGcash}
          />
        )}
        {reportPermissions.ContentFraudReport.view && (
          <PrivateRoute
            exact
            path="/content-fraud-reports"
            component={SwipeTransactionLogs}
          />
        )}
        {reportPermissions.GcashRefundDetailedReport.view && (
          <PrivateRoute
            exact
            path="/gcash-refund-report-detailed"
            component={GCashReportDetailed}
          />
        )}
        {reportPermissions.GcashRefundSummaryReport.view && (
          <PrivateRoute
            exact
            path="/gcash-refund-report-summarized"
            component={GCashReportSummarized}
          />
        )}
        {reportPermissions.CardRefundDetailedReport.view && (
          <PrivateRoute
            exact
            path="/adyen-refund-detailed-report"
            component={CardDetailed}
          />
        )}
        {reportPermissions.CardRefundSummaryReport.view && (
          <PrivateRoute
            exact
            path="/adyen-refund-summary-report"
            component={CardSummary}
          />
        )}
        {reportPermissions.XenditRefundDetailedReport.view && (
          <PrivateRoute
            exact
            path="/xendit-refund-report-detailed"
            component={XenditDetailed}
          />
        )}
        {reportPermissions.XenditRefundSummaryReport.view && (
          <PrivateRoute
            exact
            path="/xendit-refund-report-summarized"
            component={XenditSummary}
          />
        )}
        {reportPermissions.ADADeclinedReport.view && (
          <PrivateRoute
            exact
            path="/ada-declined-rate-detailed-report"
            component={ADADetailed}
          />
        )}
        {reportPermissions.ADADeclinedReport.view && (
          <PrivateRoute
            exact
            path="/ada-declined-rate-summary-report"
            component={ADASummary}
          />
        )}
        {reportPermissions.Gateway.view && (
          <PrivateRoute
            exact
            path="/gateway-credit-card"
            component={GatewayCreditCard}
          />
        )}
        {reportPermissions.Collection.view && (
          <PrivateRoute
            exact
            path="/gateway-collection"
            component={GatewayCollection}
          />
        )}
        {reportPermissions.MonthlyGenerated.view && (
          <PrivateRoute
            exact
            path="/monthly-generated"
            component={MonthlyGenerated}
          />
        )}
        {reportPermissions.MonthlyGenerated.view && (
          <PrivateRoute
            exact
            path="/monthly-generated/gateway-credit-card"
            component={MGGatewayCreditCard}
          />
        )}
        {reportPermissions.MonthlyGenerated.view && (
          <PrivateRoute
            exact
            path="/monthly-generated/gateway-collection"
            component={MGGatewayCollection}
          />
        )}
        {reportPermissions.MonthlyGenerated.view && (
          <PrivateRoute
            exact
            path="/monthly-generated/gateway-collection/company"
            component={CollectionCompany}
          />
        )}
        {reportPermissions.MonthlyGenerated.view && (
          <PrivateRoute
            exact
            path="/monthly-generated/gateway-collection/mid"
            component={CollectionMID}
          />
        )}
        {reportPermissions.MonthlyGenerated.view && (
          <PrivateRoute
            exact
            path="/monthly-generated/revenue-wireless"
            component={MGRevenueWireless}
          />
        )}
        {reportPermissions.MonthlyGenerated.view && (
          <PrivateRoute
            exact
            path="/monthly-generated/revenue-wireless/mode"
            component={WirelessMode}
          />
        )}
        {reportPermissions.MonthlyGenerated.view && (
          <PrivateRoute
            exact
            path="/monthly-generated/revenue-wireless/type"
            component={WirelessType}
          />
        )}
        {reportPermissions.Wireline.view && (
          <PrivateRoute
            exact
            path="/revenue-accounting-report"
            component={RevenueAccountingWireline}
          />
        )}
        {reportPermissions.Treasury.view && (
          <PrivateRoute exact path="/treasury-bill" component={TreasuryBill} />
        )}
        {reportPermissions.Treasury.view && (
          <PrivateRoute exact path="/treasury-bill/ytd" component={YTD} />
        )}
        {reportPermissions.Treasury.view && (
          <PrivateRoute
            exact
            path="/treasury-bill/monthly"
            component={Monthly}
          />
        )}
        {reportPermissions.Billing.view && (
          <PrivateRoute exact path="/billing" component={Billing} />
        )}
        {reportPermissions.Failed.view && (
          <PrivateRoute
            exact
            path="/failed-postings"
            component={FailedPostings}
          />
        )}
        {reportPermissions.LukeBatchFile.view && (
          <PrivateRoute exact path="/batchfiles" component={BatchFiles} />
        )}
        {reportPermissions.PayByLinkReport.view && (
          <PrivateRoute
            exact
            path="/pay-by-link-report"
            component={XenditPayByLinkReport}
          />
        )}
        {dashboardPermissions.gatewayStatus && (
          <PrivateRoute
            exact
            path={'/payment-status'}
            component={PaymentStatus}
          />
        )}
        {permissions.Channel.view && (
          <PrivateRoute
            exact
            path={'/verify/:email'}
            component={ChannelVerification}
          />
        )}
        {permissions.Config.view && (
          <PrivateRoute exact path="/config" component={SystemConfig} />
        )}
        {permissions.BillLinerConfig.view && (
          <PrivateRoute
            exact
            path="/bill-liner-config"
            component={BillLinerConfig}
          />
        )}
        {permissions.Archive.view && (
          <PrivateRoute
            exact
            path="/rudydb/transaction-logs"
            component={RudyDBTransactionLogs}
          />
        )}
        {permissions.Archive.view && (
          <PrivateRoute
            exact
            path="/rudydb/reference-codes"
            component={RudyDBReferenceCodes}
          />
        )}
        {permissions.Archive.view && (
          <PrivateRoute
            exact
            path="/archive/transaction-logs"
            component={ArchiveTransactionLogs}
          />
        )}
        {permissions.Archive.view && (
          <PrivateRoute
            exact
            path="/archive/audit-logs"
            component={ArchiveAuditLogs}
          />
        )}
        {permissions.Archive.view && (
          <PrivateRoute
            exact
            path="/archive/swipe-transaction-logs"
            component={ArchiveSwipeTransactionLogs}
          />
        )}
        {permissions.Archive.view && (
          <PrivateRoute exact path="/archive" component={Archive} />
        )}
        <Route exact path={'/checkout'} component={Checkout} />
        <PrivateRoute exact path={'/ADASimulation'} component={ADASimulation} />

        {reportPermissions.DropinSimulator.view && (
          <PrivateRoute
            exact
            path={'/DropinSimulation'}
            component={DropinSimulation}
          />
        )}

        <PrivateRoute exact path="/help" component={FAQ} />
        {me ? (
          <Route component={Page404} />
        ) : (
          <Redirect to="/login"> </Redirect>
        )}
      </Switch>
    </BrowserRouter>
  );
};

export default Router;
