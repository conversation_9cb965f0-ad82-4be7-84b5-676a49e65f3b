import { formatSubHeader, formatDescription } from '../getLabel';

describe('formatSubHeader', () => {
  test('should format correctly', () => {
    expect(formatSubHeader('Invalid File')).toBe('Invalid file format.');
  });

  test('should format correctly', () => {
    expect(formatSubHeader('No Data Found')).toBe(
      'There are empty required fields.'
    );
  });

  test('should format correctly', () => {
    expect(formatSubHeader('ROLE_NOT_FOUND: ')).toBe(
      'There are empty required fields.'
    );
  });

  test('should format correctly', () => {
    expect(formatSubHeader('ROLE_NOT_FOUND: sample role')).toBe(
      'There are incorrect inputs in the file.'
    );
  });

  test('should format correctly', () => {
    expect(
      formatSubHeader([
        {
          message: 'mobile number is required',
        },
      ])
    ).toBe('There are empty required fields.');
  });

  test('should format correctly', () => {
    expect(formatSubHeader('sample')).toBe(
      'There are incorrect inputs in the file.'
    );
  });

  test('should format correctly', () => {
    expect(formatSubHeader('')).toBe('There are incorrect inputs in the file.');
  });
});

describe('formatDescription', () => {
  test('should format correctly', () => {
    expect(formatDescription('Invalid File')).toBe(
      'Kindly upload a valid .CSV file.'
    );
  });

  test('should format correctly', () => {
    expect(formatDescription('No Data Found')).toBe(
      'Kindly check the file before uploading.'
    );
  });

  test('should format correctly', () => {
    expect(formatDescription('ROLE_NOT_FOUND: ')).toBe(
      'Kindly check the file before uploading.'
    );
  });

  test('should format correctly', () => {
    expect(formatDescription('ROLE_NOT_FOUND: sample role')).toBe(
      'Please check the file and import again.'
    );
  });

  test('should format correctly', () => {
    expect(
      formatDescription([
        {
          message: 'mobile number is required',
        },
      ])
    ).toBe('Kindly check the file before uploading.');
  });

  test('should format correctly', () => {
    expect(formatDescription('sample')).toBe(
      'Please check the file and import again.'
    );
  });

  test('should format correctly', () => {
    expect(formatDescription('')).toBe(
      'Please check the file and import again.'
    );
  });
});
