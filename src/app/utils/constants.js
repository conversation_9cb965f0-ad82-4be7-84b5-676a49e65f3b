export const BILL_TYPE_OPTIONS = [
  { value: 'Bill', label: 'Bill' },
  { value: 'NonBill', label: 'NonBill' },
  { value: 'Both', label: 'Both' },
];

// Available payment gateways
export const AVAILABLE_GATEWAYS = [
  {
    value: 'xendit',
    label: 'Xendit',
    color: 'green',
  },
];

// Available payment methods
export const AVAILABLE_PAYMENT_METHODS = {
  xendit: [
    {
      value: 'cards',
      label: 'Cards',
      color: 'green',
    },
    {
      value: 'otc',
      label: 'Over the Counter',
      color: 'green',
    },
  ],
};

// Available payment method types
export const AVAILABLE_PAYMENT_METHOD_TYPES = {
  cards: [
    {
      value: 'credit',
      label: 'Credit',
      color: 'green',
    },
    {
      value: 'debit',
      label: 'Debit',
      color: 'green',
    },
  ],
  otc: [
    {
      value: 'ecpay',
      label: 'ECPay',
      color: 'green',
    },
  ],
};

// Toggle options for enabling/disabling features
export const TOGGLE_OPTIONS = [
  {
    value: false,
    color: 'red',
    label: 'Off',
  },
  {
    value: true,
    color: 'green',
    label: 'On',
  },
];
