// TODO: Add formats for more flexibility
// Defaults to 'MM/DD/YYYY - hh:mm:ss A' format
const formatUtcDate = phDateTime => {
  if (!phDateTime) {
    return 'Invalid Date';
  }

  const date = new Date(phDateTime);

  // Format date (MM/DD/YYYY)
  const dateFormatter = new Intl.DateTimeFormat('en-US', {
    month: '2-digit',
    day: '2-digit',
    year: 'numeric',
    timeZone: 'Asia/Manila',
  });

  // Format time (hh:mm:ss AM/PM)
  const timeFormatter = new Intl.DateTimeFormat('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: true,
    timeZone: 'Asia/Manila',
  });

  return `${dateFormatter.format(date)} - ${timeFormatter.format(date)}`;
};


export default formatUtcDate;
