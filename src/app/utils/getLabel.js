export function formatSubHeader(subHeader) {
  if (subHeader.includes('Invalid File')) {
    return 'Invalid file format.';
  } else if (
    subHeader.includes('No Data Found') ||
    (subHeader.includes('ROLE_NOT_FOUND:') && subHeader.split(' ')[1] === '') ||
    (typeof subHeader !== 'string' && subHeader[0].message.includes('required'))
  ) {
    return 'There are empty required fields.';
  }
  return 'There are incorrect inputs in the file.';
}

export function formatDescription(description) {
  if (description.includes('Invalid File')) {
    return 'Kindly upload a valid .CSV file.';
  } else if (
    description.includes('No Data Found') ||
    (description.includes('ROLE_NOT_FOUND:') &&
      description.split(' ')[1] === '') ||
    (typeof description !== 'string' &&
      description[0].message.includes('required'))
  ) {
    return 'Kindly check the file before uploading.';
  }

  return 'Please check the file and import again.';
}
