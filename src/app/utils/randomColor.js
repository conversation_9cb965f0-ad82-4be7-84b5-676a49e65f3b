// https://martin.ankerl.com/2009/12/09/how-to-create-random-colors-programmatically/

function hsvToRGB(h, s, v) {
  const h_i = Math.floor(h * 6);
  const f = h * 6 - h;
  const p = v * (1 - s);
  const q = v * (1 - f * s);
  const t = v * (1 - (1 - f) * s);

  const convert = values =>
    `rgb(${values.map(val => Math.floor(val * 256)).join(', ')})`;

  if (h_i === 0) return convert([v, t, p]);
  if (h_i === 1) return convert([q, v, p]);
  if (h_i === 2) return convert([p, v, t]);
  if (h_i === 3) return convert([p, q, v]);
  if (h_i === 4) return convert([t, p, v]);
  if (h_i === 5) return convert([v, p, q]);
}

const golden_ratio_conjugate = 0.618033988749895;

let h = Math.random();

// consistency between objects
const mapping = {};

function randomColor(id) {
  if (mapping[id]) return mapping[id];
  h += golden_ratio_conjugate;
  h %= 1;
  const color = hsvToRGB(h, 0.5, 0.75);
  mapping[id] = color;
  return color;
}

export default randomColor;
