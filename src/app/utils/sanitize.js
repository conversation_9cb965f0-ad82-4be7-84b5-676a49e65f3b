// given an object `values`,
// returns the same object but removes all keys with falsy values
const sanitize = (values = {}) => {
  const obj = {};
  Object.keys(values).forEach(key => {
    if (['boolean', 'number'].includes(typeof values[key]) || !!values[key])
      obj[key] = values[key];

    if (Array.isArray(values[key]) && !values[key].length) {
      delete obj[key];
    }
  });
  return obj;
};

export default sanitize;
