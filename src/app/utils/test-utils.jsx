import React from 'react';
import { render } from '@testing-library/react';
import { ThemeProvider } from 'styled-components';
import theme from '../theme';

function customRender(ui, options = {}) {
  return render(ui, {
    wrapper: ({ children }) => (
      <ThemeProvider theme={theme}>{children}</ThemeProvider>
    ),
    ...options,
  });
}

// Re-export everything
export * from '@testing-library/react';
// Override render method
export { customRender as render };
export { default as userEvent } from '@testing-library/user-event';
