import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import svgr from 'vite-plugin-svgr';

export default ({ mode }) => {
  process.env = { ...process.env, ...loadEnv(mode, process.cwd()) };

  return defineConfig({
    plugins: [
      react({
        jsxRuntime: 'classic',
      }),
      svgr({
        include: '**/*.svg?react',
      }),
    ],
    server: {
      port: 4000,
    },
    test: {
      globals: true,
      environment: 'jsdom',
      pool: 'forks',
      coverage: {
        enabled: true,
        reporter: ['text', 'json', 'html'],
      },
    },
    build: {
      sourcemap: mode === 'development',
    },
  });
};
